<?php
/*
 |--------------------------------------
 |  EventStoreRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Models\EventStore;

class EventStoreRepository extends AbsRepository
{

    /**
     * Type: 門市試吃
     */
    const SHOP_TASTING = 'shop_tasting';

    /**
     * Type: 宅配試吃
     */
    const DELIVERY_TASTING = 'delivery_tasting';

    public function __construct()
    {
        $this->model = new EventStore;
    }

    /**
     * 取得商店是否有填寫關聯類型表單
     *
     *  @param int $store_id
     *  @param int $eventId
     *  @return string
     */
    public function getType(int $store_id, int $eventId): string
    {
        $getData = $this->model->where([['store_id', $store_id], ['event_id', $eventId]])
            ->first();

        return $getData->type;
    }
}
