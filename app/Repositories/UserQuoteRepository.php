<?php
declare(strict_types=1);
/*------------------------------------
 | 使用者詢價單的Repository
 |------------------------------------
 |
 |
 |
 */

namespace App\Repositories;

use App\Models\Wdv2\UserQuote;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\ParseKeywordTrait;

class UserQuoteRepository extends AbsRepository
{
    use ParseKeywordTrait;

    protected $model;

    public function __construct(UserQuote $userQuote)
    {
        $this->model = $userQuote;
    }

    /**
     * 取得詢價單列表
     * @param $request
     */
    public function getYzcubeListByRequest($request, $pagination = true)
    {
        $model = $this->model->with('user')
            ->withCount('storeQuotes');

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 日期區間
        if ($request['start_date'] && $request['end_date']) {
            $startDate = $request['start_date'] . ' 00:00:00';
            $endDate = $request['end_date'] . ' 23:59:59';
            $model = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 商家類型
        if ($request['store_type']) {
            $model = $model->where('type', $request['store_type']);
        }

        // 地區
        if ($request['city_code']) {
            $model = $model->where('city_code', $request['city_code']);
        }

        // 狀態 status
        if ($request['status']) {
            $model = $request['status'] == 'valid' ?
                $model->where('show_flag', '>', 0) :
                $model->where('show_flag', '=', 0);
        }

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }

    /**
     * 取得詢價單內容
     * @param $quoteID: 詢價單id
     * @return mixed
     */
    public function getContentByQuoteID($quoteID)
    {
        return $this->model->with('user', 'storeQuotes.store')
            ->withCount('storeQuotes')
            ->where('id', $quoteID)
            ->first();
    }
}
