<?php
/*
 |--------------------------------------
 |  AdCampaignRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Traits\ParseKeywordTrait;
use App\Models\AdCampaign;

class AdCampaignRepository extends AbsRepository
{
    use BaseRepositoryTrait;
    use ParseKeywordTrait;

    public function __construct(AdCampaign $adCampaign)
    {
        $this->model = $adCampaign;
    }

    /**
     * 神之後台-取得發佈中的版位
     *
     *  @return model
     */
    public function getPublishedList($type)
    {
        return $this->model->where('type', $type)
                            ->published()
                            ->orderBy('sequence')
                            ->orderBy('created_at', 'DESC')
                            ->get();
    }

    /**
     * 神之後台-廣告活動-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request, $type, $pagination = true)
    {
        $model = $this->model->where('type', $type);

        // 狀態 published:發佈中 scheduling:排程中 completed:已結束
        if ($request['status']) {
            $model = $model->{$request['status']}();
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword, $type);
        }

        // 排序
        $model = $model->orderBy('sequence')
                        ->orderBy('created_at', 'DESC');

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }

    /**
     * 神之後台-取得日期區間中有發佈中的版位
     *
     *  @return model
     */
    public function getPublishedByDateRange($type, $startDate, $endDate, $excludeId = NULL)
    {
        return $this->model->where('type', $type)
                            ->publishedByDateRange($startDate, $endDate)
                            ->where('id', '!=', $excludeId)
                            ->get();
    }
}
