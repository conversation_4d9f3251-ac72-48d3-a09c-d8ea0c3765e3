<?php
/*
 |--------------------------------------
 |  PushMessageRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Models\PushMessage;
use App\Traits\BaseRepositoryTrait;
use App\Services\Tools\KeywordSearch\SearchService;

class PushMessageRepository extends AbsRepository
{
    private $keywordSearchService;

    use BaseRepositoryTrait;

    public function __construct(
        PushMessage $pushMessage,
        SearchService $keywordSearchService
    ) {
        $this->model                = $pushMessage;
        $this->keywordSearchService = $keywordSearchService;
    }

    /**
     *  神之後台-全站推播-取得某接收者類型的排程訊息
     */
    public function getSchedulingListByReceiverType($receiverType)
    {
        return $this->model->where('receiver_type', $receiverType)
                            ->where('status', 'scheduling')
                            ->orderBy('set_publish_at', 'DESC')
                            ->get();
    }

    /**
     *  神之後台-全站推播-已推播訊息
     * @param $request
     */
    public function getPushedListByRequest($request)
    {
        // 已發佈的推播
        $model = $this->model->whereIn('status', ['immediately', 'published']);

        // receiver_type 接收者類型
        $model = $model->where('receiver_type', $request['receiver_type']);

        // keyword 關鏈字搜尋
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $this->keywordSearchService->search($model, $keyword);
        }

        // 排序 sort:id|published_at(default)
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'published_at';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
