<?php
/*
 |--------------------------------------
 |  BlogArticleRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\ParseKeywordTrait;
use App\Models\BlogArticle;

class BlogArticleRepository extends AbsRepository
{
    use ParseKeywordTrait;

    public function __construct(BlogArticle $blogArticle)
    {
        $this->model = $blogArticle;
    }

    /**
     * 隨機取得近期的部落格文章
     *
     *  @return model
     */
    public function getRandomNewSampleList($type, $limit)
    {
        // 近3個月內發佈的
        $after_at = now()->subMonths(3);

        return $this->model->select('image', 'category', 'title', 'blog_id')
                            ->type($type)
                            ->where('published_at', '>=', $after_at)
                            ->inRandomOrder()
                            ->limit($limit)
                            ->get();
    }

    /**
     * 神之後台商家管理-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 類別 type:kol(default)|blog
        if ($request['type']) {
            $model = $model->where('type', $request['type']);
        }

        // 分類名稱 category
        if ($request['category']) {
            if ($request['category'] == 'other') {
                $model = $model->where(function ($q) {
                    $q->whereNotIn('category', $this->model->categoryList)
                        ->orWhereNull('category');
                });
            } else {
                $category = $this->model->categoryList[$request['category']];
                $model    = $model->where('category', $category);
            }
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序 sort:id(default)|published_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
