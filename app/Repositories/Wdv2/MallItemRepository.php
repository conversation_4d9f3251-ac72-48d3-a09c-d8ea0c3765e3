<?php
/*
 |--------------------------------------
 |  MallItemRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories\Wdv2;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\Wdv2\MallItem;
use App\Models\Wdv2\SetTag;
use App\Models\Wdv2\SetTagRelationship;

class MallItemRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    // 價格區間列表
    public $priceRangeList = [
        ['label' => '20元以下', 'value' => ['', 20]],
        ['label' => '21 ~ 50 元', 'value' => [21, 50]],
        ['label' => '51 ~ 100 元', 'value' => [51, 100]],
        ['label' => '101 ~ 300 元', 'value' => [101, 300]],
        ['label' => '301 ~ 500 元', 'value' => [301, 500]],
        ['label' => '501 ~ 1000 元', 'value' => [501, 1000]],
        ['label' => '1001 ~ 2000 元', 'value' => [1001, 2000]],
        ['label' => '2001 元以上', 'value' => [2001, '']],
    ];

    public function __construct(
        MallItem $mallItem
    ) {
        $this->model = $mallItem;
    }

    /**
     * 婚禮小物列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->select('mall_item.id', 'mall_store.store_id', 'stores.name', 'title', 'price', 'source', 'mall_item.description');

        // 已付費的商家
        $model = $model->storeHasPaid();

        // 商家的使用費限制內
        $model = $model->storeUsageActive();

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $model->searchKeyword($keyword);
        }

        // 價格區間 price_range[] (聯集)
        if ($request['price_range']) {
            $model->where(function ($q1) use ($request) {
                foreach ($request['price_range'] as $priceRange) {
                    $priceRange = $this->priceRangeList[$priceRange]['value'];
                    $q1 = $q1->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->priceRange($priceRange);
                            });
                }
            });
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $model = $model->orderBy('mall_item.id', 'DESC'); // 因為目前updated_at的時間都一樣，所以先用id

        // 價格低到高 sort:minPrice
        } elseif ($request['sort'] == 'minPrice') {
            // 沒有價格的放最後
            $model = $model->orderByRaw('ISNULL(price), price = 0, price', 'ASC');

        // 價格高到低 sort:maxPrice
        } elseif ($request['sort'] == 'maxPrice') {
            $model = $model->orderBy('price', 'DESC');
        }

        // 人氣排序 sort:hot(default)
        $model = $model->orderBy('mall_item.rank', 'DESC')
                        ->orderBy('mall_item.id', 'DESC');

         // 分頁 page
        $model = $model->paginate(96);

        return $model;
    }

    /**
     * 婚禮小物列表的總數
     *
     *  @param $keyword
     *  @return model
     */
    public function getListTotal($keyword)
    {
        return $this->model->select('mall_item.id')
                            ->storeHasPaid()
                            ->storeUsageActive()
                            ->searchKeyword($keyword)
                            ->count();
    }

    /**
     * 取得特定價格區間的婚禮小物數量
     *
     *  @param array $key 價格區間索引值
     *  @param string $keyword 關鍵字
     *  @return model
     */
    public function countItemsByPriceRangeKey($key, $keyword)
    {
        return $this->model->storeHasPaid()
                            ->storeUsageActive()
                            ->searchKeyword($keyword)
                            ->priceRange($this->priceRangeList[$key]['value'])
                            ->count();
    }

    /**
     * 取得特定已發佈商家的婚禮小物列表
     *
     *  @param $request
     *  @return model
     */
    public function getStoreMallItemsByRequest($store, $request)
    {
        $items = $store->unsortMallItems();

        // 價格區間 price_range[] (聯集)
        if ($request['price_range']) {
            $items->where(function ($q1) use ($request) {
                foreach ($request['price_range'] as $priceRange) {
                    $priceRange = $this->priceRangeList[$priceRange]['value'];
                    $q1 = $q1->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->priceRange($priceRange);
                            });
                }
            });
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $items = $items->orderBy('id', 'DESC'); // 因為目前updated_at的時間都一樣，所以先用id

        // 價格低到高 sort:minPrice
        } elseif ($request['sort'] == 'minPrice') {
            // 沒有價格的放最後
            $items = $items->orderByRaw('ISNULL(price), price = 0, price', 'ASC');

        // 價格高到低 sort:maxPrice
        } elseif ($request['sort'] == 'maxPrice') {
            $items = $items->orderBy('price', 'DESC');
        }

        // 人氣排序 sort:hot(default)
        $items = $items->orderBy('rank', 'DESC')
                        ->orderBy('id', 'DESC');

         // 分頁 page
        $items = $items->paginate(96);

        return $items;
    }
}
