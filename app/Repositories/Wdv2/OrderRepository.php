<?php
/*
 |--------------------------------------
 |  OrderRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories\Wdv2;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Models\Store;
use App\Models\Wdv2\Order;
use App\Models\Wdv2\OrderLog;
use App\Models\Wdv2\OrderPackage;
use App\Models\Wdv2\OrderProduct;
use App\Models\Wdv2\PaymentLog;
use App\Models\Wdv2\PaymentSpgatewayLog;

class OrderRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(Order $order)
    {
        $this->model = $order;
    }

    /**
     * 神之後台-商家訂單列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request, $pagination = true)
    {
        $model = $this->model->live()
                            ->select('order.*');

        // 訂單類型 - 使用費
        $contractType = $request['contract_type'];
        if ($contractType == 'usage_fee') {
            $model = $model->whereNotIn('order.id',  function($q) {
                                $orderProductIds = collect((new OrderProduct)->setFeeContractType)->collapse();
                                $q->selectRaw('DISTINCT order_id')
                                    ->from((new OrderPackage)->getTable())
                                    ->where('show_flag', 2)
                                    ->whereIn('order_product_id', $orderProductIds);
                            });

        // 訂單類型 - 3/6/12期合約、續約使用費
        } elseif ($contractType) {
            $model = $model->join((new OrderPackage)->getTable(), function($join) use ($contractType) {
                                $orderProductIds = (new OrderProduct)->setFeeContractType[$contractType];
                                $join->on('order_package.order_id', '=', 'order.id')
                                        ->where('order_package.show_flag', 2)
                                        ->whereIn('order_product_id', $orderProductIds);
                            });
        }

        // 商家類別
        $storeType = $request['store_type'];
        if ($storeType) {
            $model = $model->join((new Store)->getTable(), function($join) use ($storeType) {
                                $join->on('stores.id', '=', 'order.store_id')
                                        ->where('stores.type', $storeType);
                            });
        }

        // 發票狀態 - 無須開立
        $invoiceStatus = $request['invoice_status'];
        if ($invoiceStatus == 'unnecessary') {
            $model = $model->where('order.amount', 0);

        // 發票狀態 - 尚未開立、已開立
        } elseif (isset($invoiceStatus) && $invoiceStatus != '') {
            $model = $model->where('order.amount', '!=', 0)
                            ->where('invoice_status', $invoiceStatus);
        }

        // 付款狀態 - 無須付款
        $paymentStatus = $request['payment_status'];
        if ($paymentStatus == 'unnecessary') {
            $model = $model->where('order.amount', 0);

        // 付款狀態 - 付款失敗、已付款、其他
        } elseif (isset($paymentStatus) && $paymentStatus != '') {
            $model = $model->where('order.amount', '!=', 0)
                            ->where('payment_status', $paymentStatus);
        }

        // 付款方式 - 匯款
        $paymentMethod = $request['payment_method'];
        if ($paymentMethod == 'spgateway') {
            $model = $model->join((new PaymentSpgatewayLog)->getTable(), function($join) {
                                $join->on('payment_spgateway_logs.no_id', '=', 'order.no_id')
                                        ->where('message', '付款成功');
                            })
                            ->groupBy('id');

        // 付款方式 - 信用卡
        } elseif ($paymentMethod == 'credit_card') {
            $model = $model->join((new PaymentLog)->getTable(), function($join) {
                                $join->on('payment_logs.order_id', '=', 'order.id');
                            })
                            ->whereNotIn('no_id',  function($q) {
                                $q->selectRaw('DISTINCT no_id')
                                    ->from((new PaymentSpgatewayLog)->getTable())
                                    ->where('message', '付款成功');
                            })
                            ->groupBy('id');

        // 付款方式 - 其他
        } elseif ($paymentMethod == 'other') {
            $model = $model->whereNotIn('order.id',  function($q) {
                                $q->selectRaw('DISTINCT order_id')
                                    ->from((new PaymentLog)->getTable());
                            })
                            ->whereNotIn('no_id',  function($q) {
                                $q->selectRaw('DISTINCT no_id')
                                    ->from((new PaymentSpgatewayLog)->getTable())
                                    ->where('message', '付款成功');
                            });
        }

        // 搜尋日期區間
        if ($request['start_date'] && $request['end_date']) {
            $dateRange = [
                $request['start_date'] . ' 00:00:00',
                $request['end_date'] . ' 23:59:59',
            ];

            // 付款日期
            if ($request['date_type'] == 'paid_at') {
                $model = $model->join((new OrderLog)->getTable(), function($join) use ($dateRange) {
                                    $join->on('order_log.order_id', '=', 'order.id')
                                            ->where('status', 1002)
                                            ->whereBetween('order_log.created_at', $dateRange);
                                })
                                ->groupBy('id');
            }

            // 訂單成立日期
            if ($request['date_type'] == 'created_at') {
                $model = $model->whereBetween('order.created_at', $dateRange);
            }
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序
        $model = $model->orderBy('created_at', 'DESC');

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }

    /**
     * 神之後台 - 更新備註 - 查看商店訂單
     *
     * @param string $note
     * @return bool
     */
    public function updateNote(string $note = NULL)
    {
        return $this->model->where('id', $this->model->id)
            ->update([
                'note' => $note,
            ]);
    }
}
