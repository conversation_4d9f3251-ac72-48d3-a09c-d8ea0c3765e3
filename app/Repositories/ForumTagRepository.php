<?php
/*
 |--------------------------------------
 |  ForumTagRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\ParseKeywordTrait;
use App\Models\ForumTag as Tag;

class ForumTagRepository extends AbsRepository
{
    use ParseKeywordTrait;

    public function __construct(Tag $tag)
    {
        $this->model = $tag;
    }

    /**
     * 取搜尋結果列表
     *
     *  @return model
     */
    public function getListBySearch($keyword, $filterArticleCount = true, $limit = null, $excludes = [])
    {
        $model = $this->model->where(function ($q) use ($keyword) {
            $keywords = $this->splitToArray($keyword);
            foreach ($keywords as $val) {
                $q = $q->orWhere('name', 'like', '%' . $val . '%');
            }
        });

        if ($filterArticleCount) {
            $model = $model->where('article_count', '!=', 0);
        }

        if ($excludes) {
            $model = $model->whereNotIn('id', $excludes);
        }

        $model = $model->orderBy('article_count', 'DESC');

        if ($limit) {
            $model = $model->limit($limit);
        }

        return $model->get();
    }

    /**
     * 取預設列表
     *
     *  @return model
     */
    public function getDefaultList($limit, $excludes = [], $order = 'article_count', $orderType = 'DESC')
    {
        return $this->model->whereNotIn('id', $excludes)
                            ->orderBy($order, $orderType)
                            ->limit($limit)
                            ->get();
    }
}
