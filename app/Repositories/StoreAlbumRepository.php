<?php
/*
 |--------------------------------------
 |  StoreAlbumRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\StoreAlbum;
use App\Models\StoreAlbumImage;
use App\Models\StudioAlbumImage;
use App\Models\DressAlbum;
use App\Models\DecorationAlbum;
use App\Models\CityData;
use App\Models\WeddingcakeShop;
use App\Models\StoreFare;
use Illuminate\Support\Str;
use DB;

class StoreAlbumRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(
        StoreAlbum $storeAlbum
    ) {
        $this->model = $storeAlbum;
    }

    /**
     * 商家作品列表的總數 By storeType
     *
     *  @param $storeType 商家類型
     *  @param $keyword 搜尋關鍵字
     *  @param $weddingDate 婚期
     *  @return model
     */
    public function getListTotalByStoreType($storeType, $keyword, $weddingDate)
    {
        return $this->model->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->count();
    }

    /**
     * 商家作品列表編號 By storeType
     *
     *  @param $storeType 商家類型
     *  @param $keyword 搜尋關鍵字
     *  @param $weddingDate 婚期
     *  @return model
     */
    public function getAlbumIdsByStoreType($storeType, $keyword, $weddingDate)
    {
        return $this->model->select(DB::raw('DISTINCT store_albums.id'))
                            ->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->pluck('id');
    }

    /**
     * 商家作品列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->select('store_albums.id', 'store_albums.store_id', 'store_albums.name', 'store_albums.cover_id');

        // 取得數據
        $model = $model->withCount(['images']);

        // 預載入
        $model = $model->with([
            'cover:id,file_name',
            'dressAlbum:album_id,price',
            'decorationAlbum:album_id,min_price,max_price',
            'store:id,name',
            'store.logo:target_id,file_name',
        ]);

        // 已付費的商家作品
        $model = $model->storeHasPaid($request['store_type']);

        // 商家的使用費限制內
        $model = $model->storeUsageActive();

        // 宅配試吃一覽
        if ($request['data_type'] == 'tasting') {

            // 自動篩選宅配試吃的禮盒標籤 (商家後台-禮盒管理, album_tags[] = 1108)
            $request['album_tags'] = $request['album_tags'] ? array_merge($request['album_tags'], [1108]) : [1108];

            // 商家必需提供宅配試吃資訊 (商家後台-試吃資訊設定, store_descriptions.has_delivery_tasting = 1)
            $model = $model->whereHas('store.descriptions', function ($q) {
                                $q->where('key', 'has_delivery_tasting')->where('value', 1);
                            });

            // 商家必須關聯宅配試吃的活動表單 (神之後台-商家內容頁, event->published(), event_store.type = 'delivery_tasting')
            $model = $model->whereHas('store.events', function ($q) {
                                $q->where('type', 'delivery_tasting');
                            });
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $model->searchKeywordWithStore($keyword);
        }

        // 婚期 wedding_date
        if ($request['wedding_date']) {
            $model = $model->searchWeddingDate($request['wedding_date']);
        }

        // 商家地點 locations
        if ($request['locations']) {

            // 喜餅商家的搜尋地點，是搜尋喜餅門市的所在地，篩選條件是聯集(AND)，將原本多次join改為子查詢
            if ($request['store_type'] == 10) {
                $model = $model->whereIn($this->model->getTable().'.store_id', function($sub) use ($request) {
                    $sub->select('store_id')
                        ->from((new WeddingcakeShop)->getTable())
                        ->whereIn('city_id', $request['locations'])
                        ->groupBy('store_id');
                });
            } else {
                $model = $model->join((new CityData)->getTable(), function($join) use ($request) {
                                    $join->on('city_data.id', '=', 'stores.city_id')
                                            ->whereIn('stores.city_id', $request['locations']);
                                });
            }
        }

        // 拍攝地點 image_locations
        // if ($request['image_locations']) {
        //     $model = $model->join((new StoreAlbumImage)->getTable(), function($join) {
        //                         $join->on('store_album_images.album_id', '=', 'store_albums.id');
        //                     })
        //                     ->join((new StudioAlbumImage)->getTable(), function($join) {
        //                         $join->on('studio_album_images.album_image_id', '=', 'store_album_images.id');
        //                     })
        //                     ->join((new CityData)->getTable(), function($join) use ($request) {
        //                         $join->on('city_data.id', '=', 'studio_album_images.city_id')
        //                                 ->whereIn('studio_album_images.city_id', $request['image_locations']);
        //                     });
        // }

        // 商家免車馬費地區 free_fares
        if ($request['free_fares']) {

            // 免車馬費地區篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.store_id', function($sub) use ($request) {
                $sub->select('store_id')
                    ->from((new StoreFare)->getTable())
                    ->whereIn('city_id', $request['free_fares'])
                    ->where('value', 1)
                    ->groupBy('store_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['free_fares'])]);
            });
        }

        // 價格區間 price_range[] (聯集)
        if ($request['price_range']) {
            $model = $model->whereHas($request['store_type_key'].'Album', function ($q1) use ($request) {
                $q1->where(function ($q2) use ($request) {
                        foreach ($request['price_range'] as $key) {
                            $q2->orWhere(function ($q3) use ($key) {
                                    $q3->priceRangeByKey($key);
                                });
                        }
                    });
            });
        }

        // 類型 types[] (聯集)
        if ($request['types']) {
            $model = $model->whereHas($request['store_type_key'].'Album', function ($q1) use ($request) {
                $q1->where(function ($q2) use ($request) {
                        foreach ($request['types'] as $key) {
                            $q2->orWhere('type', $key);
                        }
                    });
            });
        }

        // 作品集標籤 album_tags[] (聯集)
        if ($request['album_tags']) {

            // 作品集標籤篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
                $sub->select('target_id')
                    ->from('store_tag_pivot')
                    ->whereIn('store_tag_id', $request['album_tags'])
                    ->where('target_type', 'album')
                    ->groupBy('target_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['album_tags'])]);
            });
        }

        // 作品照標籤 image_tags[] (聯集)
        // if ($request['image_tags']) {

        //     // 作品照標籤篩選條件是聯集(AND)，將原本多次join改為子查詢
        //     $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
        //         $sub->select('album_id')
        //             ->from('store_tag_pivot')
        //             ->join((new StoreAlbumImage)->getTable(), function($join) {
        //                 $join->on('store_album_images.id', '=', 'store_tag_pivot.target_id');
        //             })
        //             ->whereIn('store_tag_id', $request['image_tags'])
        //             ->where('target_type', 'album_image')
        //             ->groupBy('album_id', 'target_id')
        //             ->havingRaw('COUNT(DISTINCT store_tag_id) = ?', [count($request['image_tags'])]);
        //     });
        // }

        // 禮盒需求:可客製化/葷/奶素/蛋奶素/全素 (喜餅)
        $weddingcakeDemands = ['can_customized','is_meat','is_lacto_vegetarian','is_ovo_lacto_vegetarian','is_vegetarian'];
        foreach ($weddingcakeDemands as $item) {
            if ($request[$item]) {
                $model = $model->whereHas('weddingcakeAlbum', function ($q) use ($item) {
                    $q->where($item, 1);
                });
            }
        }

        // 顯示有活動方案商家 activity_ids
        if ($request['activity_ids']) {
            $model = $model->storeHasAttendActivities($request['activity_ids']);
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $model = $model->orderBy('store_albums.edited_at', 'DESC');

        // 價格低到高 sort:minPrice
        } elseif ($request['sort'] == 'minPrice') {
            $joinModel   = resolve('App\Models\\'.Str::studly($request['store_type_key']).'Album');
            $priceColumn = ($request['store_type'] == 2) ? 'price' : 'min_price';
            $priceColumn = $request['store_type_key'].'_albums.'.$priceColumn;

            // 沒有價格的放最後
            $model = $model->join($joinModel->getTable(), function($join) {
                                $join->on('album_id', '=', 'store_albums.id');
                            })
                            ->orderByRaw("ISNULL($priceColumn), $priceColumn, $priceColumn = 0", 'ASC');

            // 若價錢一樣用另一個欄位繼續比對
            if ($request['store_type'] != 2) {
                $model = $model->orderBy($request['store_type_key'].'_albums.max_price');
            }

        // 價格高到低 sort:maxPrice
        } elseif ($request['sort'] == 'maxPrice') {
            $joinModel   = resolve('App\Models\\'.Str::studly($request['store_type_key']).'Album');
            $priceColumn = ($request['store_type'] == 2) ? 'price' : 'max_price';
            $priceColumn = $request['store_type_key'].'_albums.'.$priceColumn;
            $model = $model->join($joinModel->getTable(), function($join) {
                                $join->on('album_id', '=', 'store_albums.id');
                            })
                            ->orderBy($priceColumn, 'DESC');

            // 若價錢一樣用另一個欄位繼續比對
            if ($request['store_type'] != 2) {
                $model = $model->orderBy($request['store_type_key'].'_albums.min_price', 'DESC');
            }
        }

        // 人氣排序 sort:hot(default)
        $model = $model->orderBy('store_albums.rank', 'DESC');

         // 分頁 page
        $model = $model->paginate(48);

        return $model;
    }

    /**
     * 是否存在特定作品類型的作品集 (用於拍婚紗/婚禮佈置，類別)
     *
     *  @param $relationTable 作品集資訊的資料表
     *  @param $albumType 作品類型
     *  @param $storeType 商家類型
     *  @param $keyword
     *  @return model
     */
    public function existsStoreAlbumByType($relationTable, $albumType, $storeType, $keyword)
    {
        return $this->model->select('store_albums.id')
                            ->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->whereHas($relationTable, function ($q) use ($albumType) {
                                $q->where('type', $albumType);
                            })
                            ->exists();
    }

    /**
     * 是否存在特定價格區間的作品集 (用於婚紗禮服/婚禮佈置，價格區間)
     *
     *  @param $relationTable 作品集資訊的資料表
     *  @param $key 價格區間的索引值
     *  @param $storeType 商家類型
     *  @param $keyword 搜尋關鍵字
     *  @return model
     */
    public function existsStoreAlbumByPriceRangeKey($relationTable, $key, $storeType, $keyword)
    {
        return $this->model->select('store_albums.id')
                            ->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->whereHas($relationTable, function ($q) use ($key) {
                                $q->priceRangeByKey($key);
                            })
                            ->exists();
    }
}
