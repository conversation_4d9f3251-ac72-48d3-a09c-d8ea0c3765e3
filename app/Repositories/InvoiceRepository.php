<?php
/*
 |--------------------------------------
 |  InvoiceRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\Invoice;

class InvoiceRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(Invoice $invoice)
    {
        $this->model = $invoice;
    }

    /**
     * 商家後台發票管理-列表 By Request
     *
     *  @return model
     */
    public function getAdminListByRequest($store, $request)
    {
        $model = $store->invoiceSetting->invoices();

        // 發票日期 created_at
        if ($request['created_at']) {
            $startDate = $request['created_at'].' 00:00:00';
            $endDate   = $request['created_at'].' 23:59:59';
            $model = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 發票類型 type
        if ($request['type']) {
            $model = $model->where('type', $request['type']);
        }

        // 發票狀態 invoice_status
        if ($request['invoice_status']) {
            $model = $model->where('invoice_status', $request['invoice_status']);
        }

        // 上傳狀態 upload_status
        if ($request['upload_status']) {
            $model = $model->where('upload_status', $request['upload_status']);
        }

        // 關鍵字搜尋項目 search_type & keyword
        if ($request['search_type'] && $request['keyword']) {
            $model = $model->where($request['search_type'], 'like', '%'.$request['keyword'].'%');
        }

        // 排序
        $model = $model->orderBy('created_at', 'DESC');

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
