<?php
declare(strict_types=1);


namespace App\Repositories;

use App\Models\Wdv2\ServiceStopApply;
use App\Services\Tools\KeywordSearch\SearchService as KeywordSearchService;

class ServiceStopApplyRepository extends AbsRepository
{
    private $keywordSearchService;

    public function __construct(
        ServiceStopApply $serviceStopApply,
        KeywordSearchService $keywordSearchService
    )
    {
        $this->model = $serviceStopApply;
        $this->keywordSearchService = $keywordSearchService;
    }

    /**
     * 取得下架申請列表
     * @param $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model->with([
            'store',
            'orderSet'
        ]);

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $this->keywordSearchService->search($model, $keyword);
        }

        // 日期區間
        if ($request['start_date'] && $request['end_date']) {
            $startDate = $request['start_date'] . ' 00:00:00';
            $endDate = $request['end_date'] . ' 23:59:59';
            $model = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 商家類型
        if ($request['store_type']) {
            $model = $model->whereHas('store', function ($q) use ($request) {
                $q->where('type', $request['store_type']);
            });
        }

        // 處理狀態
        if (isset($request['status']) && $request['status'] != '') {
            $model = $model->where('status', $request['status']);
        }

        // 是否為系統下架
        if (isset($request['system_flag']) && $request['system_flag'] != '') {
            $model = $model->where('system_flag', $request['system_flag']);
        }

        // 商家還款狀態
        if (isset($request['pay_off_status']) && $request['pay_off_status'] != '') {
            $model = $model->where('pay_off_status', $request['pay_off_status']);
        }

        // WD退款狀態
        if (isset($request['refund_status']) && $request['refund_status'] != '') {
            $model = $model->where('refund_status', $request['refund_status']);
        }

        // 折讓金開立狀態
        if (isset($request['deposit_status']) && $request['deposit_status'] != '') {
            $model = $model->where('deposit_status', $request['deposit_status']);
        }

        // 自動續約
        if (isset($request['auto_pay']) && $request['auto_pay'] != '') {
            $model = $model->whereHas('orderSet', function ($q) use ($request) {
                if ($request['auto_pay'] == 'null') { // null:未選擇
                    $q->whereNull('auto_pay');
                } else {
                    $q->where('auto_pay', $request['auto_pay']);
                }
            });
        }

        // 合約狀態..要改狀態..先不做
        //if ($request['contract_status']) {
        //}

        // 排序 sort:id(default)|closing_date
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        return $model->paginate(20);
    }
}
