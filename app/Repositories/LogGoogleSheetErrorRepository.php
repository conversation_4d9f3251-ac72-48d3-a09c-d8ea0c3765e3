<?php
/*
 |--------------------------------------
 |  LogGoogleSheetErrorRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Models\LogGoogleSheetError;

class LogGoogleSheetErrorRepository extends AbsRepository
{
    public function __construct()
    {
        $this->model = new LogGoogleSheetError;
    }

    /**
     * 新增 Google Sheet 錯誤訊息
     *
     *  @param string $path
     *  @param string $action
     *  @param string $spreadsheetId
     *  @param string $sheetId
     *  @param string $sheetTitle
     *  @param string $inputData
     *  @param string $errorData
     *  @return int 
     */
    public function create(
        string $path,
        string $action,
        string $spreadsheetId = '',
        string $sheetId = '',
        string $sheetTitle = '',
        string $inputData = '',
        string $errorData = ''
    ) : int
    {
        $this->model->path = $path;
        $this->model->action = $action;
        $this->model->spreadsheet_id = $spreadsheetId;
        $this->model->sheet_id = $sheetId;
        $this->model->sheet_title = $sheetTitle;
        $this->model->input_data = $inputData;
        $this->model->error_data = $errorData;
        $this->model->save();
        
        return $this->model->id;
    }

    /**
     * 取得一筆資料
     * 
     * @param string $id
     */
    public function getForId(string $id)
    {
        return $this->model->where('id', $id)->first();
    }
}
