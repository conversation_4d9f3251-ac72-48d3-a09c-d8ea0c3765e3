<?php
/*
 |--------------------------------------
 |  VenueRoomRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\VenueRoom;
use App\Models\CityData;

class VenueRoomRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    private $cityData;

    public function __construct(
        VenueRoom $venueRoom,
        CityData $cityData
    ) {
        $this->model    = $venueRoom;
        $this->cityData = $cityData;
    }

    /**
     * 商家廳房列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->select('venue_rooms.id', 'venue_rooms.store_id', 'venue_rooms.name', 'min_number', 'max_number', 'cover_id');

        // 預載入
        $model = $model->with([
            'cover:id,file_name',
            'store:id,name,city_id',
            'store.logo:target_id,file_name',
            'store.city:id,title',
        ]);

        // 已付費的商家
        $model = $model->storeHasPaid($this->model->storeType);

        // 商家的使用費限制內
        $model = $model->storeUsageActive();

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $model->searchKeywordWithStore($keyword);
        }

        // 商家地點 locations
        if ($request['locations']) {
            $model = $model->join($this->cityData->getTable(), function($join) use ($request) {
                                $join->on('city_data.id', '=', 'stores.city_id')
                                        ->whereIn('stores.city_id', $request['locations']);
                            });
        }

        // 廳房類型 types[] banquet:宴客 witness:證婚 ceremony:文定/迎娶
        if ($request['types']) {
            foreach ($request['types'] as $type) {
                $model = $model->type($type);
            }
        }

        // 容納人數 numbers[] (聯集)
        if ($request['numbers']) {
            $model = $model->where(function($q1) use ($request) {
                foreach ($request['numbers'] as $number) {
                    $q1->orWhere(function($q2) use ($number) {
                        $q2->numberRange($number);
                    });
                }
            });
        }

        // 廳房設備 devices[] bridal_room:新娘休息室 wifi:Wi-Fi projection:投影幕設備 led:LED螢幕設備 sound:音響設備 light:特殊燈光設備 stage:舞台 pillar:無樑柱 backplane:送客背板
        if ($request['devices']) {
            foreach ($request['devices'] as $device) {
                $model = $model->device($device);
            }
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $model = $model->orderBy('venue_rooms.updated_at', 'DESC');
        }

        // 人氣排序 sort:hot(default)
        $model = $model->orderBy('venue_rooms.rank', 'DESC');

         // 分頁 page
        $model = $model->paginate(48);

        return $model;
    }

    /**
     * 商家廳房列表的總數
     *
     *  @param $keyword
     *  @return model
     */
    public function getListTotal($keyword)
    {
        return $this->model->select('venue_rooms.id')
                            ->storeHasPaid($this->model->storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->count();
    }

    /**
     * 取得特定類型的廳房ID集合
     *
     *  @param string $type 類型 banquet:宴客, ceremony:文定/迎娶, witness:證婚
     *  @param string $keyword 關鍵字
     *  @return model
     */
    public function getVenueRoomIdsByType($type, $keyword)
    {
        return $this->model->select('venue_rooms.id')
                            ->storeHasPaid($this->model->storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->type($type)
                            ->pluck('id');
    }

    /**
     * 取得特定容納人數的廳房ID集合
     *
     *  @param array $key 容納人數索引值
     *  @param string $keyword 關鍵字
     *  @return model
     */
    public function getVenueRoomIdsByNumber($key, $keyword)
    {
        return $this->model->select('venue_rooms.id')
                            ->storeHasPaid($this->model->storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->numberRange($key)
                            ->pluck('id');

    }

    /**
     * 取得特定設備的廳房ID集合
     *
     *  @param string $column 欄位
     *  @param string $keyword 關鍵字
     *  @return model
     */
    public function getVenueRoomIdsByDevice($column, $keyword)
    {
        return $this->model->select('venue_rooms.id')
                            ->storeHasPaid($this->model->storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->device($column)
                            ->pluck('id');
    }
}
