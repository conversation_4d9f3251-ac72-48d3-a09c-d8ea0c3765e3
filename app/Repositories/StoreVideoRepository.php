<?php
/*
 |--------------------------------------
 |  StoreVideoRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\StoreVideo;
use App\Models\DecorationVideo;
use App\Models\CityData;
use App\Models\StoreFare;
use DB;

class StoreVideoRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(
        StoreVideo $storeVideo
    ) {
        $this->model = $storeVideo;
    }

    /**
     * 商家影片列表的總數 By storeType
     *
     *  @param $storeType 商家類型
     *  @param $keyword 搜尋關鍵字
     *  @param $weddingDate 婚期
     *  @return model
     */
    public function getListTotalByStoreType($storeType, $keyword, $weddingDate)
    {
        return $this->model->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->count();
    }

    /**
     * 商家影片列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->select('store_videos.id', 'store_videos.store_id', 'store_videos.name', 'store_videos.cover_url');

        // 預載入
        $model = $model->with([
            'store:id,name',
            'store.logo:target_id,file_name',
        ]);

        // 已付費的商家影片
        $model = $model->storeHasPaid($request['store_type']);

        // 商家的使用費限制內
        $model = $model->storeUsageActive();

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $model->searchKeywordWithStore($keyword);
        }

        // 婚期 wedding_date
        if ($request['wedding_date']) {
            $model = $model->searchWeddingDate($request['wedding_date']);
        }

        // 商家地點 locations
        if ($request['locations']) {
            $model = $model->join((new CityData)->getTable(), function($join) use ($request) {
                                $join->on('city_data.id', '=', 'stores.city_id')
                                        ->whereIn('stores.city_id', $request['locations']);
                            });
        }

        // 商家免車馬費地區 free_fares
        if ($request['free_fares']) {

            // 免車馬費地區篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.store_id', function($sub) use ($request) {
                $sub->select('store_id')
                    ->from((new StoreFare)->getTable())
                    ->whereIn('city_id', $request['free_fares'])
                    ->where('value', 1)
                    ->groupBy('store_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['free_fares'])]);
            });
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $model = $model->orderBy('store_videos.edited_at', 'DESC');
        }

        // 人氣排序 sort:hot(default)
        $model = $model->orderBy('store_videos.rank', 'DESC');

         // 分頁 page
        $model = $model->paginate(48);

        return $model;
    }
}
