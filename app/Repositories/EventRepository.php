<?php
/*
 |--------------------------------------
 |  EventRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Traits\ParseKeywordTrait;
use App\Models\Event;

class EventRepository extends AbsRepository
{
    use BaseRepositoryTrait;
    use ParseKeywordTrait;

    public function __construct(Event $event)
    {
        $this->model = $event;
    }

    /**
     * 神之後台活動報名系統-活動列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 表單類型 normal:一般表單 order:團購表單
        if ($request['type'] == 'normal') {
            $model = $model->where('use_payment', 0);
        } elseif ($request['type'] == 'order') {
            $model = $model->where('use_payment', 1);
        }

        // 表單狀態 pending:尚未開始 published:進行中 completed:已結束
        if ($request['status']) {
            $model = $model->{$request['status']}();
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序 (因為可以複製活動，所以用id排序)
        $model = $model->orderBy('id', 'DESC');

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
