<?php
/*
 |--------------------------------------
 |  ForumCommentRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Models\ForumComment as Comment;
use Illuminate\Support\Facades\DB;

class ForumCommentRepository extends AbsRepository
{
    protected $model;
    protected $atUser;
    protected $logUpdated;
    protected $image;

    public function __construct(Comment $comment)
    {
        $this->model = $comment;
    }

    /**
     * 近30天內發佈的留言
     *
     *  @return model
     */
    public function getNewPublishedComments()
    {
        return $this->model->status('published')
                            ->hasAuthor()
                            ->where('forum_comments.created_at', '>=', now()->subDays(30))
                            ->sortCreatedAt()
                            ->get();
    }

    /**
     * 前台文章詳細內容頁，取留言列表需分頁 By Article
     *
     *  @return model
     */
    public function getPageCommentsByArticle($article, $requestComment)
    {
        $page            = isset($requestComment['page']) ? $requestComment['page'] : 1;
        $anchorId        = isset($requestComment['comment']) ? $requestComment['comment'] : NULL;
        $anchorComment   = $article->liveComments()->find($anchorId);
        $anchorComment   = ($anchorComment && $anchorComment->parent_id) ? $anchorComment->parent : $anchorComment;
        $anchorCommentId = $anchorComment ? $anchorComment->id : NULL;

        $comments = $article->comments();

        // 除了第一頁，其餘排除錨點留言
        if ($anchorCommentId && $page != 1) {
            $comments = $comments->where('forum_comments.id', '!=', $anchorCommentId);
        }
        $comments = $comments->orderBy('created_at', 'DESC')
                    ->paginate(50);


        // 第一頁需顯示錨點留言
        if ($anchorCommentId && $page == 1) {
            $hasAnchor = false;
            if ($comments->find($anchorCommentId)) {
                $hasAnchor = true;
            }
            foreach ($comments as $comment) {
                if ($comment->replies->find($anchorCommentId)) {
                    $hasAnchor = true;
                    break;
                }
            }
            if (!$hasAnchor) {
                $_items = $comments->getCollection()->prepend($anchorComment);
                $comments->setCollection($_items);
            }
        }

        return  $comments;
    }

    public function getPageCommentsByArticleV2($article, $requestComment)
    {
        $page            = isset($requestComment['page']) ? $requestComment['page'] : 1;
        $anchorId        = isset($requestComment['comment']) ? $requestComment['comment'] : NULL;
        $anchorComment   = $article->liveComments()->find($anchorId);
        $anchorComment   = ($anchorComment && $anchorComment->parent_id) ? $anchorComment->parent : $anchorComment;
        $anchorCommentId = $anchorComment ? $anchorComment->id : NULL;

        DB::statement('SET SESSION group_concat_max_len = 10000');
        $commentsQuery = "SELECT fc.*, " .
            "JSON_OBJECT( " .
                "'id', users.id, " .
                "'name', users.name, " .
                "'email', users.email, " .
                "'fb_id', users.fb_id, " .
                "'is_admin', users.is_admin, " .
                "'anonymous_key', users.anonymous_key, " .
                "'avatar', users.avatar, " .
                "'forum_get_like_count', users.forum_get_like_count, " .
                "'forum_comment_count', users.forum_comment_count " .
            ") AS user_info, " .
            "COALESCE( " .
            "CONCAT('[', GROUP_CONCAT( " .
                "DISTINCT IF( " .
                    "replies.id IS NOT NULL, " .
                    "JSON_OBJECT( " .
                        "'id', replies.id, " .
                        "'content', replies.content, " .
                        "'user_id', replies.user_id, " .
                        "'parent_id', replies.parent_id, " .
                        "'status', replies.status, " .
                        "'is_anonymous', replies.is_anonymous, " .
                        "'hide_type', replies.hide_type, " .
                        "'hide_content', replies.hide_content, " .
                        "'auto_reply', replies.auto_reply, " .
                        "'softdeleted_at', replies.softdeleted_at, " .
                        "'created_at', replies.created_at, " .
                        "'user_info', JSON_OBJECT( " .
                            "'id', reply_users.id, " .
                            "'name', reply_users.name, " .
                            "'email', reply_users.email, " .
                            "'fb_id', reply_users.fb_id, " .
                            "'is_admin', reply_users.is_admin, " .
                            "'anonymous_key', reply_users.anonymous_key, " .
                            "'avatar', reply_users.avatar, " .
                            "'forum_get_like_count', reply_users.forum_get_like_count, " .
                            "'forum_comment_count', reply_users.forum_comment_count " .
                        ") " .
                    "), NULL) " .
                    "ORDER BY replies.created_at ASC " .
                "SEPARATOR ',' " .
            "), ']'), '[]') AS replies " .
            "FROM forum_comments fc " .
            "LEFT JOIN users ON fc.user_id = users.id " .
            "LEFT JOIN forum_comments AS replies ON fc.id = replies.parent_id " .
            "LEFT JOIN users AS reply_users ON replies.user_id = reply_users.id " .
            "LEFT JOIN user_article_like ON fc.id = user_article_like.comment_id and user_article_like.deleted_at is null " .
            "WHERE fc.article_id = " . $article->id . " " .
            "AND fc.status != 'delete' " .
            "AND fc.deleted_at IS NULL " .
            "AND fc.parent_id IS NULL ";

        if ($anchorCommentId && $page != 1) {
            $commentsQuery .= "AND fc.id != " . $anchorCommentId . " ";
        }
        $commentsQuery .="GROUP BY fc.id " .
                        "ORDER BY fc.created_at DESC " .
                        "LIMIT 50 OFFSET " . (($page - 1) * 50);
        $comments = DB::select($commentsQuery);

        // 提取所有留言的id
        $commentIds = array_column($comments, 'id');

        // 第一頁需顯示錨點留言
        if ($anchorCommentId && $page == 1) {
            $hasAnchor = false;
            if (in_array($anchorCommentId, $commentIds)) {
                $hasAnchor = true;
            }

            foreach ($comments as $comment) {
                $repliesRaw = $comment->replies ?? '[]';
                $replies    = json_decode($repliesRaw, true);
                if (is_array($replies) && in_array($anchorCommentId, array_column($replies, 'id'))) {
                    $hasAnchor = true;
                    break;
                }
            }

            if (!$hasAnchor) {
                // 從DB像前面這樣取得資料，所以這裡的$anchorComment是stdClass
                $anchorCommentQuery = "SELECT fc.*, " .
                    "JSON_OBJECT( " .
                        "'id', users.id, " .
                        "'name', users.name, " .
                        "'email', users.email, " .
                        "'fb_id', users.fb_id, " .
                        "'is_admin', users.is_admin, " .
                        "'anonymous_key', users.anonymous_key, " .
                        "'avatar', users.avatar, " .
                        "'forum_get_like_count', users.forum_get_like_count, " .
                        "'forum_comment_count', users.forum_comment_count " .
                    ") AS user_info, " .
                    "COALESCE( " .
                    "CONCAT('[', GROUP_CONCAT( " .
                        "DISTINCT IF( " .
                            "replies.id IS NOT NULL, " .
                            "JSON_OBJECT( " .
                                "'id', replies.id, " .
                                "'content', replies.content, " .
                                "'user_id', replies.user_id, " .
                                "'parent_id', replies.parent_id, " .
                                "'status', replies.status, " .
                                "'is_anonymous', replies.is_anonymous, " .
                                "'hide_type', replies.hide_type, " .
                                "'hide_content', replies.hide_content, " .
                                "'auto_reply', replies.auto_reply, " .
                                "'softdeleted_at', replies.softdeleted_at, " .
                                "'created_at', replies.created_at, " .
                                "'user_info', JSON_OBJECT( " .
                                    "'id', reply_users.id, " .
                                    "'name', reply_users.name, " .
                                    "'email', reply_users.email, " .
                                    "'fb_id', reply_users.fb_id, " .
                                    "'is_admin', reply_users.is_admin, " .
                                    "'anonymous_key', reply_users.anonymous_key, " .
                                    "'avatar', reply_users.avatar, " .
                                    "'forum_get_like_count', reply_users.forum_get_like_count, " .
                                    "'forum_comment_count', reply_users.forum_comment_count " .
                                ") " .
                            "), NULL) " .
                        "SEPARATOR ',' " .
                    "), ']'), '[]') AS replies " .
                    "FROM forum_comments fc " .
                    "LEFT JOIN users ON fc.user_id = users.id " .
                    "LEFT JOIN forum_comments AS replies ON fc.id = replies.parent_id " .
                    "LEFT JOIN users AS reply_users ON replies.user_id = reply_users.id " .
                    "LEFT JOIN user_article_like ON fc.id = user_article_like.comment_id " .
                    "WHERE fc.id = " . $anchorCommentId . " " .
                    "AND fc.status != 'delete' " .
                    "AND fc.deleted_at IS NULL " .
                    "AND fc.parent_id IS NULL " .
                    "GROUP BY fc.id " .
                    "ORDER BY fc.created_at DESC ";

                $anchorComment = DB::select($anchorCommentQuery)[0];
                array_unshift($comments, $anchorComment);
            }
        }

        // 提取所有留言的content
        $commentContents = DB::table('forum_comments')
                            ->select('id', 'content')
                            ->where('article_id', $article->id)
                            ->get();

        // 所有留言的留言編輯紀錄
        $logUpdateds = DB::table('log_article_updateds')
                        ->select('comment_id', 'content', 'created_at')
                        ->where('article_id', $article->id)
                        ->get();

        $commentLikes = DB::table('user_article_like')
                        ->select('comment_id', DB::raw('COUNT(*) as like_count'))
                        ->join('users', 'user_article_like.user_id', '=', 'users.id')
                        ->where('user_article_like.deleted_at', NULL)
                        ->where('users.deleted_at', NULL)
                        ->whereIn('comment_id', $commentContents->pluck('id')->toArray())
                        ->groupBy('comment_id')
                        ->get()->keyBy('comment_id');

        // 設定每頁顯示數量
        $perPage = 50;

        // 取得總筆數
        $totalQuery = "SELECT COUNT(*) as total FROM forum_comments WHERE article_id = {$article->id} AND status != 'delete' AND parent_id is NULL AND deleted_at IS NULL";
        $total = DB::select($totalQuery)[0]->total;

        $lastPage = ceil($total / $perPage); // 計算總頁數

        $links = [];
        $links[] = [
            'url' => $page == 1 ? null : env('APP_URL') . '/api/forum/article/' . $article->id . '?page=' . ($page - 1),
            'label' => 'pagination.previous',
            'active' => false
        ];
        for($i = 1; $i <= $lastPage; $i++) {
            $links[] = [
                'url' => env('APP_URL') . '/api/forum/article/' . $article->id . '?page=' . $i,
                'label' => (string)$i,
                'active' => $i == $page ? true : false
            ];
        }
        $links[] = [
            'url' => $page == $lastPage ? null : env('APP_URL') . '/api/forum/article/' . $article->id . '?page=' . ($page + 1),
            'label' => 'pagination.next',
            'active' => false
        ];
        $result = [
            'comments' => $comments,
            'commentLikes' => $commentLikes,
            'logUpdateds' => $logUpdateds,
            'perPage' => $perPage,
            'current_page' => (int)$page,
            'total' => $total,
            'lastPage' => $lastPage,
            'path' => env('APP_URL') . '/api/forum/article/' . $article->id,
            'firstPageUrl' => env('APP_URL') . '/api/forum/article/' . $article->id . '?page=1',
            'lastPageUrl' => env('APP_URL') . '/api/forum/article/' . $article->id . '?page=' . $lastPage,
            'nextPageUrl' => $page == $lastPage ? null : env('APP_URL') . '/api/forum/article/' . $article->id . '?page=' . ($page + 1),
            'prevPageUrl' => $page == 1 ? null : env('APP_URL') . '/api/forum/article/' . $article->id . '?page=' . ($page - 1),
            'links' => $links,
            'from' => ($page - 1) * $perPage + 1,
            'to' => $page == $lastPage ? $total : $page * $perPage,
            'commentContents' => $commentContents,
        ];

        return $result;
    }

    /**
     * 神之後台文章詳細內容頁，取留言列表需分頁 By Article
     *
     *  @return model
     */
    public function getYzcubePageCommentsByArticle($article)
    {
        return $article->parentComments()
                        ->orderBy('created_at', 'DESC')
                        ->paginate(20);
    }
}
