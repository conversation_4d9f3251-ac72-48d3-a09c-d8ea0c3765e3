<?php
/*
 |--------------------------------------
 |  SystemInfoRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Models\SystemInfo;

class SystemInfoRepository extends AbsRepository
{
    public function __construct(SystemInfo $systemInfo)
    {
        $this->model = $systemInfo;
    }

    // 取內容
    public function get($key)
    {
        $model = $this->getFirst(['key' => $key]);

        return $model ? $model->content : '';
    }
}
