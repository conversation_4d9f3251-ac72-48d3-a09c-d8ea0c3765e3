<?php
/*
 |--------------------------------------
 |  ForumCategoryRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Models\ForumCategory as Category;

class ForumCategoryRepository extends AbsRepository
{
    public function __construct(Category $category)
    {
        $this->model = $category;
    }

    /**
     * 取簡易列表
     *
     *  @return model
     */
    public function getSimpleList()
    {
        return $this->model
                    ->live()
                    ->select('id', 'name', 'can_anonymous_article', 'can_anonymous_comment')
                    ->sort()
                    ->get();
    }

    /**
     * 取開放使用列表
     *
     *  @return model
     */
    public function getPublicUseList($public = true)
    {
        $model = $this->model
                        ->live()
                        ->select('id', 'name', 'description', 'article_placeholder')
                        ->sort();

        if ($public) {
            $model = $model->publicUse();
        }

        return $model->get();
    }
}
