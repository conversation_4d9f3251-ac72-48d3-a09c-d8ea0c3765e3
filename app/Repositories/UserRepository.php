<?php
/*
 |--------------------------------------
 |  UserRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Models\User;
use App\Models\UserWeddingType;
use App\Traits\ParseKeywordTrait;
use App\Services\User\Yzcube\FilterUsedService\FilterService;

class UserRepository extends AbsRepository
{
    private $weddingType;
    private $filterService;

    use BaseRepositoryTrait;
    use ParseKeywordTrait;

    public function __construct(
        User $user,
        UserWeddingType $weddingType,
        FilterService $filterService
    )
    {
        $this->model = $user;
        $this->weddingType = $weddingType;
        $this->filterService = $filterService;
    }

    /**
     * 搜尋結果列表
     *
     * @param $keyword , $limit
     *
     * @return model $users
     */
    public function getListByKeyword($keyword, $limit = 10)
    {
        return $this->model->live()
            ->where('name', 'like', '%' . $keyword . '%')
            ->orderBy('last_login_at', 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * 管理者&假帳號列表
     *
     * @return model $users
     */
    public function getAdminFakeUserList()
    {
        return $this->model->where('is_admin', 1)
            ->orWhere('is_fake', 1)
            ->get();
    }

    /**
     * 取得神之後台v3 新娘列表
     * @param $request : 搜尋條件
     * @return mixed
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model
            ->with('weddingTypes') //婚期資料
            ->withCount('weddingSchedules') //黃金團隊
            ->withCount('reserves') //預約單數
            ->withCount('quotes') //公開詢價數
            ->withCount('collects') //收藏櫃
            ->withCount('allArticles') //好婚聊聊文章數
            ->withCount('allComments') //好婚聊聊留言數
            ->withCount('feedback') //意見回饋收
            ->withCount('eventReports'); //活動報名數

        // 已使用功能篩選
        if (!empty($request['used_service'])){
            $model = $this->filterService->filter($model, $request['used_service']);
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 日期區間
        if ($request['start_date'] && $request['end_date']) {
            $startDate = $request['start_date'] . ' 00:00:00';
            $endDate = $request['end_date'] . ' 23:59:59';
            $model = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 狀態 status valid:有效
        if ($request['status'] == 'valid') {
            $model = $model->where('status', '!=', 'delete');
        }

        // 狀態 status invalid:已停用
        if ($request['status'] == 'invalid') {
            $model = $model->where('status', 'delete');
        }

        // 狀態 status deleted:已封存
        if ($request['status'] == 'deleted') {
            $model = $model->onlyTrashed();
        }

        // 假帳號
        if ($request['identity']) {
            $model = $request['identity'] == 'fake' ?
                $model->where('is_fake', 1) :
                $model->where('is_fake', 0);
        }

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
