<?php
/*
 |--------------------------------------
 |  CouponRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Models\Coupon;

class CouponRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(Coupon $coupon)
    {
        $this->model = $coupon;
    }

    /**
     * 神之後台-優惠券列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 表單類型 normal:一般表單 order:團購表單
        if ($request['type']) {
            $model = $model->where('type', $request['type']);
        }

        // 優惠卷狀態 pending:尚未開始 published:進行中 completed:已結束
        if ($request['status']) {
            $model = $model->{$request['status']}();
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序
        $model = $model->orderBy('created_at', 'DESC');

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
