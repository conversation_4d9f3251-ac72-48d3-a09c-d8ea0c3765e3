<?php
/*
 |--------------------------------------
 |  StoreAlbumRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Repositories\StoreAlbumRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\StoreAlbum;
use App\Models\StoreAlbumImage;
use App\Models\StudioAlbumImage;
use App\Models\Image;
use App\Models\CityData;
use App\Models\StoreFare;
use DB;

class StoreAlbumImageRepository extends AbsRepository
{
    private $storeAlbumRepository;

    use BaseRepositoryTrait;

    public function __construct(
        StoreAlbumImage $storeAlbumImage,
        StoreAlbumRepository $storeAlbumRepository
    ) {
        $this->model                = $storeAlbumImage;
        $this->storeAlbumRepository = $storeAlbumRepository;
    }

    /**
     * 商家作品列表的總數 By storeType
     *
     *  @param $type
     *  @return model
     */
    // public function getListTotalByStoreType($storeType, $keyword, $weddingDate)
    // {
    //     $albumIds = $this->storeAlbumRepository->getAlbumIdsByStoreType($storeType, $keyword, $weddingDate);

    //     return $this->model->whereIn('album_id', $albumIds)->count();
    // }

    /**
     * 商家作品列表 By Request
     *
     *  @param $request
     *  @return model
     */
    // public function getListByRequest($request)
    // {
    //     $model = $this->model->select('store_album_images.id', 'album_id');

    //     // 預載入
    //     $model = $model->with([
    //         'image:target_id,file_name',
    //         'tags:id',
    //         'album.store:id,name',
    //         'album.store.logo:target_id,file_name',
    //         'album.store.hasRankSharePostBrands:rank',
    //         'album.store.sharePostBrands',
    //     ]);

    //     // 取得商家作品列表ID的集合，先排除作品照標籤的篩選
    //     $_request = clone $request;
    //     $_request->request->remove('image_tags');
    //     $albumIds = $this->storeAlbumRepository->getAlbumIdsCollect($_request);
    //     $model = $model->whereIn('album_id', $albumIds);

    //     // 作品照標籤 image_tags[] (聯集)
    //     if ($request['image_tags']) {

    //         // 作品照標籤篩選條件是聯集(AND)，將原本多次join改為子查詢
    //         $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
    //             $sub->select('target_id')
    //                 ->from('store_tag_pivot')
    //                 ->whereIn('store_tag_id', $request['image_tags'])
    //                 ->where('target_type', 'album_image')
    //                 ->groupBy('target_id')
    //                 ->havingRaw('COUNT(id) = ?', [count($request['image_tags'])]);
    //         });
    //     }

    //     // 關聯相本，用於排序
    //     $model = $model->join((new StoreAlbum)->getTable(), function($join) {
    //                         $join->on('store_albums.id', '=', 'store_album_images.album_id');
    //                     });

    //     // 最新排序 sort:update
    //     if ($request['sort'] == 'update') {
    //         $model = $model->join((new Image)->getTable(), function($join) {
    //                             $join->on('images.target_id', '=', 'store_album_images.id')
    //                                     ->where('type', 'store_album_image');
    //                         })
    //                         ->orderBy('images.created_at', 'DESC');

    //     // 人氣排序 sort:hot(default)
    //     } else {
    //         $model = $model->orderBy('store_album_images.sequence', 'DESC')
    //                         ->orderBy('store_albums.rank', 'DESC');

    //     }

    //      // 分頁 page
    //     $model = $model->paginate(48);

    //     return $model;
    // }
}
