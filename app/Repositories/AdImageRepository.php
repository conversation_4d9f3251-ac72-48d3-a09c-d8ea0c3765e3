<?php
/*
 |--------------------------------------
 |  AdImageRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Traits\ParseKeywordTrait;
use App\Models\AdImage;

class AdImageRepository extends AbsRepository
{
    use BaseRepositoryTrait;
    use ParseKeywordTrait;

    public function __construct(AdImage $adImage)
    {
        $this->model = $adImage;
    }

    /**
     * 取得首頁輪播的補位廣告素材
     *
     *  @return model
     */
    public function getBackupList()
    {
        return $this->model->where('campaign_type', 'banner')
                            ->using(false)
                            ->backup(1)
                            ->get();
    }

    /**
     * 神之後台素材管理-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 類型 campaign_type:banner|mobile_banner|float
        if ($request['campaign_type']) {
            $model = $model->where('campaign_type', $request['campaign_type']);
        }

        // 狀態 pending:未使用 backup:補位圖片 using:活動使用中
        if ($request['status']) {
            $model = $model->{$request['status']}();
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywords = $this->splitToArray($keyword);
            $model = $model->where(function ($q1) use ($keywords) {
                foreach ($keywords as $val) {
                    $q1 = $q1->orWhere('name', 'like', '%' . $val . '%')
                            ->orWhere('url', 'like', '%' . $val . '%')
                            ->orWhere('url_xs', 'like', '%' . $val . '%')
                            ->orWhereHas('images', function ($q2) use ($val) {
                                $q2 = $q2->where('file_name', 'like', '%' . $val . '%');
                            })
                            ->orWhereHas('campaign', function ($q2) use ($val) {
                                $q2 = $q2->where('name', 'like', '%' . $val . '%');
                            });
                }
            });
        }

        // 排序
        $model = $model->orderBy('created_at', 'DESC');

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }

    /**
     * 取得未使用的廣告素材
     *
     *  @return model
     */
    public function getPendingList($type)
    {
        return $this->model->where('campaign_type', $type)
                            ->pending()
                            ->orderBy('created_at', 'DESC')
                            ->get();
    }
}
