<?php
/*
 |--------------------------------------
 |  FakeUserRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\ParseKeywordTrait;
use App\Models\FakeUser;

class FakeUserRepository extends AbsRepository
{
    use ParseKeywordTrait;

    public function __construct(FakeUser $fakeUser)
    {
        $this->model = $fakeUser;
    }

    /**
     * 假帳號列表 By Request
     *
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->whereHas('user');

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywords = $this->splitToArray($keyword);
            $model = $model->where(function ($q1) use ($keywords) {
                foreach ($keywords as $val) {
                    $q1 = $q1->orWhere('wedding_date', date($val))
                                ->orWhere('tags', 'like', '%' . $val . '%')
                                ->orWhere('note', 'like', '%' . $val . '%')
                                ->orWhereHas('user', function ($q2) use ($val) {
                                    $q2->where('id', $val)
                                        ->orWhere('name', 'like', '%' . $val . '%')
                                        ->orWhere('anonymous_key', 'like', '%' . $val . '%');
                                });
                }
            });
        }

        // 排序 sort:created_at(default)|wedding_date
        // 升降冪 direction:asc|desc(default)
        $sort      = $request['sort'] ?: 'created_at';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction)
                        ->orderBy('id', $direction);

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
