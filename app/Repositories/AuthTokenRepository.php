<?php
/*
 |--------------------------------------
 |  AuthTokenRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Models\AuthToken;

class AuthTokenRepository extends AbsRepository
{
    public function __construct(AuthToken $authToken)
    {
        $this->model = $authToken;
    }

    /**
     * 驗證token，取AuthToken Model
     *
     *  @return model
     */
    public function getAuthTokenByValidToken($type, $token)
    {
        return $this->model
                    ->type($type)
                    ->where('token', $token)
                    ->beforeDeadline()
                    ->first();
    }
}
