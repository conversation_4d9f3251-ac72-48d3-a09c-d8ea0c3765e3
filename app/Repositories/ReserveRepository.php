<?php
/*
 |--------------------------------------
 |  ReserveRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Models\Wdv2\Reserve;
use App\Traits\ParseKeywordTrait;
use App\Traits\JsonStringTrait;
use App\Services\Tools\KeywordSearch\SearchService as KeywordSearchService;

class ReserveRepository extends AbsRepository
{
    use ParseKeywordTrait;
    use JsonStringTrait;

    /** @var KeywordSearchService */
    private $keywordSearchService;

    /**
     * ReserveRepository constructor.
     * @param Reserve $reserve
     * @param KeywordSearchService $keywordSearchService
     */
    public function __construct(
        Reserve $reserve,
        KeywordSearchService $keywordSearchService
    )
    {
        $this->model = $reserve;
        $this->keywordSearchService = $keywordSearchService;
    }

    /**
     * 取得詢價單列表
     * @param $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getYzcubeListByRequest($request, $pagination = true)
    {
        $model = $this->model->with('user')
            ->with('rawStore');

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $this->keywordSearchService->search($model, $keyword);
        }

        // 日期區間
        if ($request['start_date'] && $request['end_date']) {
            $startDate = $request['start_date'] . ' 00:00:00';
            $endDate = $request['end_date'] . ' 23:59:59';
            $model = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 商家類型
        if ($request['store_type']) {
            $model = $model->whereHas('rawStore', function ($q) use ($request) {
                $q->where('type', $request['store_type']);
            });
        }

        // 詢問單狀態
        if ($request['status']) {
            $model = $model->where('status_flag', $request['status']);
        }

        // 使用者填單時是否登入
        if ($request['login_type']) {
            $model = $request['login_type'] == 'yes' ?
                $model->whereNotNull('user_id') :
                $model->whereNull('user_id');
        }

        // 商家ID
        if ($request['store_id']) {
            $model = $model->where('store_id', $request['store_id']);
        }

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }

    /**
     * 透過id取得詢問單內容
     * @param $reserveID : 詢問單id
     * @return mixed
     */
    public function getContentByReserveID($reserveID)
    {
        return $this->model->with('user', 'rawStore', 'reserveAppeal.yzcubeUser', 'storeDiscount')
            ->where('id', $reserveID)
            ->first();
    }


    /**
     *【取得詢問單數量】
     * 未來只要有計算費用的，一率以這個為主
     *
     * @param $storeId : 商家id
     * @param $startDate : 開始時間
     * @param $endDate : 結束時間
     * @return int
     */
    public function getReserveCount($storeId, $startDate, $endDate)
    {
        return $this->model->where('store_id', $storeId)
            ->where('status_flag', '!=', 7) //排除無效訂單
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->where('show_flag', 2)
            ->count();
    }
}
