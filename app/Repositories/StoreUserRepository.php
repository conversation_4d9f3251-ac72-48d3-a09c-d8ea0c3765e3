<?php
/*
 |--------------------------------------
 |  StoreUserRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Models\StoreUser;
use App\Models\Store;
use App\Services\Tools\KeywordSearch\SearchService;

class StoreUserRepository extends AbsRepository
{
    public function __construct(StoreUser $storeUser)
    {
        $this->model = $storeUser;
    }

    /**
     * 主動報價須通知的商家Line IDs
     *
     *  @return Collection $storeLineIds
     */
    public function getNewQuoteLineIdsByStoreType($storeType)
    {
        return $this->model->distinct('line_id')
                            ->live()
                            ->has('lineBotUser')
                            ->whereHas('storesWithLineQuoteNotify', function ($q) use ($storeType) {
                                $q->published($storeType);
                            })
                            ->pluck('line_id')
                            ->toArray();
    }

    /**
     * 取得須通知的管理者Line IDs
     *
     *  @return Collection $storeLineIds
     */
    public function getAdminLineIds()
    {
        return $this->model->distinct('line_id')
                            ->live()
                            ->has('lineBotUser')
                            ->where('is_admin', 1)
                            ->pluck('line_id')
                            ->toArray();
    }

    /**
     * 取得須通知的商家Line IDs
     *
     *  @return Collection $storeLineIds
     */
    public function getLineIdsByStoreId($storeId)
    {
        return $this->model->distinct('line_id')
                            ->live()
                            ->has('lineBotUser')
                            ->whereHas('stores', function ($q) use ($storeId) {
                                $q->where((new Store)->getTable().'.id', $storeId);
                            })
                            ->pluck('line_id')
                            ->toArray();
    }

    /**
     * 取得即時通訊須通知的商家Line IDs
     *
     *  @return Collection $storeLineIds
     */
    public function getMessageLineIdsByStoreId($storeId)
    {
        return $this->model->distinct('line_id')
                            ->live()
                            ->has('lineBotUser')
                            ->whereHas('storesWithLineMessageNotify', function ($q) use ($storeId) {
                                $q->where((new Store)->getTable().'.id', $storeId);
                            })
                            ->pluck('line_id')
                            ->toArray();
    }

    /**
     * 取得神之後台v3 商家user列表
     * @param $request : 搜尋條件
     * @return mixed
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 日期區間
        if ($request['start_date'] && $request['end_date']) {
            $startDate = $request['start_date'] . ' 00:00:00';
            $endDate = $request['end_date'] . ' 23:59:59';
            $model = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 狀態 status valid:有效
        if ($request['status'] == 'valid') {
            $model = $model->where('status', '!=', 'delete');
        }

        // 狀態 status valid:已停用
        if ($request['status'] == 'invalid') {
            $model = $model->where('status', 'delete');
        }

        // 狀態 status deleted:已封存
        if ($request['status'] == 'deleted') {
            $model = $model->onlyTrashed();
        }

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }

}
