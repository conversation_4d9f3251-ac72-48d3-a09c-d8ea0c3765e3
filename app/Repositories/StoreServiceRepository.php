<?php
/*
 |--------------------------------------
 |  StoreServiceRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\StoreService;
use App\Models\CityData;
use App\Models\WeddingcakeShop;
use App\Models\StoreFare;
use Illuminate\Support\Str;

class StoreServiceRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(
        StoreService $storeService
    ) {
        $this->model = $storeService;
    }

    /**
     * 商家方案列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->select('store_services.id', 'store_services.store_id', 'store_services.activity_id', 'store_services.type', 'store_services.name', 'store_services.description', 'store_services.min_price', 'store_services.max_price');

        // 預載入
        $model = $model->with([
            'cover:target_id,file_name',
            // 'storeHasAttendActivity:id,title',
            'venueInfo:service_id,has_tip,priced_by,seater,has_child_discount',
            'store:id,name,type,city_id',
            'store.logo:target_id,file_name',
            'store.city:id,title',
            'store.discounts:store_id',
            'store.descriptions:store_id,key,value',
            'store.activities',
            'weddingcakeInfo'
        ]);

        // 已付費的商家方案 (改用 join 提升效率)
        $model = $model->storeHasPaid($request['store_type']);

        // 商家的使用費限制內
        $model = $model->storeUsageActive();

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $model->searchKeywordWithStore($keyword);
        }

        // 婚期 wedding_date
        if ($request['wedding_date']) {
            $model = $model->searchWeddingDate($request['wedding_date']);
        }

        // 商家地點 locations
        if ($request['locations']) {

            // 喜餅商家的搜尋地點，是搜尋喜餅門市的所在地，篩選條件是聯集(AND)，將原本多次join改為子查詢
            if ($request['store_type'] == 10) {
                $model = $model->whereIn($this->model->getTable().'.store_id', function($sub) use ($request) {
                    $sub->select('store_id')
                        ->from((new WeddingcakeShop)->getTable())
                        ->whereIn('city_id', $request['locations'])
                        ->groupBy('store_id');
                });
            } else {
                $model = $model->join((new CityData)->getTable(), function($join) use ($request) {
                                    $join->on('city_data.id', '=', 'stores.city_id')
                                            ->whereIn('stores.city_id', $request['locations']);
                                });
            }
        }

        // 商家免車馬費地區 free_fares
        if ($request['free_fares']) {

            // 免車馬費地區篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.store_id', function($sub) use ($request) {
                $sub->select('store_id')
                    ->from((new StoreFare)->getTable())
                    ->whereIn('city_id', $request['free_fares'])
                    ->where('value', 1)
                    ->groupBy('store_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['free_fares'])]);
            });
        }

        // 方案需求 (聯集)
        foreach ($this->model->demandStructure[$request['store_type']] as $demand => $data) {
            if ($request[Str::snake($demand)]) {

                // 多選項目
                if (is_array($data['list'])) {
                    $model = $model->where(function ($q1) use ($demand, $request) {
                        foreach ($request[Str::snake($demand)] as $key) {
                            $q1 = $q1->orWhere(function ($q2) use ($demand, $request, $key) {
                                $q2->{$demand}($key, $request['store_type']);
                            });
                        }
                    });

                // 單選項目
                } else {
                    $model = $model->{$demand}($data['list'], $request['store_type']);
                }
            }
        }

        // 方案包含的服務 service_tags
        if ($request['service_tags']) {

            // 方案包含的服務篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
                $sub->select('target_id')
                    ->from('store_tag_pivot')
                    ->whereIn('store_tag_id', $request['service_tags'])
                    ->where('target_type', 'service')
                    // ->where('value', '>', 0) // 有勾選就有資料、沒勾選就沒資料
                    ->groupBy('target_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['service_tags'])]);
            });
        }

        // 商家提供的服務 store_tags
        if ($request['store_tags']) {

            // 商家提供的服務篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.store_id', function($sub) use ($request) {
                $sub->select('target_id')
                    ->from('store_tag_pivot')
                    ->whereIn('store_tag_id', $request['store_tags'])
                    ->where('target_type', 'store')
                    ->where('value', '>', 0)
                    ->groupBy('target_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['store_tags'])]);
            });
        }

        // 顯示有活動方案商家 activity_ids
        if ($request['activity_ids']) {
            $model = $model->storeHasAttendActivities($request['activity_ids'])
                            ->whereIn('store_services.activity_id', $request['activity_ids']);
        }

        // 顯示有獨家優惠的商家 has_discount
        if ($request['has_discount']) {
            $model = $model->storeHasDiscount();
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $model = $model->orderBy('store_services.created_at', 'DESC');

        // 價格低到高 sort:minPrice
        } elseif ($request['sort'] == 'minPrice') {
            // 沒有價格的放最後
            $model = $model->orderByRaw('ISNULL(store_services.min_price), store_services.min_price = 0, store_services.min_price', 'ASC');

        // 價格高到低 sort:maxPrice
        } elseif ($request['sort'] == 'maxPrice') {
            $model = $model->orderBy('store_services.max_price', 'DESC');
        }

        // 人氣排序 sort:hot(default)
        $model = $model->orderBy('store_services.rank', 'DESC');

         // 分頁 page
        $model = $model->paginate(48);

        return $model;
    }

    /**
     * 商家方案列表的總數 By storeType
     *
     *  @param $storeType 商家類型
     *  @param $keyword 關鍵字
     *  @param $weddingDate 婚期
     *  @return model
     */
    public function getListTotalByStoreType($storeType, $keyword, $weddingDate)
    {
        return $this->model->select('store_services.id')
                            ->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->count();
    }

    /**
     * 取得特定[價格/需求]的方案ID集合
     *
     *  @param $scopeName scope名稱
     *             priceRange:價格區間
     *             type:方案類型
     *             studioPhotoFile:檔案全贈(拍婚紗)
     *             studioCasualWear:拍攝便服(拍婚紗)
     *             studioWhiteDress:拍攝白紗(拍婚紗/婚紗禮服)
     *             studioDress:拍攝禮服(拍婚紗/婚紗禮服)
     *             weddingWhiteDress:宴客白紗(拍婚紗/婚紗禮服)
     *             weddingDress:宴客禮服(拍婚紗/婚紗禮服)
     *             studioAlbumPhoto:精修照(拍婚紗)
     *             forStudio:拍攝用(婚紗禮服)
     *             forWedding:宴客用(婚紗禮服)
     *             witnessCeremony:證婚需求(婚攝婚錄/新娘秘書)
     *             ceremonyType:儀式類型(婚攝婚錄/新娘秘書/婚禮主持人)
     *             banquetTime:婚宴時段(婚攝婚錄/新娘秘書/婚禮主持人)
     *             photographerTime:拍攝時數(婚攝婚錄)
     *             photographerCount:攝影師人數(婚攝婚錄)
     *             makeupCount:造型數(新娘秘書)
     *  @param $scopeKey scope索引值
     *  @param $storeType 商家類型
     *  @param $keyword 關鍵字
     *  @param $weddingDate 婚期
     *  @return model
     */
    public function getStoreServiceIdsByScope($scopeName, $scopeKey, $storeType, $keyword, $weddingDate)
    {
        return $this->model->select('store_services.id')
                            ->storeHasPaid($storeType)
                            ->storeUsageActive()
                            ->searchKeywordWithStore($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->{$scopeName}($scopeKey, $storeType)
                            ->pluck('id');
    }
}
