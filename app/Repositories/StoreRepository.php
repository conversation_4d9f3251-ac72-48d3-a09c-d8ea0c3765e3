<?php
/*
 |--------------------------------------
 |  StoreRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\BaseRepositoryTrait;
use App\Models\Store;
use App\Models\WeddingcakeShop;
use App\Models\StoreFare;
use App\Models\StoreAlbum;
use App\Models\StoreAlbumImage;
use App\Models\StoreService;
use App\Models\UserCollect;
use App\Models\Wdv2\StoreOrderSet;
use App\Models\Wdv2\Order;
use App\Models\Wdv2\OrderPackage;
use App\Models\Wdv2\OrderProduct;
use App\Services\Tools\KeywordSearch\SearchService;

class StoreRepository extends AbsRepository
{
    private $userCollect;
    use BaseRepositoryTrait;

    public function __construct(
        Store $store,
        UserCollect $userCollect
    ) {
        $this->model       = $store;
        $this->userCollect = $userCollect;
    }

    /**
     * 商家列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getListByRequest($request)
    {
        $model = $this->model->select($this->model->getTable().'.id', 'type', 'extra_type', 'name', 'city_id', 'min_price', 'max_price');

        // 已付費的商家類型 store_type
        $model = $model->published($request['store_type']);

        // 婚禮小物商家必須要有小物才能出現
        if ($request['store_type'] == 7) {
            $model = $model->has('mallItems');
        }

        // 使用費限制內
        $model = $model->usageActive();

        // 取得數據
        $model = $model->withCount([
            'discounts', // 優惠數
            'kolArticles', // 部落格文章數
            'sharePostBrands', // 分享文數
            'showAlbums', // 作品數
            'showVideos', // 影片數
            'showServicesWithActivity', // 方案數
            'showVenueRooms', // 廳房數
            'mallItems', // 婚禮小物數
            'weddingcakeCookieItems', // 喜餅數
        ]);

        // 預載入
        $model = $model->with([
            'attendActivities:title', // 活動方案
            'city:id,title',
            'cover:target_id,file_name',
            'logo:target_id,file_name',
            'hasRankSharePostBrands:rank',
            'primaryBrand:id',
            'discounts:store_id',
            'descriptions:store_id,key,value',
        ]);

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $model->searchKeyword($keyword);
        }

        // 婚期 wedding_date
        if ($request['wedding_date']) {
            $model = $model->searchWeddingDate($request['wedding_date']);
        }

        // 地點 locations
        if ($request['locations']) {

            // 喜餅商家的搜尋地點，是搜尋喜餅門市的所在地，篩選條件是聯集(AND)，將原本多次join改為子查詢
            if ($request['store_type'] == 10) {
                $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
                    $sub->select('store_id')
                        ->from((new WeddingcakeShop)->getTable())
                        ->whereIn('city_id', $request['locations'])
                        ->groupBy('store_id');
                });
            } else {
                $model = $model->whereIn('city_id', $request['locations']);
            }
        }

        // 商家標籤 store_tags
        if ($request['store_tags']) {

            // 商家標籤篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
                $sub->select('target_id')
                    ->from('store_tag_pivot')
                    ->whereIn('store_tag_id', $request['store_tags'])
                    ->where('target_type', 'store')
                    ->where('value', '>', 0)
                    ->groupBy('target_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['store_tags'])]);
            });
        }

        // 免車馬費地區 free_fares
        if ($request['free_fares']) {

            // 免車馬費地區篩選條件是聯集(AND)，將原本多次join改為子查詢
            $model = $model->whereIn($this->model->getTable().'.id', function($sub) use ($request) {
                $sub->select('store_id')
                    ->from((new StoreFare)->getTable())
                    ->whereIn('city_id', $request['free_fares'])
                    ->where('value', 1)
                    ->groupBy('store_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['free_fares'])]);
            });
        }

        // 服務類型 extra_services
        if ($request['extra_services']) {
            $model = $model->whereIn('extra_type', $request['extra_services']);
        }

        // 拍婚紗-檔案全贈 has_files
        if ($request['has_files']) {
            $model = $model->whereHas('descriptions', function ($q) {
                                $q = $q->where('key', 'has_files')
                                        ->whereIn('value', [1, 2]);
                            });
        }

        // 婚紗禮服-免試穿費 | 新娘秘書-試妝服務 has_trial_fee
        if ($request['has_trial_fee']) {
            // 婚紗禮服要篩選免試穿費，所以找有開放試穿(reserve_time > 0)、且無試穿費(has_trial_fee = 0)
            if ($request['store_type'] == 2) {
                $model = $model->whereHas('descriptions', function ($q) {
                                    $q = $q->where('key', 'reserve_time')
                                            ->where('value', '>', 0);
                                });
            }
            $model = $model->whereHas('descriptions', function ($q) use ($request) {
                                $value = ($request['store_type'] == 2) ? 0 : 1; // 婚紗禮服要篩選免試穿費
                                $q = $q->where('key', 'has_trial_fee')
                                        ->where('value', $value);
                            });
        }

        // 喜餅-免費門市試吃 has_free_shop_tasting
        $model = $model->freeShopTasting($request['has_free_shop_tasting']);

        // 顯示活動方案商家 activity_ids
        if ($request['activity_ids']) {
            $model = $model->whereHas('attendActivities', function ($q) use ($request) {
                                $q->whereIn('activities.id', $request['activity_ids']);
                            });
        }

        // 顯示有獨家優惠的商家 has_discount
        if ($request['has_discount']) {
            $model = $model->whereHas('discounts');
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $model = $model->orderBy('edited_at', 'DESC');

        // 評價數排序 sort:evaluationRate
        } elseif ($request['sort'] == 'evaluationRate') {
            $model = $model->orderBy('share_post_brands_count', 'DESC');

        // 討論度排序 sort:discussionRate
        } elseif ($request['sort'] == 'discussionRate') {
            $model = $model->orderBy('rank_discussion', 'DESC');

        // 價格低到高 sort:minPrice
        } elseif ($request['sort'] == 'minPrice') {
            // 沒有價格的放最後
            $model = $model->orderByRaw('ISNULL(min_price), min_price = 0, min_price', 'ASC');

        // 價格高到低 sort:maxPrice
        } elseif ($request['sort'] == 'maxPrice') {
            $model = $model->orderBy('max_price', 'DESC');
        }

        // 人氣排序 sort:hot(default)
        $model = $model->orderBy('rank', 'DESC');

         // 分頁 page
        $model = $model->paginate(48);

        return $model;
    }

    /**
     * 商家列表的總數 By type
     *
     *  @param $type
     *  @return model
     */
    public function getListTotalByType($type, $keyword = NULL, $weddingDate = NULL, $freeShopTasting = NULL)
    {
        $model = $this->model->published($type)
                            ->usageActive()
                            ->searchKeyword($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->freeShopTasting($freeShopTasting);

        // 婚禮小物商家必須要有小物才能出現
        if ($type == 7) {
            $model = $model->has('mallItems');
        }

        return $model->count();
    }

    /**
     * 神之後台商家管理-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request, $pagination = true)
    {
        $model = $this->model;

        // 預載入
        $model = $model->with([
            'orderSet.contractOrder.orderPackages:order_product_id,package_name',
            'orderSet.contractOrder.orderPackages.orderProduct:type',
            'tags:name',
        ]);

        // 商家類型 types
        if ($request['types']) {
            $model = $model->whereIn('type', $request['types']);
        }

        // 商家狀態 status
        if ($request['status']) {
            $model = $model->status($request['status']);
        }

        // 合約狀態 contract_status - 婚禮小物不需要合約
        if ($request['contract_status'] == 'not_need') {
            $model = $model->where('type', 7);

        // 合約狀態 contract_status - 未曾付費：沒有合約
        } elseif ($request['contract_status'] == 'never') {
            $model = $model->where('type', '!=', 7)
                            ->whereDoesntHave('contractOrders');

        // 合約狀態 contract_status - 合約終止：沒有目前的合約訂單
        } elseif ($request['contract_status'] == 'ended') {
            $model = $model->where('type', '!=', 7)

                            // 有合約
                            // ->whereHas('contractOrders')
                            ->select('stores.*')
                            ->join((new Order)->getTable(), function($join) {
                                $join->on('stores.id', '=', 'order.store_id')
                                        ->where('order.show_flag', 2)
                                        ->where('payment_status', 1);
                            })
                            ->groupBy('stores.id')

                            // 沒有目前的合約訂單
                            ->whereDoesntHave('orderSet.contractOrder');

        // 合約狀態 contract_status - 月付型：目前的合約訂單中，且含有自動續約的品項
        } elseif ($request['contract_status'] == 'renewing') {
            $model = $model->where('type', '!=', 7)

                            // 目前的合約訂單中，且含有自動續約的品項
                            // ->whereHas('orderSet.contractOrder.orderPackages', function ($q) {
                            //     $q = $q->whereIn('order_product_id', (new OrderProduct)->setFeeContractType['renew']);
                            // });
                            ->select('stores.*')
                            ->join((new StoreOrderSet)->getTable(), function($join) {
                                $join->on('stores.id', '=', 'store_order_set.store_id')
                                        ->whereNotNull('contract_order_id');
                            })
                            ->join((new Order)->getTable(), function($join) {
                                $join->on('store_order_set.contract_order_id', '=', 'order.id')
                                        ->where('order.show_flag', 2)
                                        ->where('payment_status', 1);
                            })
                            ->join((new OrderPackage)->getTable(), function($join) {
                                $join->on('order.id', '=', 'order_package.order_id')
                                        ->where('order_package.show_flag', 2)
                                        ->whereIn('order_product_id', (new OrderProduct)->setFeeContractType['renew']);
                            })
                            ->groupBy('stores.id');

        // 合約狀態 contract_status - 方案型
        } elseif ($request['contract_status'] == 'running') {
            $model = $model->where('stores.type', '!=', 7)

                            // 目前的合約訂單
                            // ->whereHas('orderSet.contractOrder')
                            ->select('stores.*')
                            ->join((new StoreOrderSet)->getTable(), function($join) {
                                $join->on('stores.id', '=', 'store_order_set.store_id')
                                        ->whereNotNull('contract_order_id');
                            })
                            ->join((new Order)->getTable(), function($join) {
                                $join->on('store_order_set.contract_order_id', '=', 'order.id')
                                        ->where('order.show_flag', 2)
                                        ->where('payment_status', 1);
                            })

                            // 目前的合約訂單中，且含有自動續約的品項
                            // ->whereDoesntHave('orderSet.contractOrder.orderPackages', function ($q) {
                            //     $q = $q->whereIn('order_product_id', (new OrderProduct)->setFeeContractType['renew']);
                            // });
                            ->whereNotIn('stores.id',  function($q) {
                                $q->select('stores.id')
                                    ->from($this->model->getTable())
                                    ->join((new StoreOrderSet)->getTable(), function($join) {
                                        $join->on('stores.id', '=', 'store_order_set.store_id')
                                                ->whereNotNull('contract_order_id');
                                    })
                                    ->join((new Order)->getTable(), function($join) {
                                        $join->on('store_order_set.contract_order_id', '=', 'order.id')
                                                ->where('order.show_flag', 2)
                                                ->where('payment_status', 1);
                                    })
                                    ->join((new OrderPackage)->getTable(), function($join) {
                                        $join->on('order.id', '=', 'order_package.order_id')
                                                ->where('order_package.show_flag', 2)
                                                ->whereIn('order_product_id', (new OrderProduct)->setFeeContractType['renew']);
                                    });
                            });
        }

        // 商家標記 marks
        if ($request['marks']) {
            $model = $model->whereHas('marks', function ($q) use ($request) {
                                $q = $q->whereIn('set_tag_id', $request['marks']);
                            });
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序 sort:id(default)|onlined_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }

    /**
     * 神之後台商家管理-取得排除共享W姐妹夥伴的商家列表 By Request
     *
     *  @return model
     */
    public function getListWithoutBrandStoresByRequest($request)
    {
        $store = $request['store'];

        $model = $this->model;

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 共享W姐妹的夥伴ID
        $brandStoreIds = [];
        foreach ($store->allBrands as $brand) {

            // 取得品牌的主要商家(包含自己)
            $primaryStore = $brand->primaryStores()->first();
            if ($primaryStore) {
                $brandStoreIds[] = $primaryStore->id;
            }
        }
        $model = $model->whereNotIn('id', $brandStoreIds);

        return $model->get();
    }

    /**
     * 網站主頁取得熱門商家的相本
     *
     *  @return model $storeAlbum
     */
    public function getPopularAlbums($type, $limit)
    {
        $storeTable = $this->model->getTable();
        $albumTable = (new StoreAlbum)->getTable();
        $imageTable = (new StoreAlbumImage)->getTable();

        // 熱門商家
        $stores = $this->model->select($storeTable.'.id', $storeTable.'.name')
                                ->published($type)
                                ->join($albumTable, function($join) use ($storeTable, $albumTable) {
                                    $join->on($albumTable.'.store_id', '=', $storeTable.'.id')
                                            ->where($albumTable.'.status', 'show')
                                            ->whereNotNull($albumTable.'.cover_id');
                                })
                                // 上架中的相本，先不用判斷裡面有沒有含照片
                                // ->join($imageTable, function($join) {
                                //     $join->on($imageTable'.album_id', '=', $albumTable.'.id')
                                //             ->where($imageTable'..show_flag', 2);
                                // })
                                ->groupBy($storeTable.'.id')
                                ->orderBy($storeTable.'.rank', 'DESC')
                                ->with([
                                    'popularAlbums:id,store_id,cover_id',
                                    'cover:target_id,file_name',
                                    'attendActivities:title',
                                ])
                                ->limit($limit)
                                ->get();

        // 商家的相本
        $albums = [];
        foreach ($stores as $store) {
            $albums[] = $store->popularAlbums
                                ->first()
                                ->setAttribute('store_name', $store->name)
                                ->setAttribute('activity_badge', $store->attendActivities->first()->title ?? '');
        }

        return $albums;
    }

    /**
     * 網站主頁取得熱門商家
     *
     *  @return model $store
     */
    public function getPopularStores($type, $limit)
    {
        // 熱門商家
        $stores = $this->model->select('id', 'name')
                                ->published($type)
                                ->has('logo')
                                ->has('cover')
                                ->with('attendActivities:title')
                                ->orderBy('rank', 'DESC')
                                ->limit($limit)
                                ->get();

        // 商家
        foreach ($stores as $key => $store) {
            $stores[$key] = $store->setAttribute('activity_badge', $store->attendActivities->first()->title ?? '');
        }

        return $stores;
    }

    /**
     * 網站主頁取得熱門商家的婚禮小物
     *
     *  @return model $storeAlbum
     */
    public function getPopularMallItems($type, $limit)
    {
        // 熱門商家
        $stores = $this->model->select('id', 'name')
                                ->published($type)
                                ->has('mallItems')
                                ->with(['mallItems', 'attendActivities:title'])
                                ->orderBy('rank', 'DESC')
                                ->limit($limit)
                                ->get();

        // 商家的婚禮小物
        $items = [];
        foreach ($stores as $store) {
            $items[] = $store->mallItems
                                ->first()
                                ->setAttribute('store_id', $store->id)
                                ->setAttribute('activity_badge', $store->attendActivities->first()->title ?? '');
        }

        return $items;
    }

    /**
     * 新增主動報價須通知的付費商家
     *
     *  @return model $store
     */
    public function getNewQuoteListByType($type)
    {
        return $this->model->quoteEmailSubscribers()
                            ->type($type)
                            ->whereIn('status', ['published', 'pending']) // 未上架的商家也要通知
                            ->emailNotEmpty()
                            ->inRandomOrder()
                            ->get();
    }

    /**
     * 取得特定類別商家的相本數
     *
     *  @return $count
     */
    public function getAlbumCountByType($type)
    {
        $storeTable = $this->model->getTable();
        $albumTable = (new StoreAlbum)->getTable();

        return StoreAlbum::join($storeTable, $storeTable.'.id', '=', $albumTable.'.store_id')
                            ->where($storeTable.'.type', $type)
                            ->count();
    }

    /**
     * 取得特定類別商家的單一作品數
     *
     *  @return $count
     */
    public function getWorkCountByType($type)
    {
        $storeTable = $this->model->getTable();
        $albumTable = (new StoreAlbum)->getTable();
        $imageTable = (new StoreAlbumImage)->getTable();

        return StoreAlbumImage::join($albumTable, $albumTable.'.id', '=', $imageTable.'.album_id')
                                ->join($storeTable, $storeTable.'.id', '=', $albumTable.'.store_id')
                                ->where($storeTable.'.type', $type)
                                ->count();
    }

    /**
     * 取得指定期限內商家不重複的新娘所有收藏數
     *
     *  @return model $store
     */
    public function getUniqueUserAllCollectCountByAfterAt($store, $afterAt)
    {
        // 收藏服務方案
        $serviceCollects = $this->userCollect->select('user_id')
                                                ->join((new StoreService)->getTable(), function($join) {
                                                    $join->on('store_services.id', '=', 'user_collects.target_id')
                                                        ->where('store_services.status', 'show');
                                                })
                                                ->where('store_services.store_id', $store->id)
                                                ->where('user_collects.type', 'service')
                                                ->where('user_collects.created_at', '>=', $afterAt);

        // 收藏作品
        if (in_array($store->type, [2, 10])) {
            $albumCollects = $this->userCollect->select('user_id')
                                                ->join((new StoreAlbum)->getTable(), function($join) {
                                                    $join->on('store_albums.id', '=', 'user_collects.target_id')
                                                        ->where('store_albums.status', 'show')
                                                        ->whereNotNull('store_albums.cover_id');
                                                })
                                                ->where('store_albums.store_id', $store->id)
                                                ->where('user_collects.type', 'album')
                                                ->where('user_collects.created_at', '>=', $afterAt);

        // 其他商家類型-單一作品
        } else {
            $albumCollects = $this->userCollect->select('user_id')
                                                ->join((new StoreAlbumImage)->getTable(), function($join) {
                                                    $join->on('store_album_images.id', '=', 'user_collects.target_id');
                                                })
                                                ->join((new StoreAlbum)->getTable(), function($join) {
                                                    $join->on('store_albums.id', '=', 'store_album_images.album_id')
                                                        ->where('store_albums.status', 'show')
                                                        ->whereNotNull('store_albums.cover_id');
                                                })
                                                ->where('store_albums.store_id', $store->id)
                                                ->where('user_collects.type', 'album_image')
                                                ->where('user_collects.created_at', '>=', $afterAt);
        }

        // 收藏商家
        return $this->userCollect->select('user_id')
                                    ->where('target_id', $store->id)
                                    ->where('type', 'store')
                                    ->where('created_at', '>=', $afterAt)
                                    ->union($serviceCollects)
                                    ->union($albumCollects)
                                    ->get()
                                    ->count();

        // SQLSTATE[HY000]: General error: 1390 Prepared statement contains too many placeholders
        // return $this->userCollect->select('id')
        //                             ->live()
        //                             ->where('created_at', '>=', $afterAt)
        //                             ->where(function($q1) use ($store) {

        //                                 // 收藏商家
        //                                 $q1->orWhere(function($q2) use ($store) {
        //                                         $q2->where('target_id', $store->id)
        //                                             ->where('type', 'store');
        //                                     })

        //                                 // 收藏服務方案
        //                                     ->orWhere(function($q2) use ($store) {
        //                                         $q2->whereIn('target_id', $store->showServices->pluck('id'))
        //                                             ->where('type', 'service');
        //                                     })

        //                                 // 收藏作品
        //                                     ->orWhere(function($q2) use ($store) {

        //                                         // 婚紗禮服-作品集
        //                                         if ($store->type == 2) {
        //                                             $q2->whereIn('target_id', $store->showAlbums->pluck('id'))
        //                                                 ->where('type', 'album');

        //                                         // 其他商家類型-單一作品
        //                                         } else {
        //                                             $q2->whereIn('target_id', $store->albumImages->pluck('id'))
        //                                                 ->where('type', 'album_image');
        //                                         }
        //                                     });
        //                             })
        //                             ->groupBy('user_id')
        //                             ->get()
        //                             ->count();
    }
}
