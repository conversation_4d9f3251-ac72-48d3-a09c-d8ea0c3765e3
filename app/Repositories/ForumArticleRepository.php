<?php
/*
 |--------------------------------------
 |  ForumArticleRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\ParseKeywordTrait;
use App\Models\ForumArticle as Article;
use App\Models\ForumComment as Comment;

class ForumArticleRepository extends AbsRepository
{
    use ParseKeywordTrait;

    private $comment;

    public function __construct(
        Article $article,
        Comment $comment
    ) {
        $this->model   = $article;
        $this->comment = $comment;
    }

    /**
     * 好婚聊聊-新娘正在討論的文章列表
     *
     *  @return model
     */
    public function getHotScoreSampleList($limit)
    {
        return $this->model->release()
                            // ->has('parentComments') // release()內有hasAuthor()，所以不用$comment->live()...
                            ->join($this->comment->getTable(), function($join) {
                                $join->on('article_id', '=', $this->model->getTable().'.id')
                                        ->whereNull('parent_id');
                            })
                            ->groupBy('forum_articles.id')
                            ->where('forum_articles.category_id', '!=', 5) // 5是推薦文章
                            // 列表需要的欄位 (需覆蓋release()內的select())
                            ->select('forum_articles.id', 'forum_articles.is_anonymous', 'forum_articles.user_id', 'title', 'comment_count', 'category_id')
                            ->sortHotScore()
                            ->sortPageView()
                            ->limit($limit)
                            ->get();
    }

    /**
     * 文章列表 By Request
     *
     *  @return model
     */
    public function getListByRequest($request)
    {
        // 可釋出的文章
        $model = $this->model->release();
        // $model = $this->model->select('forum_articles.*')->release();

        // 預載入 作者/封面照/圖片/更新紀錄
        $model = $model->with(['author', 'cover', 'images', 'logUpdateds']);

        // 分類 category_id
        if ($request['category_id']) {
            $model = $model->whereHas('category', function($q) use ($request) {
                $q->where('id', $request['category_id']);
            });
            // $model = $model->where('category_id', $request['category_id']);
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            // $model = $model->leftJoin('forum_article_tag', function($join) {
            //                     $join->on('forum_articles.id', '=', 'forum_article_tag.article_id');
            //                 })
            //                 ->leftJoin('forum_tags', function($join) {
            //                     $join->on('forum_article_tag.tag_id', '=', 'forum_tags.id');
            //                 })
            //                 ->groupBy('forum_articles.id');
            $keywords = $this->splitToArray($keyword);
            foreach ($keywords as $val) {
                $model = $model->where(function ($q1) use ($val) {
                    $q1 = $q1->orWhere('forum_articles.title', 'like', '%' . $val . '%')
                            ->orWhere('forum_articles.content', 'like', '%' . $val . '%')
                            ->orWhereHas('tags', function ($q2) use ($val) {
                                $q2->where('name', 'like', '%' . $val . '%');
                            });
                            // ->orWhere('forum_tags.name', 'like', '%' . $val . '%');
                });
            }
        }

        // 標籤 tag_id
        if ($request['tag_id']) {
            $model = $model->whereHas('tags', function($q) use ($request) {
                $q->where('tag_id', $request['tag_id']);
            });
            // $model = $model->join('forum_article_tag', function($join) use ($request) {
            //                     $join->on('forum_articles.id', '=', 'forum_article_tag.article_id')
            //                             ->where('forum_article_tag.tag_id', $request['tag_id']);
            //                 });
        }

        // 置頂優先
        $model = $model->orderBy('is_top', 'DESC');

        // 排序 sort:new|hot(default)
        if (!$request['sort'] || $request['sort'] == 'hot') {
            $model = $model->sortHotScore();
        }
        $model = $model->sortPublishedAt();

        // 分頁 page
        $model = $model->paginate(50, ['*'], null, $request['page']);

        return $model;
    }

    /**
     * 取得品牌已發佈的分享文列表 By Brand
     *
     *  @return model
     */
    public function getPublishedListByBrand($brand, $excludeIds = [], $limit = 4)
    {
        // 品牌的熱門分享文列表
        $model = $brand->articles()

                        // 列表需要的欄位 (需覆蓋release()內的select())
                        ->select('forum_articles.id', 'user_id', 'title', 'summary', 'page_view', 'published_at')

                        // 熱門排序
                        ->sortHotScore()
                        ->sortPublishedAt();

        // 排除分享文編號陣列 excludeIds
        if ($excludeIds) {
            $model = $model->whereNotIn('forum_articles.id', $excludeIds);
        }

        // 數量限制
        $model = $model->limit($limit);

        // 預載入 作者
        $model = $model->with('author');

        return $model->get();
    }

    /**
     * 指定期限內發佈的文章
     *
     *  @return model
     */
    public function getNewPublishedByAfterAt($after_at)
    {
        return $this->model->status('published')
                            ->hasAuthor()
                            ->where('published_at', '>=', $after_at)
                            ->sortPublishedAt()
                            ->get();
    }

    /**
     * 神之後台文章管理-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        // 預載入 作者
        $model = $this->model->select($this->model->getTable().'.*')
                                ->with('author');

        // 分類 category_id
        if ($request['category_id']) {
            $model = $model->whereHas('category', function($q) use ($request) {
                $q->where('id', $request['category_id']);
            });
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 狀態 status
        if ($request['status']) {
            $model = $model->where('status', $request['status']);
        }

        // 是否排除SEO is_exclude_seo
        if ($request['is_exclude_seo']) {
            $model = $model->where('is_exclude_seo', $request['is_exclude_seo']);
        }

        // 排序 sort:id(default)|published_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
