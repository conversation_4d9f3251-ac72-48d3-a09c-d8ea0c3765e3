<?php
/*
 |--------------------------------------
 |  BrandRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\ParseKeywordTrait;
use App\Models\Brand;

class BrandRepository extends AbsRepository
{
    use ParseKeywordTrait;

    public function __construct(Brand $brand)
    {
        $this->model = $brand;
    }

    /**
     * 神之後台品牌管理-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 狀態 status
        if ($request['status']) {
            $model = $model->where('status', $request['status']);
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywords = $this->splitToArray($keyword);
            $model = $model->where(function ($q1) use ($keywords) {
                foreach ($keywords as $val) {
                    $q1 = $q1->orWhere('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%')
                            ->orWhere('fb_page', 'like', '%' . $val . '%')
                            ->orWhere('email', 'like', '%' . $val . '%');
                }
            });
        }

        $model = $model->withCount('stores');
        $model = $model->withCount('articles');

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
