<?php

namespace App\Repositories;

use App\Models\SeoStoreRoute;
use App\Services\Tools\KeywordSearch\SearchService as KeywordSearchService;

class SeoStoreRouteRepository extends AbsRepository
{
    private $keywordSearchService;

    public function __construct(
        SeoStoreRoute $seoStoreRoute,
        KeywordSearchService $keywordSearchService
    ) {
        $this->model                = $seoStoreRoute;
        $this->keywordSearchService = $keywordSearchService;
    }

    /**
     * 神之後台-SEO商家路由客製-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model->with([
            'store:id,type,name',
            'logs'
        ]);

        // 路由名稱
        if ($request['route_name']) {
            $model = $model->whereIn('route_name', $request['route_name']);
        }

        // 商家ID
        if ($request['store_id']) {
            $model = $model->where('store_id', $request['store_id']);
        }

        // 目標類型
        if ($request['target_type']) {
            $model = $model->where('target_type', $request['target_type']);
        }

        // 目標ID
        if ($request['target_id']) {
            $model = $model->where('target_id', $request['target_id']);
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $model = $this->keywordSearchService->search($model, $keyword);
        }

        // 排序
        $model = $model->sort();

        // 分頁 page
        return $model->paginate(20);
    }
}
