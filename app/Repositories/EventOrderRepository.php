<?php
/*
 |--------------------------------------
 |  EventOrderRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Models\EventOrder;

class EventOrderRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(EventOrder $eventOrder)
    {
        $this->model = $eventOrder;
    }

    /**
     * 神之後台-活動訂單列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request, $pagination = true)
    {
        // 包含刪除的訂單
        $model = $this->model->withTrashed();

        // 付款狀態
        if ($request['payment_status']) {
            $model = $model->where('payment_status', $request['payment_status']);
        }

        // 發票狀態
        if ($request['invoice_status']) {
            $model = $model->where('invoice_status', $request['invoice_status']);
        }

        // 所屬發票設定
        if ($request['invoice_setting_id']) {
            $model = $model->where('invoice_setting_id', $request['invoice_setting_id']);
        }

        // 付款日期區間
        if ($request['start_date'] && $request['end_date']) {
            $startDate = $request['start_date'] . ' 00:00:00';
            $endDate   = $request['end_date'] . ' 23:59:59';
            $model     = $model->whereBetween('created_at', [$startDate, $endDate]);
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序
        $model = $model->orderBy('created_at', 'DESC');

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }
}
