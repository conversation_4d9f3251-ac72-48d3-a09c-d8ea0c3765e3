<?php
/*
 |--------------------------------------
 |  LineGroupRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\BaseRepositoryTrait;
use App\Models\LineGroup;

class LineGroupRepository extends AbsRepository
{
    use BaseRepositoryTrait;

    public function __construct(LineGroup $lineGroup)
    {
        $this->model = $lineGroup;
    }

    /**
     * 神之後台-LINE 群組管理列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model->with('store');

        // 商家類型 store_type
        if ($request['store_type']) {
            $model = $model->whereHas('store', function ($q) use ($request) {
                $q->where('type', $request['store_type']);
            });
        }

        // 連結商家狀態 has_store
        if (isset($request['has_store'])) {
            $relation = $request['has_store'] ? 'whereHas' : 'whereDoesntHave';
            $model->{$relation}('store');
        }

        // 前台顯示狀態 is_show
        if (isset($request['is_show'])) {
            $model->where('is_show', $request['is_show']);
        }

        // 機器人狀態 has_line_bot
        if (isset($request['has_line_bot'])) {
            $relation = $request['has_line_bot'] ? 'whereNull' : 'whereNotNull';
            $model->{$relation}('leaved_at');
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, $keyword);
        }

        // 排序
        $model = $model->orderBy('created_at', 'DESC');

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
