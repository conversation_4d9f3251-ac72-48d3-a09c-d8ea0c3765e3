<?php
/*
 |--------------------------------------
 |  ReserveAppealRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Models\Wdv2\ReserveAppeal;
use App\Services\Tools\KeywordSearch\SearchService as KeywordSearchService;

class ReserveAppealRepository extends AbsRepository
{
    /** @var KeywordSearchService */
    private $keywordSearchService;

    /**
     * ReserveRepository constructor.
     * @param ReserveAppeal $reserveAppeal
     * @param KeywordSearchService $keywordSearchService
     */
    public function __construct(
        ReserveAppeal $reserveAppeal,
        KeywordSearchService $keywordSearchService
    )
    {
        $this->model                = $reserveAppeal;
        $this->keywordSearchService = $keywordSearchService;
    }

    /**
     * 取得無效詢問申請列表
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getYzcubeListByRequest()
    {
        $model = $this->model->with([
                                'store:id,type,name',
                                'reserve.user:id,name',
                            ]);

        // 關鍵字 keyword
        if (request('keyword')) {
            $model = $this->keywordSearchService->search($model, request('keyword'));
        }

        // 商家類型 store_type
        if (request('store_type')) {
            $model = $model->whereHas('store', function ($q) {
                                $q->where('type', request('store_type'));
                            });
        }

        // 申請無效的理由 reason
        if (is_numeric(request('reason'))) {
            $model = $model->where('reason', 'like', '%'.$this->model->reasonList[request('reason')].'%');
        }

        // 處理狀態 status
        if (is_numeric(request('status'))) {
            $model = $model->where('status', request('status'));
        }

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = request('sort') ?: 'id';
        $direction = request('direction') ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
