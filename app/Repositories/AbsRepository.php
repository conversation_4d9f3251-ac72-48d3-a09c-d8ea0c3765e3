<?php
/*
 |--------------------------------------
 | Repository的主要可共用功能都提煉來這
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

abstract class AbsRepository
{
    /** @var : 注入 model */
    protected $model;

    /** @var bool : 是否分頁 是true, 否false */
    public $paginateFlag = false;

    /** @var array : 存放分頁參數 */
    protected $paginateArgs = [];

    /** @var bool : 是否加order語法 是true, 否false */
    public $orderByFlag = false;

    /** @var array : 存放order by參數 */
    protected $orderBy = [];

    /** @var bool : 是否加group語法 是true, 否false */
    public $groupByFlag = false;

    /** @var array : 存放group by參數 */
    protected $groupBy = [];

    /**
     * 請定分頁參數
     * @param string $limit : 回傳筆數
     * @param string $page : 第幾頁
     */
    public function setPaginate($limit = '', $page = '')
    {
        $this->paginateFlag = true;
        $this->paginateArgs = [
            'limit' => $limit,
            'page'  => $page,
        ];
    }

    /**
     * 取得分頁資料
     * makeQuery時使用，增加了判斷有無order by或group by
     * @param $query : db query
     * @return mixed
     */
    public function paginate($query)
    {
        $page  = $this->paginateArgs['page'] ?? 1;
        $limit = $this->paginateArgs['limit'] ?? 20;

        $query = $query->skip(($page - 1) * $limit)
                        ->take($limit);

        $query = $this->checkGrammar($query);

        return $query->get();
    }

    /**
     * 取得分頁資料
     * @param $query : db query
     * @return mixed
     */
    public function getPaginate($query)
    {
        $page  = $this->paginateArgs['page'] ?? 1;
        $limit = $this->paginateArgs['limit'] ?? 20;

        return $query->skip(($page - 1) * $limit)
                        ->take($limit)
                        ->get();
    }

    /**
     * 設定排序資料
     * @param string $column
     * @param string $sort
     */
    public function setOrderBy($column = '', $sort = 'asc')
    {
        $this->orderByFlag = true;
        $this->orderBy = [
            'column' => $column,
            'sort'   => $sort
        ];
    }

    /**
     * 取得排序資料
     * @param $query
     * @return mixed
     */
    protected function getOrderBy($query)
    {
        return $query->orderBy($this->orderBy['column'], $this->orderBy['sort']);
    }

    /**
     * 設定群組資料
     * @param string $column
     */
    public function setGroupBy($column)
    {
        $this->orderByFlag = true;
        $this->groupBy = [
            'column' => $column
        ];
    }

    /**
     * 取得群組資料
     * @param $query
     * @return mixed
     */
    protected function getGroupBy($query)
    {
        return $query->groupBy($this->groupBy['column']);
    }
//----------------------------------------

    /**
     * init query
     * @return mixed
     */
    public function makeQuery()
    {
        return $this->model->newQuery();
    }

    /**
     * 取得全部資料
     * @param array || closure $where : 搜尋條件
     * @return mixed
     */
    public function getAll()
    {
        $query = $this->makeQuery();

        $query = $this->checkGrammar($query);

        if ($this->paginateFlag) {
            return $this->getPaginate($query);
        }

        return $query->get();
    }


    /**
     * 依條件取得資料
     * @param array || closure $where : 搜尋條件
     * @return mixed
     */
    public function getData($where)
    {
        $query = $this->model->where($where);

        $query = $this->checkGrammar($query);

        if ($this->paginateFlag) {
            return $this->getPaginate($query);
        }

        return $query->get();
    }

    /**
     * 搜尋單筆資料
     * @param array || closure $where : 搜尋條件
     * @return mixed
     */
    public function getFirst($where)
    {
        return $this->model->where($where)->first();
    }

    /**
     * 搜尋where in
     * @param string $filed : 欄位名
     * @param array $data : 搜尋資料
     * @return mixed
     */
    public function getIn($filed, $data)
    {
        $query = $this->model->whereIn($filed, $data);

        $query = $this->checkGrammar($query);

        if ($this->paginateFlag) {
            return $this->getPaginate($query);
        }

        return $query->get();
    }

    /**
     * 搜尋資料筆數
     * @param array || closure $where : 搜尋條件
     * @return int
     */
    public function getCount($where)
    {
        return $this->model->where($where)->count();
    }

    /**
     * 新增資料
     * @param array $data : 要寫入的資料
     * @return mixed
     */
    public function addData($data)
    {
        return $this->model->create($data);
    }

    /**
     * 更新資料
     * @param $where array || closure $where : 更新條件
     * @param array $data : 要更新的資料
     * @return mixed
     */
    public function updateData($where, $data)
    {
        return $this->model->where($where)->update($data);
    }

    /**
     * 刪除資料
     * @param $where array || closure $where : 刪除條件
     * @return mixed
     */
    public function deleteData($where)
    {
        return $this->model->where($where)->delete();
    }

    /**
     * 沒資料就新增，有資料就變更
     * @param $data : 要新增的資料
     * @return
     */
    public function firstOrCreate($data)
    {
        return $this->model->firstOrCreate($data);
    }

    /**
     * 判斷有無排序語法
     * @param $query
     * @return mixed
     */
    protected function checkGrammar($query)
    {
        if ($this->orderByFlag) {
            $query = $this->getOrderBy($query);
        }

        if($this->groupByFlag){
            $query = $this->getGroupBy($query);
        }

        return $query;
    }

    /**
     * 沒資料就新增，有資料就變更
     * @param $where : 判斷是否存在的條件
     * @param $data : 要新增的資料
     * @return
     */
    public function updateOrCreate($where, $data)
    {
        return $this->model->updateOrCreate($where, $data);
    }

}
