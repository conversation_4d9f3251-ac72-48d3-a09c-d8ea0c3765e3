<?php
/*
 |--------------------------------------
 |  FeedbackRepository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Traits\ParseKeywordTrait;
use App\Models\Feedback;

class FeedbackRepository extends AbsRepository
{
    use ParseKeywordTrait;

    public function __construct(Feedback $feedback)
    {
        $this->model = $feedback;
    }

    /**
     * 神之後台意見回饋管理-列表 By Request
     *
     *  @return model
     */
    public function getYzcubeListByRequest($request)
    {
        $model = $this->model;

        // 狀態 status:pending|processing|completed
        if ($request['status']) {
            $model = $model->where('status', $request['status']);
        }

        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywords = $this->splitToArray($keyword);
            $model = $model->where(function ($q1) use ($keywords) {
                foreach ($keywords as $val) {
                    $q1 = $q1->orWhere('id', $val)
                            ->orWhere('user_id', $val)
                            ->orWhere('content', 'like', '%' . $val . '%')
                            ->orWhere('note', 'like', '%' . $val . '%')
                            ->orWhereHas('user', function ($q2) use ($val) {
                                $q2->where('id', $val)
                                    ->orWhere('name', 'like', '%' . $val . '%')
                                    ->orWhere('real_name', 'like', '%' . $val . '%')
                                    ->orWhere('email', 'like', '%' . $val . '%');
                            });
                }
            });
        }

        // 排序 sort:id(default)|updated_at
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: 'id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

         // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
