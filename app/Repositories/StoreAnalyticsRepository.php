<?php
/*
 |--------------------------------------
 |  StoreAnalytics Repository
 |--------------------------------------
 |
 |
 */

namespace App\Repositories;

use App\Repositories\AbsRepository;
use App\Models\Wdv2\StoreAnalytics;

class StoreAnalyticsRepository extends AbsRepository
{
    public function __construct(StoreAnalytics $storeAnalytics)
    {
        $this->model = $storeAnalytics;
    }

    /**
     *
     *  取得特定商家統計資料
     *
     * @param $storeID: 商家id
     * @param $startDate: 開始時間
     * @param $endDate: 結束時間
     * @return
     */
    public function getStoreAnalytics($storeID, $startDate, $endDate)
    {
        return $this->model->where('store_id', $storeID)
            ->where('show_flag', 2)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'asc')
            ->get();
    }
}
