<?php
/*
 |--------------------------------------
 | 商家下架處理的留言資料
 |--------------------------------------
 |
 |
 */
namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class ServiceStopApplyComment extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    /**
     * relation YzcubeUser
     * 取得管理者資料
     */
    public function yzcubeUser()
    {
        return $this->belongsTo(YzcubeUser::class);
    }
}
