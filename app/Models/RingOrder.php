<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RingOrder extends Model
{
    protected $guarded = [];

    // 款式 diamond:鑽戒 lovers:對戒 gold:金飾 other:其他
    public $ringStyleList = [
        'diamond' => '鑽戒',
        'lovers'  => '對戒',
        'gold'    => '金飾',
        'other'   => '其他',
    ];

    // 狀態 pending:待審核 approved:已認證 invalid:無效的
    public $statusList = [
        'pending'  => '待審核',
        'approved' => '已認證',
        'invalid'  => '無效的',
    ];

    use SoftDeletes;

    // relation RingBrand 婚戒大賞-品牌
    public function ringBrand()
    {
        return $this->belongsTo(RingBrand::class);
    }

    // relation RingBrand 婚戒大賞-報名資訊
    public function ringUser()
    {
        return $this->belongsTo(RingUser::class);
    }

    // scope approved 已認證訂單
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }
}
