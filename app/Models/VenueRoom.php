<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ParseKeywordTrait;

class VenueRoom extends Model
{
    use SoftDeletes;

    use ParseKeywordTrait;

    // 商家類型 5:婚宴場地
    public $storeType = 5;

    protected $guarded = [];

    protected $dates = ['edited_at'];

    protected $casts = [
        'narrates'      => 'array',
        'combine_rooms' => 'array',
    ];

    // 狀態 hide:隱藏 show:顯示
    public $statusList = [
        'hide' => '隱藏',
        'show' => '顯示',
    ];

    // 桌型 round:中式圓桌 long:西式長桌
    public $tablesList = [
        'round' => '中式圓桌',
        'long'  => '西式長桌',
    ];

    // 送客背板 0:無 1:有 2:限方案
    public $backplaneList = [
        0 => '無',
        1 => '有',
        2 => '限方案',
    ];

    // 類型 (僅用於前台)
    public $typeList = [
        'banquet'  => '宴客',
        'witness'  => '證婚',
        'ceremony' => '文定/迎娶',
    ];

    // 容納人數 (僅用於前台)
    public $numberList = [
        ['label' => '100 人以下', 'range' => ['', 100]],
        ['label' => '101-150 人', 'range' => [101, 150]],
        ['label' => '151-200 人', 'range' => [151, 200]],
        ['label' => '201-250 人', 'range' => [201, 250]],
        ['label' => '251-300 人', 'range' => [251, 300]],
        ['label' => '301-400 人', 'range' => [301, 400]],
        ['label' => '400 人以上', 'range' => [401, '']],
    ];

    // 廳房設備 (僅用於前台)
    public $deviceList = [
        'bridal_room' => ['label' => '新娘休息室', 'value' => 1],
        'wifi'        => ['label' => 'Wi-Fi', 'value' => 1],
        'projection'  => ['label' => '投影幕設備', 'value' => 1],
        'led'         => ['label' => 'LED 螢幕設備', 'value' => 1],
        'sound'       => ['label' => '音響設備', 'value' => 1],
        'light'       => ['label' => '特殊燈光設備', 'value' => 1],
        'stage'       => ['label' => '舞台', 'value' => 1],
        'pillar'      => ['label' => '無樑柱', 'value' => 0],
        'backplane'   => ['label' => '送客背板', 'value' => [1, 2]],
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // relation Image 廳房照片
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')->where('type', 'venue_room');
    }

    // relation Image 廳房封面照
    public function cover()
    {
        return $this->belongsTo(Image::class, 'cover_id');
    }

    // relation Store 所屬商家
    public function allStore()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    // relation Store 所屬上架商家
    public function store()
    {
        return $this->allStore()->published();
    }

    // relation User
    public function userCollects()
    {
        return $this->belongsToMany(User::class, (new UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'venue_room')
                    ->where((new User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation LogGaPageView GA網頁瀏覽量
    public function logGaPageViews()
    {
        return $this->hasMany(LogGaPageView::class, 'target_id')->where('type', 'store_venue_room');
    }

    // scope status 狀態
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope storeHasPaid 已付費的商家廳房
    public function scopeStoreHasPaid($query)
    {
        // 狀態 show:顯示
        $query = $query->status('show');

        // 商家類型 store_type (改用 join 提升效率)
        // $query = $query->whereHas('store', function ($q) {
        //     $q = $q->published($this->storeType);
        // });
        $query = $query->join((new Store)->getTable(), function($join) {
                            $join->on('stores.id', '=', 'venue_rooms.store_id')
                                    ->where('stores.type', $this->storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new Wdv2\StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope searchKeyword 關鍵字搜尋
    public function scopeSearchKeyword($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('venue_rooms.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchKeywordWithStore 關鍵字搜尋含商家
    public function scopeSearchKeywordWithStore($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('venue_rooms.name', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope type 廳房類型
    public function scopeType($query, $type)
    {
        return $query->where('has_'.$type, 1);
    }

    // scope numberRange 容納人數區間
    public function scopeNumberRange($query, $key)
    {
        $range = $this->numberList[$key]['range'];

        // 最少人數以下
        if (!$range[0]) {
            $query = $query->where('min_number', '<=', $range[1]);

        // 最多人數以上
        } elseif (!$range[1]) {
            $query = $query->where('max_number', '>=', $range[0]);

        // 中間的人數區間
        } else {
            $query = $query->where(function ($q1) use ($range) {
                $q1 = $q1->orWhereBetween('min_number', [$range[0], $range[1]])
                            ->orWhereBetween('max_number', [$range[0], $range[1]])
                            ->orWhere(function ($q2) use ($range) {
                                $q2 = $q2->where('min_number', '<=', $range[0])
                                        ->where('max_number', '>=', $range[1]);
                            });
            });
        }

        return $query;
    }

    // scope device 廳房設備
    public function scopeDevice($query, $column)
    {
        $value = $this->deviceList[$column]['value'];
        if (is_array($value)) {
            $query = $query->whereIn($column, $value);
        } else {
            $query = $query->where('has_'.$column, $value);
        }

        return $query;
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy($this->getTable().'.sequence')
                    ->orderBy($this->getTable().'.created_at', 'DESC');
    }

    // scope reverseSort 反向排序
    public function scopeReverseSort($query)
    {
        return $query->orderBy($this->getTable().'.sequence', 'DESC')
                    ->orderBy($this->getTable().'.created_at');
    }
}
