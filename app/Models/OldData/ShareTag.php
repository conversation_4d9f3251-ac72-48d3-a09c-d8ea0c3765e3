<?php

namespace App\Models\OldData;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class ShareTag extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public $timestamps = false;

    protected $table = '_share_tags';

    // scope nameLength
    public function scopeNameLength($query, $length = 60)
    {
        // 限制名稱長度 (預設20個中文字, utf8=3位元)
        return $query->whereRaw('LENGTH(name) < ?', [$length]);
    }
}
