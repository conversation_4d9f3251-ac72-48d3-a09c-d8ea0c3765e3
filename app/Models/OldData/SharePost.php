<?php

namespace App\Models\OldData;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class SharePost extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;
    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\SharePostPresenter';

    protected $dates = ['published_at'];

    protected $table = '_share_posts';

    // 狀態 pending:草稿 published:已發佈 delete:刪除
    public $statusList = [
        'pending'   => '草稿',
        'published' => '已發佈',
        'delete'    => '刪除',
    ];

    // 是否排除seo的選項
    public $excludeSeoList = [
        0 => '未排除',
        1 => '排除',
    ];

    // relation User
    public function allAuthor()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }

    // relation User
    public function author()
    {
        return $this->allAuthor()->live();
    }

    // relation User
    public function allLikes()
    {
        return $this->belongsToMany(\App\Models\User::class, '_user_post_like', 'post_id')
                    ->live();
    }

    // relation User
    public function likesWithTrashed()
    {
        return $this->allLikes()->wherePivot('comment_id', NULL);
    }

    // relation User
    public function likes()
    {
        return $this->likesWithTrashed()->wherePivot('deleted_at', NULL);
    }

    public function recentLikes($days = 90)
    {
        return $this->likes()->wherePivot('created_at', '>=', now()->subDays($days));
    }

    // relation Image
    public function cover()
    {
        return $this->hasOne(\App\Models\Image::class, 'target_id')
                    ->where('type', 'share_cover');
    }

    // relation Image
    public function coverBackups()
    {
        return $this->hasMany(\App\Models\Image::class, 'target_id')
                    ->where('type', 'share_cover_backup');
    }

    // relation Image
    public function images()
    {
        return $this->hasMany(\App\Models\Image::class, 'target_id')
                    ->where('type', 'share_post');
    }

    // relation Comment
    public function allComments()
    {
        return $this->hasMany(ShareComment::class, 'post_id');
    }

    // relation Comment
    public function liveComments()
    {
        return $this->allComments()->live();
    }

    // relation Comment
    public function comments()
    {
        return $this->liveComments()->whereNull('parent_id');
    }

    // relation Comment
    public function parentComments()
    {
        return $this->allComments()->whereNull('parent_id');
    }

    public function recentComments($days = 90)
    {
        return $this->liveComments()->where('_share_comments.created_at', '>=', now()->subDays($days));
    }

    // relation UserCollect
    public function allCollects()
    {
        return $this->hasMany(\App\Models\UserCollect::class, 'target_id')
                    ->where('type', 'share_post');
    }

    public function recentCollects($days = 90)
    {
        return $this->allCollects()->where('created_at', '>=', now()->subDays($days));
    }

    // relation User
    public function userCollects()
    {
        $userCollectTable = (new \App\Models\UserCollect)->getTable();

        return $this->belongsToMany(\App\Models\User::class, $userCollectTable, 'target_id', 'user_id')
                    ->wherePivot('type', 'share_post')
                    ->withPivot('created_at')
                    ->where((new \App\Models\User)->getTable().'.status', '!=', 'delete') // scopeLive()
                    ->where('user_collects.deleted_at', NULL)
                    ->orderBy($userCollectTable.'.created_at', 'DESC');
    }

    // relation User
    public function atUsers()
    {
        return $this->belongsToMany(\App\Models\User::class, '_share_at_users', 'post_id')->live()->wherePivot('comment_id', NULL);
    }

    // relation User
    public function atUsersFilterUnsendEmail()
    {
        return $this->atUsers()->wherePivot('send_email', 0);
    }

    // relation ShareCategory
    public function categories()
    {
        return $this->belongsToMany(ShareCategory::class, '_share_post_category', 'post_id', 'category_id');
    }

    // relation ShareTag
    public function tags()
    {
        return $this->belongsToMany(ShareTag::class, '_share_post_tag', 'post_id', 'tag_id');
    }

    // relation LogPostBackup
    public function logBackups()
    {
        return $this->hasMany(LogPostBackup::class, 'post_id');
    }

    // relation LogPostShare
    public function logShares()
    {
        return $this->hasMany(LogPostShare::class, 'post_id');
    }

    public function recentShares()
    {
        return $this->logShares()->where('created_at', '>=', now()->subDays(90));
    }

    // relation Brand
    public function allBrands()
    {
        return $this->belongsToMany(\App\Models\Brand::class, '_share_post_brand', 'post_id', 'brand_id')
                    ->withPivot('user_created', 'rank', 'category_id', 'is_booked', 'wedding_type_ids', 'yzcube_user_id', 'created_at', 'deleted_at');
    }

    // relation Brand
    public function brands()
    {
        return $this->allBrands()
                    ->wherePivot('deleted_at', NULL);
    }

    // scope hasAuthor 排除帳號停用的作者
    public function scopehasAuthor($query)
    {
        return $query->select($this->getTable().'.*')
                    ->join((new \App\Models\User)->getTable().' AS users', function($join) {
                        $join->on($this->getTable().'.user_id', '=', 'users.id')
                                ->whereIn('users.status', ['published', 'pending']); // ✅ 可用索引
                    });
    }

    // scope live
    public function scopeLive($query)
    {
        // 需排除帳號停用的作者
        return $query->whereIn($this->getTable().'.status', ['pending', 'published']) // ✅ 可用索引
                    ->hasAuthor()
                    ->orderBy($this->getTable().'.id'); // ✅ 幫助 chunk 排序最佳化
    }

    // scope release
    public function scopeRelease($query)
    {
        // 需排除帳號停用的作者
        return $query->status('published');
                    // ->hasAuthor();
    }

    // scope status
    public function scopeStatus($query, $status)
    {
        // 因join欄位衝突，加入getTable()
        return $query->where($this->getTable().'.status', $status);
    }

    // scope sortHotScore
    public function scopeSortHotScore($query)
    {
        return $query->orderBy('hot_score', 'DESC');
    }

    // scope sortPublishedAt
    public function scopeSortPublishedAt($query)
    {
        return $query->orderBy('published_at', 'DESC');
    }

    // scope sortCreatedAt
    public function scopeSortCreatedAt($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }

    /**
     * 統計留言+回應數
     *
     * @return int $this->comment_count
     */
    public function totalCommentCount()
    {
        // 已發佈的留言
        $comments = $this->comments()->status('published')->get();

        // 留言數
        $this->comment_count = $comments->count();

        // 回應數
        foreach ($comments as $comment) {
            $this->comment_count += $comment->replies()->status('published')->count();
        }

        return $this->comment_count;
    }
}
