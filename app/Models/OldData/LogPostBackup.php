<?php

namespace App\Models\OldData;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LogPostBackup extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $guarded = [];

    protected $table = '_log_post_backups';

    use SoftDeletes;

    // relation YzcubeUser
    public function yzcubeUser()
    {
        return $this->belongsTo(\App\Models\YzcubeUser::class);
    }
}
