<?php

namespace App\Models\OldData;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShareCategory extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;

    public $timestamps = false;

    protected $guarded = [];

    protected $table = '_share_categories';

    // relation SharePost
    public function allPosts()
    {
        return $this->belongsToMany(SharePost::class, '_share_post_category', 'category_id', 'post_id');
    }

    // relation SharePost
    public function posts()
    {
        return $this->allPosts()->release();
    }

    // scope promotion
    public function scopePromotion($query, $isPromotion = 1)
    {
        return $query->where('is_promotion', $isPromotion);
    }

    // scope sort
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }
}
