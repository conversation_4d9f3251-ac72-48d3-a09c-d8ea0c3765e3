<?php

namespace App\Models\OldData;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class ShareComment extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;
    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\ShareCommentPresenter';

    protected $table = '_share_comments';

    // 狀態 published:已發佈 delete:刪除
    public $statusList = [
        'published' => '已發佈',
        'delete'    => '刪除',
    ];

    // relation SharePost
    public function post()
    {
        return $this->belongsTo(SharePost::class, 'post_id')->release();
    }

    // relation User
    public function allAuthor()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }

    // relation User
    public function author()
    {
        return $this->allAuthor()->live();
    }

    // relation User
    public function likesWithTrashed()
    {
        return $this->belongsToMany(\App\Models\User::class, '_user_post_like', 'comment_id')->live();
    }

    // relation User
    public function likes()
    {
        return $this->likesWithTrashed()->wherePivot('deleted_at', NULL);
    }

    // relation Self
    public function parent()
    {
        return $this->belongsTo(ShareComment::class, 'parent_id')->live();
    }

    // relation Self
    public function allReplies()
    {
        return $this->hasMany(ShareComment::class, 'parent_id');
    }

    // relation Self
    public function replies()
    {
        return $this->allReplies()->live();
    }

    // relation SharePost / Self
    public function previous()
    {
        return $this->parent_id ? $this->parent() : $this->post();
    }

    // relation User
    public function atUsers()
    {
        return $this->belongsToMany(\App\Models\User::class, '_share_at_users', 'comment_id')->live();
    }

    // relation User
    public function atUsersFilterUnsendEmail()
    {
        return $this->atUsers()->wherePivot('send_email', 0);
    }

    // relation Image
    public function images()
    {
        return $this->hasMany(\App\Models\Image::class, 'target_id')->where('type', 'share_comment');
    }

    // scope hasAuthor 排除帳號停用的作者
    public function scopehasAuthor($query)
    {
        return $query->select($this->getTable().'.*')
                    ->join((new \App\Models\User)->getTable().' AS users', function($join) {
                        $join->on($this->getTable().'.user_id', '=', 'users.id')
                                ->where('users.status', '!=', 'delete');
                    });
    }

    // scope live
    public function scopeLive($query)
    {
        // 需排除帳號停用的作者
        return $query->status('published')
                    ->hasAuthor();
    }

    // scope status
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope sortCreatedAt
    public function scopeSortCreatedAt($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }
}
