<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BlogUser extends Model
{
    protected $guarded = [];

    // 狀態 published:有效 delete:停用
    public $statusList = [
        'published' => '有效',
        'delete'    => '停用',
    ];

    // relation image() 部落格作者頭像
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'blog_user');
    }

    // relation AuthToken 身份驗證紀錄
    public function tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('blog');
    }

    // // relation AuthToken 身份驗證紀錄(修改密碼)
    // public function pwd_tokens()
    // {
    //     return $this->hasMany(AuthToken::class, 'target_id')->type('yzcube_password');
    // }

    // // scope live 有效的
    // public function scopeLive($query)
    // {
    //     return $query->where('status', '!=', 'delete');
    // }
}
