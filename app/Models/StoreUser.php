<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Mail;
use Laracasts\Presenter\PresentableTrait;

class StoreUser extends Model
{
    use PresentableTrait;

    use SoftDeletes;

    protected $guarded = [];

    // 狀態 valid:有效 invalid:已停用 deleted:已封存
    public $statusList = [
        'valid'   => '有效',
        'invalid' => '已停用',
        'deleted' => '已封存',
    ];

    protected $presenter = 'App\Models\Presenters\StoreUserPresenter';

    // relation AuthToken
    public function tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('store');
    }

    // relation LogAuthToken 身份驗證紀錄
    public function logTokens()
    {
        return $this->hasManyThrough(LogAuthToken::class, AuthToken::class, 'target_id')
                    ->withTrashedParents()
                    ->where((new AuthToken)->getTable().'.type', 'store')
                    ->orderBy((new LogAuthToken)->getTable().'.created_at', 'DESC');
    }

    // relation Store 擁有的所有商家
    public function liveStores()
    {
        return $this->belongsToMany(Store::class, (new StoreUserRelationship)->getTable())
                    ->wherePivot('deleted_at', null)
                    ->where((new Store)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation Store 擁有已上架的所有商家
    public function stores()
    {
        return $this->liveStores()
                    ->where((new Store)->getTable().'.status', 'published'); // scopePublished()
    }

    // relation Store 擁有已上架且可Line通知公開詢價的所有商家
    public function storesWithLineQuoteNotify()
    {
        return $this->stores()
                    ->wherePivot('line_quote', 1);
    }

    // relation Store 擁有已上架且可Line通知即時通訊的所有商家
    public function storesWithLineMessageNotify()
    {
        return $this->stores()
                    ->wherePivot('line_message', 1);
    }

    // relation LineBotUser
    public function lineBotUser()
    {
        return $this->hasOne(\App\Models\Wdv2\LineBotUser::class, 'line_id', 'line_id')->release();
    }

    // relation WebNotification 瀏覽器推播通知
    public function webNotification()
    {
        return $this->hasOne(WebNotification::class, 'target_id')->where('target_type', 'store_user');
    }

    // relation WebNotification 有效的瀏覽器推播通知
    public function liveWebNotification()
    {
        return $this->webNotification()->live();
    }

    // relation AuthToken
    public function pwd_tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('store_password');
    }

    // relation MailBase
    public function mailBase()
    {
        return $this->hasOne(MailBase::class, 'email', 'email');
    }

    // relation MailEvent
    public function mailEvent($campaign_id)
    {
        return $this->hasMany(MailEvent::class, 'email', 'email')
                    ->where('target_type', 'store_user')
                    ->where('campaign_id', $campaign_id)
                    ->whereNotNull('ses_message_id')
                    ->orderBy('id', 'desc');
    }

    // relation LogNewsleopardResponses
    public function logNewsleopardResponse()
    {
        return $this->hasMany(LogNewsleopardResponse::class, 'target_id', 'id')
                    ->where('target_type', 'App\Models\StoreUser')
                    ->orderBy('id', 'desc');
    }

    // relation PhoneLegalize
    public function phoneLegalize()
    {
        return $this->hasMany(PhoneLegalize::class, 'phone', 'phone')
                    ->where('target_type', 'store_user');
    }

    // relation EmailLegalize
    public function emailLegalize()
    {
        return $this->hasMany(EmailLegalize::class, 'email', 'email');
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('status', '!=', 'delete');
    }

    // 新增一筆關聯在store_user_relationship
    public function addStore($store_id)
    {
        return $this->liveStores()->attach($store_id);
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatusLabel()
    {
        // 已封存
        if ($this->trashed()) {
            return $this->statusList['deleted'];
        }

        // 已停用
        if ($this->status == 'delete') {
            return $this->statusList['invalid'];
        }

        // 有效
        return $this->statusList['valid'];
    }

    /**
     * 取得手機驗證碼 目前狀態
     */
    public function getPhoneLegalizeLabel()
    {
        $phoneLegalize = $this->phoneLegalize()->orderBy('id', 'desc')->first();
        if ($phoneLegalize) {
            $expiryTime = $phoneLegalize->source == 'normal' ? 300 : 86400;
            $status = ($phoneLegalize->created_at > date('Y-m-d H:i:s', time() - $expiryTime)) ? 'valid' : 'invalid';
            return [
                'status' => $status,
                'phoneLegalize'  => $phoneLegalize,
            ];
        }

        return null;
    }

    /**
     * 取得信箱驗證碼 目前狀態
     */
    public function getEmailLegalizeLabel()
    {
        $emailLegalize = $this->emailLegalize()->orderBy('id', 'desc')->first();
        if ($emailLegalize) {
            $status = ($emailLegalize->deadline_at > now()) ? 'valid' : 'invalid';
            return [
                'status' => $status,
                'emailLegalize'  => $emailLegalize,
            ];
        }

        return null;
    }

    /**
     * 信箱是否為黑名單
     */
    public function isBlacklist()
    {
        return $this->mailBase()->where('valid', 0)->exists() ? 1 : 0;
    }
}