<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CreditCard extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // 卡片類別
    public $fundingList = [
        -1 => 'Unknown',
        0  => '信用卡 (Credit Card)',
        1  => '簽帳卡 (Debit Card)',
        2  => '預付卡 (Prepaid Card)',
    ];

    // 卡片種類
    public $typeList = [
        -1 => 'Unknown',
        1  => 'VISA',
        2  => 'MasterCard',
        3  => 'JCB',
        4  => 'Union Pay',
        5  => 'AMEX',
    ];
}
