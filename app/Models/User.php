<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;
// use App\Models\UserArticleLike;

class User extends Model
{
    protected $guarded = [];

    use PresentableTrait;

    use SoftDeletes;

    // 狀態 valid:有效 invalid:已停用 deleted:已封存
    public $statusList = [
        'valid'   => '有效',
        'invalid' => '已停用',
        'deleted' => '已封存',
    ];

    // 身份選單 bride:新娘 fake:假帳號
    public $identityList = [
        'bride' => '新娘',
        'fake'  => '假帳號',
    ];

    //使用者已使用功能選單..
    //這會影响到功能篩選時的Class name..沒事別改名
    public $usedServiceList = [
        'WeddingDate' => '婚期',
        'GoldTeam'    => '黃金團隊',
        'Quote'       => '公開詢價',
        'Collect'     => '收藏櫃',
        'Share'       => 'Ｗ姐妹',
        'Forum'       => '好婚聊聊',
        'Feedback'    => '意見回饋',
        'Event'       => '活動報名'
    ];

    protected $presenter = 'App\Models\Presenters\UserPresenter';

    // relation AuthToken
    public function tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('user');
    }

    // relation LogAuthToken 身份驗證紀錄
    public function logTokens()
    {
        return $this->hasManyThrough(LogAuthToken::class, AuthToken::class, 'target_id')
                    ->withTrashedParents()
                    ->where((new AuthToken)->getTable().'.type', 'user')
                    ->orderBy((new LogAuthToken)->getTable().'.created_at', 'DESC');
    }

    // relation AuthToken
    public function pwd_tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('user_password');
    }

    // relation UserThirdInfo 第三方登入資訊
    public function thirdInfos()
    {
        return $this->hasMany(LogUserThirdInfo::class);
    }

    // relation ForumArticle
    public function allArticles()
    {
        return $this->hasMany(ForumArticle::class);
    }

    // relation ForumArticle
    public function articles()
    {
        return $this->allArticles()->sortPublishedAt();
    }

    // relation ForumArticle
    public function releaseArticles()
    {
        return $this->allArticles()->release();
    }

    // relation ForumArticle 包含已刪除
    public function allArticlesWithTrashed()
    {
        return $this->allArticles()->withTrashed();
    }

    // relation ForumArticle
    public function articlesAnonymous($flag)
    {
        return $this->articles()->anonymous($flag);
    }

    // relation ForumComment
    public function allComments()
    {
        return $this->hasMany(ForumComment::class);
    }

    // relation ForumComment
    public function comments()
    {
        return $this->allComments()->has('article');
    }

    // relation ForumComment
    public function commentsAnonymous($flag)
    {
        return $this->comments()->anonymous($flag);
    }

    // relation UserCollect 收藏紀錄
    public function collects()
    {
        return $this->hasMany(UserCollect::class);
    }

    // relation ForumArticle
    public function tracks()
    {
        return $this->belongsToMany(ForumArticle::class, 'user_article_track', 'user_id', 'article_id')->live();
    }

    // relation ForumArticle
    public function articleLikes()
    {
        return $this->belongsToMany(ForumArticle::class, 'user_article_like', 'user_id', 'article_id')->live()->wherePivot('deleted_at', NULL)->wherePivot('comment_id', NULL);
    }

    // relation ForumComment
    public function commentLikes()
    {
        return $this->belongsToMany(ForumComment::class, 'user_article_like', 'user_id', 'comment_id')->live()->wherePivot('deleted_at', NULL);
    }

    // relation UserArticleLike
    public function likes()
    {
        return $this->hasMany(UserArticleLike::class);
    }

    // relation FakeUser
    public function fake()
    {
        return $this->hasOne(FakeUser::class);
    }

    // relation UserSubscription
    public function subscription()
    {
        return $this->hasOne(UserSubscription::class);
    }

    // relation Feedback
    public function feedback()
    {
        return $this->hasMany(Feedback::class);
    }

    // relation Store 已收藏的商家
    public function storeCollects($storeType)
    {
        $storeTable       = (new Store)->getTable();
        $userCollectTable = (new UserCollect)->getTable();

        return $this->belongsToMany(Store::class, $userCollectTable, 'user_id', 'target_id')
                    ->wherePivot('type', 'store')
                    ->wherePivotNull('deleted_at')
                    ->withPivot('created_at')
                    ->where($storeTable.'.status', 'published')
                    ->where($storeTable.'.type', $storeType)
                    ->orderBy($userCollectTable.'.created_at', 'DESC');
    }

    // relation StoreService 已收藏的服務方案
    public function storeServiceCollects($storeType)
    {
        $userCollectTable = (new UserCollect)->getTable();

        return $this->belongsToMany(StoreService::class, $userCollectTable, 'user_id', 'target_id')
                    ->wherePivot('type', 'service')
                    ->wherePivotNull('deleted_at')
                    ->withPivot('created_at')
                    ->whereHas('store', function ($query) use ($storeType) {
                        $query->where('type', $storeType);
                    })
                    ->where((new StoreService)->getTable().'.status', 'show')
                    ->orderBy($userCollectTable.'.created_at', 'DESC');
    }

    // relation UserCollect 已收藏的作品 (不含婚宴場地)
    public function storeWorkCollects($storeType)
    {
        $userCollectTable = (new UserCollect)->getTable();

        // 婚紗禮服/喜餅-作品集
        if (in_array($storeType, [2, 10])) {
            return $this->belongsToMany(StoreAlbum::class, $userCollectTable, 'user_id', 'target_id')
                        ->wherePivot('type', 'album')
                        ->wherePivotNull('deleted_at')
                        ->withPivot('created_at')
                        ->whereHas('store', function ($q1) use ($storeType) {
                            $q1->where('type', $storeType);
                        })
                        ->where((new StoreAlbum)->getTable().'.status', 'show')
                        ->orderBy($userCollectTable.'.created_at', 'DESC');


            // 其他商家類型-單一作品
        } else {
            return $this->belongsToMany(StoreAlbumImage::class, $userCollectTable, 'user_id', 'target_id')
                        ->wherePivot('type', 'album_image')
                        ->wherePivotNull('deleted_at')
                        ->withPivot('created_at')
                        ->whereHas('album', function ($q1) use ($storeType) {
                            $q1->whereHas('store', function ($q2) use ($storeType) {
                                $q2->where('type', $storeType);
                            });
                        })
                        ->orderBy($userCollectTable.'.created_at', 'DESC');
        }
    }

    // relation VenueRoom 已收藏的婚宴場地廳房
    public function storeVenueRoomCollects($storeType)
    {
        $userCollectTable = (new UserCollect)->getTable();

        return $this->belongsToMany(VenueRoom::class, $userCollectTable, 'user_id', 'target_id')
                    ->wherePivot('type', 'venue_room')
                    ->wherePivotNull('deleted_at')
                    ->withPivot('created_at')
                    ->whereHas('store', function ($query) use ($storeType) {
                        $query->where('type', $storeType);
                    })
                    ->where((new VenueRoom)->getTable().'.status', 'show')
                    ->orderBy($userCollectTable.'.created_at', 'DESC');
    }

    // relation UserWeddingType
    public function weddingTypes()
    {
        return $this->hasMany(UserWeddingType::class)->orderBy('type');
    }

    // relation UserWeddingSchedule
    public function weddingSchedules()
    {
        return $this->hasMany(UserWeddingSchedule::class);
    }

    // relation UserQuote
    public function quotes()
    {
        return $this->hasMany(\App\Models\Wdv2\UserQuote::class)->sort();
    }

    // relation UserWedding
    public function wedding()
    {
        return $this->hasOne(UserWedding::class);
    }

    // relation EventReport
    public function eventReports()
    {
        return $this->hasMany(EventReport::class);
    }

    // relation Reserve 所有的詢問單
    public function allReserves()
    {
        return $this->hasMany(\App\Models\Wdv2\Reserve::class);
    }

    // relation Reserve 有效的詢問單
    public function reserves()
    {
        return $this->allReserves()->live();
    }

    // relation WebNotification 瀏覽器推播通知
    public function webNotification()
    {
        return $this->hasOne(WebNotification::class, 'target_id')->where('target_type', 'user');
    }

    // relation WebNotification 有效的瀏覽器推播通知
    public function liveWebNotification()
    {
        return $this->webNotification()->live();
    }

    // relation FirestoreMessage Firestore的訊息記錄
    public function firestoreMessages()
    {
        return $this->hasMany(FirestoreMessage::class);
    }

    // relation RingUser
    public function ringUser()
    {
        return $this->hasOne(RingUser::class);
    }

    // relation CouponMultiple 試吃優惠代碼
    public function tastingCoupon()
    {
        return $this->hasOne(CouponMultiple::class)
                    ->whereHas('coupon', function ($query) {
                        $query->where('issuance_type', 'user_quote_tasting');
                    });
    }

    // relation MailBase
    public function mailBase()
    {
        return $this->hasOne(MailBase::class, 'email', 'email');
    }

    // relation PhoneLegalize
    public function phoneLegalize()
    {
        return $this->hasMany(PhoneLegalize::class, 'phone', 'phone')
                    ->where('target_type', 'user');
    }

    // relation EmailLegalize
    public function emailLegalize()
    {
        return $this->hasMany(EmailLegalize::class, 'email', 'email');
    }

    // relation MailEvent
    public function mailEvent($campaign_id)
    {
        return $this->hasMany(MailEvent::class, 'email', 'email')
                    ->where('target_type', 'user')
                    ->where('campaign_id', $campaign_id)
                    ->whereNotNull('ses_message_id')
                    ->orderBy('id', 'desc');
    }

    // relation LogNewsleopardResponses
    public function logNewsleopardResponse()
    {
        return $this->hasMany(LogNewsleopardResponse::class, 'target_id', 'id')
                    ->where('target_type', 'App\Models\User')
                    ->orderBy('id', 'desc');
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('status', '!=', 'delete');
    }

    // scope sortCreatedAt 排序建立時間遞減
    public function scopeSortCreatedAt($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }

    // scope recentlyActive 近期活躍新娘 (最近有登入)
    public function scopeRecentlyActive($query, $afterAt)
    {
        return $query->live()
                        ->where('last_login_at', '>=', $afterAt)
                        ->sortCreatedAt();
    }

    // scope interactiveByAfterAt 指定期限內有互動的新娘 (新註冊會員、收藏、發文、留言&回覆、按讚、追蹤的真實新娘)
    public function scopeInteractiveByAfterAt($query, $afterAt)
    {
        return $query->live()
                        ->where('is_admin', 0)
                        ->where('is_fake', 0)
                        ->where(function ($q1) use ($afterAt) {
                            // 新註冊會員
                            $q1->orWhere('created_at', '>=', $afterAt)
                                // 收藏
                                ->orWhereHas('collects', function ($q2) use ($afterAt) {
                                    $q2->where('created_at', '>=', $afterAt);
                                })
                                // 發佈文章
                                ->orWhereHas('articles', function ($q2) use ($afterAt) {
                                    $q2->status('published')
                                        ->where('published_at', '>=', $afterAt);
                                })
                                // 留言&回覆
                                ->orWhereHas('comments', function ($q2) use ($afterAt) {
                                    $q2->live()
                                        ->where('forum_comments.created_at', '>=', $afterAt);
                                })
                                // 追蹤
                                ->orWhereHas('tracks', function ($q2) use ($afterAt) {
                                    $q2->where('user_article_track.created_at', '>=', $afterAt);
                                })
                                // 按讚
                                ->orWhereHas('likes', function ($q2) use ($afterAt) {
                                    $q2->where('created_at', '>=', $afterAt);
                                });
                        })
                        ->sortCreatedAt();
    }

    // scope doesntPostsHasInteractive 一年內未發佈分享文，但有互動的新娘
    public function scopeDoesntPostsHasInteractive($query)
    {
        $afterAt = now()->subMonths(12);

        return $query->doesntHave('releaseArticles')
                        ->interactiveByAfterAt($afterAt);
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatusLabel()
    {
        // 已封存
        if ($this->trashed()) {
            return $this->statusList['deleted'];
        }

        // 已停用
        if ($this->status == 'delete') {
            return $this->statusList['invalid'];
        }

        // 有效
        return $this->statusList['valid'];
    }

    /**
     * 第三方ID拿用户第三方資料
     *
     * @return User|null
     */
    public function findByThirdPartyId($thirdId, $type)
    {
        return $this->thirdInfos()->where(['type' => $type, 'third_id' => $thirdId])->orderBy('id', 'DESC')->first();
    }

     /**
     * 紀錄第三方登入資訊
     */
    public function recordThirdInfo($id, $name, $avatar, $type)
    {
        $this->thirdInfos()->updateOrCreate(
            ['user_id' => $this->id, 'type' => $type],
            [
                'name'     => $name,
                'third_id' => $id,
                'avatar'   => $avatar,
            ]
        );
    }

    /**
     * 取得草稿文章
     *
     * @return model $article
     */
    public function getDraftArticle()
    {
        return $this->articles()->status('pending')->first();
    }

    /**
     * 統計論壇得到的讚總數
     *
     * @return int $user->forum_get_like_count
     */
    public function totalGetLikeCount()
    {
        $this->forum_get_like_count = 0;

        $articleIds = $this->articles()->pluck('id');
        $commentIds = $this->comments()->pluck('id');

        // 擁有的所有文章
        $articleLikeCount = UserArticleLike::articleLikeValid()->whereIn('article_id', $articleIds)
                            ->whereHas('validUser')
                            ->whereNull('comment_id')
                            ->count();
        $this->forum_get_like_count += $articleLikeCount;


        // 擁有的所有留言
        $commentLikeCount = UserArticleLike::valid()->whereIn('comment_id', $commentIds)
                            ->whereHas('validUser')
                            ->count();
        $this->forum_get_like_count += $commentLikeCount;

        return $this->forum_get_like_count;
    }

    /**
     * 統計論壇的文章和留言總數
     *
     * @return int $user->forum_comment_count
     */
    public function totalArticleCommentCount()
    {
        $this->forum_comment_count = $this->articles()->status('published')->count();
        $this->forum_comment_count += $this->comments()->status('published')->autoReply(0)->count();

        return $this->forum_comment_count;
    }

    /**
     * 取得管理者&假帳號的UserToken
     *
     * @return model|string $token
     */
    public function getYzcubeUserToken()
    {
        // 一般使用者不顯示
        if (!$this->is_admin && !$this->is_fake) {
            return '';
        }

        return $this->getUserToken();
    }

    /**
     * 取得使用者的UserToken
     *
     * @return model|string $token
     */
    public function getUserToken()
    {
        $authToken = $this->tokens()->whereNull('deadline_at')->first();

        return $authToken ? $authToken->token : '';
    }

    /**
     * 排序主要婚期 (結婚/訂結同天/訂婚/歸寧)
     *
     * @return model $weddingTypes
     */
    public function getSortMainWeddingTypeList()
    {
        return $this->weddingTypes
            ->sortBy(function ($item, $key) {
                return array_search($item->type, [2, 3, 1, 4]);
            });
    }

    /**
     * 取得主要婚期日 (婚期不為空)
     *
     * @return model $weddingType
     */
    public function getFirstMainDateWeddingType()
    {
        return $this->getSortMainWeddingTypeList()
            ->filter(function ($item, $key) {
                return $item->date;
            })
            ->first();
    }

    /**
     * 取得當前的主要婚期 (婚期須大於今日)
     *
     * @return model $weddingType
     */
    public function getFirstCurrentMainWeddingType()
    {
        return $this->getSortMainWeddingTypeList()
            ->filter(function ($item, $key) {
                return $item->date > now();
            })
            ->first();
    }

    /**
     * 統計我的詢價紀錄中，商家報價未讀訊息數
     *
     * @return int $quotes_unread_count
     */
    public function getQuotesUnreadCount()
    {
        return $this->quotes->sum(function ($quote) {
            return $quote->getUnreadCount();
        });
    }

    /**
     * 取得手機驗證碼 目前狀態
     */
    public function getPhoneLegalizeLabel()
    {
        $phoneLegalize = $this->phoneLegalize()->orderBy('id', 'desc')->first();
        if ($phoneLegalize) {
            $expiryTime = $phoneLegalize->source == 'normal' ? 300 : 86400;
            $status = ($phoneLegalize->created_at > date('Y-m-d H:i:s', time() - $expiryTime)) ? 'valid' : 'invalid';
            return [
                'status' => $status,
                'phoneLegalize'  => $phoneLegalize,
            ];
        }

        return null;
    }

    /**
     * 取得信箱驗證碼 目前狀態
     */
    public function getEmailLegalizeLabel()
    {
        $emailLegalize = $this->emailLegalize()->orderBy('id', 'desc')->first();
        if ($emailLegalize) {
            $status = ($emailLegalize->deadline_at > now()) ? 'valid' : 'invalid';
            return [
                'status' => $status,
                'emailLegalize'  => $emailLegalize,
            ];
        }

        return null;
    }

    /**
     * 信箱是否為黑名單
     */
    public function isBlacklist()
    {
        return $this->mailBase()->where('valid', 0)->exists() ? 1 : 0;
    }
}
