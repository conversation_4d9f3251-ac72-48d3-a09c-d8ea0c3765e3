<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventProductItem extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // relation EventOrderItem 活動訂單品項
    public function orderItems()
    {
        return $this->hasMany(EventOrderItem::class, 'item_id');
    }

    // relation EventOrderItem 已售出的活動訂單品項
    public function soldItems()
    {
        return $this->orderItems()
                    ->where(function ($q1) {
                        $q1->whereHas('order', function ($q2) {
                                $q2->where('amount', 0);
                            })
                            ->orWhereHas('logPayment', function ($q2) {
                                $q2->success();
                            });
                    });
    }
}
