<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class WeddingDate extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // relation UserWeddingType
    public function userWeddingTypes()
    {
        return $this->hasMany(UserWeddingType::class, 'date', 'date');
    }

    /**
     * 更新使用者設定的婚期數量
     *
     */
    public function updateUserWeddingCount()
    {
        $this->user_wedding_count = $this->userWeddingTypes->count();
        $this->save();
    }

    /**
     * 總計婚期數量
     *
     */
    public function totalWeddingDateCount()
    {
        return $this->ga_search_count + $this->user_wedding_count + $this->message_count + $this->reserve_count + $this->user_quote_count;
    }
}
