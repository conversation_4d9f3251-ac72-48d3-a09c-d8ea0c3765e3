<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LogMetaScraper extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $table = 'log_meta_scraper';

    protected $fillable = [
        'type',
        'target_id',
        'scraper_type',
        'url',
        'title',
        'description',
        'image',
        'is_self',
        'favicon',
    ];
}
