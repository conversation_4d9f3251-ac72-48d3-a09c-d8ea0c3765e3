<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class EventOrder extends Model
{
    protected $guarded = [];

    protected $presenter = 'App\Models\Presenters\EventOrderPresenter';

    use SoftDeletes;
    use PresentableTrait;

    protected $casts = [
        'invoice_data' => 'array',
    ];

    // 訂單明細架構
    public $orderList = [
        'items'    => [], // 訂購商品項目
        'images'   => [], // 上傳照片
        'quantity' => 0, // 訂單總商品數量
        'subtotal' => 0, // 商品小計
        'discount' => [
            'order'  => 0, // 訂單折扣金額
            'coupon' => 0, // 優惠代碼折扣金額
        ],
        'extra_fees' => [], // 活動訂單的額外費用
        'total'      => 0, // 訂單總金額
    ];

    // 付款狀態 pending:尚未付款 success:已付款 unnecessary:無須付款 fail:付款失敗
    public $paymentStatusList = [
        'pending'     => '尚未付款',
        'success'     => '已付款',
        'unnecessary' => '無須付款',
        'fail'        => '付款失敗',
    ];

    // 發票狀態 pending:尚未開立 success:已開立 unnecessary:無須開立 invalid:已作廢
    public $invoiceStatusList = [
        'pending'     => '尚未開立',
        'success'     => '已開立',
        'unnecessary' => '無須開立',
        'invalid'     => '已作廢',
    ];

    // relation event 活動
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // relation allEvent 所有活動
    public function allEvent()
    {
        return $this->event()->withTrashed();
    }

    // relation report 活動報名紀錄
    public function report()
    {
        return $this->belongsTo(EventReport::class, 'event_report_id');
    }

    // relation allReport 所有活動報名紀錄
    public function allReport()
    {
        return $this->report()->withTrashed();
    }

    // relation EventOrderItem 活動訂單品項
    public function orderItems()
    {
        return $this->hasMany(EventOrderItem::class, 'order_no', 'order_no');
    }

    // relation EventOrderDetail 活動訂單明細
    public function details()
    {
        return $this->hasMany(EventOrderDetail::class);
    }

    // relation logPayment 付款紀錄
    public function logPayment()
    {
        return $this->hasOne(LogPayment::class, 'order_number', 'order_no');
    }

    // relation invoiceSetting 發票設定
    public function invoiceSetting()
    {
        return $this->belongsTo(InvoiceSetting::class);
    }

    // relation invoice 發票紀錄
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'order_no', 'order_no');
    }

    /**
     * 更新付款狀態
     *
     * @return string $payment_status
     */
    public function updatePaymentStatus()
    {
        // 免費訂單的付款狀態為無須付款
        if (!$this->amount) {
            $this->payment_status = 'unnecessary';
            $this->save();
            return;
        }

        // 沒有付款紀錄，預設為尚未付款
        if (!$this->logPayment) {
            return;
        }

        // logPayment->status TapPay交易代碼，成功的話為0
        $this->payment_status = ($this->logPayment->status == 0) ? 'success' : 'fail';
        $this->save();
    }
}
