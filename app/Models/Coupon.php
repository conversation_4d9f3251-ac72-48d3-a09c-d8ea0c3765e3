<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Coupon extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // 優惠卷類型 single:單組通用代碼 multiple:多組獨立代碼
    public $typeList = [
        'single'   => '單組通用代碼',
        'multiple' =>'多組獨立代碼',
    ];

    // 優惠卷狀態 pending:尚未開始 published:進行中 completed:已結束
    public $statusList = [
        'pending' => '尚未開始',
        'published' => '進行中',
        'completed' => '已結束',
    ];

    // 折價條件 price:滿額 quantity:滿件
    public $conditionTypeList = [
        'price'    => '滿額',
        'quantity' => '滿件',
    ];

    // 折價方式 cash:折扣金額 ratio:折扣%數
    public $discountTypeList = [
        'cash'  => '折扣金額',
        'ratio' => '折扣%數',
    ];

    // relation CouponSingle 單組通用代碼紀錄
    public function single()
    {
        return $this->hasMany(CouponSingle::class);
    }

    // relation CouponMultiple 多組獨立代碼紀錄
    public function multiple()
    {
        return $this->hasMany(CouponMultiple::class);
    }

    // relation Event 活動表單
    public function events()
    {
        return $this->belongsToMany(Event::class, 'coupon_events');
    }

    // scope published 發佈中
    public function scopePublished($query)
    {
        return $query->where(function ($q) {
                            $q->whereNull('start_date')
                                ->orWhere('start_date', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('end_date')
                                ->orWhere('end_date', '>', now());
                        });
    }

    // scope pending 尚未開始
    public function scopePending($query)
    {
        return $query->where('start_date', '>', now());

    }

    // scope completed 已結束
    public function scopeCompleted($query)
    {
        return $query->where('end_date', '<=', now());
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatus()
    {
        // 尚未開始
        if ($this->start_date && $this->start_date > now()) {
            return 'pending';
        }

        // 已結束
        if ($this->end_date && $this->end_date <= now()) {
            return 'completed';
        }

        return 'published';
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatusLabel()
    {
        return $this->statusList[$this->getStatus()];
    }
}
