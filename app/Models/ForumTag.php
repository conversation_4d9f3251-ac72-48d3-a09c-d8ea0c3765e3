<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class ForumTag extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public $timestamps = false;

    // relation ForumArticle
    public function articles()
    {
        return $this->belongsToMany(ForumArticle::class, 'forum_article_tag', 'tag_id', 'article_id')
                    ->live();
    }
}
