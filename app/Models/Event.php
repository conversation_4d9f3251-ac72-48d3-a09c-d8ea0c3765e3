<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Event extends Model
{
    use SoftDeletes;
    use ToJsonDateTrait;

    // 表單類型 normal:一般表單 order:團購表單
    public $typeList = [
        'normal' => '一般表單',
        'order'  =>'團購表單',
    ];

    // 表單狀態 pending:尚未開始 published:進行中 completed:已結束
    public $statusList = [
        'pending' => '尚未開始',
        'published' => '進行中',
        'completed' => '已結束',
    ];

    // relation EventReport
    public function reports()
    {
        return $this->hasMany(EventReport::class);
    }

    // relation Image
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'event_image');
    }

    // relation Image
    public function image_xs()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'event_image_xs');
    }

    // relation Image
    public function image_meta()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'event_image_meta');
    }

    // relation EventProduct 活動商品
    public function products()
    {
        return $this->hasMany(EventProduct::class);
    }

    // relation EventExtraFee 額外費用
    public function extraFees()
    {
        return $this->hasMany(EventExtraFee::class);
    }

    // relation EventOrder 活動訂單
    public function orders()
    {
        return $this->hasMany(EventOrder::class);
    }

    // relation EventOrderItem 已售出的活動訂單品項
    public function soldOrders()
    {
        return $this->orders()
                    ->where(function ($q1) {
                        $q1->where('amount', 0)
                            ->orWhereHas('logPayment', function ($q2) {
                                $q2->success();
                            });
                    });
    }

    // relation Coupon 優惠卷
    public function coupons()
    {
        return $this->belongsToMany(Coupon::class, 'coupon_events');
    }

    // relation tool 婚展報到系統
    public function eventTool()
    {
        return $this->belongsTo(EventTool::class);
    }

    // relation EventExhibition 婚展多選項目
    public function exhibitions()
    {
        return $this->hasMany(EventExhibition::class)->sort();
    }

    // relation EventCalendar Google行事曆
    public function calendar()
    {
        return $this->hasOne(EventCalendar::class, 'target_id')->where('target_type', 'event');
    }

    // relation invoiceSetting 發票設定
    public function invoiceSetting()
    {
        return $this->belongsTo(InvoiceSetting::class);
    }

    // scope published 發佈中
    public function scopePublished($query)
    {
        return $query->where(function ($q) {
                            $q->whereNull('start_date')
                                ->orWhere('start_date', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('end_date')
                                ->orWhere('end_date', '>', now());
                        });
    }

    // scope pending 尚未開始
    public function scopePending($query)
    {
        return $query->where('start_date', '>', now());

    }

    // scope completed 已結束
    public function scopeCompleted($query)
    {
        return $query->where('end_date', '<=', now());
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatus()
    {
        // 尚未開始
        if ($this->start_date && $this->start_date > now()) {
            return 'pending';
        }

        // 已結束
        if ($this->end_date && $this->end_date <= now()) {
            return 'completed';
        }

        return 'published';
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatusLabel()
    {
        return $this->statusList[$this->getStatus()];
    }

    /**
     * 計算成功訂購/報名人數
     *
     * @return string $status
     */
    public function countSuccessReports()
    {
        return $this->use_payment ? $this->soldOrders->count() : $this->reports->count();
    }

    /**
     * 撈取商店
     */
    public function store()
    {
        return $this->belongsToMany(Store::class)->with('subscription','accounts')
                ->whereNull('event_store.deleted_at')
                ->published();
    }
}
