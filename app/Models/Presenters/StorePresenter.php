<?php

namespace App\Models\Presenters;

use Illuminate\Support\Str;
use Laracasts\Presenter\Presenter;
use App\Traits\Model\SummaryTrait;

class StorePresenter extends Presenter
{
    use SummaryTrait;

    // 已上架
    function is_published()
    {
        return $this->status == 'published';
    }

    // 已下架
    function is_leave()
    {
        return $this->status == 'leave';
    }

    // 未刪除(存活)
    function is_live()
    {
        return $this->status != 'delete';
    }

    // 付費商家
    function has_paid()
    {
        return in_array($this->status, ['published', 'pending']);
    }

    // 已生效的
    function is_effectived()
    {
        return in_array($this->status, ['published', 'pending', 'checkout']);
    }

    // 取得商家描述內容陣列
    function description_array($key)
    {
        return $this->descriptions->filter(function ($item) use ($key) {
            return $item->key == $key;
        });
    }

    // 取得商家描述內容
    function description($key, $isPlural = false, $default = '')
    {
        $items = $this->description_array($key);

        // 陣列
        if ($isPlural) {
            return $items->pluck('value');
        }

        $value = $items->first()->value ?? $default;

        // 是數值就轉浮點數，不然預設字串
        return is_numeric($value) ? (float)$value : $value;
    }

    // 平均的評價分數
    function avg_rank()
    {
        return floor($this->hasRankSharePostBrands->avg('rank') * 10) / 10;
    }

    // 最高人氣相簿的封面照
    // function popular_cover()
    // {
    //     return $this->popularAlbums[0]->cover->file_name ?? '';
    // }

    // 關於的內容簡介
    function about_text($limit = NULL)
    {
        return $this->getSummaryStripTags($this->description('about'), $limit);
    }

    /**
     * 目前合約狀態
     *
     * @return string $contract_status
     */
    function contract_status_label()
    {
        // 婚禮小物不需要合約
        if ($this->type == 7) {
            return '';
        }

        // 未曾付費：沒有合約
        if (!$this->contractOrders->count()) {
            return $this->contractStatusList['never'];
        }

        // 合約終止：沒有目前的合約訂單
        if (!$this->orderSet->contractOrder) {
            return $this->contractStatusList['ended'];
        }

        // 月付型：目前的合約訂單中，且含有自動續約的品項
        if ($this->orderSet->contractOrder->hasRenewingOrderProduct()) {
            return $this->contractStatusList['renewing'];
        }

        // 方案型
        return $this->contractStatusList['running'];
    }

    /**
     * 取得使用費上限設定狀態
     *
     * @return string $contract_status
     */
    function usage_limit_label()
    {
        // 沒有發票資訊
        if (!$this->orderSet) {
            return '尚未設定';
        }

        $usageLimit = $this->orderSet->usage_limit;

        // 沒有設定上限
        if (!$usageLimit) {
            return '無上限';
        }

        // 設定費直接折抵，無需繳使用費
        $payDiscount = $this->entity->getBasicSettingFee($this->type, true);
        if ($usageLimit <= $payDiscount) {
            return '不額外付使用費';
        }

        // 使用費最低應繳金額 50 元
        if ($usageLimit <= $payDiscount + 50) {
            return '最低金額 $50';
        }

        return '自訂 $'.$usageLimit;
    }

    // 婚禮小物名稱列表
    function mall_items_text($limit)
    {
        return Str::limit($this->mallItems->implode('title', '、'), $limit);
    }

    // 喜餅免費門市試吃
    function has_free_shop_tasting()
    {
        return ($this->description('has_shop_tasting') && $this->description('shop_tasting_min_fee') === 0.0);
    }
}
