<?php

namespace App\Models\Presenters\Wdv2;

use Illuminate\Support\Str;
use Laracasts\Presenter\Presenter;
use App\Traits\Model\SummaryTrait;

class StoreQuotePresenter extends Presenter
{
    use SummaryTrait;

    // 詳細內容 for HTML
    function detail_text($limit = NULL)
    {
        $summaryText = $this->getSummaryStripTags($this->detail->content);

        return $limit ? Str::limit($summaryText, $limit) : $summaryText;
    }

    // 詳細內容 for HTML
    function detail_html()
    {
        $content = nl2br($this->detail->content);
        $content = str_replace('[user_name]', $this->userQuote->user->name, $content);

        return $this->uriRedirectGenerator($content);
    }

    // 內容中有連結的部分，使用轉址方式紀錄GA
    private function uriRedirectGenerator($content)
    {
        // 初始設定
        // 先用舊的外站轉址連結 {domain}/redirect/store/{store_id}?type=quote，統一與 slim 報價 email 內的連結一樣，紀錄報價單的外站點擊 GA event
        // 等之後改寫商家報價系統，再換成新的短網址導轉 {domain}/redirect/{redirect_url_key}
        $redirectPath = config('params.wdv3.www_url').'/redirect/store/'.$this->store_id.'?type=quote';

        // 正規表達式
        $urlReg   = '/(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/imu';
        $emailReg = '/[A-Za-z0-9\_\-\.]+@[A-Za-z0-9_-]+\.([A-Za-z0-9_-][A-Za-z0-9_]+)/';
        // $phoneReg = '/[(]?[+]?[0-9]{2,4}[)-]?[\s]?[0-9]{3,4}[\s-]?[0-9]{3,6}/'; // 2021-10-18: 遇到網址裡面放電話，該怎麼辦!?
        $phoneReg = '/(?<![\/(+)\w-])[(]?[+]?\d{2,4}[)-]?[\s]?\d{3,4}[\s-]?\d{3,6}/'; // 無寬度左不合樣(Negative Lookbehind)(?<!...)：左邊的字串不匹配"/", "(", "+", ")", "\w", "-"

        // 網址轉址
        $content = preg_replace_callback(
            $urlReg,
            function ($matches) use (&$count, $redirectPath) {
                if (strpos($matches[0],'weddingday.com.tw') === false) {
                    $href = $redirectPath.'&sqid='.$this->id.'&number='.(++$count).'.WDcenter&uri='.urlencode($matches[0]);
                } else {
                    $href = $matches[0].'?utm_source=WDcenter&utm_campaign=quotation_tobride&utm_content=link_'.$this->id.'_'.(++$count);
                }
                return '<a target="_blank" href="'.$href.'">'. $matches[0].'</a>';
            },
            $content
        );

        // email轉址
        $content = preg_replace_callback(
            $emailReg,
            function ($matches) use (&$count, $redirectPath) {
                $href = $redirectPath.'&sqid='.$this->id.'&number='.(++$count).'.WDcenter&uri='.urlencode('mailto:'.$matches[0]);
                return '<a target="_blank" href="'.$href.'">'. $matches[0].'</a>';
            },
            $content
        );

        // 電話轉址
        $content = preg_replace_callback(
            $phoneReg,
            function ($matches) use (&$count, $redirectPath) {
                $href = $redirectPath.'&sqid='.$this->id.'&number='.(++$count).'.WDcenter&uri='.urlencode('tel:'.$matches[0]);
                return '<a target="_blank" href="'.$href.'">'. $matches[0].'</a>';
            },
            $content
        );

        return $content;
    }

    // 聯絡資訊
    function contact_info()
    {
        // 初始設定
        // 先用舊的外站轉址連結 {domain}/redirect/store/{store_id}?type=quote，統一與 slim 報價 email 內的連結一樣，紀錄報價單的外站點擊 GA event
        // 等之後改寫商家報價系統，再換成新的短網址導轉 {domain}/redirect/{redirect_url_key}
        $store        = $this->store;
        $redirectPath = config('params.wdv3.www_url').'/redirect/store/'.$this->store_id.'?type=quote';

        // 官網
        $website = $store->website;
        if ($website) {
            $websiteUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_website&uri='.urlencode($website);
        }

        // 粉絲頁
        $fanpage = $store->fb_page;
        if ($fanpage) {
            $fanpageUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_FB&uri='.urlencode($fanpage);
        }

        // Instagram
        $instagram = $store->instagram;
        if ($instagram) {
            $instagramUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_IG&uri='.urlencode($instagram);
        }

        // 手機
        $phone = $store->phone;
        if ($phone) {
            $phoneUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_phone&uri='.urlencode('tel:'.$phone);
        }

        // 市話
        $tel = $store->tel;
        if ($tel) {
            $telUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_tel&uri='.urlencode('tel:'.$tel);
        }

        // Line
        $line = $store->line;
        if ($line) {
            $lineUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_LineID&uri='.urlencode('line://ti/p/'.$line);
        }

        // 信箱
        $email    = $store->contact_email ?: $store->email;
        $emailUrl = $redirectPath.'&sqid='.$this->id.'&number=WDcenter_email&uri='.urlencode('mailto:'.$email);

        // 作品一覽
        if ($store->present()->has_paid) {
            $workUrl = config('params.wdv3.www_url').'/store-'.$store->typeKeyList[$store->type].'/'.$store->id.'/work?utm_source=WDcenter&utm_campaign=quotation_tobride&utm_content=store_'.$store->id.'_worklist';
        }

        // 新娘評價
        $primaryBrand = $store->getPrimaryBrand();
        if ($primaryBrand && $store->sharePostBrands->count() > 0) {
            $brandUrl = config('params.wdv3.www_url').'/brand/'.$primaryBrand->id.'?utm_source=WDcenter&utm_campaign=quotation_tobride&utm_content=store_'.$store->id.'_sharelist';
        }

        return [
            'website'       => $website,
            'website_url'   => $websiteUrl ?? '',
            'fanpage'       => $fanpage,
            'fanpage_url'   => $fanpageUrl ?? '',
            'instagram'     => $instagram,
            'instagram_url' => $instagramUrl ?? '',
            'phone'         => $phone,
            'phone_info'    => $store->phone_info,
            'phone_url'     => $phoneUrl ?? '',
            'tel'           => $tel,
            'tel_info'      => $store->tel_info,
            'tel_url'       => $telUrl ?? '',
            'line'          => $line,
            'line_url'      => $lineUrl ?? '',
            'email'         => $email,
            'email_url'     => $emailUrl,
            'work_url'      => $workUrl ?? '',
            'brand_url'     => $brandUrl ?? '',
        ];
    }

}
