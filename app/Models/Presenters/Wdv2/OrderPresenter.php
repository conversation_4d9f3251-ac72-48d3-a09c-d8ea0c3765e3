<?php

namespace App\Models\Presenters\Wdv2;

use Lara<PERSON>s\Presenter\Presenter;
use App\Models\Wdv2\OrderProduct;

class OrderPresenter extends Presenter
{
    // 訂單內容標籤
    function contractTypeLabel()
    {
        // 使用費
        $setFeeContractType = (new OrderProduct)->setFeeContractType;
        $orderProductIds    = collect($setFeeContractType)->collapse();
        if ($this->orderPackages->whereIn('order_product_id', $orderProductIds)->isEmpty()) {
            return $this->contractTypeList['usage_fee'];
        }

        foreach ($setFeeContractType as $contractType => $orderProductIds) {
            if ($this->orderPackages->whereIn('order_product_id', $orderProductIds)->isNotEmpty()) {
                return $this->contractTypeList[$contractType];
            }
        }

        return '怪怪的喲～';
    }

    // 付款狀態標籤
    function paymentStatusLabel()
    {
        // 無須付款
        if (!$this->amount) {
            return $this->paymentStatusList['unnecessary'];
        }

        return $this->paymentStatusList[$this->payment_status];
    }

    // 發票狀態標籤
    function invoiceStatusLabel()
    {
        // 無須開立
        if (!$this->amount) {
            return $this->invoiceStatusList['unnecessary'];
        }

        return $this->invoiceStatusList[$this->invoice_status];
    }

    // 付款方式標籤
    function paymentMethodLabel()
    {
        // 匯款
        if ($this->paymentSpgatewayLog->isNotEmpty()) {
            return $this->paymentMethodList['spgateway'];
        }

        // 信用卡
        if ($this->allPaymentLogs->isNotEmpty()) {
            return $this->paymentMethodList['credit_card'];
        }

        // 其他
        return $this->paymentMethodList['other'];
    }

    // 付款卡號
    function paymentCreditCard()
    {
        return $this->paymentLog->last()->card_last_four ?? '';
    }

    // 付款交易訊息
    function paymentMessage()
    {
        return $this->allPaymentLogs->last()->msg ?? '';
    }

    // 付款時間
    function paidAt()
    {
        $orderLog = $this->orderLogs->firstWhere('status', 1002);
        return $orderLog ? $orderLog->created_at->format('Y-m-d H:i:s') : '';
    }
}
