<?php

namespace App\Models\Presenters\Wdv2;

use Illuminate\Support\Str;
use Laracasts\Presenter\Presenter;
use App\Models\Store;
use App\Models\UserWeddingType;
use App\Models\Wdv2\UserQuote;
use App\Transformers\CityCodeTransformer;

class UserQuotePresenter extends Presenter
{
    // 商家類型
    function store_type_name()
    {
        $store = new Store;
        return $store->quoteTypeList[$this->type];
    }

    // 婚禮形式
    function wedding_type_name()
    {
        $weddingType = new UserWeddingType;
        return $weddingType->typeList[$this->wedding_type];
    }

    // 婚宴地區
    function cityArea($code = NULL)
    {
        $code = $code ?: $this->city_code;
        if (!$code) {
            return '';
        }

        $cityCodeTransformer = new CityCodeTransformer;
        return $cityCodeTransformer->getValue($code);
    }

    // 婚宴場地類型
    function wedding_venue_type_name()
    {
        if (!$this->wedding_venue_type) {
            return '';
        }

        $userQuote = new UserQuote;
        return $userQuote->weddingVenueTypeList[$this->wedding_venue_type];
    }

    // 詳細內容 for HTML
    function detail_html($limit = NULL, $showStoreType = true)
    {
        $detail = $this->detail;

        $content = '';
        if ($showStoreType) {
            $content .= '<b>服務類型：</b>'.$this->store_type_name().'<br />';
        }
        $content .= '<b>婚期：</b>'.$this->date.'<br />';

        // 舊格式
        if (isset($detail->place)) {
            $detail->remark = $limit ? Str::limit($detail->remark, $limit) : $detail->remark;
            $content .= '<b>服務地點：</b>'.$detail->place.'<br />';
            $content .= '<b>服務需求/其他備註</b><br />';
            $content .= nl2br($detail->remark);
            return $content;
        }

        // 新格式
        $content .= '<b>婚禮形式：</b>'.$this->wedding_type_name().'<br />';
        if ($this->city_code) {
            $content .= '<b>婚宴地區：</b>'.$this->cityArea($this->city_code).'<br />';
        }
        if ($this->wedding_venue_type) {
            $content .= '<b>婚宴場地類型：</b>'.$this->wedding_venue_type_name().'<br />';
        }
        if ($this->wedding_venue) {
            $content .= '<b>婚宴場地：</b>'.$this->wedding_venue.'<br />';
        }

        $userQuote = new UserQuote;
        $detailList = $userQuote->detailList[$this->type];
        switch ($this->type) {
            // 婚攝/婚錄
            case 3:
                if (!empty($detail->time)) {
                    $content .= '<b>婚宴時段：</b>'.$detailList['time'][$detail->time].'<br />';
                }
                if (!empty($detail->ceremony)) {
                    $content .= '<b>儀式需求：</b>'.$detailList['ceremony'][$detail->ceremony].'<br />';
                }
                if (!empty($detail->service)) {
                    $content .= '<b>主要服務：</b>'.$detailList['service'][$detail->service].'<br />';
                }
                if (!empty($detail->min_budget) || !empty($detail->max_budget)) {
                    $content .= '<b>期望預算：</b>'.$this->getBudgetRange($detail->min_budget, $detail->max_budget).'<br />';
                }
                if (!empty($detail->service_city_code)) {
                    $content .= '<b>開拍地區：</b>'.$this->cityArea($detail->service_city_code).'<br />';
                }
                if (!empty($detail->extra_service)) {
                    $content .= '<b>其他服務：</b>'.($limit ? Str::limit($detail->extra_service, $limit) : $detail->extra_service).'<br />';
                }
                break;

            // 新娘秘書
            case 4:
                if (!empty($detail->service)) {
                    $content .= '<b>主要服務：</b>'.$detailList['service'][$detail->service].'<br />';
                }
                if (!empty($detail->time)) {
                    $content .= '<b>婚宴時段：</b>'.$detailList['time'][$detail->time].'<br />';
                }
                if (!empty($detail->service_time)) {
                    $content .= '<b>開妝時段：</b>'.$detailList['service_time'][$detail->service_time].'<br />';
                }
                if (!empty($detail->ceremony)) {
                    $content .= '<b>儀式需求：</b>'.$detailList['ceremony'][$detail->ceremony].'<br />';
                }
                if (!empty($detail->amount)) {
                    $content .= '<b>造型數量：</b>'.$detailList['amount'][$detail->amount].'<br />';
                }
                if (!empty($detail->min_budget) || !empty($detail->max_budget)) {
                    $content .= '<b>期望預算：</b>'.$this->getBudgetRange($detail->min_budget, $detail->max_budget).'<br />';
                }
                if (!empty($detail->service_city_code)) {
                    $content .= '<b>開妝地區：</b>'.$this->cityArea($detail->service_city_code).'<br />';
                }
                if (!empty($detail->extra_amount)) {
                    $content .= '<b>造型需求：</b>'.($limit ? Str::limit($detail->extra_amount, $limit) : $detail->extra_amount).'<br />';
                }
                if (!empty($detail->extra_service)) {
                    $content .= '<b>其他服務：</b>'.($limit ? Str::limit($detail->extra_service, $limit) : $detail->extra_service).'<br />';
                }
                break;

            // 婚禮佈置
            case 6:
                if (!empty($detail->time)) {
                    $content .= '<b>婚宴時段：</b>'.$detailList['time'][$detail->time].'<br />';
                }
                if (!empty($detail->service)) {
                    foreach ($detailList['service'] as $key => $value) {
                        $detail->service = str_replace($key, $value, $detail->service);
                    }
                    $content .= '<b>主要服務：</b>'.($limit ? Str::limit($detail->service, $limit) : $detail->service).'<br />';
                }
                if (!empty($detail->min_budget) || !empty($detail->max_budget)) {
                    $content .= '<b>期望預算：</b>'.$this->getBudgetRange($detail->min_budget, $detail->max_budget).'<br />';
                }
                break;

            // 婚禮主持人
            case 8:
                if (!empty($detail->time)) {
                    $content .= '<b>婚宴時段：</b>'.$detailList['time'][$detail->time].'<br />';
                }
                if (!empty($detail->service)) {
                    foreach ($detailList['service'] as $key => $value) {
                        $detail->service = str_replace($key, $value, $detail->service);
                    }
                    $content .= '<b>主要服務：</b>'.($limit ? Str::limit($detail->service, $limit) : $detail->service).'<br />';
                }
                if (!empty($detail->ceremony)) {
                    $content .= '<b>儀式需求：</b>'.$detailList['ceremony'][$detail->ceremony].'<br />';
                }
                if (!empty($detail->service_city_code)) {
                    $content .= '<b>儀式地區：</b>'.$this->cityArea($detail->service_city_code).'<br />';
                }
                if (!empty($detail->min_budget) || !empty($detail->max_budget)) {
                    $content .= '<b>期望預算：</b>'.$this->getBudgetRange($detail->min_budget, $detail->max_budget).'<br />';
                }
                if (!empty($detail->extra_service)) {
                    $content .= '<b>其他服務：</b>'.($limit ? Str::limit($detail->extra_service, $limit) : $detail->extra_service).'<br />';
                }
                break;
        }

        if (!empty($detail->remark)) {
            $content .= '<b>補充內容：</b>'.($limit ? Str::limit($detail->remark, $limit) : $detail->remark).'<br />';
        }

        return $content;
    }

    // 取得預算範圍
    function getBudgetRange($min, $max)
    {
        if (!$min) {
            return number_format($max).'以下';
        }

        if (!$max) {
            return number_format($min).'以上';
        }

        return number_format($min).' ~ '.number_format($max);
    }
}
