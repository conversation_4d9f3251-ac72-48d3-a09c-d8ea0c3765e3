<?php

namespace App\Models\Presenters\Wdv2;

use Lara<PERSON>s\Presenter\Presenter;
use App\Traits\Model\SummaryTrait;

class StoreServicePresenter extends Presenter
{
    use SummaryTrait;

    // 封面照
    function cover_image()
    {
        // 方案封面照
        if (!empty($this->information->pictures)) {
            return $this->information->pictures[0];
        }

        // 商家封面照
        return $this->store->cover->file_name ?? '';
    }

    // 內容簡介 (去除Img HTML標籤)
    function content_strip_images()
    {
        if (empty($this->information->content)) {
            return '';
        }

        return $this->getContentStripImageTags($this->information->content);
    }

    // 內容簡介
    function summary_text($limit = NULL)
    {
        if (empty($this->information->content)) {
            return $this->getSummaryTags();
        }

        $result = $this->getSummaryStripTags($this->information->content, $limit);
        if (!$result) {
            $result = $this->getSummaryTags();
        }

        return $result;
    }

    // 取得方案需求標籤的簡介
    private function getSummaryTags()
    {
        $tags = [];
        foreach ($this->tags as $tag) {
            $_temp  = $tag->serviceTags[$this->store->type][$tag->id] ?? $tag->name;
            $tags[] = str_replace('[number]', $tag->pivot->number, $_temp);
        }

        return implode('、', $tags);
    }
}
