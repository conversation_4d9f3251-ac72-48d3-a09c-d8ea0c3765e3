<?php

namespace App\Models\Presenters;

use Laracasts\Presenter\Presenter;
use App\Models\Wdv2\ServiceStopApply;

class ServiceStopApplyChangeLogPresenter extends Presenter
{
    /**
     * 轉換狀態類型為中文
     * @param $type: 狀態類型
     * @return mixed
     */
    public function chineseType($type)
    {
        return $this->typeList[$type];
    }

    /**
     * 轉換狀態為中文
     * @param $type: 狀態類型
     * @param $status: 狀態數字
     * @return mixed|null
     */
    public function chineseStatus($type, $status)
    {
        //從ServiceStopApply model取出狀態成員
        $serviceStopApply = resolve(ServiceStopApply::class);

        switch($type) {
            //商家付款狀態
            case 'pay_off_status':
                return $serviceStopApply->payOffStatusList[$status];
            //WD退款狀態
            case 'refund_status':
                return $serviceStopApply->refundStatusList[$status];
            //折讓單開立狀態
            case 'deposit_status':
                return $serviceStopApply->depositStatusList[$status];
            default:
                return null;
        }
    }
}
