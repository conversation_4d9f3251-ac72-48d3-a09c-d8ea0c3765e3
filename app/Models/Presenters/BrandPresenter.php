<?php

namespace App\Models\Presenters;

use Laracasts\Presenter\Presenter;

class BrandPresenter extends Presenter
{
    // 品牌商家名稱
    function store_name()
    {
        // 取得主要商家
        $store = $this->getPrimaryStore();

        return $store ? $store->name : $this->name;
    }

    // 品牌商家email
    function store_email()
    {
        // 取得主要商家
        $store = $this->getPrimaryStore();

        return $store ? $store->email : $this->email;
    }

    // 取得主要商家
    private function getPrimaryStore()
    {
        return $this->primaryStores->first();
    }
}
