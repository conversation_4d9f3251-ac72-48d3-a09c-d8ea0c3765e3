<?php

namespace App\Models\Presenters;

use Illuminate\Support\Str;
use Laracasts\Presenter\Presenter;
use App\Services\Image\GetImageUrlService;
use App\Models\Image;

class ForumCommentPresenter extends Presenter
{
    // 作者名
    function author_name()
    {
        return $this->is_anonymous ? $this->author->present()->anonymous_name : $this->author->name;
    }

    // 作者頭像
    function author_avatar()
    {
        // 匿名頭像
        if ($this->is_anonymous) {
            return $this->author->present()->anonymous_avatar;
        }

        return $this->author->present()->display_avatar;
    }

    // 作者得到的讚數
    function author_get_like_count()
    {
        return $this->is_anonymous ? $this->getAnonymousDigital($this->author->forum_get_like_count) : $this->author->forum_get_like_count;
    }

    // 作者熱心留言數量
    function author_comment_count()
    {
        return $this->is_anonymous ? $this->getAnonymousDigital($this->author->forum_comment_count) : $this->author->forum_comment_count;
    }

    // 以人為本的匿名數據
    private function getAnonymousDigital($value)
    {
        if ($value < 10) {
            return $value;
        }
        $maxDigit = pow(10, strlen($value) - 1);
        return (floor($value / $maxDigit) * $maxDigit).'+';
    }

    // 摘要
    function summary_object($showTrashed = true)
    {
        $summaryObj = json_decode($this->summary);

        // 顯示留言已刪除
        if ($showTrashed && in_array($this->status, ['softdelete', 'delete'])) {
            $summaryObj->text = '(這篇留言已經被刪除囉...)';
        }

        // 顯示縮圖
        $getImageUrlService = new GetImageUrlService;
        foreach ($summaryObj->images as $key => $image) {
            if ($image->isSelf) {
                $summaryObj->images[$key] = $getImageUrlService->get($image->link, 200, 200);
            } else {
                $summaryObj->images[$key] = $summaryObj->images[$key]->link;
            }
        }

        return $summaryObj;
    }

    // 好婚聊聊 摘要
    function summary_object_v2($showTrashed = true)
    {
        $summaryObj = json_decode($this->summary);
        $imgResult = [];

        // 顯示留言已刪除
        if ($showTrashed && in_array($this->status, ['softdelete', 'delete'])) {
            $summaryObj->text = '(這篇留言已經被刪除囉...)';
        }

        // 顯示縮圖
        $getImageUrlService = new GetImageUrlService;
        foreach ($summaryObj->images as $key => $image) {
            if ($image->isSelf) {
                $model = Image::where('file_name', $image->link)->first();
            }
            $imgResult[] = [
                'is_external' => !$image->isSelf,
                'link' => ($image->isSelf ? $getImageUrlService->get($image->link) : $summaryObj->images[$key]->link),
                'width' => ($image->isSelf ? $model->width : null),
                'height' => ($image->isSelf ? $model->height : null)
            ];
        }
        $summaryObj->images = $imgResult;

        return $summaryObj;
    }

    // 純文字摘要
    function summary_text($limit, $showTrashed = true)
    {
        return Str::limit($this->summary_object($showTrashed)->text, $limit);
    }

    // 前台連結
    function frontend_url()
    {
        return config('params.wdv3.forum_url').'/article/'.$this->article_id.'?comment='.$this->id;
    }

    // 時間格式轉換
    function time_format($column, $humans = false)
    {
        // 未刪除的留言，則刪除時間預設為空值
        if ($column == 'softdeleted_at' && !in_array($this->status, ['softdelete', 'delete'])) {
            return '';
        }

        // 24小時內使用人性化時間格式
        if ($humans && $this->$column > now()->subDay(1)) {
            return $this->$column->diffForHumans();
        } elseif ($humans) {
            return $this->$column->format('Y-m-d H:i');
        }

        //老文章不一定有carbon的format()..會噴exception..try catch也捕捉不到
        if(method_exists($this->$column, 'format')){
            return $this->$column->format('Y-m-d H:i:s');
        }

        return '';
    }
}
