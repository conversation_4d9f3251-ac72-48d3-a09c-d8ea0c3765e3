<?php

namespace App\Models\Presenters;

use Illuminate\Support\Str;
use Laracasts\Presenter\Presenter;
use App\Services\Image\GetImageUrlService;

class ForumArticlePresenter extends Presenter
{
    // 作者名
    function author_name()
    {
        return $this->is_anonymous ? $this->author->present()->anonymous_name : $this->author->name;
    }

    // 作者頭像
    function author_avatar($anonymousAvatar = true)
    {
        // 匿名頭像
        if ($this->is_anonymous) {
            $avatar = $anonymousAvatar ? 'anonymous_avatar' : 'default_avatar';
            return $this->author->present()->{$avatar};
        }

        return $this->author->present()->display_avatar;
    }

    // 作者得到的讚數
    function author_get_like_count()
    {
        return $this->is_anonymous ? $this->getAnonymousDigital($this->author->forum_get_like_count) : $this->author->forum_get_like_count;
    }

    // 作者熱心留言數量
    function author_comment_count()
    {
        return $this->is_anonymous ? $this->getAnonymousDigital($this->author->forum_comment_count) : $this->author->forum_comment_count;
    }

    // 以人為本的匿名數據
    private function getAnonymousDigital($value)
    {
        if ($value < 10) {
            return $value;
        }
        $maxDigit = pow(10, strlen($value) - 1);
        return (floor($value / $maxDigit) * $maxDigit).'+';
    }

    // 封面照
    function cover_image()
    {
        // 已設定的封面
        if ($this->cover) {
            return $this->cover->file_name;
        }

        // 預設首張圖片
        foreach ($this->images as $image) {
            return $image->file_name;
        }

        return '';
    }

    // 摘要
    function summary_object($showTrashed = true)
    {
        $summaryObj = json_decode($this->summary);

        // 顯示文章已刪除
        if ($showTrashed && in_array($this->status, ['softdelete', 'delete'])) {
            $summaryObj->text = '(這篇文章已經被刪除囉...)';
        }

        // 顯示縮圖
        $getImageUrlService = new GetImageUrlService;
        foreach ($summaryObj->images as $key => $image) {
            if ($image->isSelf) {
                $model = $this->images->firstWhere('file_name', $image->link);
                $summaryObj->images[$key] = $getImageUrlService->get($image->link, $model->width, $model->height);
            } else {
                $summaryObj->images[$key] = $summaryObj->images[$key]->link . '?width=0&height=0';
            }
        }

        return $summaryObj;
    }

    // 好婚聊聊列表與內文 摘要
    function summary_object_v2($showTrashed = true)
    {
        $summaryObj = json_decode($this->summary);
        $imgResult = [];

        // 顯示文章已刪除
        if ($showTrashed && in_array($this->status, ['softdelete', 'delete'])) {
            $summaryObj->text = '(這篇文章已經被刪除囉...)';
        }

        // 顯示縮圖
        $getImageUrlService = new GetImageUrlService;
        foreach ($summaryObj->images as $key => $image) {
            if ($image->isSelf) {
                $model = $this->images->firstWhere('file_name', $image->link);
            }
            $imgResult[] = [
                'is_external' => !$image->isSelf,
                'link' => ($image->isSelf ? $getImageUrlService->get($image->link) : $summaryObj->images[$key]->link),
                'width' => ($image->isSelf ? $model->width : null),
                'height' => ($image->isSelf ? $model->height : null)
            ];
        }
        $summaryObj->images = $imgResult;

        return $summaryObj;
    }

    // 摘要的首張圖片
    function summary_images($limit = NULL)
    {
        $summaryObj = $this->summary_object_v2();

        return $summaryObj ? array_slice($summaryObj->images, 0 ,$limit) : [];
    }

    // 摘要的首張圖片
    // function summary_cover()
    // {
    //     $summaryObj = $this->summary_object_v2();
    //     if (!$summaryObj) {
    //         return '';
    //     }

    //     // 預設首張圖片
    //     foreach ($summaryObj->images as $image) {
    //         if ($image['is_external']) {
    //             return $image->link;
    //         }
    //     }

    //     return '';
    // }

    // 純文字摘要
    function summary_text($limit, $showTrashed = true)
    {
        return Str::limit($this->summary_object($showTrashed)->text, $limit);
    }

    // 前台連結
    function frontend_url()
    {
        return config('params.wdv3.forum_url').'/article/'.$this->id;
    }

    // 時間格式轉換
    function time_format($column, $humans = false)
    {
        // 空值直接返回
        if (!$this->$column) {
            return '';
        }

        // 未刪除的文章，則刪除時間預設為空值
        if ($column == 'softdeleted_at' && !in_array($this->status, ['softdelete', 'delete'])) {
            return '';
        }

        // 24小時內使用人性化時間格式
        if ($humans && $this->$column > now()->subDay(1)) {
            return $this->$column->diffForHumans();
        } elseif ($humans) {
            return $this->$column->format('Y-m-d H:i');
        }

        return $this->$column->format('Y-m-d H:i:s');
    }

    // 最後更新時間
    function last_updated_at()
    {
        if ($this->logUpdateds->count()) {
            return $this->logUpdateds[0]->created_at;
        }

        return $this->published_at ? $this->published_at->format('Y-m-d H:i:s') : '';
    }
}
