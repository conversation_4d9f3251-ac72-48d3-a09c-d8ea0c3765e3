<?php

namespace App\Models\Presenters;

use Laracasts\Presenter\Presenter;

class UserPresenter extends Presenter
{
    // user頭像
    function display_avatar()
    {
        if ($this->avatar) {
            // 如果是第三方來的會有Https開頭 就不加上config('params.image_url')
            if (strpos($this->avatar, 'https://') === 0) {
                return $this->avatar;
            }
            return config('params.image_url') . '/original/' . $this->avatar . '?width=200';
        }

        // 預設頭像
        return $this->fb_avatar() ?: $this->default_avatar();
    }

    // user預設頭像
    function default_avatar()
    {
        // 備份檔放/wdv3/images/
        return config('params.image_url') . '/original/' . $this->rand_avatar_image();
    }

    // user隨機預設頭像
    function rand_avatar_image()
    {
        return 'avatar_normal_' . rand(1, 5) . '.png'; // 新娘系列
        // return 'user_avatar' . rand(1, 3) . '.png'; // 花草系列
        // return 'avatar_xmas' . rand(1, 5) . '.png'; // 聖誕系列
    }

    // user匿名名稱
    function anonymous_name()
    {
        return '匿名鬼鬼' . $this->anonymous_key;
    }

    // user匿名頭像
    function anonymous_avatar()
    {
        return config('params.file_url') . '/wdv3/images/anonymous_ghost.png'; // 匿名鬼鬼
        // return config('params.file_url') . '/wdv3/images/anonymous_normal.png'; // 新年鞭炮
        // return config('params.file_url') . '/wdv3/images/anonymous_ghost_xmas.png'; // 雪人鬼鬼
    }

    // FB 頭像（改版v2前就綁定fb的user用）
    function fb_avatar()
    {
        return $this->fb_id ? 'https://graph.facebook.com/' . $this->fb_id . '/picture?width=200&height=200' : '';
    }
}
