<?php

namespace App\Models\Presenters;

use Laracasts\Presenter\Presenter;

class EventOrderPresenter extends Presenter
{
    // 訂單明細列表
    function detailList()
    {
        $result = [];
        foreach ($this->details as $detail) {
            $detail->name = $detail->name ?: $detail->keyList[$detail->key];
            $result[]     = $detail->only(['key', 'name', 'is_positive', 'price']);
        }

        return $result;
    }

    // 付款狀態標籤
    function paymentStatusLabel()
    {
        // 付款狀態為尚未付款，則顯示付款異常
        if ($this->payment_status == 'pending') {
            return '付款異常，請聯繫WD';
        }

        return $this->paymentStatusList[$this->payment_status];
    }

    // 發票狀態標籤
    function invoiceStatusLabel()
    {
        return $this->invoiceStatusList[$this->invoice_status];
    }

    // 付款卡號
    // function paymentCreditCard()
    // {
    //     $creditCard = $this->logPayment->creditCard;
    //     if (!$creditCard) {
    //         return '';
    //     }

    //     return substr_replace($creditCard->bin_code, ' ', 4, 0).'** **** '.$creditCard->last_four;
    // }
}
