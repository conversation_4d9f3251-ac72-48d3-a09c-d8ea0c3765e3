<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SeoStoreRouteLog extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    protected $dates = ['created_at'];

    // relation YzcubeUser 神之後台管理員
    public function yzcubeUser()
    {
        return $this->belongsTo(YzcubeUser::class);
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }
}
