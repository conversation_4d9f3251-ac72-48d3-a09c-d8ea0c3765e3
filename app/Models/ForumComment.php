<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class ForumComment extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;
    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\ForumCommentPresenter';

    protected $guarded = [];

    protected $dates = ['softdeleted_at'];

    // 狀態 published:已發佈 softdelete:顯示軟刪除 delete:刪除
    public $statusList = [
        'published'  => '已發佈',
        'softdelete' => '顯示軟刪除',
        'delete'     => '刪除',
    ];

    // 隱藏類型 line:LineID facebook:FB連結 email:連絡信箱 phone:連絡電話
    public $hideTypeList = [
        'line'     => 'LineID',
        'social'   => '社群連結',
        'email'    => '連絡信箱',
        'phone'    => '連絡電話',
    ];

    // relation ForumArticle and not delete
    public function article()
    {
        return $this->belongsTo(ForumArticle::class, 'article_id')->live();
    }

    // relation ForumArticle
    public function articleRel()
    {
        return $this->belongsTo(ForumArticle::class, 'article_id');
    }

    // relation User
    public function allAuthor()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // relation User
    public function author()
    {
        return $this->allAuthor()->live();
    }

    // relation User
    public function likesWithTrashed()
    {
        return $this->belongsToMany(User::class, 'user_article_like', 'comment_id')->live();
    }

    // relation User
    public function likes()
    {
        return $this->likesWithTrashed()->wherePivot('deleted_at', NULL);
    }

    // relation User
    public function atUsers()
    {
        return $this->belongsToMany(User::class, 'forum_at_users', 'comment_id')->live();
    }

    // relation User
    public function atUsersFilterUnsendEmail()
    {
        return $this->atUsers()->wherePivot('send_email', 0);
    }

    // relation Self
    public function parent()
    {
        return $this->belongsTo(ForumComment::class, 'parent_id')->live();
    }

    // relation Self
    public function allReplies()
    {
        return $this->hasMany(ForumComment::class, 'parent_id');
    }

    // relation Self
    public function replies()
    {
        return $this->allReplies()->live();
    }

    // relation ForumArticle / Self
    public function previous()
    {
        return $this->parent_id ? $this->parent() : $this->article();
    }

    // relation Image
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')->where('type', 'forum_comment');
    }

    // relation logUpdateds
    public function logUpdateds()
    {
        return $this->hasMany(LogArticleUpdated::class, 'comment_id')->sort();
    }

    // scope hasAuthor 排除帳號停用的作者
    public function scopehasAuthor($query)
    {
        return $query->select($this->getTable().'.*')
                    ->join((new User)->getTable().' AS users', function($join) {
                        $join->on($this->getTable().'.user_id', '=', 'users.id')
                                ->where('users.status', '!=', 'delete');
                    });
    }

    // scope live
    public function scopeLive($query)
    {
        // 需排除帳號停用的作者
        return $query->where('status', '!=', 'delete');
    }

    // scope status
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope sortCreatedAt
    public function scopeSortCreatedAt($query)
    {
        return $query->orderBy($this->getTable().'.created_at', 'DESC');
    }

    // scope anonymous
    public function scopeAnonymous($query, $flag)
    {
        return $query->where('is_anonymous', $flag);
    }

    // scope autoReply
    public function scopeAutoReply($query, $flag)
    {
        return $query->where('auto_reply', $flag);
    }
}
