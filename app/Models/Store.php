<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Support\Str;
use DB;

class Store extends Model
{
    use SoftDeletes;

    use ParseKeywordTrait;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\StorePresenter';

    protected $guarded = [];

    protected $dates = ['edited_at'];

    // 所有商家類型
    public $typeList = [
        1  => '拍婚紗',
        2  => '婚紗禮服',
        3  => '婚攝/婚錄',
        4  => '新娘秘書',
        5  => '婚宴場地',
        6  => '婚禮佈置',
        7  => '婚禮小物',
        8  => '婚禮主持人',
        9  => '婚戒',
        10 => '喜餅',
        11 => '婚鞋',
        12 => '蜜月旅行',
        13 => '禮車',
        14 => '喜帖',
        15 => '婚禮週邊',
    ];

    // 商家類型-索引
    public $typeKeyList = [
        1  => 'studio',
        2  => 'dress',
        3  => 'photographer',
        4  => 'makeup',
        5  => 'venue',
        6  => 'decoration',
        7  => 'mall',
        8  => 'host',
        10 => 'weddingcake',
    ];

    // 收藏的商家類型
    public $collectTypeList = [
        1  => '拍婚紗',
        2  => '婚紗禮服',
        4  => '新娘秘書',
        3  => '婚攝/婚錄',
        8  => '婚禮主持人',
        5  => '婚宴場地',
        6  => '婚禮佈置',
        7  => '婚禮小物',
        10 => '喜餅',
    ];

    // 主動報價的商家類型(有檔期)
    public $quoteTypeList = [
        4 => '新娘秘書',
        3 => '婚攝/婚錄',
        8 => '婚禮主持人',
        6 => '婚禮佈置',
    ];

    // 有詢問單的商家
    public $hasReserveList = [
        1  => '拍婚紗',
        2  => '婚紗禮服',
        3  => '婚攝/婚錄',
        4  => '新娘秘書',
        6  => '婚禮佈置',
        8  => '婚禮主持人',
    ];

    // 需計算排名的商家
    public $needRankList = [
        1  => '拍婚紗',
        2  => '婚紗禮服',
        3  => '婚攝/婚錄',
        4  => '新娘秘書',
        5  => '婚宴場地',
        6  => '婚禮佈置',
        8  => '婚禮主持人',
        10 => '喜餅',
    ];

    // 新娘的黃金團隊商家
    public $teamTypeList = [
        5  => '婚宴場地',
        1  => '拍婚紗',
        4  => '新娘秘書',
        3  => '婚攝/婚錄',
        2  => '禮服/西服',
        8  => '婚禮主持人',
        6  => '婚禮佈置',
        10 => '喜餅',
        15 => '婚禮週邊',
        9  => '婚戒/金飾',
        11 => '婚鞋',
        12 => '蜜月旅行',
    ];

    // 商家狀態
    public $statusList = [
        'published' => '上架中',
        'pending'   => '等待上架',
        'checkout'  => '下架清算中',
        'leave'     => '下架',
        'delete'    => '停用',
    ];

    // 合約狀態
    public $contractStatusList = [
        'running'  => '方案型',
        'renewing' => '月付型',
        'ended'    => '合約終止',
        'never'    => '未曾付費',
        'not_need' => '不需要合約',
    ];

    // 服務類型
    public $extraServiceList = [
        // 拍婚紗 (加值服務相關功能一律開放)
        // 1 => [
        //     101 => '一般服務',
        //     102 => '啟用加值服務',
        // ],

        // 婚攝/婚錄
        3 => [
            301 => '平面婚攝',
            302 => '動態婚錄',
            303 => '婚攝+婚錄',
        ],
    ];

    // relation Store 商家 - Wdv2
    public function storeWdv2()
    {
        return $this->belongsTo(Wdv2\Store::class, 'id');
    }

    // relation Image 商家LOGO
    public function logo()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'store_logo');
    }

    // relation Image 商家封面照
    public function cover()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'store_cover');
    }

    // relation Image 喜餅商家-門市試吃-門市店內照片
    public function shopTastingImage()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'store_shop_tasting');
    }

    // relation Image 喜餅商家-宅配試吃-宅配禮盒照片
    public function deliveryTastingImage()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'store_delivery_tasting');
    }

    // relation StoreDescription 商家描述內容
    public function descriptions()
    {
        return $this->hasMany(StoreDescription::class);
    }

    // relation StoreEmailLegalize
    public function emailLegalizes()
    {
        return $this->hasMany(StoreEmailLegalize::class);
    }

    // relation StoreTag 商家提供的服務
    public function tags()
    {
        return $this->belongsToMany(StoreTag::class, 'store_tag_pivot', 'target_id')
                    ->wherePivot('target_type', 'store')
                    ->withPivot('value', 'note');
    }

    // relation StoreTag 有勾選提供服務的標籤
    public function providTags()
    {
        return $this->tags()->wherePivot('value', '>', 0);
    }

    // relation CityData 縣市
    public function city()
    {
        return $this->belongsTo(CityData::class, 'city_id');
    }

    // relation AreaData 地區
    public function area()
    {
        return $this->belongsTo(AreaData::class, 'area_id');
    }

    // relation openingHours 商家的營業時間列表
    public function openingHours()
    {
        return $this->hasMany(StoreOpeningHour::class, 'target_id')
                    ->where('target_type', 'store');
    }

    // relation weddingcakeShops 喜餅商家的門市資訊
    public function weddingcakeShops()
    {
        return $this->hasMany(WeddingcakeShop::class)->sort();
    }

    // relation StoreService 商家方案
    public function services()
    {
        return $this->hasMany(StoreService::class);
    }

    // relation StoreService 已排序的商家方案
    public function sortServices()
    {
        return $this->services()->sort();
    }

    // relation StoreService 可顯示的商家方案
    public function showServices()
    {
        return $this->sortServices()->status('show');
    }

    // relation StoreService 可顯示的商家方案 (只有一般方案，不包含活動方案)
    public function showServicesWithoutActivity()
    {
        return $this->showServices()->whereNull('activity_id');
    }

    // relation StoreService 可顯示的商家方案 (包含一般方案、有效的活動方案)
    public function showServicesWithActivity()
    {
        return $this->showServices()
                    ->where(function($q1) {
                        $q1->whereNull('activity_id')
                            ->orWhereHas('store', function ($q2) {
                                $q2->whereHas('attendActivities');
                            });
                    });
    }

    // relation StoreAlbum 商家作品集
    public function albums()
    {
        return $this->hasMany(StoreAlbum::class);
    }

    // relation StoreAlbum 已排序的商家作品集
    public function sortAlbums()
    {
        return $this->albums()->sort();
    }

    // relation StoreAlbum 可顯示的商家作品集
    public function showAlbums()
    {
        return $this->sortAlbums()->status('show');
    }

    // relation StoreAlbum 熱門排序作品集
    public function popularAlbums()
    {
        return $this->albums()->status('show')->orderBy('rank', 'DESC');
    }

    // relation StoreAlbumImage 商家作品集的所有照片
    public function albumImages()
    {
        return $this->hasManyThrough(
                StoreAlbumImage::class,
                StoreAlbum::class,
                'store_id', // albums.store_id
                'album_id' // image.album_id
            )
            ->where((new StoreAlbum)->getTable().'.status', 'show')
            ->whereNotNull('store_id');
    }

    // relation StoreVideo 商家影片
    public function videos()
    {
        return $this->hasMany(StoreVideo::class);
    }

    // relation StoreVideo 已排序的商家影片
    public function sortVideos()
    {
        return $this->videos()->sort();
    }

    // relation StoreVideo 可顯示的商家影片
    public function showVideos()
    {
        return $this->sortVideos()->status('show');
    }

    // relation MallItem 婚禮小物(未排序)
    public function unsortMallItems()
    {
        return $this->hasManyThrough(Wdv2\MallItem::class, Wdv2\MallStore::class)
                    ->with('mallStore.logo')
                    ->where((new Wdv2\MallStore)->getTable().'.show_flag', 2)
                    ->where((new Wdv2\MallItem)->getTable().'.show_flag', 2);
    }

    // relation MallItem 婚禮小物
    public function mallItems()
    {
        return $this->unsortMallItems()
                    ->orderBy('rank', 'DESC')
                    ->orderBy('created_at', 'DESC')
                    ->orderBy('id', 'DESC');
    }

    // relation VenueDishes 婚宴場地喜宴菜
    public function venueDishes()
    {
        return $this->hasMany(VenueDishes::class);
    }

    // relation VenueDishes 已排序的婚宴場地喜宴菜
    public function sortVenueDishes()
    {
        return $this->venueDishes()->sort();
    }

    // relation VenueRoom 婚宴場地廳房
    public function venueRooms()
    {
        return $this->hasMany(VenueRoom::class);
    }

    // relation VenueRoom 已排序的婚宴場地廳房
    public function sortVenueRooms()
    {
        return $this->venueRooms()->sort();
    }

    // relation VenueRoom 可顯示的婚宴場地廳房
    public function showVenueRooms()
    {
        return $this->sortVenueRooms()->status('show');
    }

    // relation WeddingcakeCookie 喜餅系列
    public function weddingcakeCookies()
    {
        return $this->hasMany(WeddingcakeCookie::class);
    }

    // relation WeddingcakeCookie 已排序的喜餅系列
    public function sortWeddingcakeCookies()
    {
        return $this->weddingcakeCookies()->sort();
    }

    // relation WeddingcakeCookie 喜餅
    public function weddingcakeCookieItems()
    {
        return $this->hasManyThrough(WeddingcakeCookieItem::class, WeddingcakeCookie::class);
    }

    // relation DressContract 禮服合約
    public function dressContracts() {
        return $this->hasMany(DressContract::class);
    }

    // relation DressContract 已排序的禮服合約
    public function sortDressContracts() {
        return $this->dressContracts()->sort();
    }

    // relation DressOrder 禮服合約
    public function dressOrders() {
        return $this->hasMany(DressOrder::class);
    }

    // relation DressOrder 已排序的禮服合約
    public function sortDressOrders() {
        return $this->dressOrders()->sortWeddingDate('ASC');
    }

    // relation StoreSubscription 取消訂閱
    public function subscription()
    {
        return $this->hasOne(StoreSubscription::class);
    }

    // relation BlogArticle
    public function articles()
    {
        return $this->belongsToMany(BlogArticle::class, 'store_articles')
                    ->orderBy('sequence')
                    ->withPivot('id', 'tag')
                    ->withTimestamps();
    }

    // relation BlogArticle
    public function kolArticles()
    {
        return $this->articles()->type('kol');
    }

    // relation BlogArticle
    public function blogArticles()
    {
        return $this->articles()->type('blog');
    }

    // relation User 收藏商家的user
    public function userCollects()
    {
        return $this->belongsToMany(User::class, (new UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'store')
                    ->wherePivotNull('deleted_at')
                    ->where((new User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation StoreService 已收藏的服務方案
    public function allServiceCollects()
    {
        return $this->hasManyThrough(
                    UserCollect::class,
                    StoreService::class,
                    'store_id', // store_services.store_id
                    'target_id' // user_collects.target_id
                )
                ->where('user_collects.type', 'service')
                ->whereNull('user_collects.deleted_at');
                // ->where('store_services.status', 'show');
    }

    // relation UserCollect 已收藏的作品
    public function allWorkCollects()
    {
        // 婚紗禮服-作品集 & 喜餅-禮盒
        if (in_array($this->type, [2, 10])) {
            return $this->hasManyThrough(
                        UserCollect::class,
                        StoreAlbum::class,
                        'store_id', // store_albums.store_id
                        'target_id' // user_collects.target_id
                    )
                    ->where('user_collects.type', 'album')
                    ->whereNull('user_collects.deleted_at');
                    // ->where('store_albums.status', 'show');

            // 其他商家類型-單一作品
        } else {
            return $this->where('stores.id', $this->id)
                        ->join('store_albums', function($join) {
                            $join->on('store_albums.store_id', '=', 'stores.id')
                                    ->whereNull('store_albums.deleted_at')
                                    ->where('store_albums.store_id', $this->id);
                                    // ->where('store_albums.status', 'show');
                        })
                        ->join('store_album_images', function($join) {
                            $join->on('store_album_images.album_id', '=', 'store_albums.id');
                        })
                        ->join('user_collects', function($join) {
                            $join->on('user_collects.target_id', '=', 'store_album_images.id')
                                    ->where('user_collects.type', 'album_image')
                                    ->whereNull('user_collects.deleted_at');
                        });
        }
    }

    // relation Brand 品牌
    public function allBrands()
    {
        return $this->belongsToMany(Brand::class, (new Wdv2\ShareStoreLink)->getTable(), 'store_id', 'share_store_id')
                    ->withPivot('show_flag')
                    ->orderBy('pivot_show_flag', 'DESC');
    }

    // relation Brand 品牌
    public function brands()
    {
        return $this->allBrands()
                    ->wherePivot('show_flag', '!=', 0);
    }

    // relation primaryBrand 主要品牌
    public function primaryBrand()
    {
        return $this->allBrands()
                    ->wherePivot('show_flag', 2);
    }

    // relation SharePostBrand 分享文關聯表
    public function sharePostBrands()
    {
        return $this->hasManyThrough(
                ForumArticleBrand::class,
                Wdv2\ShareStoreLink::class,
                'store_id', // share_store_link.store_id
                'brand_id', // forum_article_brand.brand_id
                'id', // stores.id
                'share_store_id' // share_store_link.share_store_id
            )
            ->where('share_store_link.show_flag', '!=', 0)
            ->whereNull('forum_article_brand.deleted_at')
            ->whereHas('article', function($q) {
                $q->release();
            });
            // Relation 的 withCount('sharePostBrands')，join 會被蓋掉吶!?
            // ->join((new Wdv2\SharePost)->getTable(), function($join) {
            //     $join->on('share_posts.id', '=', 'share_post_brand.post_id')
            //             ->where('share_posts.status', 'published');
            // })
            // // 需排除帳號停用的作者
            // ->join((new User)->getTable(), function($join) {
            //     $join->on('share_posts.user_id', '=', 'users.id')
            //             ->where('users.status', '!=', 'delete');
            // });
    }

    // relation SharePostBrand 有評分的分享文關聯表
    public function hasRankSharePostBrands()
    {
        return $this->sharePostBrands()->where('rank', '>', 0);
    }

    // relation StoreOrderSet 發票資訊
    public function orderSet()
    {
        return $this->hasOne(Wdv2\StoreOrderSet::class);
    }

    // relation Order 訂單
    public function orders()
    {
        return $this->hasMany(Wdv2\Order::class)->live();
    }

    // relation Order 未付款的訂單
    public function unpaidOrders()
    {
        return $this->orders()->unpaid();
    }

    // relation Order 合約(設定費)訂單
    public function contractOrders()
    {
        return $this->orders()
                    ->paid()
                    ->whereHas('orderPackages', function ($q1) {
                        $q1->whereHas('orderProduct', function ($q2) {
                            $q2->where('type', 1);
                        });
                    });
    }

    // relation marks 商家標記
    public function marks()
    {
        $setTag             = new Wdv2\SetTag;
        $setTagRelationship = new Wdv2\SetTagRelationship;

        // 所有商家標記
        $tagIds = $setTag->storeMarks()->pluck('id');

        return $this->belongsToMany(Wdv2\SetTag::class, $setTagRelationship->getTable(), 'object_id')
                    ->wherePivot('type', 1)
                    ->whereIn($setTag->getTable().'.id', $tagIds);
    }

    // relation SetTag 車馬費 - Wdv2
    public function faresWdv2()
    {
        $setTag             = new Wdv2\SetTag;
        $setTagRelationship = new Wdv2\SetTagRelationship;

        // 所有車馬費區域
        $fareAreaIds = $setTag->fareAreas()->pluck('id');
        $fareIds     = $setTag->whereIn('parent', $fareAreaIds)->pluck('id');

        return $this->belongsToMany(Wdv2\SetTag::class, $setTagRelationship->getTable(), 'object_id')
                    ->wherePivot('type', 1)
                    ->withPivot('number')
                    ->whereIn($setTag->getTable().'.id', $fareIds);
    }

    // relation StoreFare 車馬費
    public function fares()
    {
        return $this->belongsToMany(CityData::class, (new StoreFare)->getTable(), 'store_id', 'city_id')
                    ->withPivot('value', 'number')
                    ->sort();
    }

    // relation StoreFare 免車馬費
    public function freeFares()
    {
        return $this->fares()
                    ->wherePivot('value', 1);
    }

    // relation activities 所有活動方案
    public function activities()
    {
        return $this->belongsToMany(Activity::class)->withPivot(['is_attend', 'content']);
    }

    // relation attendActivities 參與的活動方案
    public function attendActivities()
    {
        return $this->activities()->published()->wherePivot('is_attend', 1);
    }

    // relation StoreDiscount 所有優惠方案
    public function allDiscounts()
    {
        return $this->hasMany(StoreDiscount::class);
    }

    // relation StoreDiscount 發佈中的優惠方案
    public function discounts()
    {
        return $this->allDiscounts()->published();
    }

    // relation events 包含刪除的活動表單
    public function allEventsWithTrashed()
    {
        return $this->belongsToMany(Event::class)->withPivot('type')
                                                    ->withTimestamps();
    }

    // relation events 所有活動表單
    public function allEvents()
    {
        return $this->allEventsWithTrashed()->whereNull('event_store.deleted_at');
    }

    // relation events 進行中的活動表單
    public function events()
    {
        return $this->allEvents()->published();
    }

    // relation Reserve 詢問單
    public function reserves()
    {
        return $this->hasMany(Wdv2\Reserve::class)->live();
    }

    // relation StoreMember 團隊成員
    public function members()
    {
        return $this->hasMany(StoreMember::class)->sort();
    }

    // relation StoreFull 檔期
    public function full()
    {
        return $this->hasMany(Wdv2\StoreFull::class);
    }

    // relation LogGaPageView GA網頁瀏覽量
    public function logGaPageViews()
    {
        return $this->hasMany(LogGaPageView::class, 'target_id');
    }

    // relation LogGaPageView 子項目的GA網頁瀏覽量
    public function logGaPageViewsSubItems()
    {
        return $this->hasMany(LogGaPageView::class, 'parent_id');
    }

    // relation LogGaEvent GA事件
    public function logGaEvents()
    {
        return $this->hasMany(LogGaEvent::class, 'target_id')->where('type', '!=', 'quote');
    }

    // relation LogGaEvent 主動報價的GA事件
    public function logGaEventsQuotes()
    {
        return $this->hasMany(LogGaEvent::class, 'parent_id')->where('type', 'quote');
    }

    // relation StoreAnalytics 商家的GA分析紀錄
    public function analytics()
    {
        return $this->hasMany(Wdv2\StoreAnalytics::class);
    }

    // relation GoogleAnalytics 原始的GA分析紀錄
    public function rawAnalytics()
    {
        $googleAnalytics = new Wdv2\GoogleAnalytics;
        $analyticsType   = array_search('商家', $googleAnalytics->typeList);

        return $this->hasMany(Wdv2\GoogleAnalytics::class, 'object_id')
                    ->where('type', $analyticsType);
    }

    // relation StoreUser 商家所有帳號
    public function allAccounts()
    {
        return $this->belongsToMany(StoreUser::class, (new StoreUserRelationship)->getTable())
                    ->withTrashed()
                    ->withPivot('store_id', 'store_user_id', 'line_quote', 'line_message', 'line_event', 'deleted_at')
                    ->withTimestamps();
    }

    // relation StoreUser 商家帳號
    public function accounts()
    {
        return $this->allAccounts()
                    ->wherePivot('deleted_at', NULL)
                    ->where((new StoreUser)->getTable().'.status', '!=', 'delete');
    }

    // relation ServiceStopApply 下架申請
    public function serviceStopApply()
    {
        return $this->hasMany(Wdv2\ServiceStopApply::class, 'store_id', 'id');
    }

    // relation CardRecord 信用卡紀錄
    public function cardRecord()
    {
        return $this->hasMany(Wdv2\CardRecord::class);
    }

    // relation LogFreeStoreOrderSet 贈送設定費/使用費的設定紀錄
    public function logFreeStoreOrderSets()
    {
        return $this->hasMany(LogFreeStoreOrderSet::class);
    }

    // relation CurrencyPackage 貨幣發放清單
    public function currencies()
    {
        return $this->hasMany(Wdv2\CurrencyPackage::class)->live();
    }

    // relation CurrencyPackage 可用的W幣
    public function usableWcoins()
    {
        return $this->currencies()->expiration()->Wcoin();
    }

    // relation CurrencyPackage 可用的折讓金
    public function usableAllowances()
    {
        return $this->currencies()->expiration()->allowance();
    }

    // relation StoreQuote 商家的公開報價
    public function storeQuotes()
    {
        return $this->hasMany(Wdv2\StoreQuote::class);
    }

    // relation FirestoreMessage Firestore的訊息記錄
    public function firestoreMessages()
    {
        return $this->hasMany(FirestoreMessage::class);
    }

    // relation StoreIntegral 商家排行積分
    public function integrals()
    {
        return $this->hasMany(StoreIntegral::class);
    }

    // relation InvoiceSetting 發票設定
    public function invoiceSetting()
    {
        return $this->hasOne(InvoiceSetting::class);
    }

    // relation StoreAutoReply 公版自動回覆訊息
    public function defaultAutoReply()
    {
        return $this->hasOne(StoreAutoReplyDefault::class, 'store_type', 'type')
                    ->where('type', 'first_auto_reply');
    }

    // relation StoreAutoReply 自動回覆訊息
    public function autoReply()
    {
        return $this->hasOne(StoreAutoReply::class);
    }

    // relation LineGroup LINE群組
    public function lineGroup()
    {
        return $this->hasOne(LineGroup::class);
    }

    // relation LineGroup LINE群組
    public function showLineGroup()
    {
        return $this->lineGroup()->where('is_show', 1);
    }

    // scope deleted 停用
    public function scopeDeleted($query)
    {
        return $query->where($this->getTable().'.status', 'delete');
    }

    // scope pending 編輯中
    public function scopePending($query)
    {
        return $query->where($this->getTable().'.status', 'pending');
    }

    // scope leave 下架
    public function scopeLeave($query)
    {
        return $query->where($this->getTable().'.status', 'leave');
    }

    // scope live 未停用
    public function scopeLive($query)
    {
        return $query->where($this->getTable().'.status', '!=', 'delete');
    }

    // scope published 上架的
    public function scopePublished($query, $type = NULL)
    {
        // 特定類型
        if ($type) {
            $query = $query->where($this->getTable().'.type', $type);
        }

        return $query->where($this->getTable().'.status', 'published');
    }

    // scope status 特定狀態
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // scope type 特定類型
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }

    // scope emailNotEmpty Email不為空
    public function scopeEmailNotEmpty($query)
    {
        return $query->where(function ($q) {
                        $q->whereNotNull('email')
                            ->where('email', '!=', '');
                    });
    }

    // scope quoteEmailSubscribers 排除取消訂閱-公開詢價
    public function scopeQuoteEmailSubscribers($query)
    {
        return $query->whereDoesntHave('subscription', function ($q) {
                            $q->where('email_quote', 0);
                        });
    }

    // scope usageActive 使用費限制內
    public function scopeUsageActive($query)
    {
        // 改用 join 提升效率
        // return $query->whereDoesntHave('orderSet', function ($q) {
        //     $q->where('usage_notice', '100%');
        // });
        return $query->leftJoin((new Wdv2\StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope searchKeyword 關鍵字搜尋
    public function scopeSearchKeyword($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchKeywordForAlbum 關鍵字搜尋 For 相本一覽篩選
    public function scopeSearchKeywordForAlbum($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q1) use ($keywords) {
                        foreach ($keywords as $val) {
                            $q1 = $q1->orWhere('stores.name', 'like', '%' . $val . '%')
                                        ->orWhereHas('showAlbums', function ($q2) use ($val) {
                                            $q2 = $q2->where('stores.name', 'like', '%' . $val . '%');
                                        });
                        }
                    })
                    ->with(['showAlbums' => function($qa) use ($keyword) {
                        $qa->select('store_albums.id', 'store_albums.store_id')
                            ->join($this->getTable(), function($join) {
                                $join->on('stores.id', '=', 'store_albums.store_id');
                            })
                            ->searchKeywordWithStore($keyword);
                    }]);
    }

    // scope searchKeywordForVideo 關鍵字搜尋 For 影片一覽篩選
    public function scopeSearchKeywordForVideo($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q1) use ($keywords) {
                        foreach ($keywords as $val) {
                            $q1 = $q1->orWhere('stores.name', 'like', '%' . $val . '%')
                                        ->orWhereHas('showVideos', function ($q2) use ($val) {
                                            $q2 = $q2->where('stores.name', 'like', '%' . $val . '%');
                                        });
                        }
                    })
                    ->with(['showVideos' => function($qa) use ($keyword) {
                        $qa->select('store_videos.id', 'store_videos.store_id')
                            ->join($this->getTable(), function($join) {
                                $join->on('stores.id', '=', 'store_videos.store_id');
                            })
                            ->searchKeywordWithStore($keyword);
                    }]);
    }

    // scope searchKeywordForService 關鍵字搜尋 For 方案一覽篩選
    public function scopeSearchKeywordForService($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q1) use ($keywords) {
                        foreach ($keywords as $val) {
                            $q1 = $q1->orWhere('stores.name', 'like', '%' . $val . '%')
                                        ->orWhereHas('showServicesWithActivity', function ($q2) use ($val) {
                                            $q2 = $q2->where('stores.name', 'like', '%' . $val . '%');
                                        });
                        }
                    })
                    ->with(['showServicesWithActivity' => function($qs) use ($keyword) {
                        $qs->select('store_services.id', 'store_services.store_id')
                            ->join($this->getTable(), function($join) {
                                $join->on('stores.id', '=', 'store_services.store_id');
                            })
                            ->searchKeywordWithStore($keyword);
                    }]);
    }

    // scope searchKeywordForVenueRoom 關鍵字搜尋 For 廳房一覽篩選
    public function scopeSearchKeywordForVenueRoom($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q1) use ($keywords) {
                        foreach ($keywords as $val) {
                            $q1 = $q1->orWhere('stores.name', 'like', '%' . $val . '%')
                                        ->orWhereHas('showVenueRooms', function ($q2) use ($val) {
                                            $q2 = $q2->where('stores.name', 'like', '%' . $val . '%');
                                        });
                        }
                    })
                    ->with(['showVenueRooms' => function($qr) use ($keyword) {
                        $qr->select('venue_rooms.id', 'venue_rooms.store_id')
                            ->join($this->getTable(), function($join) {
                                $join->on('stores.id', '=', 'venue_rooms.store_id');
                            })
                            ->searchKeywordWithStore($keyword);
                    }]);
    }

    // scope searchWeddingDate 婚期搜尋
    public function scopeSearchWeddingDate($query, $date)
    {
        return $query->where(function ($q1) use ($date) {
            // 上午沒有滿檔
            $q1 = $q1->whereDoesntHave('full', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 1);
                    })
                    // 或下午沒有滿檔
                    ->orWhereDoesntHave('full', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 2);
                    });
        });
    }

    // scope freeShopTasting 免費門市試吃
    public function scopeFreeShopTasting($query, $freeShopTasting = false)
    {
        if (!$freeShopTasting) {
            return $query;
        }

        return $query->whereHas('descriptions', function ($q) {
                        $q = $q->where('key', 'has_shop_tasting')
                                ->where('value', 1);
                    })
                    ->whereHas('descriptions', function ($q) {
                        $q = $q->where('key', 'shop_tasting_min_fee')
                                ->where('value', 0);
                    });
    }

    /**
     * 取得主要品牌
     *
     * @return model $brand
     */
    public function getPrimaryBrand()
    {
        return $this->primaryBrand()->first();
    }

    /**
     * 是否有檔期的商家類型
     *
     *  @param $storeType 商家類型
     *  @return bool
     */
    public function hasScheduleDateByType($storeType = NULL)
    {
        $storeType = $storeType ?: $this->type;

        return in_array($storeType, array_keys($this->quoteTypeList));
    }

    /**
     * 取得基本設定費 (每月最低只要多少元)
     * [新娘秘書/婚攝/婚錄/婚禮主持人] 的設定費：599
     * [禮服] 的設定費：699
     * [婚禮佈置] 的設定費：799
     * [拍婚紗] 的設定費：999
     *
     *  @param $storeType 商家類型
     *  @param $payDiscount 是否為折抵
     * @return int $fixed_price
     */
    public function getBasicSettingFee($storeType = NULL, $payDiscount = false)
    {
        $storeType = $storeType ?: $this->type;

        $orderProduct      = new Wdv2\OrderProduct;
        $orderProductArray = $payDiscount ? $orderProduct->PAY_DISCOUNT : $orderProduct->SETTING_FEE;
        $orderProductId    = $orderProductArray[$storeType] ?? '';
        $orderProduct      = $orderProduct->find($orderProductId);

        return $orderProduct->fixed_price ?? '';
    }

    /**
     * 取得訊息單價費用 (一則訊息費用為 xxx 元)
     * [新娘秘書/婚攝/婚錄/婚禮主持人] 的訊息費用：150
     * [禮服] 的訊息費用：175
     * [婚禮佈置] 的訊息費用：200
     * [拍婚紗] 的訊息費用：250
     *
     *  @param $storeType 商家類型
     * @return int $fixed_price
     */
    public function getMessageUnitPrice($storeType = NULL)
    {
        $storeType = $storeType ?: $this->type;

        $orderProduct   = new Wdv2\OrderProduct;
        $orderProductId = $orderProduct->MESSAGE_FEE[$storeType] ?? '';
        $orderProduct   = $orderProduct->find($orderProductId);

        return $orderProduct->fixed_price ?? '';
    }

    /**
     * 取得貨幣餘額
     * @param string $type 類型 Wcoins:W幣 allowances:折讓金
     *
     * @return int $amount
     */
    public function getCurrencyBalance($type)
    {
        return $this->{'usable'.$type.'s'}
                    ->sum(function ($item) {
                        return $item->amount - $item->used_amount;
                    });
    }

    /**
     * 更新商家的價格區間
     *
     *  @return void
     */
    public function updatePriceRange()
    {
        $this->min_price = NULL;
        $this->max_price = NULL;

        // 婚禮佈置/喜餅的價格在作品裡...喜餅的價格在禮盒裡...
        if (in_array($this->type, [6, 10])) {

            // 婚禮佈置/喜餅的所有作品
            foreach ($this->showAlbums as $album) {

                // 作品的價格區間
                $minPrice = $album->storeTypeAlbum->min_price ?? NULL;
                $maxPrice = $album->storeTypeAlbum->max_price ?? NULL;

                // 商家的價格區間
                $this->getStorePrice($minPrice, $maxPrice);

            }
        } else {

            // 商家的所有服務方案
            foreach ($this->showServicesWithActivity as $service) {

                // 商家的價格區間
                $this->getStorePrice($service->min_price, $service->max_price);
            }
        }

        $this->save();
    }

    /**
     * 取得商家的價格區間
     */
    private function getStorePrice($minPrice, $maxPrice)
    {
        if (!is_null($minPrice) && (!$this->min_price || $this->min_price > $minPrice)) {
            $this->min_price = $minPrice;
        }
        if (!is_null($maxPrice) && (!$this->max_price || $this->max_price < $maxPrice)) {
            $this->max_price = $maxPrice;
        }
    }

    /**
     * 取得商家後台的蓋板公告通知列表
     *
     * @return mixed
     */
    public function getAdminNotifyList()
    {
        $storeNotifies = StoreNotify::select('key', 'is_mandatory', 'title', 'content', 'link_text', 'link_subdomain', DB::raw("REPLACE(link_path, '{store_id}', '".$this->id."') link_path"))
                                        ->published()
                                        ->whereJsonContains('type_values', $this->type)
                                        ->whereJsonContains('status_values', $this->status)
                                        ->whereDoesntHave('notReminds', function($q) {
                                            $q->where('store_id', $this->id);
                                        })
                                        ->sort();

        // 驗證發票資訊是否設定完成
        if (!empty($this->orderSet->invoice_contact->invoiceType) && // 發票類型不為空
            ($this->orderSet->invoice_contact->invoiceType == 2 || $this->orderSet->store_detail->cropNo) && // 發票類型：二聯 or 三聯+統編
            $this->orderSet->store_detail->cropName // 公司名稱（商家名稱）
        ) {
            $storeNotifies->where('key', '!=', 'set_storeOrderSet_202208');
        }

        $storeNotifies = $storeNotifies->get();

        // 商家方案資料改版 service_update_202303
        $storeNotifies = $storeNotifies->map(function ($storeNotifie) {
            if ($storeNotifie->key == 'service_update_202303') {
                $notUpdatedServices = $this->showServicesWithoutActivity()->whereNull('edited_at')->get();
                $storeNotifie->link_items = $notUpdatedServices->map(function ($service) {
                    return [
                        'text' => $service->name,
                        'path' => '/admin/'.$this->id.'/setting/service/'.$service->id,
                    ];
                });
                $storeNotifie->content = str_replace('{number}', $notUpdatedServices->count(), $storeNotifie->content);
            }
            return $storeNotifie;
        });

        return $storeNotifies;
    }

    /**
     * 取得特定已發佈商家的上一個作品
     *
     * @param $store 商家
     * @param $album 商家作品
     * @return mixed
     */
    public function getLastReleaseAlbum($album)
    {
        // 上一個排序的作品
        $data = $this->getReleaseAlbums(1, [$album->id], $album->sort, '<=', 'reverseSort');

        // 沒有的話，取最後一個排序的作品
        if (!$data->count()) {
            $data = $this->getReleaseAlbums(1, [$album->id], NULL, NULL, 'reverseSort');
        }

        return $data->first();
    }

    /**
     * 取得已發佈的作品列表(排除自己的其他推薦作品)
     * 排序旗標之前的項目，要往後排 ex.排序旗標=4 : 123456 => 456123
     *
     * @param $limit 數量
     * @param $album 作品
     * @return mixed
     */
    public function getOtherReleaseAlbums($limit, $album)
    {
        // 旗標之後的項目
        $data = $this->getReleaseAlbums($limit, [$album->id], $album->sequence, '>');

        // 數量不足的話，再加入旗標之前的項目
        $limit -= $data->count();
        if ($limit) {
            $_temp = $this->getReleaseAlbums($limit, [$album->id], $album->sequence, '<=');
            $data = $data->merge($_temp);
        }

        return $data;
    }

    /**
     * 取得已發佈的作品列表(其他推薦作品)
     *
     * @param $limit 數量
     * @param $excludeIds 需排除的作品編號
     * @param $sortFlag 旗標
     * @param $operation 運算符
     * @param $stoting 排序 sort:預設排序 reverseSort:反向排序
     * @return model $storeAlbums
     */
    public function getReleaseAlbums($limit, $excludeIds = [], $sortFlag = NULL, $operation = NULL, $stoting = 'sort')
    {
        // 上架作品集
        $albums = $this->albums()
                        ->status('show')
                        ->select('id', 'store_id', 'name', 'cover_id');

        // 取得數據
        $albums = $albums->withCount(['images']);

        // 需排除的作品編號
        if ($excludeIds) {
            $albums = $albums->whereNotIn('id', $excludeIds);
        }

        // 旗標前/後的項目
        if ($sortFlag && $operation) {
            $albums = $albums->where('sequence', $operation, $sortFlag);
        }

        // 數量限制
        $albums = $albums->limit($limit);

        // 排序
        $albums = $albums->{$stoting}();

        return $albums->get();
    }

    /**
     * 取得已發佈的作品列表 By Request
     *
     * @param $request
     * @return model $storeAlbums
     */
    public function getReleaseAlbumsByRequest($request)
    {
        $typeKey = $this->typeKeyList[$this->type];

        // 上架作品集
        $albums = $this->albums()
                        ->status('show')
                        ->select('store_albums.id', 'store_id', 'name', 'cover_id');

        // 取得數據
        $albums = $albums->withCount(['images']);

        // 價格區間 price_range[] (聯集)
        if ($request['price_range']) {
            $albums = $albums->whereHas($typeKey.'Album', function ($q1) use ($request) {
                $q1->where(function ($q2) use ($request) {
                        foreach ($request['price_range'] as $key) {
                            $q2->orWhere(function ($q3) use ($key) {
                                    $q3->priceRangeByKey($key);
                                });
                        }
                    });
            });
        }

        // 類型 types[] (聯集)
        if ($request['types']) {
            $albums = $albums->whereHas($typeKey.'Album', function ($q1) use ($request) {
                $q1->where(function ($q2) use ($request) {
                        foreach ($request['types'] as $key) {
                            $q2->orWhere('type', $key);
                        }
                    });
            });
        }

        // 拍攝地點 image_locations
        // if ($request['image_locations']) {
        //     $albums = $albums->join((new StoreAlbumImage)->getTable(), function($join) {
        //                         $join->on('store_album_images.album_id', '=', 'store_albums.id');
        //                     })
        //                     ->join((new StudioAlbumImage)->getTable(), function($join) {
        //                         $join->on('studio_album_images.album_image_id', '=', 'store_album_images.id');
        //                     })
        //                     ->join((new CityData)->getTable(), function($join) use ($request) {
        //                         $join->on('city_data.id', '=', 'studio_album_images.city_id')
        //                                 ->whereIn('studio_album_images.city_id', $request['image_locations']);
        //                     });
        // }

        // 作品集標籤 album_tags[] (聯集)
        if ($request['album_tags']) {

            // 作品集標籤篩選條件是聯集(AND)，將原本多次join改為子查詢
            $albums = $albums->whereIn((new StoreAlbum)->getTable().'.id', function($sub) use ($request) {
                $sub->select('target_id')
                    ->from('store_tag_pivot')
                    ->whereIn('store_tag_id', $request['album_tags'])
                    ->where('target_type', 'album')
                    ->groupBy('target_id')
                    ->havingRaw('COUNT(id) = ?', [count($request['album_tags'])]);
            });
        }

        // 作品照標籤 image_tags[] (聯集)
        // if ($request['image_tags']) {

        //     // 作品照標籤篩選條件是聯集(AND)，將原本多次join改為子查詢
        //     $albums = $albums->whereIn((new StoreAlbum)->getTable().'.id', function($sub) use ($request) {
        //         $sub->select('album_id')
        //             ->from('store_tag_pivot')
        //             ->join((new StoreAlbumImage)->getTable(), function($join) {
        //                 $join->on('store_album_images.id', '=', 'store_tag_pivot.target_id');
        //             })
        //             ->whereIn('store_tag_id', $request['image_tags'])
        //             ->where('target_type', 'album_image')
        //             ->groupBy('album_id', 'target_id')
        //             ->havingRaw('COUNT(DISTINCT store_tag_id) = ?', [count($request['image_tags'])]);
        //     });
        // }

        // 人氣排序 sort:hot
        if ($request['sort'] == 'hot') {
            $albums = $albums->orderBy('rank', 'DESC');

        // 最新排序 sort:update
        } elseif ($request['sort'] == 'update') {
            $albums = $albums->orderBy('edited_at', 'DESC');

        // 價格低到高 sort:minPrice
        } elseif ($request['sort'] == 'minPrice') {
            $joinModel   = resolve('App\Models\\'.Str::studly($typeKey).'Album');
            $priceColumn = ($this->type == 2) ? 'price' : 'min_price';

            // 沒有價格的放最後
            $albums = $albums->join($joinModel->getTable(), function($join) {
                                $join->on('album_id', '=', 'store_albums.id');
                            })
                            ->orderByRaw("ISNULL($priceColumn), $priceColumn = 0, $priceColumn", 'ASC');

        // 價格高到低 sort:maxPrice
        } elseif ($request['sort'] == 'maxPrice') {
            $joinModel   = resolve('App\Models\\'.Str::studly($typeKey).'Album');
            $priceColumn = ($this->type == 2) ? 'price' : 'max_price';
            $albums = $albums->join($joinModel->getTable(), function($join) {
                                $join->on('album_id', '=', 'store_albums.id');
                            })
                            ->orderBy($priceColumn, 'DESC');
        }

        // 推薦排序 sort:recommend(default)
        $albums = $albums->sort();

        // 分頁 page
        return $albums->paginate(48);
    }

    /**
     * 取得已發佈的影片列表(排除自己的其他推薦影片)
     * 排序旗標之前的項目，要往後排 ex.排序旗標=4 : 123456 => 456123
     *
     * @param $limit 數量
     * @param $video 影片
     * @return mixed
     */
    public function getOtherReleaseVideos($limit, $video)
    {
        // 旗標之後的項目
        $data = $this->getReleaseVideos($limit, [$video->id], $video->sequence, '>');

        // 數量不足的話，再加入旗標之前的項目
        $limit -= $data->count();
        if ($limit) {
            $_temp = $this->getReleaseVideos($limit, [$video->id], $video->sequence, '<=');
            $data = $data->merge($_temp);
        }

        return $data;
    }

    /**
     * 取得已發佈的影片列表(其他推薦影片)
     *
     * @param $limit 數量
     * @param $excludeIds 需排除的影片編號
     * @param $sortFlag 旗標
     * @param $operation 運算符
     * @return model $storeVideos
     */
    public function getReleaseVideos($limit, $excludeIds = [], $sortFlag = NULL, $operation = NULL)
    {
        // 上架影片
        $videos =  $this->videos()
                        ->status('show')
                        ->select('id', 'store_id', 'name', 'cover_url');

        // 需排除的影片編號
        if ($excludeIds) {
            $videos = $videos->whereNotIn('id', $excludeIds);
        }

        // 旗標前/後的項目
        if ($sortFlag && $operation) {
            $videos = $videos->where('sequence', $operation, $sortFlag);
        }

        // 數量限制
        $videos = $videos->limit($limit);

        // 排序
        $videos = $videos->sort();

        return $videos->get();
    }

    /**
     * 取得已發佈的影片列表 By Request
     *
     * @param $request
     * @return model $storeVideos
     */
    public function getReleaseVideosByRequest($request)
    {
        // 上架影片
        $videos = $this->videos()
                        ->status('show')
                        ->select('id', 'store_id', 'name', 'cover_url');

        // 人氣排序 sort:hot
        if ($request['sort'] == 'hot') {
            $videos = $videos->orderBy('rank', 'DESC');
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $videos = $videos->orderBy('edited_at', 'DESC');
        }

        // 推薦排序 sort:recommend(default)
        $videos = $videos->sort();

        // 分頁 page
        return $videos->paginate(48);
    }

    /**
     * 取得已發佈的方案列表(排除自己的其他推薦方案)
     * 排序旗標之前的項目，要往後排 ex.排序旗標=4 : 123456 => 456123
     *
     * @param $limit 數量
     * @param $service 方案
     * @return mixed
     */
    public function getOtherReleaseServices($limit, $service)
    {
        // 旗標之後的項目
        $data = $this->getReleaseServices($limit, [$service->id], $service->sequence, '>');

        // 數量不足的話，再加入旗標之前的項目
        $limit -= $data->count();
        if ($limit) {
            $_temp = $this->getReleaseServices($limit, [$service->id], $service->sequence, '<=');
            $data = $data->merge($_temp);
        }

        return $data;
    }

    /**
     * 取得已發佈的方案列表(其他推薦方案)
     *
     * @param $limit 數量
     * @param $excludeIds 需排除的影片編號
     * @param $sortFlag 旗標
     * @param $operation 運算符
     * @return model $storeVideos
     */
    public function getReleaseServices($limit, $excludeIds = [], $sortFlag = NULL, $operation = NULL)
    {
        // 上架方案
        $services = $this->showServicesWithActivity()
                            ->select('id', 'store_id', 'activity_id', 'name', 'description', 'min_price', 'max_price', 'type')
                            ->with('userCollects:id');

        // 需排除的方案編號
        if ($excludeIds) {
            $services = $services->whereNotIn('id', $excludeIds);
        }

        // 旗標前/後的項目
        if ($sortFlag && $operation) {
            $services = $services->where('sequence', $operation, $sortFlag);
        }

        // 數量限制
        $services = $services->limit($limit);

        return $services->get();
    }

    /**
     * 取得已發佈的方案列表 By Request
     *
     *  @param $request
     *  @return model
     */
    public function getReleaseServicesByRequest()
    {
        return $this->showServicesWithActivity()
                    ->select('id', 'store_id', 'activity_id', 'name', 'description', 'min_price', 'max_price', 'type')
                    ->with('userCollects:id')
                    ->paginate(48);
    }

    /**
     * 取得特定已發佈商家的上一個廳房
     *
     * @param $room 婚宴廳房
     * @return mixed
     */
    public function getLastReleaseVenueRoom($room)
    {
        // 上一個排序的廳房
        $data = $this->getReleaseVenueRooms(1, [$room->id], $room->sequence, '<=', 'reverseSort');

        // 沒有的話，取最後一個排序的廳房
        if (!$data->count()) {
            $data = $this->getReleaseVenueRooms(1, [$room->id], NULL, NULL, 'reverseSort');
        }

        return $data->first();
    }

    /**
     * 取得已發佈的廳房列表(排除自己的其他推薦廳房)
     * 排序旗標之前的項目，要往後排 ex.排序旗標=4 : 123456 => 456123
     *
     * @param $limit 數量
     * @param $room 婚宴廳房
     * @return mixed
     */
    public function getOtherReleaseVenueRooms($limit, $room)
    {
        // 不是婚宴場地，直接回空集合
        if ($this->type != 5) {
            return collect([]);
        }

        // 旗標之後的項目
        $data = $this->getReleaseVenueRooms($limit, [$room->id], $room->sequence, '>');

        // 數量不足的話，再加入旗標之前的項目
        $limit -= $data->count();
        if ($limit) {
            $_temp = $this->getReleaseVenueRooms($limit, [$room->id], $room->sequence, '<=');
            $data = $data->merge($_temp);
        }

        return $data;
    }

    /**
     * 取得已發佈的廳房列表(其他推薦廳房)
     *
     * @param $limit 數量
     * @param $excludeIds 需排除的影片編號
     * @param $sortFlag 旗標
     * @param $operation 運算符
     * @param $stoting 排序 sort:預設排序 reverseSort:反向排序
     * @return model $venueRooms
     */
    public function getReleaseVenueRooms($limit, $excludeIds = [], $sortFlag = NULL, $operation = NULL, $stoting = 'sort')
    {
        // 不是婚宴場地，直接回空集合
        if ($this->type != 5) {
            return collect([]);
        }

        // 上架廳房
        $rooms = $this->venueRooms()
                        ->status('show')
                        ->select('id', 'store_id', 'name', 'min_number', 'max_number', 'narrates', 'cover_id');

        // 取得數據
        $rooms = $rooms->withCount(['userCollects']);

        // 需排除的影片編號
        if ($excludeIds) {
            $rooms = $rooms->whereNotIn('id', $excludeIds);
        }

        // 旗標前/後的項目
        if ($sortFlag && $operation) {
            $rooms = $rooms->where('sequence', $operation, $sortFlag);
        }

        // 數量限制
        $rooms = $rooms->limit($limit);

        // 排序
        $rooms = $rooms->{$stoting}();

        return $rooms->get();
    }

    /**
     * 取得已發佈的廳房列表 By Request
     *
     *  @param $request
     * @return model $storeVideos
     */
    public function getReleaseVenueRoomsByRequest($request)
    {
        // 上架廳房
        $rooms = $this->venueRooms()
                        ->status('show')
                        ->select('id', 'store_id', 'name', 'min_number', 'max_number', 'narrates', 'cover_id');

        // 取得數據
        $rooms = $rooms->withCount(['userCollects']);

        // 人氣排序 sort:hot
        if ($request['sort'] == 'hot') {
            $rooms = $rooms->orderBy('rank', 'DESC');
        }

        // 最新排序 sort:update
        if ($request['sort'] == 'update') {
            $rooms = $rooms->orderBy('edited_at', 'DESC');
        }

        // 推薦排序 sort:recommend(default)
        $rooms = $rooms->sort();

        // 分頁 page
        return $rooms->paginate(48);
    }

    /**
     * 取得合約到期日
     *
     * @return date $contractDeadline
     */
    public function getContractDeadline()
    {
        // 檢查發票資訊
        if (!$this->orderSet) {
            return NULL;
        }

        if ($this->orderSet->free_setting_times == '永久') {
            return '永久';
        } elseif ($this->orderSet->free_setting_times) {
            $log = $this->logFreeStoreOrderSets()
                        ->where('column', 'free_setting_times')
                        ->orderBy('created_at', 'DESC')
                        ->first();
            return date('Y-m-d', strtotime($log->free_end_at));
        } elseif ($this->orderSet->deadline) {
            return date('Y-m-d', strtotime($this->orderSet->deadline));
        }

        return NULL;
    }
}
