<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WeddingcakeShop extends Model
{
    protected $guarded = [];

    // relation openingHours 門市的營業時間列表
    public function openingHours()
    {
        return $this->hasMany(StoreOpeningHour::class, 'target_id')
                    ->where('target_type', 'weddingcake_shop');
    }

    // relation CityData 縣市
    public function city()
    {
        return $this->belongsTo(CityData::class, 'city_id');
    }

    // relation AreaData 地區
    public function area()
    {
        return $this->belongsTo(AreaData::class, 'area_id');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }
}
