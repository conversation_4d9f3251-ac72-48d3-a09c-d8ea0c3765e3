<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LogNewsleopardResponse extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'data'       => 'object',
        'content'    => 'object',
        'created_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // relation LogSurenotifyWebhook
    public function surenotifyWebhook()
    {
        return $this->hasMany(LogSurenotifyWebhook::class, 'message_id', 'message_id');
    }
}
