<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class WeddingcakeCookie extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    // relation items 喜餅品項
    public function items()
    {
        return $this->hasMany(WeddingcakeCookieItem::class);
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }
}
