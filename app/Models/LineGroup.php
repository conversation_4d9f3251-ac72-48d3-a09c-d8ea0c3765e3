<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LineGroup extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected $dates = ['leaved_at'];

    // relation Store 所屬商家
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    // relation Image
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'line_group_qrcode');
    }

    // relation LineGroupMessage 訊息記錄
    public function messages()
    {
        return $this->hasMany(LineGroupMessage::class, 'group_id', 'group_id');
    }

    // relation LineGroupMessage 近期的訊息記錄
    public function recentlyMessages()
    {
        return $this->messages()->recently();
    }

    // relation LineGroupMessage 近期的發言者
    public function recentlySpeakers()
    {
        return $this->recentlyMessages()->groupBy('user_id');
    }
}
