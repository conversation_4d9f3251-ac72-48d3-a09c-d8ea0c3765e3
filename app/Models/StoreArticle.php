<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class StoreArticle extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // relation BlogArticle
    public function blog()
    {
        return $this->belongsTo(BlogArticle::class, 'blog_article_id');
    }

    // scope type
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }
}
