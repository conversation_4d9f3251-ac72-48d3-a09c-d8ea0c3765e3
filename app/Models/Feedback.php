<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class Feedback extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\FeedbackPresenter';

    // 身份 user:新人 store:商家
    public $identityList = [
        'user'  => '新人',
        'store' => '商家',
    ];

    // 狀態 pending:待處理 processing:處理中 completed:已完成
    public $statusList = [
        'pending'    => '待處理',
        'processing' => '處理中',
        'completed'  => '已完成',
    ];

    // relation User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // relation Image
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')->where('type', 'feedback');
    }

    // relation FeedbackReply
    public function replies()
    {
        return $this->hasMany(FeedbackReply::class);
    }
}
