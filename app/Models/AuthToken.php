<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AuthToken extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;

    protected $guarded = [];

    public $timestamps = false;

    protected $dates = ['deadline_at'];

    // 類型 user:新娘前台登入 user_password:新娘前台修改密碼 store:商家後台登入 yzcube:神之後台登入
    public $statusList = [
        'user'            => '新娘前台登入',
        'user_password'   => '新娘前台修改密碼',
        'store'           => '商家後台登入',
        'yzcube'          => '神之後台登入',
        'yzcube_password' => '神之後台修改密碼',
        'blog'            => '部落格後台登入',
        'blog_yzcube'     => '部落格後台登入 (使用神之後台帳號)',
        'blog_password'   => '部落格後台修改密碼',
    ];

    // relation User
    public function user()
    {
        return $this->belongsTo(User::class, 'target_id')->live();
    }

    // relation YzcubeUser
    public function yzcube()
    {
        return $this->belongsTo(YzcubeUser::class, 'target_id')->live();
    }

    // relation StoreUser
    public function admin()
    {
        return $this->belongsTo(StoreUser::class, 'target_id')->live();
    }

    // relation StoreUser
    public function logs()
    {
        return $this->hasMany(LogAuthToken::class);
    }

    // scope type()
    public function scopeType($query, $type)
    {
        if (is_array($type)) {
            return $query->whereIn('type', $type);
        }
        return $query->where('type', $type);
    }

    // scope beforeDeadline
    public function scopeBeforeDeadline($query)
    {
        return $query->where(function ($q) {
            $q->where('deadline_at', '>', now())
                ->orWhereNull('deadline_at');
        });
    }
}
