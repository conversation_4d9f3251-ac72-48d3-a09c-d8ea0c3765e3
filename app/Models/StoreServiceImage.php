<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreServiceImage extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    // relation Image 方案說明照
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'store_service_image');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence')
                    ->orderBy('id', 'DESC');
    }
}
