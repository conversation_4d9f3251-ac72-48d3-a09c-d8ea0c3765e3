<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StoreNotify extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    // relation StoreNotifyNotRemind 商家後台的蓋板公告通知-不再提醒
    public function notReminds()
    {
        return $this->hasMany(StoreNotifyNotRemind::class);
    }

    // scope published 發佈中
    public function scopePublished($query)
    {
        return $query->where(function ($q) {
                            $q->whereNull('start_date')->orWhere('start_date', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('end_date')->orWhere('end_date', '>', now());
                        });
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('id');
    }
}
