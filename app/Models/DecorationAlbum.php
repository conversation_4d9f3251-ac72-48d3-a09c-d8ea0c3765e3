<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DecorationAlbum extends Model
{
    // 商家類型 6:婚禮佈置
    // public $storeType = 6;

    public $timestamps = false;

    protected $guarded = [];

    protected $casts = [
        'narrates' => 'array',
    ];

    // 地點類型
    public $locationTypeList = [
        1 => '自宅',
        2 => '路邊流水席',
    ];

    // 佈置類型
    public $typeList = [
        1 => '公版背板',
        2 => '客製化',
        3 => '道具出租',
        4 => 'Candy Bar',
    ];

    // 價格區間列表
    public $priceRangeList = [
        ['label' => '1萬以下', 'value' => ['', 10000]],
        ['label' => '1萬 ~ 1萬5', 'value' => [10000, 15000]],
        ['label' => '1萬5 ~ 2萬', 'value' => [15000, 20000]],
        ['label' => '2萬 ~ 2萬5', 'value' => [20000, 25000]],
        ['label' => '2萬5 ~ 3萬', 'value' => [25000, 30000]],
        ['label' => '3萬以上', 'value' => [30000, '']],
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // relation Brand 佈置地點
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    // scope priceRangeByKey 特定價格區間 (用於婚禮佈置，價格區間)
    public function scopePriceRangeByKey($query, $key)
    {
        // 取得價格區間的範圍
        $priceRange = $this->priceRangeList[$key]['value'];

        // 最低價以下
        if ($key == 0) {
            $query = $query->where('min_price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif ($key == count($this->priceRangeList) - 1) {
            $query = $query->where('max_price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            $query = $query->where(function ($q1) use ($priceRange) {
                $q1 = $q1->orWhereBetween('min_price', [$priceRange[0], $priceRange[1]])
                            ->orWhereBetween('max_price', [$priceRange[0], $priceRange[1]])
                            ->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->where('min_price', '<=', $priceRange[0])
                                        ->where('max_price', '>=', $priceRange[1]);
                            });
            });
        }

        return $query;
    }
}
