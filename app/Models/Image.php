<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Image extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;

    protected $guarded = [];

    public $timestamps = false;

    // 類型
    public $typeList = [
        'forum_article'           => '好婚聊聊文章',
        'forum_cover'             => '好婚聊聊封面照',
        'forum_comment'           => '好婚聊聊留言',
        'user_profile'            => '會員頭像',
        'user_fb_avatar'          => '會員FB頭像',
        'user_line_avatar'        => '會員LINE頭像',
        'user_google_avatar'      => '會員Google頭像',
        'wedding_groom'           => '新郎頭像',
        'wedding_bride'           => '新娘頭像',
        // 'share_post'              => 'W姐妹分享文',
        // 'share_cover'             => 'W姐妹封面照',
        // 'share_cover_backup'      => 'W姐妹封面照備份',
        // 'share_comment'           => 'W姐妹留言',
        'feedback'                => '意見回饋',
        'feedback_reply'          => '意見回饋的回覆',
        'ad_image'                => '廣告素材',
        'ad_image_xs'             => '廣告素材mobile版',
        'event_description'       => '活動說明',
        'event_image'             => '活動圖案名稱',
        'event_image_xs'          => '活動圖案名稱mobile版',
        'event_image_meta'        => '活動圖案名稱meta版',
        'event_product_image'     => '活動商品圖',
        'event_order_item'        => '訂購品項的上傳照',
        'store_logo'              => '商家LOGO',
        'store_cover'             => '商家封面照',
        'store_shop_tasting'      => '商家喜餅門市店內照片',
        'store_delivery_tasting'  => '商家喜餅宅配禮盒照片',
        'store_feature'           => '商家特色說明照',
        'store_service_cover'     => '商家方案封面照',
        'store_service_image'     => '商家方案說明照',
        'store_album_image'       => '商家作品照',
        'store_member'            => '商家成員照',
        'venue_dishes'            => '婚宴場地喜宴菜',
        'venue_room'              => '婚宴場地廳房照片',
        'weddingcake_cookie_item' => '喜餅品項照片',
        'activity_image'          => '活動方案活動視覺圖',
        'line_group_qrcode'       => '加入LINE群組QRcode',
        'dress_album_private_note_image'=> '婚紗禮服作品不公開備註',
        'meta_scraper'            => 'Meta爬蟲圖片',
        'blog_user'               => '部落格作者頭像',
    ];

    // relation SharePost
    public function post()
    {
        return $this->belongsTo(SharePost::class, 'target_id');
    }

    // relation ForumArticle
    public function article()
    {
        return $this->belongsTo(ForumArticle::class, 'target_id');
    }
}
