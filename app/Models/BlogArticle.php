<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BlogArticle extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // 部落格類型
    public $typeList = [
        'kol'  => '好婚鑑定團',
        'blog' => '好婚專欄',
    ];

    // 分類名稱 (部分key使用商家類型)
    public $categoryList = [
        2               => '禮服試穿',
        4               => '新秘試妝',
        5               => '婚宴場地直擊',
        'wedding_shoes' => '婚鞋試穿',
        'bride_cake'    => '喜餅試吃',
        'wedding_event' => '婚禮活動',
        'diamond_ring'  => '鑽戒體驗',
    ];

    use SoftDeletes;

    // relation Store
    public function stores()
    {
        return $this->belongsToMany(Store::class, 'store_articles')->orderBy('sequence');
    }

    // scope type
    public function scopeType($query, $type)
    {
        return $query->where('type', $type);
    }
}
