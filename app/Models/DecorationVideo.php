<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DecorationVideo extends Model
{
    // 商家類型 6:婚禮佈置
    // public $storeType = 6;

    public $timestamps = false;

    protected $guarded = [];

    // 地點類型
    public $locationTypeList = [
        1 => '自宅',
        2 => '路邊流水席',
    ];

    // relation Brand 佈置地點
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }
}
