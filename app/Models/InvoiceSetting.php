<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceSetting extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    // relation InvoiceProduct 發票品項
    public function products()
    {
        return $this->hasMany(InvoiceProduct::class)->sort();
    }

    // relation Invoice 發票記錄
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }
}
