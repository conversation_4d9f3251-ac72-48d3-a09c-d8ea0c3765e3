<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DressAlbum extends Model
{
    // 商家類型 2:婚紗禮服
    public $storeType = 2;

    public $timestamps = false;

    protected $guarded = [];

    // 價格區間列表
    public $priceRangeList = [
        ['label' => '1萬以下', 'value' => ['', 10000]],
        ['label' => '1萬 ~ 1萬5', 'value' => [10000, 15000]],
        ['label' => '1萬5 ~ 2萬', 'value' => [15000, 20000]],
        ['label' => '2萬 ~ 2萬5', 'value' => [20000, 25000]],
        ['label' => '2萬5 ~ 3萬', 'value' => [25000, 30000]],
        ['label' => '3萬以上', 'value' => [30000, '']],
    ];

    // scope priceRangeByKey 價格區間搜尋 (用於婚紗禮服，單一價格)
    public function scopePriceRangeByKey($query, $key)
    {
        // 取得價格區間的範圍
        $priceRange = $this->priceRangeList[$key]['value'];

        // 最低價以下
        if (!$priceRange[0]) {
            return $query->where('price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif (!$priceRange[1]) {
            return $query->where('price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            return $query->whereBetween('price', $priceRange);
        }
    }

    // 不公開備註的圖片
    public function privateNoteImages()
    {
        return $this->hasMany(Image::class, 'target_id')->where('type', 'dress_album_private_note_image');
    }
}
