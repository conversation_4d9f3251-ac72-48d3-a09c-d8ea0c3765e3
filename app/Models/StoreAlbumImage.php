<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreAlbumImage extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    // relation StoreAlbum 商家作品集
    public function album()
    {
        return $this->belongsTo(StoreAlbum::class)->status('show');
    }

    // relation Image 商家作品照
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'store_album_image');
    }

    // relation StoreTag 作品照標籤
    public function tags()
    {
        return $this->belongsToMany(StoreTag::class, 'store_tag_pivot', 'target_id')
                    ->wherePivot('target_type', 'album_image');
    }

    // relation 拍婚紗商家的照片欄位
    public function studioImage()
    {
        return $this->hasOne(StudioAlbumImage::class, 'album_image_id');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence')
                    ->orderBy('id', 'DESC');
    }
}
