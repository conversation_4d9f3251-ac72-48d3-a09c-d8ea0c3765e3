<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class Activity extends Model
{
    use SoftDeletes;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\ActivityPresenter';

    protected $guarded = [];

    protected $casts = [
        'store_types' => 'array',
    ];

    // 狀態 pending:尚未開始 prepared:準備中 published:進行中 completed:已結束 deleted:隱藏
    public $statusList = [
        'pending'   => '尚未開始',
        'prepared'  => '準備中',
        'published' => '進行中',
        'completed' => '已結束',
        'deleted'   => '隱藏',
    ];

    // relation Image
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'activity_image');
    }

    // relation Store 所有的參與商家
    public function allStores()
    {
        return $this->belongsToMany(Store::class)->withTrashed();
    }

    // relation Store 參與商家
    public function stores()
    {
        return $this->allStores()->withPivot(['is_attend', 'content']);
    }

    // relation attendSores 是否參與的商家
    public function attendSores($isAttend)
    {
        return $this->allStores()->wherePivot('is_attend', $isAttend);
    }

    // relation StoreService 活動方案
    public function services()
    {
        return $this->hasMany(StoreService::class);
    }

    // scope storeType 商家類型
    public function scopeStoreType($query, $storeType)
    {
        return $query->where(function($q) use ($storeType) {
                        $q->whereJsonContains('store_types', $storeType)
                            ->orWhereNull('store_types');
                    });
    }

    // scope published 發佈中
    public function scopePublished($query)
    {
        return $query->where('start_date', '<=', now())
                        ->where('end_date', '>', now());
    }

    // scope storeHasAttend 有參加的商家
    public function scopeStoreHasAttend($query, $storeId)
    {
        return $query->whereHas('stores', function ($q) use ($storeId) {
                        $q->where('stores.id', $storeId)
                            ->where('is_attend', 1);
                    });
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatus()
    {
        // 隱藏
        if ($this->deleted_at) {
            return 'deleted';
        }

        // 尚未開始
        if ($this->release_date && $this->release_date > now()) {
            return 'pending';
        }

        // 準備中
        if ($this->start_date && $this->start_date > now()) {
            return 'prepared';
        }

        // 進行中
        if ($this->end_date && $this->end_date > now()) {
            return 'published';
        }

        // 已結束
        return 'completed';
    }

    /**
     * 重置已結束的活動方案
     *
     * @return voit
     */
    public function resetCompleted()
    {
        $today = now()->format('Y-m-d');
        if ($this->deleted_at || $this->end_date < $today) {

            // 有參加商家，要取消參加
            $attendSoreIds = $this->attendSores(1)->pluck('store_id');
            $this->attendSores(1)->syncWithPivotValues($attendSoreIds, ['is_attend' => 0]);

            // 商家的活動方案要隱藏
            $this->services()->update(['status' => 'hide']);
        }
    }
}
