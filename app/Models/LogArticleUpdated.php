<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LogArticleUpdated extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $guarded = [];

    // relation YzcubeUser 神之後台管理員
    public function yzcubeUser()
    {
        return $this->belongsTo(\App\Models\YzcubeUser::class);
    }

    // scope sort 建立時間排序（新到舊）
    public function scopeSort($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }
}
