<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use App\Models\BlogArticlePageView;

class LogGaPageView extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $guarded = [];

    // 類型
    public $typeList = [
        'all'              => '官網所有頁面',
        // 'forum_article'    => '好婚聊聊文章',
        // 'share_post'       => 'W姐妹分享文',
        'kol_article'      => '鑑定團文章',
        // 'blog_article'     => '好婚專欄文章',
        'store'            => '商家所有頁面',
        'store_organic'    => '商家自然流量',
        'store_album'      => '商家相本頁',
        'store_video'      => '商家影片頁',
        'store_service'    => '商家服務頁',
        'store_venue_room' => '商家廳房頁',
        'store_member'     => '商家成員頁',
    ];

    public function sumKolPageView($post_id)
    {
        $count = 0;

        // 每日瀏覽量 (2023-07後才有紀錄)
        $count += $this->where('type', 'kol_article')
                    ->where('target_id', $post_id)
                    ->get()
                    ->sum(function ($log) {
                        return $log->pageViews;
                    });

        // 每月瀏覽量 (補2023-07前的紀錄)
        $count += BlogArticlePageView::where('type', 'kol')
                    ->where('blog_id', $post_id)
                    ->where('month', '<', '2023-07')
                    ->get()
                    ->sum(function ($pageView) {
                        return $pageView->count;
                    });

        return $count;
    }
}
