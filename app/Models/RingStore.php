<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RingStore extends Model
{
    public $timestamps = false;

    use SoftDeletes;

    // relation RingBrand 婚戒大賞-品牌
    public function ringBrand()
    {
        return $this->belongsTo(RingBrand::class);
    }

    // relation RingOrder 婚戒大賞-預約紀錄
    public function reserves()
    {
        return $this->hasMany(RingReserve::class);
    }

    // relation RingPoint 婚戒大賞-點數紀錄
    public function points()
    {
        return $this->hasMany(RingPoint::class);
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }
}
