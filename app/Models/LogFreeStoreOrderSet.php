<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LogFreeStoreOrderSet extends Model
{
    use ToJsonDateTrait;

    protected $table = 'log_free_store_order_set';

    protected $guarded = [];

    /**
     * relation YzcubeUser
     * 取得管理者資料
     */
    public function yzcubeUser()
    {
        return $this->belongsTo(YzcubeUser::class);
    }
}
