<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WeddingcakeService extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    // 優惠限制 min_amount:低消 min_quantity:最少數量 no_limit:無限制
    public $limitTypeList = [
        'min_amount'   => '須達最低訂購金額 [value] 元',
        'min_quantity' => '須達最低訂購 [value] 盒',
        'no_limit'     => '無限制',
    ];

    // 優惠條件 full_quantity:滿盒 full_amount:滿額 no_limit:無限制
    public $conditionTypeList = [
        'full_quantity' => '滿盒優惠',
        'full_amount'   => '滿額優惠',
        'no_limit'      => '無限制',
    ];

    // 折扣方式 cash:折扣金額 ratio:折扣%數 other:其他優惠/贈品
    public $discountTypeList = [
        'cash'  => '折扣金額',
        'ratio' => '折扣 % 數',
        'other' => '其他優惠/贈品',
    ];

    // relation WeddingcakeServiceDiscount 喜餅方案的優惠內容
    public function discounts()
    {
        return $this->hasMany(WeddingcakeServiceDiscount::class);
    }
}
