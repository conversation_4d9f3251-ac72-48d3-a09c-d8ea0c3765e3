<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventExhibition extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // relation event 活動
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // relation tool 婚展報到系統
    public function eventTool()
    {
        return $this->belongsTo(EventTool::class);
    }

    // relation EventCalendar Google行事曆
    public function calendar()
    {
        return $this->hasOne(EventCalendar::class, 'target_id')->where('target_type', 'event_exhibition');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence')
                    ->orderBy('created_at', 'DESC');
    }
}
