<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LogPayment extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    // relation creditCard 信用卡資訊
    public function creditCard()
    {
        return $this->belongsTo(CreditCard::class);
    }

    // scope success 付款成功
    public function scopeSuccess($query)
    {
        return $query->where('status', 0);

    }

    // scope fail 付款失敗
    public function scopeFail($query)
    {
        return $query->where('status', '!=', 0);

    }
}
