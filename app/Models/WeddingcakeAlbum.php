<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WeddingcakeAlbum extends Model
{
    // 商家類型 10:喜餅
    // public $storeType = 10;

    public $timestamps = false;

    protected $guarded = [];

    // 價格區間列表
    public $priceRangeList = [
        ['label' => '500 以下', 'value' => ['', 500]],
        ['label' => '501 ~ 600', 'value' => [501, 600]],
        ['label' => '601 ~ 700', 'value' => [601, 700]],
        ['label' => '701 ~ 800', 'value' => [701, 800]],
        ['label' => '801 ~ 900', 'value' => [801, 900]],
        ['label' => '901 ~ 1,000 ', 'value' => [901, 1000]],
        ['label' => '1,001 以上', 'value' => [1001, '']],
    ];

    // scope priceRangeByKey 特定價格區間 (用於喜餅，價格區間)
    public function scopePriceRangeByKey($query, $key)
    {
        // 取得價格區間的範圍
        $priceRange = $this->priceRangeList[$key]['value'];

        // 最低價以下
        if ($key == 0) {
            $query = $query->where('min_price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif ($key == count($this->priceRangeList) - 1) {
            $query = $query->where('max_price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            $query = $query->where(function ($q1) use ($priceRange) {
                $q1 = $q1->orWhereBetween('min_price', [$priceRange[0], $priceRange[1]])
                            ->orWhereBetween('max_price', [$priceRange[0], $priceRange[1]])
                            ->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->where('min_price', '<=', $priceRange[0])
                                        ->where('max_price', '>=', $priceRange[1]);
                            });
            });
        }

        return $query;
    }
}
