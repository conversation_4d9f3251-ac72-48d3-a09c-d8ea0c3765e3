<?php
/*
 |--------------------------------------
 | 商家下架處理的狀態變更記錄
 |--------------------------------------
 |
 |
 */

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Laracasts\Presenter\PresentableTrait;

class ServiceStopApplyChangeLog extends Model
{
    protected $guarded = [];
    protected $presenter = 'App\Models\Presenters\ServiceStopApplyChangeLogPresenter';

    use ToJsonDateTrait;
    use PresentableTrait;

    /** @var array : 狀態列表 */
    public $typeList = [
        'pay_off_status' => '商家還款狀態',
        'refund_status'  => 'WD退款狀態',
        'deposit_status' => '折讓單開立狀態'
    ];

    /**
     * relation YzcubeUser
     * 取得管理者資料
     */
    public function yzcubeUser()
    {
        return $this->belongsTo(YzcubeUser::class);
    }
}
