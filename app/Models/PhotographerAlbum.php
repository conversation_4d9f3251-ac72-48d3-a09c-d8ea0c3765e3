<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PhotographerAlbum extends Model
{
    // 商家類型 3:婚攝/婚錄
    // public $storeType = 3;

    public $timestamps = false;

    protected $guarded = [];

    // 地點類型
    public $locationTypeList = [
        1 => '自宅',
        2 => '路邊流水席',
    ];

    // relation Brand 宴客地點
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }
}
