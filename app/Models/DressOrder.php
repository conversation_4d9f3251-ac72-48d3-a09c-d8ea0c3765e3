<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DressOrder extends Model
{

    protected $guarded = [];

    // 參考 UserWeddingType 婚禮形式 變形
    public $dressUseList = [
        5             => '拍照',
        3             => '結婚',
        1             => '訂婚（文定）',
        4             => '歸寧'
    ];

    // relation DressContract 商家禮服合約
    public function contract()
    {
        return $this->belongsTo(DressContract::class, 'contract_id');
    }

    // relation StoreAlbum 商家禮服訂單的禮服
    public function dresses() 
    {
        return $this->belongsToMany(StoreAlbum::class, 'dress_album_order', 'dress_order_id', 'album_id');
    }

    // scope sortWeddingDate 排序
    public function scopeSortWeddingDate($query, $direction = 'ASC')
    {
        return $query->orderBy('wedding_date', $direction);
    }

    // scope orderHasAlbum 訂單裡有禮服
    public function scopeOrderHasAlbum($query) 
    {
        return $query->whereHas('dresses');
    }
}
