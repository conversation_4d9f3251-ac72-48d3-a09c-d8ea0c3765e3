<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserWeddingType extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // 婚禮形式
    public $typeList = [
        1 => '訂婚',
        2 => '結婚',
        3 => '訂結同天',
        4 => '歸寧',
    ];

    use SoftDeletes;

    // relation WeddingDate
    public function weddingDate()
    {
        return $this->belongsTo(WeddingDate::class, 'date', 'date');
    }

    // relation UserWeddingSchedule
    public function schedules()
    {
        return $this->hasMany(UserWeddingSchedule::class);
    }

    /**
     * 更新婚禮形式的婚期
     *
     */
    public function updateDate($date)
    {
        // 重新統計更新前的婚期
        if ($this->weddingDate) {
            $this->weddingDate->updateUserWeddingCount();
        }

        // 更新婚期
        $this->date = $date;
        $this->save();

        $weddingDate = $this->weddingDate()->first();
        if (!$weddingDate) {
            $weddingDate = new WeddingDate;
            $weddingDate->date = $date;
            $weddingDate->week = date('w', strtotime($date));
        }

        // 重新統計更新後的婚期
        $weddingDate->updateUserWeddingCount();

        // 因為更新婚期前，已先取出WeddingDate來重新統計了，所以要把新的WeddingDate放回UserWeddingType
        $this->weddingDate = $weddingDate;
    }
}
