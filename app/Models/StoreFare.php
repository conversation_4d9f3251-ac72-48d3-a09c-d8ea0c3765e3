<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreFare extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    // 商家的服務類型列表
    public $valueList = [

        // 婚攝/婚錄
        3 => [
            1 => '免車馬費',
            2 => '實報實銷',
            0 => '不提供服務',
            3 => '固定費用',
        ],

        // 新娘秘書
        4 => [
            1 => '免車馬費',
            2 => '實報實銷',
            0 => '不提供服務',
            3 => '固定費用',
        ],

        // 婚禮佈置
        6 => [
            1 => '提供服務',
            0 => '不提供服務',
        ],

        // 婚禮主持人
        8 => [
            1 => '免車馬費',
            2 => '實報實銷',
            0 => '不提供服務',
            3 => '固定費用',
        ],
    ];

}
