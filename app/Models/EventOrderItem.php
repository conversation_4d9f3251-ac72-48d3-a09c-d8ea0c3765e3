<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventOrderItem extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // relation EventOrder 活動訂單
    public function order()
    {
        return $this->belongsTo(EventOrder::class, 'order_no', 'order_no');
    }

    // relation LogPayment 付款紀錄
    public function logPayment()
    {
        return $this->hasOne(LogPayment::class, 'order_number', 'order_no');
    }

    // relation Images 訂購品項的上傳照
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')->where('type', 'event_order_item');
    }

    // relation product 活動商品
    public function product()
    {
        return $this->belongsTo(EventProduct::class);
    }
}
