<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StoreMember extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    // relation Image 商家成員照
    public function cover()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'store_member');
    }

    // relation StoreAlbum 相本
    public function albums()
    {
        return $this->belongsToMany(StoreAlbum::class, 'store_album_member', 'member_id', 'album_id')
                    ->status('show')
                    ->sort();
    }

    // relation StoreVideo 影片
    public function videos()
    {
        return $this->belongsToMany(StoreVideo::class, 'store_video_member', 'member_id', 'video_id')
                    ->status('show')
                    ->sort();
    }

    // relation LogGaPageView GA網頁瀏覽量
    public function logGaPageViews()
    {
        return $this->hasMany(LogGaPageView::class, 'target_id')->where('type', 'store_member');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence')
                    ->orderBy('created_at', 'DESC');
    }
}
