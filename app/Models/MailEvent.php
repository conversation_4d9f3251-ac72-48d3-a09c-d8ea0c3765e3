<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class MailEvent extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    protected $sendStatusMapping = [
        'RenderingFailure' => '渲染錯誤',
        'Reject' => '拒絕',
        'DeliveryDelay' => '寄送過時',
        'Complaint' => '投訴',
        'Bounce' => '退信',
        'Delivery' => '已送達',
        'Send' => '已寄出'
    ];
    protected $openStatusMapping = [
        'Click' => '已點擊',
        'Open' => '已開啟'
    ];

    /**
     * 取得寄送與開啟狀態
     */
    public function getStatusByMesId($sesMessageId){
        $statusArr = $this->where('ses_message_id', $sesMessageId)
            ->orderby('id', 'desc')
            ->get()->pluck('eventType')->toArray();

        $sendStatus = '未寄出';
        $openStatus = '未開啟';
        foreach ($this->sendStatusMapping as $eventType => $status) {
            if (in_array($eventType, $statusArr)) {
                $sendStatus = $status;
                break;
            }
        }
        foreach ($this->openStatusMapping as $eventType => $status) {
            if (in_array($eventType, $statusArr)) {
                $openStatus = $status;
                break;
            }
        }
        return ['sendStatus' => $sendStatus, 'openStatus' => $openStatus];
    }
}
