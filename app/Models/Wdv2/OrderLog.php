<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class OrderLog extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public $timestamps = false;

    protected $dates = ['created_at'];

    protected $casts = [
        'detail'=> 'object',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.order_log';
    }
}
