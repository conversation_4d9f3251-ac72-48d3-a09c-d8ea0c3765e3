<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class OrderProduct extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.order_product';
    }

    /** 月續費中的合約 */
    // public $monthlySetFeeList = [
    //     1049  => '婚紗拍攝設定費',
    //     1050  => '婚禮禮服設定費',
    //     1051  => '婚禮攝影設定費',
    //     1052  => '新娘秘書設定費',
    //     1053  => '婚禮佈置設定費',
    //     1054  => '婚禮主持人設定費',
    //     1055  => '婚紗拍攝設定費（優惠）',
    //     1056  => '婚禮禮服設定費（優惠）',
    //     1057  => '婚禮攝影設定費（優惠）',
    //     1058  => '新娘秘書設定費（優惠）',
    //     1059  => '婚禮佈置設定費（優惠）',
    //     1060  => '婚禮主持人設定費（優惠）',
    // ];

    /// 原本寫死的slim的參數..搬到model來..但大小寫懶得改了...

    /** 即時通訊收費的ID */
    public $PAY_MESSAGE_PRODUCT = [
        //商家類型id => 收費id
        1  => 1062,
        2  => 1063,
        3  => 1064,
        4  => 1066,
        6  => 1067,
        8  => 1068,
        10 => 1074,
    ];

    /** 詢問單收費的ID */
    public $PAY_RESERVE_PRODUCT = [
        //商家類型id => 收費id
        1  => 1021,
        2  => 1022,
        3  => 1023,
        4  => 1025,
        6  => 1026,
        8  => 1027,
        10 => 1074,
    ];

    /** @var int 點擊收費的ID */
    public $PAY_CLICK_PRODUCT = 1028;

    /** 最大付款天數  */
    // public $MAXPAY_DAY = 5;

    /** 費用類別  */
    // public $PRODICTTYPE = [
    //     1 => '設定費',
    //     2 => '使用費',
    //     3 => '折讓金',
    //     4 => 'W幣',
    //     5 => '設定費優惠',
    //     6 => '使用費優惠',
    //     7 => '使用費上限折抵'
    // ];

    /** 付款狀態  */
    // public $PAYMENTSTATUS = [
    //     0 => '未付款',
    //     1 => '完成付款',
    //     2 => '取消',
    // ];

    /** 設定費折抵使用費的優惠ID */
    public $PAY_DISCOUNT = [
        1  => 1030,
        2  => 1031,
        3  => 1032,
        4  => 1034,
        6  => 1035,
        8  => 1036,
        10 => 1072,
    ];

    /** 基本設定費ID */
    public $SETTING_FEE = [
        1  => 1049,
        2  => 1050,
        3  => 1051,
        4  => 1052,
        6  => 1053,
        8  => 1054,
        10 => 1073,
    ];

    /** 訊息費用ID */
    public $MESSAGE_FEE = [
        1  => 1062,
        2  => 1063,
        3  => 1064,
        4  => 1066,
        6  => 1067,
        8  => 1068,
        10 => 1074,
    ];

    /** 設定費折抵  */
    // public $PAY_USAGELIMIT = 1048;

    /** 合約緩衝天數，首次合作會給予一個緩衝時間，讓商家可以上刊東西，*/
    // public $BUFFDEADLINE = 14 ;

    /** 設定費的目前運作設定檔 */
    // public $SALESPACKAGE = [
    //     1 => [1=>100, 2=>107, 3=>114],
    //     2 => [1=>101, 2=>108, 3=>115],
    //     3 => [1=>102, 2=>109, 3=>116],
    //     4 => [1=>104, 2=>111, 3=>118],
    //     6 => [1=>105, 2=>112, 3=>119],
    //     8 => [1=>106, 2=>113, 3=>120]
    // ];

    /** 折讓金的品項ID  */
    // public $PAY_DEPOSIT = 1039 ;

    /** W幣的品項ID  */
    // public $PAY_WTOKEN = 1040 ;

    // 設定費的合約類型
    public $setFeeContractType = [
        'season'    => [1000, 1001, 1002, 1003, 1004, 1005, 1006], // 3期合約
        'half_year' => [1007, 1008, 1009, 1010, 1011, 1012, 1013, 1041, 1042, 1043, 1044, 1045, 1046, 1047], // 6期合約 & 改版特惠
        'full_year' => [1014, 1015, 1016, 1017, 1018, 1019, 1020], // 12期合約
        'renew'     => [1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060], // 續約(優惠)
    ];

    // scope live 有效
    public function scopeLive($query)
    {
        return $query->where('show_flag', 2);
    }

    /**
     * 取得所有類型的即時通訊單價
     *
     *  @param $request
     *  @return model
     */
    public function getAllMessageUnitPrice()
    {
        foreach ($this->PAY_MESSAGE_PRODUCT as $storeType => $id) {
            $result[$storeType] = $this->find($id)->fixed_price ?? '';
        }

        return $result;
    }

    /**
     * 取得所有類型的詢問單單價
     *
     *  @param $request
     *  @return model
     */
    public function getAllReserveUnitPrice()
    {
        foreach ($this->PAY_RESERVE_PRODUCT as $storeType => $id) {
            $result[$storeType] = $this->find($id)->fixed_price ?? '';
        }

        return $result;
    }

    /**
     * 取得所有類型的詢問單單價
     *
     *  @param $request
     *  @return model
     */
    public function getClickUnitPrice()
    {
        return $this->find($this->PAY_CLICK_PRODUCT)->fixed_price ?? '';
    }
}
