<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class UserDate extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // 商家類型
    public $setTagList = [
        222 => '訂婚(文定)',
        223 => '結婚',
        224 => '訂結同天',
        225 => '歸寧',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.user_date';
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('show_flag', 2);
    }
}
