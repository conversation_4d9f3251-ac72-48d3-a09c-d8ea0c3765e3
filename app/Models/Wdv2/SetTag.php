<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class SetTag extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $guarded = [];

    // 商家的方案需求列表
    public $serviceTags = [

        // 拍婚紗
        1 => [
            179 => '檔案全拿',
            175 => '拍攝白紗 [number] 套',
            176 => '拍攝禮服 [number] 套',
            177 => '宴客白紗 [number] 套',
            178 => '宴客禮服 [number] 套',
            174 => '入本數 [number] 張',
        ],

        // 婚紗禮服
        2 => [
            189 => '拍照白紗 [number] 件',
            190 => '拍照禮服 [number] 件',
            191 => '宴客白紗 [number] 件',
            192 => '宴客禮服 [number] 件',
        ],

        // 婚攝/婚錄
        3 => [
            180 => '儀式',
            181 => '午宴',
            182 => '晚宴',
            183 => '拍攝 [number] 小時',
            184 => '攝影師 [number] 位',
        ],

        // 新娘秘書
        4 => [
            185 => '儀式',
            186 => '午宴',
            187 => '晚宴',
            188 => '造型數 [number] 個',
        ],

        // 婚禮主持人
        8 => [
            228 => '儀式',
            229 => '午宴',
            230 => '晚宴',
            231 => '出勤 [number] 小時',
            232 => '出勤 [number] 位',
        ],
    ];

    // 商家標記-大師
    public $storeMasterId = 156;

    // 商家標記-白名單(小編/培養/大師)
    public $storeWhiteIds = [146, 147, 156];

    // 商家標記-黑名單(偷雞/假流量)
    public $storeBlackIds = [158, 159];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.set_tag';
    }

    // relation Self
    public function children()
    {
        return $this->hasMany(self::class, 'parent')->sort();
    }

    // relation Store 商家
    public function stores()
    {
        return $this->belongsToMany(\App\Models\Store::class, (new SetTagRelationship)->getTable(), 'set_tag_id', 'object_id')
                    ->wherePivot('type', 1);
    }

    // relation Store 免車馬費的商家
    public function freeFareStores()
    {
        return $this->stores()
                    ->wherePivot('number', 0);
    }

    // relation StoreAlbum 商家相本
    public function storeAlbums()
    {
        return $this->belongsToMany(StoreAlbum::class, (new SetTagRelationship)->getTable(), 'set_tag_id', 'object_id')
                    ->wherePivot('type', 3);
    }

    // scope locationAreas 所在地的所有區域
    public function scopelocationAreas()
    {
        return $this->where('parent', 86);
    }

    // scope fareAreas 車馬費的所有區域
    public function scopeFareAreas()
    {
        return $this->where('parent', 87);
    }

    // scope storeMarks 商家標記
    public function scopeStoreMarks()
    {
        return $this->where('parent', 142);
    }

    // scope dressTags 禮服標籤
    public function scopeDressTags()
    {
        return $this->whereIn('group_id', [1, 2])->sort();
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sort');
    }
}
