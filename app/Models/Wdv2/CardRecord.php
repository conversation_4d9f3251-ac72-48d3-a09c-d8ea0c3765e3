<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CardRecord extends Model
{
    use HasFactory;
    use ToJsonDateTrait;

    protected $guarded = [];

    // 卡片類別
    public $fundingList = [
        -1 => 'Unknown',
        0  => '信用卡 (Credit Card)',
        1  => '簽帳卡 (Debit Card)',
        2  => '預付卡 (Prepaid Card)',
    ];

    // 卡片種類
    public $typeList = [
        -1 => 'Unknown',
        1  => 'VISA',
        2  => 'MasterCard',
        3  => 'JCB',
        4  => 'Union Pay',
        5  => 'AMEX',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.card_records';
    }

    /**
     * relation Store
     * 取得商家資料
     */
    public function store()
    {
        return $this->belongsTo(\App\Models\Store::class);
    }
}
