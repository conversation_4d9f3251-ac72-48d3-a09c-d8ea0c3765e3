<?php
/*--------------------------------
 | 1v1 詢問單
 |--------------------------------
 |
 |
 |
 */

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Model\AsJsonUnicode;

class Reserve extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    protected $casts = [
        'contact' => 'object',
        'content' => 'object',
    ];

    use AsJsonUnicode;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database') . '.reserve';
    }

    // 詢問單處理狀態
    public $statusFlagList = [
        1 => '待處理',
        2 => '已處理',
        3 => '新人爽約',
        4 => '已刪除',
        5 => '已滿檔',
        6 => '無效詢問單申請中',
        7 => '無效單'
    ];

    //詢問單內容欄位..就是json裡會有的欄位
    public $contentList = [
        'content'      => '詳細資訊',
        'reserveDate'  => '婚期',
        'reserve_hour' => '小時', //不曉得是3小
        'contact'      => '希望聯絡方式',
        'wishDate'     => '希望試穿日',
        'useDate'      => '需求日期',
        'use_time'     => '需求時段',
        'area'         => '地區', //地區
        'venue'        => '宴客地點',
        'restaurant'   => '餐廳', //餐廳
        'storeService' => '服務方案ID',
        'box'          => '希望包套',
        'free'         => '需求地區', //存id..要自己去set_tag轉
        'remark'       => '其他要求/備註',
    ];

    // relation Store
    public function rawStore()
    {
        return $this->belongsTo(\App\Models\Store::class, 'store_id');
    }

    // relation Store
    public function store()
    {
        return $this->rawStore()->live();
    }

    // relation User
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    // relation ReserveAppeal
    public function reserveAppeal()
    {
        return $this->hasOne(ReserveAppeal::class, 'reserve_id', 'id');
    }

    // relation StoreDiscount
    public function storeDiscount()
    {
        return $this->hasMany(\App\Models\StoreDiscount::class, 'store_id', 'store_id');
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('show_flag', '!=', 0);
    }
}
