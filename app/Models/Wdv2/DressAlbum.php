<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Database\Eloquent\Model;

class DressAlbum extends Model
{
    use ToJsonDateTrait;

    use ParseKeywordTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.album_dress';
    }

  }