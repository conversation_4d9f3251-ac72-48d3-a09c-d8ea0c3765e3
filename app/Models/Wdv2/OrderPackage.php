<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class OrderPackage extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.order_package';
    }

    /**
     * 取得產品資料
     */
    public function orderProduct()
    {
        return $this->belongsTo(OrderProduct::class)->live();
    }

    // scope live 有效
    public function scopeLive($query)
    {
        return $query->where('show_flag', 2);
    }
}
