<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Database\Eloquent\Model;

class DressOrder extends Model
{
    use ToJsonDateTrait;

    use ParseKeywordTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store_pro_newlywed_order';
    }

    // relation StoreAlbum 商家禮服訂單的禮服
    public function dresses()
    {
        $pivotTable = config('database.connections.mysql_wdv2.database').'.album_dress_calendar';

        return $this->belongsToMany(StoreAlbum::class, $pivotTable, 'order_id', 'album_id')
                    ->wherePivot('show_flag', 2);
    }

  }
  