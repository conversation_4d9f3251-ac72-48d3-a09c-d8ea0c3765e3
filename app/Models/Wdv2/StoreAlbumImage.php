<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class StoreAlbumImage extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.image';
    }

    // relation StoreAlbum
    public function album()
    {
        return $this->belongsTo(StoreAlbum::class)->release();
    }

    // relation User
    public function userCollects()
    {
        return $this->belongsToMany(\App\Models\User::class, (new \App\Models\UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'album_image')
                    ->where((new \App\Models\User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('show_flag', '!=', 0);
    }

    // scope release
    public function scopeRelease($query)
    {
        return $query->where('show_flag', 2);
    }

    // scope sort
    public function scopeSort($query)
    {
        return $query->orderBy('sort')
                    ->orderBy('created_at', 'DESC');
    }
}
