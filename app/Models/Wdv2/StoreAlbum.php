<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Database\Eloquent\Model;

class StoreAlbum extends Model
{
    use ToJsonDateTrait;

    use ParseKeywordTrait;

    protected $guarded = [];

    protected $dates = ['onlined_at'];

    protected $casts = [
        'description'=> 'object',
    ];

    // 作品類型
    public $typeList = [
        // 拍婚紗
        1 => [
            1 => '婚紗攝影',
            2 => '寶寶寫真',
            3 => '個人寫真',
            4 => '孕婦寫真',
            5 => '全家福',
            // 6 => '閨蜜婚紗',
            // 7 => '海外婚紗',
        ],

        // 婚攝/婚錄
        3 => [
            1 => '平面作品',
            2 => '動態作品',
        ],

        // 婚禮主持人
        // 8 => [
        //     1 => '相片紀錄',
        //     2 => '影像紀錄',
        // ],
    ];

    // 價格區間列表 (只有婚禮佈置需要)
    public $priceRangeList = [
        ['label' => '1萬以下', 'value' => ['', 10000]],
        ['label' => '1萬 ~ 1萬5', 'value' => [10000, 15000]],
        ['label' => '1萬5 ~ 2萬', 'value' => [15000, 20000]],
        ['label' => '2萬 ~ 2萬5', 'value' => [20000, 25000]],
        ['label' => '2萬5 ~ 3萬', 'value' => [25000, 30000]],
        ['label' => '3萬以上', 'value' => [30000, '']],
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.album';
    }

    // relation Store
    public function storeWithTrashed()
    {
        return $this->belongsTo(\App\Models\Store::class, 'store_id');
    }

    // relation Store
    public function store()
    {
        return $this->storeWithTrashed()->published();
    }

    // relation StoreAlbumImage
    public function imagesWithTrashed()
    {
        return $this->hasMany(StoreAlbumImage::class, 'album_id');
    }

    // relation StoreAlbumImage
    public function images()
    {
        return $this->imagesWithTrashed()->release()->sort();
    }

    // relation DressAlbum
    public function dressAlbum()
    {
        return $this->hasOne(DressAlbum::class, 'album_id');
    }

    // relation StoreMember
    public function members()
    {
        $pivotTable = config('database.connections.mysql_wdv2.database').'.album_member_relationship';

        return $this->belongsToMany(StoreMember::class, $pivotTable, 'album_id', 'member_id')
                    ->live();
    }

    // relation User
    public function userCollects()
    {
        return $this->belongsToMany(\App\Models\User::class, (new \App\Models\UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'album')
                    ->where((new \App\Models\User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation UserCollect 所有單一作品的收藏紀錄
    public function imageCollects()
    {
        return $this->hasManyThrough(\App\Models\UserCollect::class, StoreAlbumImage::class, 'album_id', 'target_id')
                    ->where((new StoreAlbumImage)->getTable().'.show_flag', 2) // scopeRelease()
                    ->where((new \App\Models\UserCollect)->getTable().'.type', 'album_image');
    }

    // relation StoreFull 商家的檔期
    public function storeFull()
    {
        return $this->hasMany(StoreFull::class, 'store_id', 'store_id');
    }

    // relation SetTag 禮服標籤
    public function dressTags()
    {
        return $this->belongsToMany(SetTag::class, (new SetTagRelationship)->getTable(), 'object_id', 'set_tag_id')
                    ->wherePivot('type', 3);
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('show_flag', '!=', 0);
    }

    // scope release 上架
    public function scopeRelease($query)
    {
        return $query->where('album.show_flag', 2)
                    ->whereNotNull('album.cover_image');
                    // 上架中的相本，先不用判斷裡面有沒有含照片
                    // ->has('images');
    }

    // scope storeHasPaid 限特定類型已付費的商家作品
    public function scopeStoreHasPaid($query, $storeType)
    {
        // 上架
        $query = $query->release();

        // 商家類型 store_type (改用 join 提升效率)
        // $query = $query->whereHas('store', function ($q) use ($storeType) {
        //     $q = $q->published($storeType);
        // });
        $query = $query->join((new \App\Models\Store)->getTable(), function($join) use ($storeType) {
                            $join->on('stores.id', '=', 'album.store_id')
                                    ->where('stores.type', $storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope sort排序
    public function scopeSort($query)
    {
        return $query->orderBy($this->getTable().'.sort')
                    ->orderBy($this->getTable().'.created_at', 'DESC');
    }

    // scope reverseSort 反向排序
    public function scopeReverseSort($query)
    {
        return $query->orderBy($this->getTable().'.sort', 'DESC')
                    ->orderBy($this->getTable().'.created_at');
    }

    // scope searchKeyword 關鍵字搜尋
    public function scopeSearchKeyword($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('album.name', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchWeddingDate 婚期搜尋
    public function scopeSearchWeddingDate($query, $date)
    {
        return $query->where(function ($q1) use ($date) {
            // 上午沒有滿檔
            $q1 = $q1->whereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 1);
                    })
                    // 或下午沒有滿檔
                    ->orWhereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 2);
                    });
        });
    }

    // scope priceRange 價格區間搜尋 (用於婚紗禮服，單一價格)
    public function scopePriceRange($query, $priceRange)
    {
        // 最低價以下
        if (!$priceRange[0]) {
            return $query->where('album.price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif (!$priceRange[1]) {
            return $query->where('album.price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            return $query->whereBetween('album.price', $priceRange);
        }
    }

    // scope priceRangeByKey 特定價格區間 (用於婚禮佈置，價格區間)
    public function scopePriceRangeByKey($query, $key)
    {
        // 取得價格區間的範圍
        $priceRange = $this->priceRangeList[$key]['value'];

        // 最低價以下
        if ($key == 0) {
            $query = $query->where('album.min_price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif ($key == count($this->priceRangeList) - 1) {
            $query = $query->where('album.max_price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            $query = $query->where(function ($q1) use ($priceRange) {
                $q1 = $q1->orWhereBetween('album.min_price', [$priceRange[0], $priceRange[1]])
                            ->orWhereBetween('album.max_price', [$priceRange[0], $priceRange[1]])
                            ->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->where('album.min_price', '<=', $priceRange[0])
                                        ->where('album.max_price', '>=', $priceRange[1]);
                            });
            });
        }

        return $query;
    }
}
