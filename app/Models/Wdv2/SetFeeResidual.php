<?php

namespace App\Models\Wdv2;

use Illuminate\Database\Eloquent\Model;
use App\Traits\Model\ToJsonDateTrait;

class SetFeeResidual extends Model
{
    use ToJsonDateTrait;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.set_fee_residuals';
    }

    protected $guarded = [];


    /**
     * 取得下架申請資料
     */
    public function serviceStopApply()
    {
        return $this->belongsTo(ServiceStopApply::class, 'service_stop_applies_id');
    }

    /**
     * 取得訂單資料
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * 取得訂單package資料
     */
    public function orderPackage()
    {
        return $this->hasOne(OrderPackage::class, 'order_id', 'order_id');
    }
}
