<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Laracasts\Presenter\PresentableTrait;

class StoreQuote extends Model
{
    use ToJsonDateTrait;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\Wdv2\StoreQuotePresenter';

    protected $casts = [
        'detail' => 'object',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store_quote';
    }

    // relation Store
    public function store()
    {
        return $this->belongsTo(\App\Models\Store::class)->live();
    }

    // relation UserQuote
    public function userQuote()
    {
        return $this->belongsTo(UserQuote::class)->has('user');
    }
}
