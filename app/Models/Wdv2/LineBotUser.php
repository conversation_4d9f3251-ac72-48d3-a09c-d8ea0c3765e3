<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LineBotUser extends Model
{
    use ToJsonDateTrait;

    protected $casts = [
        'profile'=> 'object'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.line_bot_user';
    }

    // scope release
    public function scopeRelease($query)
    {
        return $query->where('show_flag', 2);
    }
}
