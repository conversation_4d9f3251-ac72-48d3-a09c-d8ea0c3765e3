<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class UserPhoneLegalize extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.user_phone_legalize';
    }
}
