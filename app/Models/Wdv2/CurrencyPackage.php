<?php
/**
 * 發放的清單(這個才是重頭戲)
 *
 * @version 0.1.0
 * <AUTHOR>
 * @date 2018/10/17
 * @since 2018/10/17 聽說ikea販售的是男性的尊嚴，好像有這麼一點道理，但衣櫥真的很重耶
 */

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;


class CurrencyPackage extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.currency_package';
    }

    protected $casts = [
        'description'=> 'object'
    ];

    // scope live 未停用
    public function scopeLive($query)
    {
        return $query->where('show_flag', '!=', 0);
    }

    // scope expiration 有效期限內
    public function scopeExpiration($query)
    {
        return $query->where(function ($q) {
            $q->where('expiration', '>', now())
                ->orWhereNull('expiration');
        });
    }

    // scope Wcoin W幣
    public function scopeWcoin($query)
    {
        return $query->where('currency_product_id', 1);
    }

    // scope allowance 折讓金
    public function scopeAllowance($query)
    {
        return $query->where('currency_product_id', 2);
    }
}
