<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class MallStore extends Model
{
    use ToJsonDateTrait;

    // 商家類型 7:婚禮小物
    public $storeType = 7;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.mall_store';
    }

    public function logo()
    {
        return $this->belongsTo(\App\Models\Image::class, 'store_id', 'target_id')
                    ->where('type', 'store_logo');
    }
}
