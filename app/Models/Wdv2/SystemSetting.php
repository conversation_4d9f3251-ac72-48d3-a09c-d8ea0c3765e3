<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public $timestamps = false;

    protected $casts = [
        'description'=> 'object',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.system_setting';
    }
}
