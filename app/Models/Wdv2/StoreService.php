<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Database\Eloquent\Model;
use Laracasts\Presenter\PresentableTrait;

class StoreService extends Model
{
    use ToJsonDateTrait;

    use ParseKeywordTrait;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\Wdv2\StoreServicePresenter';

    protected $guarded = [];

    protected $casts = [
        'information'=> 'object',
    ];

    // 方案需求(含價格區間)架構
    public $demandStructure = [
        // 拍婚紗 Scope
        1 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '2萬以下', 'value' => ['', 20000]],
                    ['label' => '2萬 ~ 3萬', 'value' => [20000, 30000]],
                    ['label' => '3萬 ~ 4萬', 'value' => [30000, 40000]],
                    ['label' => '4萬 ~ 5萬', 'value' => [40000, 50000]],
                    ['label' => '5萬 ~ 6萬', 'value' => [50000, 60000]],
                    ['label' => '6萬以上', 'value' => [60000, '']],
                ]
            ],
            // 入本數
            'studioAlbumPhoto' => [
                'set_tag_id' => 174,
                'list'       => ['10張以下', '11-20張', '21-30張', '31張以上'],
            ],
            // 拍攝白紗
            'studioWhiteDress' => [
                'set_tag_id' => 175,
                'list'       => ['1套', '2套', '3套以上'],
            ],
            // 拍攝禮服
            'studioDress' => [
                'set_tag_id' => 176,
                'list'       => ['1套', '2套', '3套以上'],
            ],
            // 宴客白紗
            'weddingWhiteDress' => [
                'set_tag_id' => 177,
                'list'       => ['不需要', '1套以上'],
            ],
            // 宴客禮服
            'weddingDress' => [
                'set_tag_id' => 178,
                'list'       => ['不需要', '1套', '2套', '3套', '4套以上'],
            ],
            // 檔案全拿 (單選項目)
            'studioPhotoFile' => [
                'set_tag_id' => 179,
                'list'       => true,
            ],
        ],

        // 婚紗禮服 Scope
        2 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1萬以下', 'value' => ['', 10000]],
                    ['label' => '1萬 ~ 2萬', 'value' => [10000, 20000]],
                    ['label' => '2萬 ~ 3萬', 'value' => [20000, 30000]],
                    ['label' => '3萬 ~ 4萬', 'value' => [30000, 40000]],
                    ['label' => '4萬以上', 'value' => [40000, '']],
                ]
            ],
            // 拍照白紗
            'studioWhiteDress' => [
                'set_tag_id' => 189,
                'list'       => ['1件', '2件', '3件', '4件', '5件以上'],
            ],
            // 拍攝禮服
            'studioDress' => [
                'set_tag_id' => 190,
                'list'       => ['1件', '2件', '3件', '4件', '5件以上'],
            ],
            // 宴客白紗
            'weddingWhiteDress' => [
                'set_tag_id' => 191,
                'list'       => ['1件', '2件', '3件', '4件', '5件以上'],
            ],
            // 宴客禮服
            'weddingDress' => [
                'set_tag_id' => 192,
                'list'       => ['1件', '2件', '3件', '4件', '5件以上'],
            ],
        ],

        // 婚攝/婚錄 Scope
        3 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1萬以下', 'value' => ['', 10000]],
                    ['label' => '1萬 ~ 2萬', 'value' => [10000, 20000]],
                    ['label' => '2萬 ~ 3萬', 'value' => [20000, 30000]],
                    ['label' => '3萬 ~ 4萬', 'value' => [30000, 40000]],
                    ['label' => '4萬 ~ 5萬', 'value' => [40000, 50000]],
                    ['label' => '5萬以上', 'value' => [50000, '']],
                ],
            ],
            // 拍攝時數
            'photographerTime' => [
                'set_tag_id' => 183,
                'list'       => ['4小時以下', '5-8小時', '9-12小時', '不限時拍到結束'],
            ],
            // 儀式 ['單儀式', '雙儀式', '無儀式']
            'ceremony' => [
                'set_tag_id' => 180,
                'list'       => ['有儀式', '無儀式'],
            ],
            // 婚宴時段 ['午宴', '晚宴', '午宴+晚宴', '無宴客'],
            'banquetTime' => [
                'set_tag_id' => [181, 182],
                'list'       => ['午宴', '晚宴', '無宴客'],
            ],
            // 攝影師人數
            'photographerCount' => [
                'set_tag_id' => 184,
                'list'       => ['1位', '2位', '3位', '4位以上'],
            ],
        ],

        // 新娘秘書 Scope
        4 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '5千以下', 'value' => ['', 5000]],
                    ['label' => '5千 ~ 1萬', 'value' => [5000, 10000]],
                    ['label' => '1萬 ~ 2萬', 'value' => [10000, 20000]],
                    ['label' => '2萬 ~ 3萬', 'value' => [20000, 30000]],
                    ['label' => '3萬 ~ 4萬', 'value' => [30000, 40000]],
                    ['label' => '4萬以上', 'value' => [40000, '']],
                ],
            ],
            // 造型數
            'makeupCount' => [
                'set_tag_id' => 188,
                'list'       => ['1個', '2個', '3個', '4個', '5個以上'],
            ],
            // 儀式 ['單儀式', '雙儀式', '無儀式']
            'ceremony' => [
                'set_tag_id' => 185,
                'list'       => ['有儀式', '無儀式'],
            ],
            // 婚宴時段 ['午宴', '晚宴', '午宴+晚宴', '無宴客'],
            'banquetTime' => [
                'set_tag_id' => [186, 187],
                'list'       => ['午宴', '晚宴', '無宴客'],
            ],
        ],

        // 婚禮主持人 Scope
        8 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1萬以下', 'value' => ['', 10000]],
                    ['label' => '1萬 ~ 2萬', 'value' => [10000, 20000]],
                    ['label' => '2萬 ~ 3萬', 'value' => [20000, 30000]],
                    ['label' => '3萬 ~ 4萬', 'value' => [30000, 40000]],
                    ['label' => '4萬 ~ 5萬', 'value' => [40000, 50000]],
                    ['label' => '5萬以上', 'value' => [50000, '']],
                ],
            ],
        ],
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store_service';
    }

    // relation Store
    public function store()
    {
        return $this->belongsTo(\App\Models\Store::class)->published();
    }

    // relation User
    public function userCollects()
    {
        return $this->belongsToMany(\App\Models\User::class, (new \App\Models\UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'service')
                    ->where((new \App\Models\User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation StoreServiceTag
    public function tags()
    {
        $pivotTable = config('database.connections.mysql_wdv2.database').'.store_service_tag';

        return $this->belongsToMany(SetTag::class, $pivotTable, 'service_id')
                    ->withPivot('number')
                    ->sort();
    }

    // relation StoreFull 商家的檔期
    public function storeFull()
    {
        return $this->hasMany(StoreFull::class, 'store_id', 'store_id');
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where($this->getTable().'.show_flag', '!=', 0);
    }

    // scope release
    public function scopeRelease($query)
    {
        return $query->where($this->getTable().'.show_flag', 2);
    }

    // scope sort
    public function scopeSort($query)
    {
        return $query->orderBy($this->getTable().'.sort')
                    ->orderBy($this->getTable().'.created_at', 'DESC');
    }

    // scope storeHasPaid 限特定類型已付費的商家方案
    public function scopeStoreHasPaid($query, $storeType)
    {
        // 上架 (改用 join 提升效率)
        // $query = $query->release();
        $query = $query->where('store_service.show_flag', 2);

        // 商家類型 store_type (改用 join 提升效率)
        // $query = $query->whereHas('store', function ($q) use ($storeType) {
        //     $q = $q->published($storeType);
        // });
        $query = $query->join((new \App\Models\Store)->getTable(), function($join) use ($storeType) {
                            $join->on('stores.id', '=', 'store_service.store_id')
                                    ->where('stores.type', $storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope storeHasDiscount 商家有獨家優惠
    public function scopeStoreHasDiscount($query)
    {
        return $query->join((new \App\Models\StoreDiscount)->getTable(), function($join) {
                        $join->on('store_discounts.store_id', '=', 'stores.id')
                                ->where(function ($q) {
                                    $q->whereNull('start_date')->orWhere('start_date', '<=', now());
                                })
                                ->where(function ($q) {
                                    $q->whereNull('end_date')->orWhere('end_date', '>', now());
                                });
                    });
    }

    // scope searchKeyword 關鍵字搜尋
    public function scopeSearchKeyword($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('store_service.name', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchWeddingDate 婚期搜尋
    public function scopeSearchWeddingDate($query, $date)
    {
        return $query->where(function ($q1) use ($date) {
            // 上午沒有滿檔
            $q1 = $q1->whereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 1);
                    })
                    // 或下午沒有滿檔
                    ->orWhereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 2);
                    });
        });
    }

    // scope priceRange 特定價格區間
    public function scopePriceRange($query, $key, $storeType)
    {
        // 特定商家類型的價格區間列表
        $priceRangeList = $this->demandStructure[$storeType]['priceRange']['list'];

        // 排除例外
        if (!array_key_exists($key, $priceRangeList)) {
            return $query;
        }

        // 取得價格區間的範圍
        $priceRange = $priceRangeList[$key]['value'];

        // 最低價以下
        if ($key == 0) {
            $query = $query->where('store_service.min_price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif ($key == count($priceRangeList) - 1) {
            $query = $query->where('store_service.max_price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            $query = $query->where(function ($q1) use ($priceRange) {
                $q1 = $q1->orWhereBetween('store_service.min_price', [$priceRange[0], $priceRange[1]])
                            ->orWhereBetween('store_service.max_price', [$priceRange[0], $priceRange[1]])
                            ->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->where('store_service.min_price', '<=', $priceRange[0])
                                        ->where('store_service.max_price', '>=', $priceRange[1]);
                            });
            });
        }

        return $query;
    }

    // scope studioAlbumPhoto 特定入本數
    public function scopeStudioAlbumPhoto($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['studioAlbumPhoto'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 入本數
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
            // 10張以下
            if ($key == 0) {
                $q = $q->where('number', '<=', 10);
            // 11-20張
            } elseif ($key == 1) {
                $q = $q->whereBetween('number', [11, 20]);
            // 21-30張
            } elseif ($key == 2) {
                $q = $q->whereBetween('number', [21, 30]);
            // 31張以上
            } elseif ($key == 3) {
                $q = $q->where('number', '>=', 31);
            }
        });
    }

    // scope studioWhiteDress 特定拍攝白紗數
    public function scopeStudioWhiteDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['studioWhiteDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 拍攝白紗
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key) ? '>=' : '=';
            $q = $q->where('set_tag_id', $demand['set_tag_id'])
                    ->where('number', $operator, $key + 1);
        });
    }

    // scope studioDress 特定拍攝禮服數
    public function scopeStudioDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['studioDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 拍攝禮服
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key) ? '>=' : '=';
            $q = $q->where('set_tag_id', $demand['set_tag_id'])
                    ->where('number', $operator, $key + 1);
        });
    }

    // scope weddingWhiteDress 特定宴客白紗數
    public function scopeWeddingWhiteDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['weddingWhiteDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 宴客白紗
        $relation = ($demand['list'][0] != '不需要' OR $key) ? 'whereHas' : 'whereDoesntHave';
        return $query->{$relation}('tags', function ($q) use ($key, $demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key) ? '>=' : '=';
            // 有不需要的選項就是 key 套(件)
            $number = ($demand['list'][0] == '不需要') ? $key : $key + 1;
            if ($demand['list'][0] != '不需要' OR $key) {
                $q = $q->where('number', $operator, $number);
            }
        });
    }

    // scope weddingDress 特定宴客禮服數
    public function scopeWeddingDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['weddingDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 宴客禮服
        $relation = ($demand['list'][0] != '不需要' OR $key) ? 'whereHas' : 'whereDoesntHave';
        return $query->{$relation}('tags', function ($q) use ($key, $demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key) ? '>=' : '=';
            // 有不需要的選項就是 key 套(件)
            $number = ($demand['list'][0] == '不需要') ? $key : $key + 1;
            if ($demand['list'][0] != '不需要' OR $key) {
                $q = $q->where('number', $operator, $number);
            }
        });
    }

    // scope studioPhotoFile 檔案全拿
    public function scopeStudioPhotoFile($query, $key, $storeType)
    {
        $demand = $this->demandStructure[$storeType]['studioPhotoFile'];

        // 檔案全拿
        $relation = $key ? 'whereHas' : 'whereDoesntHave';
        return $query->{$relation}('tags', function ($q) use ($demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
        });
    }

    // scope photographerTime 拍攝時數
    public function scopePhotographerTime($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['photographerTime'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 拍攝時數
        $relation = ($key == 3) ? 'whereDoesntHave' : 'whereHas';
        return $query->{$relation}('tags', function ($q) use ($key, $demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
            // 4小時以下
            if ($key == 0) {
                $q = $q->where('number', '<=', 4);
            // 5-8小時
            } elseif ($key == 1) {
                $q = $q->whereBetween('number', [5, 8]);
            // 9-12小時
            } elseif ($key == 2) {
                $q = $q->whereBetween('number', [9, 12]);
            // 不限時拍到結束
            } elseif ($key == 3) {
                $q = $q->where('number', 4);
            }
        });
        return $query;
    }

    // scope photographerCount 攝影師人數
    public function scopePhotographerCount($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['photographerCount'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 攝影師人數
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
            // 1位
            if ($key == 0) {
                $q = $q->where('number', 1);
            // 2位
            } elseif ($key == 1) {
                $q = $q->where('number', 2);
            // 3位
            } elseif ($key == 2) {
                $q = $q->where('number', 3);
            // 4位以上
            } elseif ($key == 3) {
                $q = $q->where('number', '>=', 4);
            }
        });
        return $query;
    }

    // scope makeupCount 造型數
    public function scopeMakeupCount($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['makeupCount'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 造型數
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
            // 1個
            if ($key == 0) {
                $q = $q->where('number', 1);
            // 2個
            } elseif ($key == 1) {
                $q = $q->where('number', 2);
            // 3個
            } elseif ($key == 2) {
                $q = $q->where('number', 3);
            // 4個
            } elseif ($key == 3) {
                $q = $q->where('number', 4);
            // 5個以上
            } elseif ($key == 4) {
                $q = $q->where('number', '>=', 5);
            }
        });
        return $query;
    }

    // scope ceremony 儀式
    public function scopeCeremony($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['ceremony'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 儀式
        $relation = $key ? 'whereDoesntHave' : 'whereHas';
        return $query->{$relation}('tags', function ($q) use ($demand) {
            $q = $q->where('set_tag_id', $demand['set_tag_id']);
        });
    }

    // scope banquetTime 婚宴時段
    public function scopeBanquetTime($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['banquetTime'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 午宴
        if ($key == 0) {
            return $query->whereHas('tags', function ($q) use ($demand) {
                $q = $q->where('set_tag_id', $demand['set_tag_id'][0]);
            });

        // 晚宴
        } elseif ($key == 1) {
            return $query->whereHas('tags', function ($q) use ($demand) {
                $q = $q->where('set_tag_id', $demand['set_tag_id'][1]);
            });

        // 無宴客
        } else {
            return $query->whereDoesntHave('tags', function ($q) use ($demand) {
                $q = $q->whereIn('set_tag_id', $demand['set_tag_id']);
            });
        }
    }
}
