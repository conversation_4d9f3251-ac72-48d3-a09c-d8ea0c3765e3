<?php
/*--------------------------------
 | 無效詢問單申請
 |--------------------------------
 |
 |
 |
 */
namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class ReserveAppeal extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.reserve_appeals';
    }

    // 申請無效的理由
    public $reasonList = [
        '無效/錯誤的電話號碼',
        '查無此人',
        '此為重複訂單',
        '其他',
    ];

    // 處理狀態
    public $statusList = [
        0 => '未處理',
        1 => '同意',
        2 => '駁回',
    ];

    // relation Store
    public function store()
    {
        return $this->belongsTo(\App\Models\Store::class);
    }

    // relation yzcubeUser
    public function yzcubeUser()
    {
        return $this->belongsTo(\App\Models\YzcubeUser::class);
    }

    // relation Reserve
    public function reserve()
    {
        return $this->belongsTo(Reserve::class, 'reserve_id', 'id');
    }
}
