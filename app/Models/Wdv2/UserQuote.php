<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Laracasts\Presenter\PresentableTrait;

class UserQuote extends Model
{
    use ToJsonDateTrait;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\Wdv2\UserQuotePresenter';

    protected $casts = [
        'detail' => 'object',
    ];

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database') . '.user_quote';
    }

    public $weddingVenueTypeList = [
        1 => '飯店',
        2 => '婚宴會館',
        3 => '餐廳',
        4 => '流水席',
        5 => '自宅',
    ];

    public $detailList = [
        // 婚攝/婚錄
        3 => [
            // 1.你的婚宴時段？
            'time' => ['lunch' => '午宴', 'dinner' => '晚宴', 'both' => '午宴+晚宴', 'none' => '無宴客'],
            // 2.你當天的儀式需求？（依婚禮形式/婚宴時段會有不一樣的選項, 歸寧跳過這個步驟）
            'ceremony' => [
                'morning' => '早上儀式', 'afternoon' => '下午儀式', 'allday' => '早上文定+下午迎娶', 'none' => '無儀式',
                'morning_1' => '單儀式（上午文定）', 'afternoon_1' => '單儀式（下午文定）', 'morning_2' => '單儀式（上午迎娶）', 'afternoon_2' => '單儀式（下午迎娶）', 'morning_3' => '雙儀式（上午文定+迎娶）', 'afternoon_3' => '雙儀式（下午文定+迎娶）', 'allday' => '雙儀式（上午文定+下午迎娶）', 'none' => '無儀式',
            ],
            // 3.你需要的主服務？
            'service' => ['photo' => '平面攝影', 'video' => '動態錄影', 'both' => '平面攝影+動態錄影'],
        ],

        // 新娘秘書
        4 => [
            // 1.你需要的主服務？
            'service' => ['bride' => '新娘妝髮', 'bridegroom' => '純親友妝'],
            // 2-1.你的婚宴時段？
            'time' => ['lunch' => '午宴', 'dinner' => '晚宴', 'both' => '午宴+晚宴', 'none' => '無宴客'],
            // 2-2.預計開妝時段？
            'service_time' => ['morning' => '上午', 'afternoon' => '下午'],
            // 3.你當天的儀式需求？（依婚禮形式/婚宴時段會有不一樣的選項, 歸寧跳過這個步驟）
            'ceremony' => [
                'morning' => '早上儀式', 'afternoon' => '下午儀式', 'allday' => '早上文定+下午迎娶', 'none' => '無儀式',
                'morning_1' => '單儀式（上午文定）', 'afternoon_1' => '單儀式（下午文定）', 'morning_2' => '單儀式（上午迎娶）', 'afternoon_2' => '單儀式（下午迎娶）', 'morning_3' => '雙儀式（上午文定+迎娶）', 'afternoon_3' => '雙儀式（下午文定+迎娶）', 'allday' => '雙儀式（上午文定+下午迎娶）', 'none' => '無儀式',
            ],
            // 4.你的造型數量？
            'amount' => [1 => '1個造型', 2 => '2個造型（訂婚建議選項）', 3 => '3個造型（結婚建議選項）', 4 => '4個造型（訂結同天建議選項）', 5 => '5個造型'],
        ],

        // 婚禮佈置
        6 => [
            // 1.你的婚宴時段？
            'time' => ['lunch' => '午宴', 'dinner' => '晚宴', 'both' => '午宴+晚宴'],
            // 2.你需要的服務？
            'service' => ['backplane' => '背板出租', 'tool' => '道具出租', 'custom' => '客製化佈置', 'dry_flower' => '乾燥/不凋花佈置', 'flower' => '鮮花佈置', 'ball' => '氣球佈置', 'candy_bar' => 'Candy Bar'],
        ],

        // 婚禮主持人
        8 => [
            // 1.你的婚宴時段？
            'time' => ['lunch' => '午宴', 'dinner' => '晚宴', 'both' => '午宴+晚宴', 'none' => '無宴客'],
            // 2.你需要的主服務？
            'service' => ['host' => '婚宴主持', 'plan' => '婚禮企劃', 'ceremony' => '訂結儀式引導', 'band' => '樂團表演'],
            // 3.你需要引導的儀式需求與時段？（有選文定/迎娶儀式引導的才會出現, 歸寧跳過這個步驟）
            'ceremony' => [
                'morning' => '早上儀式', 'afternoon' => '下午儀式', 'allday' => '早上文定+下午迎娶', 'none' => '無儀式',
                'morning_1' => '單儀式（上午文定）', 'afternoon_1' => '單儀式（下午文定）', 'morning_2' => '單儀式（上午迎娶）', 'afternoon_2' => '單儀式（下午迎娶）', 'morning_3' => '雙儀式（上午文定+迎娶）', 'afternoon_3' => '雙儀式（下午文定+迎娶）', 'allday' => '雙儀式（上午文定+下午迎娶）',
            ],
        ],
    ];

    // relation User
    public function allUser()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id', 'id');
    }

    // relation User
    public function user()
    {
        return $this->allUser()->live();
    }

    // relation StoreQuote
    public function storeQuotes()
    {
        return $this->hasMany(StoreQuote::class)->has('store');
    }

    // scope sort
    public function scopeSort($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }

    // scope release
    public function scopeRelease($query)
    {
        return $query->where('show_flag', 2);
    }

    /**
     * 取得商家報價未讀訊息數
     *
     * @return int $unread_count
     */
    public function getUnreadCount()
    {
        return $this->storeQuotes()->where('read', 0)->count();
    }
}
