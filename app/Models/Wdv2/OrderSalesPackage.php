<?php

namespace App\Models\Wdv2;

use Illuminate\Database\Eloquent\Model;

class OrderSalesPackage extends Model
{
    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.order_sales_package';
    }

    /**
     * 擁有的訂單商品品項列表
     */
    public function orderProducts()
    {
        return $this->belongsToMany(OrderProduct::class, config('database.connections.mysql_wdv2.database').'.order_sales_package_list')->withTimestamps();
    }
}
