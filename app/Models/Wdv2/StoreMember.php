<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class StoreMember extends Model
{
    use ToJsonDateTrait;

    protected $casts = [
        'description' => 'object',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.member';
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('show_flag', '!=', 0);
    }

    // scope sort
    public function scopeSort($query)
    {
        return $query->orderBy('sort')
                    ->orderBy('created_at', 'DESC');
    }

    // relation StoreAlbum 相本
    public function albums()
    {
        return $this->belongsToMany(\App\Models\StoreAlbum::class, 'store_album_member', 'member_id', 'album_id')
                    ->status('show')
                    ->sort();
    }

    // relation StoreVideo 影片
    public function videos()
    {
        return $this->belongsToMany(\App\Models\StoreVideo::class, 'store_video_member', 'member_id', 'video_id')
                    ->status('show')
                    ->sort();
    }
}
