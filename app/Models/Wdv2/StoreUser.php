<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Laracasts\Presenter\PresentableTrait;

class StoreUser extends Model
{
    use ToJsonDateTrait;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\Wdv2\StoreUserPresenter';

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store_user';
    }

    // relation AuthToken
    public function tokens()
    {
        return $this->hasMany(\App\Models\AuthToken::class, 'target_id')->type('store');
    }

    // relation LogAuthToken 身份驗證紀錄
    public function logTokens()
    {
        return $this->hasManyThrough(\App\Models\LogAuthToken::class, \App\Models\AuthToken::class, 'target_id')
                    ->withTrashedParents()
                    ->where((new \App\Models\AuthToken)->getTable().'.type', 'store')
                    ->orderBy((new \App\Models\LogAuthToken)->getTable().'.created_at', 'DESC');
    }

    // relation Store 擁有的所有商家
    public function liveStores()
    {
        return $this->belongsToMany(\App\Models\Store::class, (new StoreUserRelationship)->getTable())
                    ->wherePivot('show_flag', 2)
                    ->where((new \App\Models\Store)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation Store 擁有已上架的所有商家
    public function stores()
    {
        return $this->liveStores()
                    ->where((new \App\Models\Store)->getTable().'.status', 'published'); // scopePublished()
    }

    // relation Store 擁有已上架且可Line通知公開詢價的所有商家
    public function storesWithLineQuoteNotify()
    {
        return $this->stores()
                    ->wherePivot('line_quote', 1);
    }

    // relation Store 擁有已上架且可Line通知即時通訊的所有商家
    public function storesWithLineMessageNotify()
    {
        return $this->stores()
                    ->wherePivot('line_message', 1);
    }

    // relation LineBotUser
    public function lineBotUser()
    {
        return $this->hasOne(LineBotUser::class, 'line_id', 'line_id')->release();
    }

    // relation WebNotification 瀏覽器推播通知
    public function webNotification()
    {
        return $this->hasOne(\App\Models\WebNotification::class, 'target_id')->where('target_type', 'store_user');
    }

    // relation WebNotification 有效的瀏覽器推播通知
    public function liveWebNotification()
    {
        return $this->webNotification()->live();
    }

    // relation AuthToken
    public function pwd_tokens()
    {
        return $this->hasMany(\App\Models\AuthToken::class, 'target_id')->type('store_password');
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('show_flag', '!=', 0);
    }

    // 新增一筆關聯在store_user_relationship
    public function addStore($store_id)
    {
        return $this->liveStores()->attach($store_id);
    }
}
