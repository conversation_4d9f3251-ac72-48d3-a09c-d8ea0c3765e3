<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class StoreOrderSet extends Model
{
    use ToJsonDateTrait;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store_order_set';
    }

    protected $guarded = [];

    protected $casts = [
        'invoice_contact' => 'object',
        'store_detail'    => 'object',
    ];

    /**
     * 自動續約
     * @var string[]
     */
    public $autoPayList = [
        0      => '不同意',
        1      => '同意',
        'null' => '未選擇',
    ];

    /**
     * 目前的合約(設定費)訂單
     * relation Order
     */
    public function contractOrder()
    {
        return $this->belongsTo(Order::class, 'contract_order_id');
    }
}
