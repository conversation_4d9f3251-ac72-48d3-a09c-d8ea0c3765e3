<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Laracasts\Presenter\PresentableTrait;

class Order extends Model
{
    use ToJsonDateTrait;
    use PresentableTrait;

    protected $guarded = [];

    protected $presenter = 'App\Models\Presenters\Wdv2\OrderPresenter';

    protected $dates = ['expiration'];

    protected $casts = [
        'description' => 'object',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.order';
    }

    // 訂單內容 season:3期合約 half_year:6期合約 full_year:12期合約 usage_fee:使用費 renew:續約使用費
    public $contractTypeList = [
        'season'    => '3期合約',
        'half_year' => '6期合約',
        'full_year' => '12期合約',
        'usage_fee' => '使用費',
        'renew'     => '續約使用費'
    ];

    // 發票狀態 0:尚未開立 1:已開立 unnecessary:無須開立
    public $invoiceStatusList = [
        0             => '尚未開立',
        1             => '已開立',
        'unnecessary' => '無須開立',
    ];

    // 付款狀態 0:付款失敗 1:已付款 2:其他 unnecessary:無須付款
    public $paymentStatusList = [
        0             => '付款失敗',
        1             => '已付款',
        2             => '其他',
        'unnecessary' => '無須付款',
    ];

    // 付款方式 credit_card:信用卡 spgateway:匯款 other:其他
    public $paymentMethodList = [
        'credit_card' => '信用卡',
        'spgateway'   => '匯款',
        'other'       => '其他',
    ];

    // 日期類型 payment_at:付款日期 created_at:訂單成立日期
    public $dateTypeList = [
        'payment_at' => '付款日期',
        'created_at' => '訂單成立日期',
    ];

    // relation Store 所屬商家
    public function store()
    {
        return $this->belongsTo(\App\Models\Store::class);
    }

    // relation allStore 所有商家
    public function allStore()
    {
        return $this->store()->withTrashed();
    }

    // relation OrderPackage
    public function orderPackages()
    {
        return $this->hasMany(OrderPackage::class)->live();

        // return $this->hasManyThrough(
        //             OrderProduct::class,
        //             OrderPackage::class,
        //             'order_id',//order_package.order_id
        //             'id',//order_product.id
        //             'id',
        //             'order_product_id'//order_package.order_product_id
        //             )->select('order_package.*', 'order_product.type', 'order_product.fixed_price');
    }

    // relation allPaymentLogs 所有的信用卡交易記錄
    public function allPaymentLogs()
    {
        return $this->hasMany(PaymentLog::class);
    }

    // relation paymentLog 信用卡交易記錄
    public function paymentLog()
    {
        return $this->hasMany(PaymentLog::class)->success();
    }

    // relation paymentSpgatewayLog 離線匯款交易記錄
    public function paymentSpgatewayLog()
    {
        return $this->hasMany(PaymentSpgatewayLog::class, 'no_id', 'no_id')->success();
    }

    // relation OrderLog 付款的Log
    public function orderLogs()
    {
        return $this->hasMany(OrderLog::class);
    }

    // scope live 有效
    public function scopeLive($query)
    {
        return $query->where($this->getTable().'.show_flag', 2);
    }

    // scope unpaid 未付款
    public function scopeUnpaid($query)
    {
        return $query->where('payment_status', 0);
    }

    // scope paid 已付款
    public function scopePaid($query)
    {
        return $query->where('payment_status', 1);
    }

    /**
     * 是否含有自動續約的品項
     *
     * return bool
     */
    public function hasRenewingOrderProduct()
    {
        return $this->orderPackages->contains(function ($orderPackage, $key) {
            $orderProduct = new OrderProduct;
            return in_array($orderPackage->order_product_id, $orderProduct->setFeeContractType['renew']);
        });
    }

    /**
     * 取得合約訂單中，設定費的品項
     *
     * return $model $orderPackage
     */
    public function getContractOrderPackage()
    {
        return $this->orderPackages()
                    ->whereHas('orderProduct', function ($q) {
                        $q->where('type', 1);
                    })
                    ->first();
    }

    /**
     * 取得合約訂單中，折抵設定費品項
     *
     * return $model $orderPackages
     */
    public function getSettingDiscountOrderPackages()
    {
        return $this->orderPackages()
                    ->whereHas('orderProduct', function ($q) {
                        $q->where('type', 5);
                    })
                    ->get();
    }
}
