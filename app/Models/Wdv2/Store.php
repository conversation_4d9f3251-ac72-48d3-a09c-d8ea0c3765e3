<?php

namespace App\Models\Wdv2;

use Illuminate\Database\Eloquent\Model;

class Store extends Model
{
    protected $dates = ['edited_at'];

    protected $guarded = [];

    protected $casts = [
        'contact'     => 'object',
        'description' => 'object',
        'picture'     => 'object',
        'summary'     => 'object',
    ];

    // 商家類型-新舊資料庫的轉換索引列表 (舊=>新)
    public $convertTypeKeyList = [
            1  => 1,
            2  => 2,
            3  => 3,
            4  => 4,
            5  => 5,
            6  => 6,
            7  => 7,
            8  => 8,
            9  => 9,
            10 => 11,
            11 => 2,
            12 => 10,
            13 => 12,
            14 => 15,
            15 => 13,
            16 => 8,
            17 => 14,
            18 => 2,
            19 => 15,
            20 => 8,
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store';
    }

    // relation StoreOrderSet 發票資訊
    public function orderSet()
    {
        return $this->hasOne(StoreOrderSet::class);
    }

    // relation StoreAlbum 未停用的作品集
    public function liveAlbums()
    {
        return $this->hasMany(StoreAlbum::class)->live();
    }

    // relation StoreService 未停用的方案
    public function liveServices()
    {
        return $this->hasMany(StoreService::class)->live();
    }

    // relation StoreService 服務方案
    public function services()
    {
        return $this->hasMany(StoreService::class)->release()->sort();
    }

    // relation SetTag 所在地
    public function locations()
    {
        $setTag             = new SetTag;
        $setTagRelationship = new SetTagRelationship;

        // 所有所在地區域
        $locationAreaIds = $setTag->locationAreas()->pluck('id');
        $locationIds     = $setTag->whereIn('parent', $locationAreaIds)->pluck('id');

        return $this->belongsToMany(SetTag::class, $setTagRelationship->getTable(), 'object_id')
                    ->wherePivot('type', 1)
                    ->whereIn($setTag->getTable().'.id', $locationIds);
    }

    // relation ServiceStopApply 下架申請
    public function serviceStopApply()
    {
        return $this->hasMany(ServiceStopApply::class, 'store_id', 'id');
    }

    // relation DressContract 禮服合約
    public function dressContracts() {
        return $this->hasMany(DressContract::class)
                    ->where('show_flag', 2);
    }

    /**
     * 取得Wdv3的商家狀態
     *
     * @return string $status 商家狀態 published:上架中 pending:等待上架 checkout:下架清算中 leave:已下架 delete:停用
     */
    public function getStatusForWdv3()
    {
        // 停用：show_flag=0
        if (!$this->show_flag) {
            return 'delete';
        }

        // 下架清算中：未處理
        if ($this->serviceStopApply()->pending()->exists()) {
            return 'checkout';
        }

        // 等待上架：show_flag=1
        if ($this->show_flag == 1) {
            return 'pending';
        }

        // 已下架：level=1
        if ($this->level == 1) {
            return 'leave';
        }

        // 上架中：婚禮小物沒有合約 or 有目前合約訂單
        if ($this->type == 7 OR ($this->orderSet && $this->orderSet->contractOrder)) {
            return 'published';
        }

        // 預設已下架
        return 'leave';
    }

    /**
     * 取得Wdv3的商家類型
     *
     * @return string $type 商家類型
     */
    public function getTypeForWdv3()
    {
        return $this->convertTypeKeyList[$this->type];
    }
}
