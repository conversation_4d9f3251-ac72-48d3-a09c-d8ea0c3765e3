<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class PaymentLog extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.payment_logs';
    }

    // scope success 交易成功
    public function scopeSuccess($query)
    {
        return $query->where('status', 0);
    }
}
