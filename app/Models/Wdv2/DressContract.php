<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Database\Eloquent\Model;

class DressContract extends Model
{
    use ToJsonDateTrait;

    use ParseKeywordTrait;

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.store_pro_newlywed';
    }

    // relation StoreAlbum
    public function StoreAlbumWithTrashed()
    {
        return $this->belongsTo(\App\Models\StoreAlbum::class);
    }

    // relation DressOrder
    public function orders()
    {
        return $this->hasMany(DressOrder::class, 'store_pro_newlywed_id')
                    ->where('show_flag', '!=',  0);
    }
  }
