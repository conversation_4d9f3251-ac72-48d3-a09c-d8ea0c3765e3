<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class GoogleAnalytics extends Model
{
    use ToJsonDateTrait;

    protected $casts = [
        'description' => 'object',
    ];

    // 類型
    public $typeList = [
        1 => '商家',
        2 => '分享文',
        3 => '作品',
        4 => '方案',
        5 => '婚禮小物',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.google_analytics';
    }
}
