<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceStopApply extends Model
{
    use HasFactory;
    use ToJsonDateTrait;

    protected $guarded = [];

    protected $dates = ['closing_date'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.service_stop_applies';
    }

    /**
     * 處理狀態
     */
    public $statusList = [
        0 => '未處理',
        1 => '處理中',
        2 => '已結案',
    ];

    /**
     * 下架原因
     */
    public $systemFlagList = [
        0 => '自行下架',
        1 => '系統下架',
    ];

    /**
     * 商家還款狀態
     */
    public $payOffStatusList = [
        0 => '尚未還款',
        1 => '已還款',
        2 => '無須還款',
    ];

    /**
     * WD退款狀態
     */
    public $refundStatusList = [
        0 => '尚未退款',
        1 => '已退款',
        2 => '無須退款',
    ];

    /**
     * 折讓單開立狀態
     */
    public $depositStatusList = [
        0 => '尚未開立',
        1 => '已開立',
        2 => '無須開立',
    ];

    /**
     * relation StoreOrderSet
     * 取得商家訂單設定資料
     */
    public function orderSet()
    {
        return $this->belongsTo(StoreOrderSet::class, 'store_id', 'store_id');
    }

    /**
     * relation Store
     * 取得商家資料
     */
    public function store()
    {
        return $this->belongsTo(\App\Models\Store::class, 'store_id', 'id');
    }

    /**
     * relation SetFeeResidual
     * 取得商家設定費殘值
     */
    public function setFeeResidual()
    {
        return $this->hasMany(SetFeeResidual::class, 'service_stop_applies_id');
    }

    /**
     * relation YzcubeUser
     * 取得管理者資料
     */
    public function yzcubeUser()
    {
        return $this->belongsTo(\App\Models\YzcubeUser::class);
    }
    /**
     * relation ServiceStopApplyChangeLog
     * 取得狀態變更資料
     */
    public function serviceStopApplyChangeLog()
    {
        return $this->hasMany(\App\Models\ServiceStopApplyChangeLog::class, 'service_stop_applies_id');
    }

    /**
     * relation ServiceStopApplyComment
     * 取得留言資料
     */
    public function serviceStopApplyComment()
    {
        return $this->hasMany(\App\Models\ServiceStopApplyComment::class, 'service_stop_applies_id');
    }

    // scope pending 未處理
    public function scopePending($query)
    {
        return $query->where('status', 0);
    }

    // scope completed 已完成
    public function scopeCompleted($query)
    {
        return $query->where('status', 1);
    }
}
