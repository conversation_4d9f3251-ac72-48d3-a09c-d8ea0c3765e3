<?php

namespace App\Models\Wdv2;

use App\Traits\Model\ToJsonDateTrait;
use App\Traits\ParseKeywordTrait;
use Illuminate\Database\Eloquent\Model;

class MallItem extends Model
{
    use ToJsonDateTrait;

    use ParseKeywordTrait;

    // 商家類型 7:婚禮小物
    public $storeType = 7;

    protected $casts = [
        'description'=> 'object',
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // setTable
        $this->table = config('database.connections.mysql_wdv2.database').'.mall_item';
    }

    // scope release 上架
    public function scopeRelease($query)
    {
        return $query->where($this->getTable().'.show_flag', 2);
    }

    // scope storeHasPaid 限特定類型已付費的商家作品
    public function scopeStoreHasPaid($query)
    {
        // 上架
        $query = $query->release();

        // 商家類型 store_type (改用 join 提升效率)
        $query = $query->join((new MallStore)->getTable(), function($join) {
                            $join->on('mall_store.id', '=', 'mall_item.mall_store_id')
                                    ->where('mall_store.show_flag', 2);
                        })
                        ->join((new \App\Models\Store)->getTable(), function($join) {
                            $join->on('stores.id', '=', 'mall_store.store_id')
                                    ->where('stores.type', $this->storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope searchKeyword 關鍵字搜尋
    public function scopeSearchKeyword($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('mall_item.title', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope priceRange 價格區間搜尋
    public function scopePriceRange($query, $priceRange)
    {
        // 最低價以下
        if (!$priceRange[0]) {
            return $query->where('mall_item.price', '<=', $priceRange[1]);

        // 最高價以上
        } elseif (!$priceRange[1]) {
            return $query->where('mall_item.price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            return $query->whereBetween('mall_item.price', $priceRange);
        }
    }

    public function mallStore()
    {
        return $this->belongsTo(MallStore::class, 'mall_store_id', 'id');
    }
}
