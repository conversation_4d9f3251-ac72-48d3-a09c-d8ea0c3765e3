<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class UserWeddingSchedule extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // 團隊狀態
    public $statusList = [
        'pending'  => '尋找中',
        'booked'   => '已下訂',
        'nullable' => '不需要',
    ];

    // relation Brand
    public function brands()
    {
        return $this->belongsToMany(Brand::class, 'user_wedding_brands');
    }

    // relation Brand
    public function brandsWithPostCount()
    {
        return $this->brands()->withCount('articles');
    }

    // scope complete
    public function scopeComplete($query)
    {
        return $query->where('status', '!=', 'pending');
    }

}
