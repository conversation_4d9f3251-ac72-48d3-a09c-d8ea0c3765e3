<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CityData extends Model
{
    protected $table = 'city_data';

    public $timestamps = false;

    // relation Self 分區下的縣市
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id')->sort();
    }

    // relation Store 商家的所在地
    public function stores()
    {
        return $this->hasMany(Store::class, 'city_id');
    }

    // relation Store 喜餅商家的門市所在地
    public function weddingcakeShopStores()
    {
        return $this->belongsToMany(Store::class, (new WeddingcakeShop)->getTable(), 'city_id', 'store_id');
    }

    // relation studioAlbumImages 拍婚紗的作品照資訊
    public function studioAlbumImages()
    {
        return $this->hasMany(StudioAlbumImage::class, 'city_id');
    }

    // relation StoreFare 商家的車馬費
    public function storeFares()
    {
        return $this->hasMany(StoreFare::class, 'city_id');
    }

    // relation Store 免車馬費的商家
    public function freeFareStores()
    {
        return $this->belongsToMany(Store::class, (new StoreFare)->getTable(), 'city_id', 'store_id')
                    ->wherePivot('value', 1);
    }

    // scope region 縣市分區
    public function scopeRegion($query)
    {
        return $query->whereNull('parent_id')->sort();
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }
}
