<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class EventStore extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'event_store';

    protected $fillable = [
        'event_id',
        'store_id',
        'type'
    ];

    protected $casts = [
        'event_id' => 'int',
        'store_id' => 'int',
        'type'     => 'string',
    ];
}