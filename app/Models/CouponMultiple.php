<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CouponMultiple extends Model
{
    protected $table = 'coupon_multiple';

    public $timestamps = false;

    protected $guarded = [];

    // relation Coupon 優惠卷
    public function coupon()
    {
        return $this->belongsTo(Coupon::class);
    }

    // relation Event 活動表單
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // relation EventReport 活動報表
    public function eventReport()
    {
        return $this->belongsTo(EventReport::class);
    }

    // relation EventReport 包含刪除的活動報表
    public function eventReportWithTrashed()
    {
        return $this->eventReport()->withTrashed();
    }

    // scope unused 未使用過的
    public function scopeUnused($query)
    {
        return $query->whereNull('used_at');
    }

    // scope used 已使用過的
    public function scopeUsed($query)
    {
        return $query->whereNotNull('used_at');
    }
}
