<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventProduct extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // relation Image 產品圖
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'event_product_image');
    }

    // relation EventProductItem 活動商品品項
    public function items()
    {
        return $this->hasMany(EventProductItem::class);
    }
}
