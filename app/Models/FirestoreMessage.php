<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FirestoreMessage extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    // 結算狀態
    // public $typeList = [
    //     NULL              => '計算中',
    //     'effective'       => '有效',
    //     'empty_date'      => '無效-結算時新娘無婚期',
    //     'full_date'       => '無效-商家滿檔',
    //     'user_unread_48h' => '無效-新娘48小時未讀',
    // ];

    // relation Store 商家
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * 儲存結算結果
     *
     * @return void
     */
    public function saveSettleResult($status, $settleAt, $roomRef)
    {
        $this->settle_status = $status;
        $this->settle_at     = $settleAt;
        $this->save();

        // 更新聊天室
        $roomRef->update([
            ['path' => 'settle_status', 'value' => $status],
            ['path' => 'settle_at', 'value' => $settleAt],
        ]);
    }
}
