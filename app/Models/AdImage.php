<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdImage extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    use SoftDeletes;

    // 類型 banner:首頁輪播 mobile_banner:手機側邊欄Banner float:浮動廣告
    public $campaignTypeList = [
        'banner'        => '首頁輪播',
        'mobile_banner' => '手機側邊欄Banner',
        'float'         => '浮動廣告',
    ];

    // 狀態 pending:未使用 backup:補位圖片 using:活動使用中
    public $statusList = [
        'pending' => '未使用',
        'backup'  => '補位圖片',
        'using'   => '活動使用中',
    ];

    // 連結類型 none:無連結 internal:內部連結 external:外部連結
    public $linkTypeList = [
        'none'     => '無連結',
        'internal' => '內部連結',
        'external' => '外部連結',
    ];

    // relation Image
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')->whereIn('type', ['ad_image', 'ad_image_xs']);
    }

    // relation Image
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'ad_image');
    }

    // relation Image
    public function image_xs()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'ad_image_xs');
    }

    // relation AdCampaign
    public function campaign()
    {
        return $this->belongsTo(AdCampaign::class, 'ad_campaign_id');
    }

    // scope backup 補位圖片
    public function scopeBackup($query, $status = 1)
    {
        return $query->where('is_backup', $status);
    }

    // scope pending 未使用
    public function scopePending($query)
    {
        return $query->backup(0)->using(false);
    }

    // scope using 活動使用中
    public function scopeUsing($query, $status = true)
    {
        if ($status) {
            return $query->whereNotNull('ad_campaign_id');
        }

        return $query->whereNull('ad_campaign_id');
    }
}
