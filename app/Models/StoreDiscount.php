<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StoreDiscount extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;

    protected $guarded = [];

    // scope published
    public function scopePublished($query)
    {
        return $query->where(function ($q) {
                            $q->whereNull('start_date')->orWhere('start_date', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('end_date')->orWhere('end_date', '>', now());
                        });
    }
}
