<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class <PERSON><PERSON>rand extends Model
{
    public $timestamps = false;

    protected $casts = [
        'reserve_time_list' => 'array',
        'reserve_item_list' => 'array',
    ];

    // relation RingStore 婚戒大賞-門市
    public function ringStores()
    {
        return $this->hasMany(RingStore::class);
    }

    // relation RingOrder 婚戒大賞-預約紀錄
    public function reserves()
    {
        return $this->hasMany(RingReserve::class);
    }

    // relation RingPoint 婚戒大賞-點數紀錄
    public function points()
    {
        return $this->hasMany(RingPoint::class);
    }

    // relation RingOrder 婚戒大賞-訂單紀錄
    public function orders()
    {
        return $this->hasMany(RingOrder::class);
    }

    // relation RingOrder 婚戒大賞-已認證訂單紀錄
    public function approvedOrders()
    {
        return $this->orders()->approved();
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }
}
