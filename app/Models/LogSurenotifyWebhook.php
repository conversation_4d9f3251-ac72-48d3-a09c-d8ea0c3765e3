<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LogSurenotifyWebhook extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'content' => 'json',
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

     // 事件類型 delivery:到達|open:開信|click:點擊|bounce:退信|complaint:抱怨
     protected $sendStatusMapping = [
        'complaint' => '抱怨',
        'bounce' => '拒絕',
        'delivery' => '已送達',
    ];

    /**
     * 取得寄送與開啟狀態
     */
    public function ScopeGetStatusByMesId($query, $messageId)
    {
        $statusArr = $query->where('message_id', $messageId)
            ->orderby('id', 'desc')
            ->get()->pluck('event')->toArray();

        $sendStatus = '已傳送';
        foreach ($this->sendStatusMapping as $eventType => $status) {
            if (in_array($eventType, $statusArr)) {
                $sendStatus = $status;
                break;
            }
        }

        return $sendStatus;
    }
}
