<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class YzcubeUser extends Model
{
    protected $guarded = [];

    // 狀態 pending:未驗證 published:已驗證 delete:刪除
    public $statusList = [
        'pending'   => '未驗證',
        'published' => '已驗證',
        'delete'    => '刪除',
    ];

    // relation AuthToken 身份驗證紀錄
    public function tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('yzcube');
    }

    // relation AuthToken 身份驗證紀錄(修改密碼)
    public function pwd_tokens()
    {
        return $this->hasMany(AuthToken::class, 'target_id')->type('yzcube_password');
    }

    // scope live 有效的
    public function scopeLive($query)
    {
        return $query->where('status', '!=', 'delete');
    }

    // relation ReserveAppeal 無效詢問申請
    public function reserveAppeal()
    {
        return $this->hasMany(Wdv2\ReserveAppeal::class);
    }
}
