<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class Brand extends Model
{
    use SoftDeletes;

    use ToJsonDateTrait;

    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\BrandPresenter';

    protected $guarded = [];

    // 狀態 pending:待審核 published:已認證
    public $statusList = [
        'pending'    => '待審核',
        'published'  => '已認證',
    ];

    // relation ForumArticle 分享文
    public function articles()
    {
        return $this->belongsToMany(ForumArticle::class, 'forum_article_brand', 'brand_id', 'article_id')
                    ->wherePivot('deleted_at', NULL)
                    ->withPivot('rank')
                    ->release();
    }

    // relation ForumArticle 有評價分數的分享文
    public function hasRankArticles()
    {
        return $this->articles()
                    ->wherePivot('rank', '>', 0);
    }

    // relation Store
    public function stores()
    {
        return $this->belongsToMany(Store::class, (new Wdv2\ShareStoreLink)->getTable(), 'share_store_id', 'store_id')
                    ->wherePivot('show_flag', '!=', 0)
                    ->where((new Store)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation UserBrandWish
    public function userWishes()
    {
        return $this->hasMany(UserBrandWish::class);
    }

    // relation LogRepeatBrand
    public function logRepeats()
    {
        return $this->hasMany(LogRepeatBrand::class);
    }

    // relation primaryStores 主要商家
    public function primaryStores()
    {
        return $this->stores()
                    // ->withPivot('show_flag')
                    // ->orderBy('pivot_show_flag', 'DESC')
                    ->wherePivot('show_flag', 2); // 主要商家
    }

    // relation paidStores 已付費商家
    public function paidStores()
    {
        return $this->primaryStores()
                    ->published();
    }

    // relation BrandSubscription
    public function subscription()
    {
        return $this->hasOne(BrandSubscription::class);
    }

    // scope quoteSubscribers 排除取消訂閱-公開報價通知
    public function scopeQuoteSubscribers($query)
    {
        return $query->whereDoesntHave('subscription', function ($q) {
                            $q->where('user_quote', 0);
                        });
    }

    // 取得品牌最高評分的推薦文
    public function getHighestRankArticleByAuthor($user)
    {
        return $this->articles()
                    ->withPivot('rank', 'user_created')
                    ->orderBy('pivot_rank', 'DESC')
                    ->where('user_id', $user->id)
                    ->first();
    }
}
