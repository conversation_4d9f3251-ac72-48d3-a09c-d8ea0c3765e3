<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ForumCategory extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;

    protected $guarded = [];

    // 狀態 pending:待處理 published:發佈
    public $statusList = [
        'pending'   => '待處理',
        'published' => '發佈',
    ];

    // relation ForumArticle
    public function articles()
    {
        return $this->hasMany(ForumArticle::class, 'category_id');
    }

    // scope live
    public function scopeLive($query)
    {
        return $query->where('status', 'published');
    }

    // scope sort
    public function scopeSort($query)
    {
        return $query->orderBy('sequence')
                    ->orderBy('created_at', 'DESC');
    }

    // scope publicUse
    public function scopePublicUse($query)
    {
        return $query->where('is_public_use', 1);
    }
}
