<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    // 發票類型 paper:紙本列印 email:E-mail通知 carrier:存載具
    public $typeList = [
        'paper'   => '紙本列印',
        'email'   => 'E-mail通知',
        'carrier' => '存載具/捐贈',
    ];

    // 發票狀態 success:已開立 invalid:已作廢
    public $statusList = [
        'success' => '已開立',
        'invalid' => '已作廢',
    ];

    // 上傳狀態 pending:未上傳 completed:已上傳成功 uploading:上傳中 failed:上傳失敗 timeout:上傳逾時
    public $uploadList = [
        'pending'   => '未上傳',
        'completed' => '已上傳成功',
        'uploading' => '上傳中',
        'failed'    => '上傳失敗',
        'timeout'   => '上傳逾時',
    ];

    // ezPay 的上傳狀態 0:未上傳 1:已上傳成功 2:上傳中 3:上傳失敗 4:上傳逾時
    public $ezPayUploadList = [
        0 => 'pending', // 未上傳
        1 => 'completed', // 已上傳成功
        2 => 'uploading', // 上傳中
        3 => 'failed', // 上傳失敗
        4 => 'timeout', // 上傳逾時
    ];

    // 載具類型 phone:手機條碼 citizen:自然人憑證條碼 donate:愛心捐贈碼
    public $carrierTypeList = [
        'phone'   => '手機條碼',
        'citizen' => '自然人憑證條碼',
        'donate'  => '愛心捐贈碼',
    ];

    // 關鍵字搜尋項目 buyer_ubn:買受人統編 buyer_name:買受人名稱 order_no:訂單編號 invoice_number:發票號碼
    public $searchTypeList = [
        'buyer_ubn'      => '買受人統編',
        'buyer_name'     => '買受人名稱',
        'order_no'       => '訂單編號',
        'invoice_number' => '發票號碼',
    ];

    // relation InvoiceSetting 發票設定
    public function setting()
    {
        return $this->belongsTo(InvoiceSetting::class, 'invoice_setting_id');
    }

    // relation InvoiceItem 發票品項紀錄
    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    // relation InvoiceAllowance 發票折讓紀錄
    public function allowances()
    {
        return $this->hasMany(InvoiceAllowance::class);
    }

    // relation InvoiceAllowance 發票折讓紀錄(包含作廢折讓)
    public function allowancesWithTrashed()
    {
        return $this->allowances()->withTrashed();
    }

    // relation LogInvoiceEmail 電子發票通知紀錄
    public function logEmails()
    {
        return $this->hasMany(LogInvoiceEmail::class)->sort();
    }

    // relation eventOrder 活動訂單
    public function eventOrder()
    {
        return $this->hasOne(EventOrder::class, 'order_no', 'order_no');
    }

}
