<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use App\Traits\ParseKeywordTrait;

class StoreVideo extends Model
{
    use SoftDeletes;

    use ParseKeywordTrait;

    protected $guarded = [];

    protected $dates = ['edited_at'];

    protected $casts = [
        'oembed_data' => 'object',
    ];

    // 狀態 hide:隱藏 show:顯示
    public $statusList = [
        'hide' => '隱藏',
        'show' => '顯示',
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // relation Store 所屬商家
    public function allStore()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    // relation Store 包含刪除的所屬商家
    public function allStoreWithTrashed()
    {
        return $this->allStore()->withTrashed();
    }

    // relation Store 所屬上架商家
    public function store()
    {
        return $this->allStore()->published();
    }

    // relation 婚攝/婚錄的影片欄位
    public function photographerVideo()
    {
        return $this->hasOne(PhotographerVideo::class, 'video_id');
    }

    // relation 婚禮佈置的影片欄位
    public function decorationVideo()
    {
        return $this->hasOne(DecorationVideo::class, 'video_id');
    }

    // relation 商家不同類型的影片欄位
    public function storeTypeVideo()
    {
        // 驗證商家類型的影片資訊 Model 是否存在
        $storeTypeKey = $this->allStoreWithTrashed->typeKeyList[$this->allStoreWithTrashed->type];
        $classPath    = 'App\Models\\'.Str::studly($storeTypeKey).'Video';
        if (!class_exists($classPath)) {
            return NULL;
        }

        return $this->hasOne($classPath, 'video_id');
    }

    // relation StoreMember
    public function members()
    {
        return $this->belongsToMany(StoreMember::class, 'store_video_member', 'video_id', 'member_id');
    }

    // relation StoreFull 商家的檔期
    public function storeFull()
    {
        return $this->hasMany(Wdv2\StoreFull::class, 'store_id', 'store_id');
    }

    // scope status 狀態
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope storeHasPaid 限特定類型已付費的商家影片
    public function scopeStoreHasPaid($query, $storeType)
    {
        // 狀態 show:顯示
        $query = $query->status('show');

        // 商家類型 store_type (改用 join 提升效率)
        // $query = $query->whereHas('store', function ($q) use ($storeType) {
        //     $q = $q->published($storeType);
        // });
        $query = $query->join((new Store)->getTable(), function($join) use ($storeType) {
                            $join->on('stores.id', '=', 'store_videos.store_id')
                                    ->where('stores.type', $storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new Wdv2\StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope searchKeyword 關鍵字搜尋
    // public function scopeSearchKeyword($query, $keyword)
    // {
    //     $keywords = $this->splitToArray($keyword);
    //     return $query->where(function ($q) use ($keywords) {
    //         foreach ($keywords as $val) {
    //             $q = $q->orWhere('store_videos.name', 'like', '%' . $val . '%');
    //         }
    //     });
    // }

    // scope searchKeywordWithStore 關鍵字搜尋含商家
    public function scopeSearchKeywordWithStore($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('store_videos.name', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchWeddingDate 婚期搜尋
    public function scopeSearchWeddingDate($query, $date)
    {
        return $query->where(function ($q1) use ($date) {
            // 上午沒有滿檔
            $q1 = $q1->whereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 1);
                    })
                    // 或下午沒有滿檔
                    ->orWhereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 2);
                    });
        });
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy($this->getTable().'.sequence')
                    ->orderBy($this->getTable().'.created_at', 'DESC');
    }
}
