<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WeddingcakeCookieItem extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    protected $casts = [
        'other' => 'array',
    ];

    // 葷素類型 meat:葷 lacto_vegetarian:奶素 ovo_lacto_vegetarian:蛋奶素 wuxinsu:五辛素 vegan:全素
    public $veganTypeList = [
        'meat'                 => '葷',
        'lacto_vegetarian'     => '奶素',
        'ovo_lacto_vegetarian' => '蛋奶素',
        'wuxinsu'              => '五辛素',
        'vegan'                => '全素',
    ];

    // 其他 alcohol:含酒精 nut:含堅果 gluten-free:無麩質
    public $otherList = [
        'alcohol'     => '含酒精',
        'nut'         => '含堅果',
        'gluten-free' => '無麩質',
    ];

    // relation Image 喜餅照
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'weddingcake_cookie_item');
    }
}
