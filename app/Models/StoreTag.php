<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreTag extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    // 禮服類型標籤
    public $dressTypeId = 1014;

    // 禮盒類型標籤
    public $weddingboxTypeId = 1107;

    // relation Self 子商家標籤
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id')->sort();
    }

    // relation StoreTagEnum 商家標籤列舉值
    public function enum()
    {
        return $this->hasMany(StoreTagEnum::class);
    }

    // relation Store 商家
    public function stores()
    {
        return $this->belongsToMany(Store::class, 'store_tag_pivot', 'store_tag_id', 'target_id')
                    ->wherePivot('target_type', 'store')
                    ->withPivot('value');
    }

    // relation Store 有勾選提供服務的商家
    public function providStores()
    {
        return $this->stores()->wherePivot('value', '>', 0);
    }

    // relation StoreService 商家方案
    public function services()
    {
        return $this->belongsToMany(StoreService::class, 'store_tag_pivot', 'store_tag_id', 'target_id')
                    ->wherePivot('target_type', 'service')
                    ->withPivot('value');
    }

    // relation StoreService 有勾選提供服務的商家方案
    public function providServices()
    {
        // 有勾選就有資料、沒勾選就沒資料
        // return $this->services()->where(function($query) {
        //                             $query->where('store_tag_pivot.value', '>', 0)
        //                                     ->orWhereNull('store_tag_pivot.value');
        //                         });
        return $this->services();
    }

    // relation StoreAlbum 商家相本集
    public function albums()
    {
        return $this->belongsToMany(StoreAlbum::class, 'store_tag_pivot', 'store_tag_id', 'target_id')
                    ->wherePivot('target_type', 'album');
    }

    // relation StoreAlbumImage 商家相本照
    public function albumImages()
    {
        return $this->belongsToMany(StoreAlbumImage::class, 'store_tag_pivot', 'store_tag_id', 'target_id')
                    ->wherePivot('target_type', 'album_image');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }

    // scope simpleTypes 簡易類型標籤 (用於商家後台的作品列表)
    public function scopeSimpleTypes($query, $storeTypeKey)
    {
        switch ($storeTypeKey) {

            // 禮服類型標籤 (禮服)
            case 'dress':
                $parentId = $this->dressTypeId;
                break;

            // 禮盒類型標籤 (喜餅)
            case 'weddingcake':
                $parentId = $this->weddingboxTypeId;
                break;

            // 預設關聯不到任何東西
            default:
                $parentId = 0;
                break;
        }

        // return $query->select($this->getTable().'.id')
        return $query->where('parent_id', $parentId);
    }

    /**
     * 依類型取得後台的設定列表
     *
     *  @return list
     */
    public function getSettingListByType($storeType, $type)
    {
        $storeTagCategory = StoreTagCategory::where('store_type', $storeType)
                                            ->where('type', $type)
                                            ->first();
        if (!$storeTagCategory) {
            return collect([]);
        }

        return $this->with('children')
                    ->where('category_id', $storeTagCategory->id)
                    ->whereNull('parent_id')
                    ->sort()
                    ->get()
                    ->map(function($parent) {
                        return [
                            'id'   => $parent->id,
                            'name' => $parent->name,
                            'list' => $parent->children->map(function($children) {
                                        return [
                                            'tag_id' => $children->id,
                                            'name'   => $children->name,
                                            'values' => $children->enum->map(function($item) {
                                                return $item->only(['value', 'name']);
                                            }),
                                        ];
                                    }),
                        ];
                    });
    }

    /**
     * 依類型取得前台的篩選列表
     *
     *  @return list
     */
    public function getSearchListByType($storeType, $type, $keyword, $weddingDate = NULL, $freeShopTasting = NULL, $forService = false)
    {
        $storeTagCategory = StoreTagCategory::where('store_type', $storeType)
                                            ->where('type', $type)
                                            ->first();
        if (!$storeTagCategory) {
            return false;
        }

        return $this->select('id', 'name')
                    ->where('category_id', $storeTagCategory->id)
                    ->whereNull('parent_id')
                    ->with([
                        'children' => function($q1) use ($storeType, $type, $keyword, $weddingDate, $freeShopTasting, $forService) {
                            // 限特定類型的已付費商家數
                            $q1->select('id', 'name', 'parent_id')
                                ->whereNotIn('id', [253, 254]); // 排除場地「不提供專員服務」、「不提供現場佈置」的方案標籤

                            // 商家提供的服務
                            if ($type == 'store') {
                                $q1->with(['providStores' => function($q2) use ($storeType, $keyword, $weddingDate, $freeShopTasting, $forService) {
                                    $q2->published($storeType)
                                        ->usageActive()
                                        ->searchWeddingDate($weddingDate)
                                        ->freeShopTasting($freeShopTasting);
                                    if ($forService) {
                                        $q2->searchKeywordForService($keyword);
                                    } else {
                                        $q2->searchKeyword($keyword);
                                    }
                                }]);
                            }

                            // 方案包含的服務
                            if ($type == 'service') {
                                $q1->with(['providServices' => function($q2) use ($storeType, $keyword, $weddingDate) {
                                    $q2->storeHasPaid($storeType)
                                        ->storeUsageActive()
                                        ->searchKeywordWithStore($keyword)
                                        ->searchWeddingDate($weddingDate);
                                }]);
                            }

                            // 作品集標籤 (只有婚紗禮服/婚禮佈置需要, 都沒有篩選婚期)
                            // (婚紗禮服標籤量太大了，決定先不計算數量)
                            if ($type == 'album' && $storeType != 2) {
                                $q1->with(['albums' => function($q2) use ($storeType, $keyword) {
                                    $q2->storeHasPaid($storeType)
                                        ->storeUsageActive()
                                        ->searchKeywordWithStore($keyword);
                                }]);
                            }

                            // 作品照標籤
                            if ($type == 'album_image') {
                                $q1->with(['albumImages.album' => function($q2) use ($storeType, $keyword, $weddingDate) {
                                    $q2->storeHasPaid($storeType)
                                        ->storeUsageActive()
                                        ->searchKeywordWithStore($keyword)
                                        ->searchWeddingDate($weddingDate);
                                }]);
                            }
                        }
                    ])
                    ->sort()
                    ->get();
    }

    /**
     * 依類型取得前台特定商家的篩選列表
     *
     *  @return list
     */
    public function getSearchListByStore($store, $type)
    {
        $storeTagCategory = StoreTagCategory::where('store_type', $store->type)
                                            ->where('type', $type)
                                            ->first();
        if (!$storeTagCategory) {
            return false;
        }

        return $this->select('id', 'name')
                    ->where('category_id', $storeTagCategory->id)
                    ->whereNull('parent_id')
                    ->with([
                        'children' => function($q1) use ($store, $type) {
                            // 限特定類型的已付費商家數
                            $q1->select('id', 'name', 'parent_id');

                            // 商家提供的服務
                            if ($type == 'store') {
                                $q1->with(['providStores' => function($q2) use ($store) {
                                    $q2->find($store->id);
                                }]);
                            }

                            // 方案包含的服務
                            if ($type == 'service') {
                                $q1->with(['providServices' => function($q2) use ($store) {
                                    $q2->where('store_id', $store->id);
                                }]);
                            }

                            // 作品集標籤 (只有婚紗禮服/婚禮佈置需要, 都沒有篩選婚期)
                            if ($type == 'album') {
                                $q1->with(['albums' => function($q2) use ($store) {
                                    $q2->where('store_id', $store->id);
                                }]);
                            }

                            // 作品照標籤
                            if ($type == 'album_image') {
                                $q1->with(['albumImages.album' => function($q2) use ($store) {
                                    $q2->where('store_id', $store->id);
                                }]);
                            }
                        }
                    ])
                    ->sort()
                    ->get();
    }
}
