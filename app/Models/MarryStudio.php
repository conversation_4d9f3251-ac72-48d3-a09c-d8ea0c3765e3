<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MarryStudio extends Model
{
    protected $guarded = [];

    // 商家類別
    public $typeArray = [
        'prewedding', // 1.拍婚紗
        'dress', // 2.婚紗禮服
        'record', // 3.婚禮攝影
        'makeup', // 4.新娘秘書
        'venue', // 5.婚宴場地
        'decor', // 6.婚禮佈置
        'shoes', // 婚鞋
        'invitations', // 喜帖/謝卡
        'wedding_ring', // 婚戒
        'surrounding', // 周邊
        'favors', // 婚禮小物
        'bride_cake', // 喜餅
        'limousine', // 禮車
        'planners', // 8.婚禮主持/企劃
    ];

    protected $storeTypes = [
        3 => [
            'types'    => ['record'],
            'keywords' => ['婚禮攝影', '婚禮紀錄', '婚攝', '婚錄'],
        ],
        4 => [
            'types'    => ['makeup'],
            'keywords' => ['新秘', '新娘秘書', '造型', '彩妝', 'makeup', 'make-up'],
        ],
        6 => [
            'types'    => ['decor'],
            'keywords' => ['佈置', '花藝', '婚佈'],
        ],
        8 => [
            'types'    => ['planners'],
            'keywords' => ['主持', '婚禮顧問', '派對顧問'],
        ],
    ];

    // relation MarryStudioSubscription 取消訂閱
    public function subscription()
    {
        return $this->hasOne(MarryStudioSubscription::class);
    }

    // scope emailNotEmpty Email不為空
    public function scopeEmailNotEmpty($query)
    {
        return $query->where(function ($q) {
                        $q->whereNotNull('email')
                            ->where('email', '!=', '');
                    });
    }

    // scope quoteSubscribers 排除取消訂閱-公開報價通知
    public function scopeQuoteSubscribers($query)
    {
        return $query->whereDoesntHave('subscription', function ($q) {
                            $q->where('user_quote', 0);
                        });
    }

    // scope storeType 特定的商家類型
    public function scopeStoreType($query, $type)
    {
        $storeType = $this->storeTypes[$type];
        return $query->where(function ($q) use ($storeType) {
                        $q = $q->whereIn('type', $storeType['types']);
                        foreach ($storeType['keywords'] as $keyword) {
                            $q = $q->orWhere('name', 'like', '%'.$keyword.'%');
                        }
                    });
    }

    // scope unknownStoreTypes 未知的商家類型
    public function scopeUnknownStoreTypes($query)
    {
        $query = $query->whereNull('type');

        $keywords = collect($this->storeTypes)->pluck('keywords')->collapse();
        foreach ($keywords as $keyword) {
            $query = $query->where('name', 'not like', '%'.$keyword.'%');
        }

        return $query;
    }

    // scope excludeStoreEmail 排除已存在的商家Email
    public function scopeExcludeStoreEmail($query)
    {
        return $query->where('has_store_email', 0)
                    ->where('has_store_contact_email', 0);
    }
}
