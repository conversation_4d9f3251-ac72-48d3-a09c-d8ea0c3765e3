<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DressContract extends Model
{
    protected $guarded = [];

    // relation DressOrder 商家禮服合約的訂單
    public function orders()
    {
        return $this->hasMany(DressOrder::class, 'contract_id');
    }

    // relation DressOrder 已排序的禮服合約
    public function sortOrders() {
        return $this->orders()->sortWeddingDate('DESC');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('id', 'DESC');
    }

    // scope weddingDateRange 婚期區間
    public function scopeWeddingDateRange($query, $startDate, $endDate) {
        
        $query = $query->whereHas('orders', function ($q1) use ($startDate, $endDate) {
            if ($startDate && $endDate) {
                $q1 = $q1->whereBetween('wedding_date', [$startDate, $endDate]);
            } else if (!$startDate && $endDate) {
                $q1 = $q1->where('wedding_date', '<=', $endDate);
            } else if ($startDate && !$endDate) { 
                $q1 = $q1->where('wedding_date', '>=', $startDate);
            }
        });

        return $query;
    }
}
