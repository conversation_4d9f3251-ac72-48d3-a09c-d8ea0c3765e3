<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\ParseKeywordTrait;

class StoreService extends Model
{
    use SoftDeletes;

    use ParseKeywordTrait;

    protected $guarded = [];

    protected $dates = ['edited_at'];

    // 狀態 hide:隱藏 show:顯示
    public $statusList = [
        'hide' => '隱藏',
        'show' => '顯示',
    ];

    // 方案需求(含價格區間)架構
    public $demandStructure = [
        // 拍婚紗 Scope
        1 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '2 萬以下', 'value' => ['', 20000]],
                    ['label' => '2 萬 ~ 3 萬', 'value' => [20000, 30000]],
                    ['label' => '3 萬 ~ 4 萬', 'value' => [30000, 40000]],
                    ['label' => '4 萬 ~ 5 萬', 'value' => [40000, 50000]],
                    ['label' => '5 萬 ~ 6 萬', 'value' => [50000, 60000]],
                    ['label' => '6 萬以上', 'value' => [60000, '']],
                ],
            ],
            // 方案類型
            'type' =>[
                'list' => [
                    'wedding'   => '婚紗攝影',
                    'maternity' => '孕婦寫真',
                    'baby'      => '寶寶寫真',
                    'family'    => '全家福',
                    'friend'    => '閨蜜寫真',
                    'personal'  => '個人寫真',
                    'couple'    => '情侶寫真',
                ],
            ],
            // 檔案全贈
            'studioPhotoFile' => [
                'tag_id' => 163,
                'list'   => true,
            ],
            // 拍攝便服
            'studioCasualWear' => [
                'tag_id' => 156,
                'list'   => true,
            ],
            // 拍攝白紗
            'studioWhiteDress' => [
                'tag_id' => [154],
                'list'   => ['1 套', '2 套', '3 套以上'],
            ],
            // 拍攝禮服
            'studioDress' => [
                'tag_id' => [155],
                'list'   => ['1 套', '2 套', '3 套以上'],
            ],
            // 宴客白紗
            'weddingWhiteDress' => [
                'tag_id' => [159],
                'list'   => ['不需要', '1 套以上'],
            ],
            // 宴客禮服
            'weddingDress' => [
                'tag_id' => [160],
                'list'   => ['不需要', '1 套', '2 套', '3 套', '4 套以上'],
            ],
            // 精修照
            'studioAlbumPhoto' => [
                'tag_id' => 166,
                'list'   => ['10 張以下', '11 ~ 20 張', '21 ~ 30 張', '31 張以上'],
            ],
        ],

        // 婚紗禮服 Scope
        2 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1 萬以下', 'value' => ['', 10000]],
                    ['label' => '1 萬 ~ 2 萬', 'value' => [10000, 20000]],
                    ['label' => '2 萬 ~ 3 萬', 'value' => [20000, 30000]],
                    ['label' => '3 萬 ~ 4 萬', 'value' => [30000, 40000]],
                    ['label' => '4 萬以上', 'value' => [40000, '']],
                ],
            ],
            // 方案類型
            'type' =>[
                'list' => [
                    'wedding'    => '婚紗禮服',
                    'bridesmaid' => '伴娘服',
                    'mother'     => '媽媽服',
                    'child'      => '花童服',
                    'suit'       => '西服',
                ],
            ],
            // 拍攝用
            'forStudio' => [
                'tag_id' => [168, 170],
                'list'   => true,
            ],
            // 宴客用
            'forWedding' => [
                'tag_id' => [169, 170],
                'list'   => true,
            ],
            // 拍照白紗
            'studioWhiteDress' => [
                'tag_id' => [172, 176],
                'list'   => ['1 件', '2 件', '3 件', '4 件', '5 件以上'],
            ],
            // 拍攝禮服
            'studioDress' => [
                'tag_id' => [173, 177],
                'list'   => ['1 件', '2 件', '3 件', '4 件', '5 件以上'],
            ],
            // 宴客白紗
            'weddingWhiteDress' => [
                'tag_id' => [174, 176],
                'list'   => ['1 件', '2 件', '3 件', '4 件', '5 件以上'],
            ],
            // 宴客禮服
            'weddingDress' => [
                'tag_id' => [175, 177],
                'list'   => ['1 件', '2 件', '3 件', '4 件', '5 件以上'],
            ],
        ],

        // 婚攝/婚錄 Scope
        3 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1 萬以下', 'value' => ['', 10000]],
                    ['label' => '1 萬 ~ 2 萬', 'value' => [10000, 20000]],
                    ['label' => '2 萬 ~ 3 萬', 'value' => [20000, 30000]],
                    ['label' => '3 萬 ~ 4 萬', 'value' => [30000, 40000]],
                    ['label' => '4 萬 ~ 5 萬', 'value' => [40000, 50000]],
                    ['label' => '5 萬以上', 'value' => [50000, '']],
                ],
            ],
            // 方案類型
            'type' =>[
                'list' => [
                    'photography' => '平面婚攝',
                    'video'       => '動態婚錄',
                    'both'        => '婚攝+婚錄',
                    'other'       => '其他服務',
                ],
            ],
            // 證婚需求
            'witnessCeremony' => [
                'tag_id' => [246,248],
                'list'   => true,
            ],
            // 習俗儀式類型
            'ceremonyType' => [
                'tag_id' => [[187,239], [188,239], [184]], // 因為後台新增「單/雙儀式皆適用」，所以改為陣列
                'list'   => ['單儀式(文定/迎娶)', '雙儀式(文定+迎娶)', '無習俗儀式'], // 無習俗儀式 => 「僅宴客，無習俗儀式」
            ],
            // 婚宴時段
            'banquetTime' => [
                'tag_id' => [[194,240], [195,240], [196], [183]], // 因為後台新增「午/晚宴皆適用」，所以改為陣列
                'list'   => ['午宴', '晚宴', '午宴 + 晚宴', '無宴客'], // 無宴客 => 「僅有習俗儀式，無宴客」
            ],
            // 拍攝時數
            'photographerTime' => [
                'tag_id' => [199, 199, 199, 200],
                'list'   => ['4 小時以下', '5 ~ 8 小時', '9 ~ 15 小時', '不限時拍到結束'],
            ],
            // 攝影師人數
            'photographerCount' => [
                'tag_id' => 198,
                'list'   => ['1 位', '2 位', '3 位', '4 位以上'],
            ],
        ],

        // 新娘秘書 Scope
        4 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '5 千以下', 'value' => ['', 5000]],
                    ['label' => '5 千 ~ 1 萬', 'value' => [5000, 10000]],
                    ['label' => '1 萬 ~ 2 萬', 'value' => [10000, 20000]],
                    ['label' => '2 萬 ~ 3 萬', 'value' => [20000, 30000]],
                    ['label' => '3 萬 ~ 4 萬', 'value' => [30000, 40000]],
                    ['label' => '4 萬以上', 'value' => [40000, '']],
                ],
            ],
            // 方案類型
            'type' =>[
                'list' => [
                    'try'      => '試妝',
                    'wedding'  => '新娘造型',
                    'relative' => '親友造型',
                    'studio'   => '拍婚紗造型',
                    'personal' => '個人造型',
                    'teaching' => '彩妝教學',
                    'other'    => '其他服務',
                ],
            ],
            // 證婚需求
            'witnessCeremony' => [
                'tag_id' => [250,252],
                'list'   => true,
            ],
            // 習俗儀式類型
            'ceremonyType' => [
                'tag_id' => [[206,241], [207,241], [203]], // 因為後台新增「單/雙儀式皆適用」，所以改為陣列
                'list'   => ['單儀式(文定/迎娶)', '雙儀式(文定+迎娶)', '無習俗儀式'], // 無習俗儀式 => 「僅宴客，無習俗儀式」
            ],
            // 婚宴時段
            'banquetTime' => [
                'tag_id' => [[213,242], [214,242], [215], [202]], // 因為後台新增「午/晚宴皆適用」，所以改為陣列
                'list'   => ['午宴', '晚宴', '午宴 + 晚宴', '無宴客'], // 無宴客 => 「僅有習俗儀式，無宴客」
            ],
            // 造型數
            'makeupCount' => [
                'tag_id' => 217,
                'list'   => ['1 個', '2 個', '3 個', '4 個', '5 個以上'],
            ],
        ],

        // 婚宴場地 Scope
        5 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1 萬以下', 'value' => ['', 10000]],
                    ['label' => '1 萬 ~ 2 萬', 'value' => [10000, 20000]],
                    ['label' => '2 萬 ~ 3 萬', 'value' => [20000, 30000]],
                    ['label' => '3 萬 ~ 4 萬', 'value' => [30000, 40000]],
                    ['label' => '4 萬 ~ 5 萬', 'value' => [40000, 50000]],
                    ['label' => '5 萬以上', 'value' => [50000, '']],
                ],
            ],
        ],

        // 婚禮主持人 Scope
        8 => [
            // 價格區間
            'priceRange' => [
                'list' => [
                    ['label' => '1 萬以下', 'value' => ['', 10000]],
                    ['label' => '1 萬 ~ 2 萬', 'value' => [10000, 20000]],
                    ['label' => '2 萬 ~ 3 萬', 'value' => [20000, 30000]],
                    ['label' => '3 萬 ~ 4 萬', 'value' => [30000, 40000]],
                    ['label' => '4 萬 ~ 5 萬', 'value' => [40000, 50000]],
                    ['label' => '5 萬以上', 'value' => [50000, '']],
                ],
            ],
            // 習俗儀式類型
            'ceremonyType' => [
                'tag_id' => [[228,243], [229,243]], // 因為後台新增「單/雙儀式皆適用」，所以改為陣列
                'list'   => ['單儀式(文定/迎娶)', '雙儀式(文定+迎娶)'],
            ],
            // 婚宴時段
            'banquetTime' => [
                'tag_id' => [[231,244], [232,244], [233]], // 因為後台新增「午/晚宴皆適用」，所以改為陣列
                'list'   => ['午宴', '晚宴', '午宴 + 晚宴'],
            ],
        ],

        // 喜餅 Scope
        10 => [
            // 優惠條件
            'conditionType' => [
                'list' => [
                    'full_quantity' => '滿盒優惠',
                    'full_amount'   => '滿額優惠',
                ],
            ],
        ],
    ];

    // relation Store 所屬商家
    public function allStore()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    // relation Store 所屬上架商家
    public function store()
    {
        return $this->allStore()->published();
    }

    // relation Activity 所屬活動方案
    public function activity()
    {
        return $this->belongsTo(Activity::class);
    }

    // relation Activity 所屬商家有參加的活動方案
    public function storeHasAttendActivity()
    {
        return $this->activity()->published()->storeHasAttend($this->store_id);
    }

    // relation VenueService 婚宴場地的方案資訊
    public function venueInfo()
    {
        return $this->hasOne(VenueService::class, 'service_id');
    }

    // relation WeddingcakeService 喜餅的方案資訊
    public function weddingcakeInfo()
    {
        return $this->hasOne(WeddingcakeService::class, 'service_id');
    }

    // relation Image 方案封面照
    public function cover()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'store_service_cover');
    }

    // relation Image 方案說明照
    public function images()
    {
        return $this->hasMany(StoreServiceImage::class, 'service_id')->sort();
    }

    // relation StoreTag 方案包含的服務
    public function tags()
    {
        return $this->belongsToMany(StoreTag::class, 'store_tag_pivot', 'target_id')
                    ->wherePivot('target_type', 'service')
                    ->withPivot('value', 'number');
    }

    // relation StoreTag 有勾選提供服務的標籤
    public function providTags()
    {
        return $this->tags()->wherePivot('value', '>', 0);
    }

    // relation StoreFull 商家的檔期
    public function storeFull()
    {
        return $this->hasMany(Wdv2\StoreFull::class, 'store_id', 'store_id');
    }

    // relation StoreAlbum 喜餅-適用禮盒
    public function allWeddingcakeAlbums()
    {
        return $this->belongsToMany(StoreAlbum::class, 'weddingcake_album_service')
                    ->where('store_id', $this->store_id);
    }

    // relation StoreAlbum 喜餅-適用禮盒(顯示中)
    public function weddingcakeAlbums()
    {
        return $this->allWeddingcakeAlbums()->status('show');
    }

    // relation User
    public function userCollects()
    {
        return $this->belongsToMany(User::class, (new UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'service')
                    ->where((new User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation LogGaPageView GA網頁瀏覽量
    public function logGaPageViews()
    {
        return $this->hasMany(LogGaPageView::class, 'target_id')->where('type', 'store_service');
    }

    // scope status 狀態
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope storeHasPaid 限特定類型已付費的商家方案
    public function scopeStoreHasPaid($query, $storeType)
    {
        // 狀態 show:顯示
        $query = $query->status('show');

        // 商家類型 store_type (改用 join 提升效率)
        // $query = $query->whereHas('store', function ($q) {
        //     $q = $q->published($storeType);
        // });
        $query = $query->join((new Store)->getTable(), function($join) use ($storeType) {
                            $join->on('stores.id', '=', 'store_services.store_id')
                                    ->where('stores.type', $storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new Wdv2\StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope storeHasAttendActivities 商家有參加活動方案
    public function scopeStoreHasAttendActivities($query, $activityIds)
    {
        // 商家有參加活動方案
        return $query->join('activity_store', function($join) use ($activityIds) {
                        $join->on('activity_store.store_id', '=', 'stores.id')
                                ->whereIn('activity_store.activity_id', $activityIds)
                                ->where('is_attend', 1);
                    })
                    // 活動方案正在進行中
                    ->join((new Activity)->getTable(), function($join) use ($activityIds) {
                        $join->on('activities.id', '=', 'activity_store.activity_id')
                                ->whereIn('activities.id', $activityIds)
                                ->where(function ($q) {
                                    $q->whereNull('start_date')->orWhere('start_date', '<=', now());
                                })
                                ->where(function ($q) {
                                    $q->whereNull('end_date')->orWhere('end_date', '>', now());
                                });
                    });
    }

    // scope storeHasDiscount 商家有獨家優惠
    public function scopeStoreHasDiscount($query)
    {
        return $query->join((new StoreDiscount)->getTable(), function($join) {
                        $join->on('store_discounts.store_id', '=', 'stores.id')
                                ->where(function ($q) {
                                    $q->whereNull('start_date')->orWhere('start_date', '<=', now());
                                })
                                ->where(function ($q) {
                                    $q->whereNull('end_date')->orWhere('end_date', '>', now());
                                });
                    });
    }

    // scope searchKeyword 關鍵字搜尋
    public function scopeSearchKeyword($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('store_services.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchKeywordWithStore 關鍵字搜尋含商家
    public function scopeSearchKeywordWithStore($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('store_services.name', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchWeddingDate 婚期搜尋
    public function scopeSearchWeddingDate($query, $date)
    {
        return $query->where(function ($q1) use ($date) {
            // 上午沒有滿檔
            $q1 = $q1->whereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 1);
                    })
                    // 或下午沒有滿檔
                    ->orWhereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 2);
                    });
        });
    }

    // scope priceRange 價格區間
    public function scopePriceRange($query, $key, $storeType)
    {
        // 取得價格區間的範圍
        $priceRange = $this->demandStructure[$storeType]['priceRange']['list'][$key]['value'];

        // 最低價格以下
        if (!$priceRange[0]) {
            $query = $query->where('store_services.min_price', '<=', $priceRange[1]);

        // 最高價格以上
        } elseif (!$priceRange[1]) {
            $query = $query->where('store_services.max_price', '>=', $priceRange[0]);

        // 中間的價格區間
        } else {
            $query = $query->where(function ($q1) use ($priceRange) {
                $q1 = $q1->orWhereBetween('store_services.min_price', $priceRange)
                            ->orWhereBetween('store_services.max_price', $priceRange)
                            ->orWhere(function ($q2) use ($priceRange) {
                                $q2 = $q2->where('store_services.min_price', '<=', $priceRange[0])
                                        ->where('store_services.max_price', '>=', $priceRange[1]);
                            });
            });
        }

        return $query;
    }

    // scope type 方案類型
    public function scopeType($query, $type)
    {
        return $query->where($this->getTable().'.type', $type);
    }

    // scope studioPhotoFile 檔案全贈(拍婚紗)
    public function scopeStudioPhotoFile($query, $key, $storeType)
    {
        $demand = $this->demandStructure[$storeType]['studioPhotoFile'];

        // 檔案全贈
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->where('store_tag_id', $demand['tag_id'])
                    ->where('value', $key);
        });
    }

    // scope studioCasualWear 拍攝便服(拍婚紗)
    public function scopeStudioCasualWear($query, $key, $storeType)
    {
        $demand = $this->demandStructure[$storeType]['studioCasualWear'];

        // 拍攝便服
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $operator = $key ? '>=' : '=';
            $q = $q->where('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $key);
        });
    }

    // scope studioWhiteDress 拍攝白紗(拍婚紗/婚紗禮服)
    public function scopeStudioWhiteDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['studioWhiteDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 拍攝白紗
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key + 1) ? '>=' : '=';
            // 有不需要的選項就是 key 套(件)
            $number = ($demand['list'][0] == '不需要') ? $key : $key + 1;
            $q = $q->whereIn('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $number);
        });
    }

    // scope studioDress 拍攝禮服(拍婚紗/婚紗禮服)
    public function scopeStudioDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['studioDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 拍攝禮服
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key + 1) ? '>=' : '=';
            // 有不需要的選項就是 key 套(件)
            $number = ($demand['list'][0] == '不需要') ? $key : $key + 1;
            $q = $q->whereIn('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $number);
        });
    }

    // scope weddingWhiteDress 宴客白紗(拍婚紗/婚紗禮服)
    public function scopeWeddingWhiteDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['weddingWhiteDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 宴客白紗
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key + 1) ? '>=' : '=';
            // 有不需要的選項就是 key 套(件)
            $number = ($demand['list'][0] == '不需要') ? $key : $key + 1;
            $q = $q->whereIn('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $number);
        });
    }

    // scope weddingDress 宴客禮服(拍婚紗/婚紗禮服)
    public function scopeWeddingDress($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['weddingDress'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 宴客禮服
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key + 1) ? '>=' : '=';
            // 有不需要的選項就是 key 套(件)
            $number = ($demand['list'][0] == '不需要') ? $key : $key + 1;
            $q = $q->whereIn('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $number);
        });
    }

    // scope studioAlbumPhoto 精修照(拍婚紗)
    public function scopeStudioAlbumPhoto($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['studioAlbumPhoto'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 精修照
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->where('store_tag_id', $demand['tag_id']);
            // 10 張以下
            if ($key == 0) {
                $q = $q->where('number', '<=', 10);
            // 11 ~ 20 張
            } elseif ($key == 1) {
                $q = $q->whereBetween('number', [11, 20]);
            // 21 ~ 30 張
            } elseif ($key == 2) {
                $q = $q->whereBetween('number', [21, 30]);
            // 31 張以上
            } elseif ($key == 3) {
                $q = $q->where('number', '>=', 31);
            }
        });
    }

    // scope forStudio 拍攝用(婚紗禮服)
    public function scopeForStudio($query, $key, $storeType)
    {
        $demand = $this->demandStructure[$storeType]['forStudio'];

        // 拍攝用
        $relation = $key ? 'whereHas' : 'whereDoesntHave';
        return $query->{$relation}('tags', function ($q) use ($demand) {
            $q = $q->whereIn('store_tag_id', $demand['tag_id']);
        });
    }

    // scope forWedding 宴客用(婚紗禮服)
    public function scopeForWedding($query, $key, $storeType)
    {
        $demand = $this->demandStructure[$storeType]['forWedding'];

        // 宴客用
        $relation = $key ? 'whereHas' : 'whereDoesntHave';
        return $query->{$relation}('tags', function ($q) use ($demand) {
            $q = $q->whereIn('store_tag_id', $demand['tag_id']);
        });
    }

    // scope witnessCeremony 證婚需求(婚攝婚錄/新娘秘書)
    public function scopeWitnessCeremony($query, $key, $storeType)
    {
        $demand = $this->demandStructure[$storeType]['witnessCeremony'];

        // 證婚需求
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->whereIn('store_tag_id', $demand['tag_id']);
        });
    }

    // scope ceremonyType 習俗儀式類型(婚攝婚錄/新娘秘書/婚禮主持人)
    public function scopeCeremonyType($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['ceremonyType'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 習俗儀式類型 (因為後台新增「單/雙儀式皆適用」，所以改為陣列，用whereIn)
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->whereIn('store_tag_id', $demand['tag_id'][$key]);
        });
    }

    // scope banquetTime 婚宴時段(婚攝婚錄/新娘秘書/婚禮主持人)
    public function scopeBanquetTime($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['banquetTime'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 婚宴時段 (因為後台新增「午/晚宴皆適用」，所以改為陣列，用whereIn)
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->whereIn('store_tag_id', $demand['tag_id'][$key]);
        });
    }

    // scope photographerTime 拍攝時數(婚攝婚錄)
    public function scopePhotographerTime($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['photographerTime'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 拍攝時數
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            $q = $q->where('store_tag_id', $demand['tag_id'][$key]);
            // 4 小時以下
            if ($key == 0) {
                $q = $q->where('number', '<=', 4);
            // 5 ~ 8 小時
            } elseif ($key == 1) {
                $q = $q->whereBetween('number', [5, 8]);
            // 9 ~ 15 小時 ()
            } elseif ($key == 2) {
                $q = $q->where('number', '>=', 9);
            }
        });
    }

    // scope photographerCount 攝影師人數(婚攝婚錄)
    public function scopePhotographerCount($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['photographerCount'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 攝影師人數
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key + 1) ? '>=' : '=';
            $q = $q->where('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $key + 1);
        });
    }

    // scope makeupCount 造型數(新娘秘書)
    public function scopeMakeupCount($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['makeupCount'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 造型數
        return $query->whereHas('tags', function ($q) use ($key, $demand) {
            // 最後一項是 key+1 套(件)以上
            $operator = (count($demand['list']) == $key + 1) ? '>=' : '=';
            $q = $q->where('store_tag_id', $demand['tag_id'])
                    ->where('number', $operator, $key + 1);
        });
    }

    // scope conditionType 優惠條件(喜餅)
    public function scopeConditionType($query, $key, $storeType)
    {
        // 排除例外
        $demand = $this->demandStructure[$storeType]['conditionType'];
        if (!array_key_exists($key, $demand['list'])) {
            return $query;
        }

        // 優惠條件
        return $query->whereHas('weddingcakeInfo', function ($q) use ($key) {
            $q = $q->where('condition_type', $key);
        });
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy($this->getTable().'.activity_id', 'DESC')
                    ->orderBy($this->getTable().'.sequence')
                    ->orderBy($this->getTable().'.created_at', 'DESC');
    }
}
