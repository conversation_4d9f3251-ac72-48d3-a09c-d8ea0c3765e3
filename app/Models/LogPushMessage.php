<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LogPushMessage extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    public $timestamps = false;

    // scope unread
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    // scope read
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }
}
