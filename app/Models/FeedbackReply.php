<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class FeedbackReply extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;
    protected $guarded = [];

    // relation Feedback
    public function feedback()
    {
        return $this->belongsTo(Feedback::class);
    }

    // relation Image
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')->where('type', 'feedback_reply');
    }
}
