<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VenueDishes extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    // relation Image 喜宴菜圖片
    public function image()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'venue_dishes');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence')
                    ->orderBy('created_at', 'DESC');
    }
}
