<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class UserArticleLike extends Model
{
    use ToJsonDateTrait;

    protected $table = 'user_article_like';

    protected $guarded = [];

    public $timestamps = false;

    public $incrementing = true;

    public function scopeValid($query)
    {
        return $query->whereNull('deleted_at');
    }

    public function scopeArticleLikeValid($query)
    {
        return $query->whereNull('deleted_at')->whereNull('comment_id');
    }

    // relation user deleted_at null
    public function validUser()
    {
        return $this->belongsTo(User::class, 'user_id')->live();
    }
}
