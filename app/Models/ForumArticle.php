<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laracasts\Presenter\PresentableTrait;

class ForumArticle extends Model
{
    use ToJsonDateTrait;

    use SoftDeletes;
    use PresentableTrait;

    protected $presenter = 'App\Models\Presenters\ForumArticlePresenter';

    protected $guarded = [];

    protected $dates = ['published_at', 'softdeleted_at'];

    // 狀態 pending:草稿 published:已發佈 softdelete:顯示軟刪除 delete:刪除
    public $statusList = [
        'pending'    => '草稿',
        'published'  => '已發佈',
        'softdelete' => '顯示軟刪除',
        'delete'     => '刪除',
    ];

    // 是否排除seo的選項
    public $excludeSeoList = [
        0 => '未排除',
        1 => '排除',
    ];

    // relation User
    public function allAuthor()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // relation User
    public function author()
    {
        return $this->allAuthor()->live();
    }

    // relation User
    public function tracks()
    {
        return $this->belongsToMany(User::class, 'user_article_track', 'article_id')->live();
    }

    public function recentCollects($days = 90)
    {
        return $this->tracks()->wherePivot('created_at', '>=', now()->subDays($days));
    }

    // relation User
    public function allLikes()
    {
        return $this->belongsToMany(User::class, 'user_article_like', 'article_id')->live();
    }

    // relation User
    public function likesWithTrashed()
    {
        return $this->allLikes()->wherePivot('comment_id', NULL);
    }

    // relation User
    public function likes()
    {
        return $this->likesWithTrashed()->wherePivot('deleted_at', NULL);
    }

    public function recentLikes($days = 90)
    {
        return $this->likes()->wherePivot('created_at', '>=', now()->subDays($days));
    }

    // relation User
    public function atUsers()
    {
        return $this->belongsToMany(User::class, 'forum_at_users', 'article_id')->live()->wherePivot('comment_id', NULL);
    }

    // relation User
    public function atUsersFilterUnsendEmail()
    {
        return $this->atUsers()->wherePivot('send_email', 0);
    }

    // relation ForumAutoReply
    public function autoReply()
    {
        return $this->hasOne(ForumAutoReply::class, 'article_id');
    }

    // relation ForumCategory
    public function category()
    {
        return $this->belongsTo(ForumCategory::class, 'category_id')->live();
    }

    // relation Brand 所有品牌
    public function allBrands()
    {
        return $this->belongsToMany(Brand::class, 'forum_article_brand', 'article_id', 'brand_id')
                    ->withPivot('user_created', 'rank', 'is_booked', 'wedding_type_ids', 'yzcube_user_id', 'created_at', 'deleted_at');
    }

    // relation Brand 有效的品牌
    public function brands()
    {
        return $this->allBrands()
                    ->wherePivot('deleted_at', NULL);
    }

    // relation Event
    public function event()
    {
        return $this->belongsTo(Event::class, 'event_id');
    }

    // relation ForumComment
    public function allComments()
    {
        return $this->hasMany(ForumComment::class, 'article_id');
    }

    // relation ForumComment
    public function liveComments()
    {
        return $this->allComments()->live();
    }

    // relation ForumComment
    public function comments()
    {
        return $this->liveComments()->whereNull('parent_id');
    }

    // relation ForumComment
    public function parentComments()
    {
        return $this->allComments()->whereNull('parent_id');
    }

    public function recentComments($days = 90)
    {
        return $this->liveComments()->where('forum_comments.created_at', '>=', now()->subDays($days));
    }

    // relation ForumTag
    public function tags()
    {
        return $this->belongsToMany(ForumTag::class, 'forum_article_tag', 'article_id', 'tag_id');
    }

    // relation LogArticleUpdated
    public function logUpdateds()
    {
        return $this->hasMany(LogArticleUpdated::class, 'article_id')->whereNull('comment_id')->sort();
    }

    // relation LogArticleShare
    public function logShares()
    {
        return $this->hasMany(LogArticleShare::class, 'article_id');
    }

    // relation Image 封面照
    public function cover()
    {
        return $this->hasOne(Image::class, 'target_id')
                    ->where('type', 'forum_cover');
    }

    public function recentShares($days = 90)
    {
        return $this->logShares()->where('created_at', '>=', now()->subDays($days));
    }

    // relation Image 圖片
    public function images()
    {
        return $this->hasMany(Image::class, 'target_id')
                    ->where('type', 'forum_article');
    }

    // relation User
    public function logLikes()
    {
        return $this->allLikes()->withPivot('created_at');
    }

    // relation User
    public function logTracks()
    {
        return $this->tracks()->withPivot('created_at');
    }

    // scope hasAuthor 排除帳號停用的作者
    public function scopehasAuthor($query)
    {
        return $query->select($this->getTable().'.*')
                    ->join((new User)->getTable().' AS users', function($join) {
                        $join->on($this->getTable().'.user_id', '=', 'users.id')
                                ->where('users.status', '!=', 'delete');
                    });
    }

    // scope live
    public function scopeLive($query)
    {
        // 需排除帳號停用的作者
        return $query->where($this->getTable().'.status', '!=', 'delete');
    }

    // scope release
    public function scopeRelease($query)
    {
        // 需排除帳號停用的作者
        return $query->whereIn($this->getTable().'.status', ['published', 'softdelete']);
    }

    // sscpoe recommend() 推薦文
    public function scopeRecommend($query)
    {
        return $query->where('category_id', 5); // 5是推薦文章
    }

    // scope status
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope sortHotScore
    public function scopeSortHotScore($query)
    {
        return $query->orderBy('hot_score', 'DESC');
    }

    // scope sortPublishedAt
    public function scopeSortPublishedAt($query)
    {
        return $query->orderBy('published_at', 'DESC');
    }

    // scope sortPageView
    public function scopeSortPageView($query)
    {
        return $query->orderBy('page_view', 'DESC');
    }

    // scope anonymous
    public function scopeAnonymous($query, $flag)
    {
        return $query->where('is_anonymous', $flag);
    }

    // scope created_at
    public function scopeSortCreatedAt($query)
    {
        return $query->where('created_at', 'DESC');
    }

    /**
     * 統計留言+回應數
     *
     * @return int $this->comment_count
     */
    public function totalCommentCount()
    {
        // 已發佈的留言
        $comments = $this->comments()->status('published')->get();

        // 留言數
        $this->comment_count = $comments->count();

        // 回應數
        foreach ($comments as $comment) {
            $this->comment_count += $comment->replies()->status('published')->count();
        }

        return $this->comment_count;
    }
}
