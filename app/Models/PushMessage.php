<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PushMessage extends Model
{
    use SoftDeletes;

    use ToJsonDateTrait;

    protected $casts = [
        'images' => 'object',
    ];

    // 接收者類型 user:新娘 store:商家
    public $receiverTypeList = [
        'user'  => '新娘',
        'store' => '商家',
    ];

    // 狀態 scheduling:排程中 immediately:立即推播 published:已發佈
    public $statusList = [
        'scheduling'  => '排程中',
        'immediately' => '立即推播',
        'published'   => '已發佈',
    ];

    // relation logs 推播紀錄
    public function logs()
    {
        return $this->hasMany(LogPushMessage::class, 'push_id');
    }

    // relation logs 推播未讀紀錄
    public function unReadLogs()
    {
        return $this->logs()->unRead();
    }

    // relation logs 推播已讀紀錄
    public function readLogs()
    {
        return $this->logs()->read();
    }
}
