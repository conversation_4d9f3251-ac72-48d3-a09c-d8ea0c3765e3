<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RedirectUrl extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    // 商家的欄位索引
    public $storeColumnList = [
        'address'       => '地址',
        'phone'         => '行動電話',
        'tel'           => '市話', // wdv3.store
        'email'         => 'Email',
        'contact_email' => '聯絡Email', // wdv3.store
        'line'          => 'Line ID',
        'website'       => '官方網站',
        // 'fanpage'       => '粉絲專頁', // wdv2.store
        'fb_page'       => '粉絲專頁', // wdv3.store
        'instagram'     => 'Instagram', // wdv3.store
    ];

    // 門市的欄位索引
    public $shopColumnList = [
        'address' => '地址',
        'phone'   => '行動電話',
        'tel'     => '市話',
    ];

    // 品牌的欄位索引
    public $brandColumnList = [
        'website'   => '官方網站',
        'fb_page'   => '粉絲專頁',
        'instagram' => 'Instagram'
    ];

    // relation Store 商家
    public function store()
    {
        return $this->belongsTo(Store::class, 'target_id');
    }

    // relation Brand 品牌
    public function brand()
    {
        return $this->belongsTo(Brand::class, 'target_id');
    }

    // relation LogRedirectUrl 轉址資訊紀錄
    public function logs()
    {
        return $this->hasMany(LogRedirectUrl::class);
    }
}
