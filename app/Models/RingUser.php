<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RingUser extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // relation User 使用者
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // relation RingOrder 婚戒大賞-所有預約紀錄
    public function allReserves()
    {
        return $this->hasMany(RingReserve::class)->sortDate();
    }

    // relation RingOrder 婚戒大賞-預約紀錄
    public function reserves()
    {
        return $this->allReserves()->where('type', 'reserve');
    }

    // relation RingPoint 婚戒大賞-點數紀錄
    public function points()
    {
        return $this->hasMany(RingPoint::class);
    }

    // relation RingOrder 婚戒大賞-訂單紀錄
    public function orders()
    {
        return $this->hasMany(RingOrder::class);
    }

    // scope validToken 有效的集點驗證碼
    public function scopeValidToken($query, $token)
    {
        $validRange = [now(), now()->addMinutes(5)];

        return $query->where('token', $token)
                    ->whereBetween('deadline_at', $validRange);
    }

    /**
     * 更新報名資訊的到店數&累積點數
     *
     * @return void
     */
    public function updateCollectPointCount()
    {
        // 到店數&累積點數
        $this->point_count = $this->collect_count = $this->points->count();

        // 若訂單審核通過，則集滿累積點數
        if ($this->orders()->approved()->exists()) {
            $this->collect_count = 5;
        }

        $this->save();
    }
}
