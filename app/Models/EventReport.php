<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EventReport extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $guarded = [];

    use SoftDeletes;

    // 姓名(去左右空白)
    public function getNameAttribute($value)
    {
        return trim($value);
    }
    // 電子信箱(去左右空白 & 轉小寫)
    public function getEmailAttribute($value)
    {
        return strtolower(trim($value));
    }
    // 行動電話(去左右空白)
    public function getPhoneAttribute($value)
    {
        return trim($value);
    }

    // relation event 活動
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // relation eventWithTrashed 包含刪除的活動
    public function eventWithTrashed()
    {
        return $this->event()->withTrashed();
    }

    // relation order 活動訂單
    public function order()
    {
        return $this->hasOne(EventOrder::class);
    }

    // relation CouponSingle 單組通用代碼紀錄
    public function couponSingle()
    {
        return $this->hasOne(CouponSingle::class);
    }

    // relation CouponMultiple 多組獨立代碼紀錄
    public function couponMultiple()
    {
        return $this->hasOne(CouponMultiple::class);
    }

    /**
     * 取得已使用的優惠代碼
     *
     * @return string $status
     */
    public function getCouponCode()
    {
        // 單組通用代碼
        if ($this->couponSingle) {
            return $this->couponSingle->coupon->code;
        }

        // 多組獨立代碼
        if ($this->couponMultiple) {
            return $this->couponMultiple->coupon->code.$this->couponMultiple->code;
        }

        return '';
    }
}
