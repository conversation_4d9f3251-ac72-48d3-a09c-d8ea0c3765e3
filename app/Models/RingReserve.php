<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RingReserve extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    protected $casts = [
        'items' => 'array',
    ];

    // 預約類型 reserve:預約 point:到店集點 order:下訂審核成功
    // public $typeList = [
    //     'reserve' => '預約',
    //     'point'   => '到店集點',
    //     'order'   => '下訂審核成功',
    // ];

    // 備註 reserve:(空白) point:已到店 order:已下訂
    public $noteList = [
        'reserve' => '',
        'point'   => '已到店',
        'order'   => '已下訂',
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    // relation RingBrand 婚戒大賞-品牌
    public function ringBrand()
    {
        return $this->belongsTo(RingBrand::class);
    }

    // relation RingStore 婚戒大賞-門市
    public function ringStore()
    {
        return $this->belongsTo(RingStore::class);
    }

    // relation RingUser 婚戒大賞-報名資訊
    public function ringUser()
    {
        return $this->belongsTo(RingUser::class);
    }

    // scope sortDate 預約日期排序
    public function scopeSortDate($query)
    {
        return $query->orderBy('date');
    }
}
