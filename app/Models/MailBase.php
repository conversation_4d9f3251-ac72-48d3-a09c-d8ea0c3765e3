<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class MailBase extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    protected $table = 'mail_base';

    /**
     * 取得user資料
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function userByMail()
    {
        return $this->belongsTo(User::class, 'email', 'email');
    }
}
