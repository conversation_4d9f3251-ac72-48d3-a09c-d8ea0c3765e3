<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LogGoogleSheetError extends Model
{
    use HasFactory;

    protected $table = 'log_google_sheet_errors';

    protected $fillable = [
        'path',
        'action',
        'spreadsheet_id',
        'sheet_id',
        'sheet_title',
        'input_data',
        'error_data'
    ];

    protected $casts = [
        'path'           => 'string',
        'action'         => 'string',
        'spreadsheet_id' => 'string',
        'sheet_id'       => 'string',
        'sheet_title'    => 'string',
        'input_data'     => 'string',
        'error_data'     => 'string'
    ];
}
