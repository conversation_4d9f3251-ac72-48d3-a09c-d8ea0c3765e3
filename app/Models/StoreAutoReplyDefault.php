<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreAutoReplyDefault extends Model
{
    protected $guarded = [];

    // 自動回覆類型
    public $typeList = [
        'available_date'      => '全天有檔期',
        'half_available_date' => '部分有檔期',
        'unavailable_date'    => '全天撞檔',
        'no_wedding_date'     => '無婚期',
        'need_phone_number'   => '索取電話',
        'get_phone_number'    => '取得電話',
        'get_event_normal'    => '取得一般表單',
        'get_event_order'     => '取得團購表單',
        'customer_service'    => '客服自動回覆',
        'greeting_normal'     => '一般的問候語', // 新娘一進來就會看到文字
        'greeting_has_event'  => '有表單的問候語', // 新娘一進來就會看到文字
        'first_auto_reply'    => '首次自動回覆(公版)', // 新娘送出第一次文字後會跳出的回覆，僅會跳一次
    ];
}
