<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LogAuthToken extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    /**
     * relation AuthToken
     * 取得身份驗證
     */
    public function authToken()
    {
        return $this->belongsTo(AuthToken::class);
    }

    /**
     * relation AuthToken
     * 取得身份驗證
     */
    public function authTokenWithTrashed()
    {
        return $this->authToken()->withTrashed();
    }
}
