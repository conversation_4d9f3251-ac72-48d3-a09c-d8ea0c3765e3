<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class UserWedding extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    // relation Image
    public function avatar_groom()
    {
        return $this->hasOne(Image::class, 'target_id', 'user_id')->where('type', 'wedding_groom');
    }

    // relation Image
    public function avatar_bride()
    {
        return $this->hasOne(Image::class, 'target_id', 'user_id')->where('type', 'wedding_bride');
    }
}
