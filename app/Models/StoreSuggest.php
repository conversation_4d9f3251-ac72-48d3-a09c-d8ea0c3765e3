<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class StoreSuggest extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    // relation Store
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    // relation User
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
