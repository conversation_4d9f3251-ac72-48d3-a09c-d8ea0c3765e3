<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdCampaign extends Model
{
    use ToJsonDateTrait;

    protected $guarded = [];

    use SoftDeletes;

    // 類型 banner:首頁輪播 mobile_banner:手機側邊欄Banner float:浮動廣告
    public $typeList = [
        'banner'        => '首頁輪播',
        'mobile_banner' => '手機側邊欄Banner',
        'float'         => '浮動廣告',
    ];

    // 狀態 published:發佈中 scheduling:排程中 completed:已結束
    public $statusList = [
        'published'  => '發佈中',
        'scheduling' => '排程中',
        'completed'  => '已結束',
    ];

    // relation AdImage 首頁輪播的素材
    public function bannerAdImages()
    {
        return $this->hasMany(AdImage::class)->where('campaign_type', 'banner');
    }

    // relation AdImage 手機側邊欄Banner的素材
    public function mobileBannerAdImage()
    {
        return $this->hasOne(AdImage::class)->where('campaign_type', 'mobile_banner');
    }

    // relation AdImage 浮動廣告的素材
    public function floatAdImage()
    {
        return $this->hasOne(AdImage::class)->where('campaign_type', 'float');
    }

    // scope published 發佈中
    public function scopePublished($query)
    {
        return $query->where(function ($q) {
                            $q->whereNull('start_date')
                                ->orWhere('start_date', '<=', now());
                        })
                        ->where(function ($q) {
                            $q->whereNull('end_date')
                                ->orWhere('end_date', '>', now());
                        });
    }

    // scope scheduling 排程中
    public function scopeScheduling($query)
    {
        return $query->where('start_date', '>', now());
    }

    // scope completed 已結束
    public function scopeCompleted($query)
    {
        return $query->where('end_date', '<=', now());
    }

    // scope publishedByDateRange 取日期區間中的版位
    public function scopePublishedByDateRange($query, $startDate, $endDate)
    {
        // 2020-12-01 上下架時間改為必填 (76行code瞬間變成9行，好爽唷～)
        return $query->where(function ($q1) use ($startDate, $endDate) {
                        $q1 = $q1->orWhereBetween('start_date', [$startDate, $endDate])
                                ->orWhereBetween('end_date', [$startDate, $endDate])
                                ->orWhere(function ($q2) use ($startDate, $endDate) {
                                    $q2 = $q2->where('start_date', '<=', $startDate)
                                            ->where('end_date', '>=', $endDate);
                                });
                    });
    }

    /**
     * 取得目前狀態
     *
     * @return string $status
     */
    public function getStatusLabel()
    {
        // 排程中
        if ($this->start_date && $this->start_date > now()) {
            return $this->statusList['scheduling'];
        }

        // 已結束
        if ($this->end_date && $this->end_date <= now()) {
            return $this->statusList['completed'];
        }

        return $this->statusList['published'];
    }
}
