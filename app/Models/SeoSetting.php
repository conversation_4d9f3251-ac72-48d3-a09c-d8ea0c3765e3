<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SeoSetting extends Model
{
    protected $guarded = [];

    use SoftDeletes;

    // relation SeoParam SEO參數
    public function params()
    {
        return $this->hasMany(SeoParam::class, 'route_name', 'route_name');
    }

    // scope routeName() 該路由的設定紀錄
    public function scopeRouteName($query, $routeName)
    {
        return $query->where('route_name', $routeName)
                        ->orderBy('created_at', 'DESC');
    }

    /**
     * 取得該路由的設定紀錄
     */
    public function getList($routeName)
    {
        $result = [
            'params' => [],
            'list'   => [],
        ];

        // 設定紀錄列表
        $models = $this->routeName($routeName)->get();
        foreach ($models as $model) {

            // SEO參數
            if (!$result['params']) {
                $result['params'] = $model->params->map(function($param) {
                    return $param->only(['key', 'summary']);
                });
            }

            $result['list'][] = [
                'id'          => $model->id,
                'title'       => $model->title ?: '',
                'description' => $model->description ?: '',
                'note'        => $model->note ?: '',
                'created_at'  => $model->created_at->format('Y-m-d H:i:s'),
            ];
        }

        return $result;
    }

    /**
     * 取取得該路由最新的設定預設紀錄
     */
    public function getLatest($routeName)
    {
        return $this->routeName($routeName)->first();
    }
}
