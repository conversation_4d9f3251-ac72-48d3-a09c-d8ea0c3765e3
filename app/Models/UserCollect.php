<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserCollect extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    use SoftDeletes;

    // 收藏類型
    public $typeList = [
        'store'       => '商家',
        'album'       => '作品集',
        'album_image' => '作品照',
        'service'     => '服務方案',
        'share_post'  => '分享文',
        'venue_room'  => '婚宴場地廳房'
    ];

    // relation StoreAlbum 商家作品集
    public function storeAlbum()
    {
        return $this->belongsTo(StoreAlbum::class, 'target_id')
                    ->status('show')
                    ->whereHas('store');
    }

    // relation StoreAlbumImage 商家作品照
    public function storeAlbumImage()
    {
        return $this->belongsTo(StoreAlbumImage::class, 'target_id')
                    ->whereHas('album.store');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('created_at', 'DESC');
    }
}
