<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreDescription extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    // 共用的欄位列表
    public $columnList = [
        'narrate'    => '特色標語',
        'about'      => '關於你們',
        'feature'    => '特色說明',
        'discount'   => '最新優惠',
        'traffic'    => '交通資訊',
        'other'      => '想對新娘說的話',
        'device'     => '設備工具', // 婚攝/婚錄 & 新娘秘書
        'experience' => '資歷介紹', // 婚攝/婚錄 & 新娘秘書 & 主持人
        // 'pandemic_measure'  => '防疫措施',
        // 'extension_type'    => '延期類型',
        // 'extension_date'    => '延期日期',
        // 'pandemic_discount' => '疫情優惠',
    ];

    // 額外的欄位列表
    public $extraColumnList = [
        // 拍婚紗
        1 => [
            'has_files' => '有無檔案全贈',
        ],
        // 婚紗禮服
        2 => [
            'look_time'      => '租借日多久前可參觀禮服',
            'reserve_time'   => '租借日多久前可試穿禮服',
            'has_trial_fee'  => '有無試穿費',
            'min_trial_fee'  => '試穿費金額-最小',
            'max_trial_fee'  => '試穿費金額-最大',
            'trial_fee_info' => '試穿費說明',
            'pieces'         => '店內婚紗件數',
            'rooms'          => '試衣間數',
            'frequency'      => '試穿件數',
            'use_time'       => '試穿時數',
            'people'         => '可攜伴數',
            'is_photograph'  => '可否拍照',
            'is_pet'         => '攜帶寵物',
            'is_eat'         => '可否飲食',
        ],
        // 新娘秘書
        4 => [
            'has_trial_fee'  => '試妝服務',
            'min_trial_fee'  => '試妝費金額-最小',
            'max_trial_fee'  => '試妝費金額-最大',
            'trial_fee_info' => '試妝優惠與內容說明',
        ],
        // 婚宴場地
        5 => [
            'room_count'   => '廳房總數',
            'min_number'   => '最小建議人數',
            'max_number'   => '最大建議人數',
            'dish_tasting' => '試菜優惠說明',
        ],
        // 喜餅
        10 => [
            'countdown_wedding_days'            => '建議於婚期多久前下訂',
            'additional_orders'                 => '喜餅追加及調整說明',
            'free_delivery_method'              => '免運計算方式',
            'logistics_info'                    => '物流配送說明',
            'logistics_date_info'               => '指定配送日與時段說明',
            'logistics_main_island'             => '運費計算方式-台灣本島',
            'logistics_outer_island'            => '運費計算方式-外島地區',
            'logistics_overseas'                => '運費計算方式-海外地區',
            'has_multiple_shops'                => '有無多間門市',
            'has_shop_tasting'                  => '是否提供門市試吃',
            'shop_tasting_min_fee'              => '門市試吃-最低價格',
            'shop_tasting_max_fee'              => '門市試吃-最高價格',
            'shop_tasting_time'                 => '門市試吃-預約時間',
            'shop_tasting_content'              => '門市試吃-試吃內容',
            'shop_tasting_method'               => '門市試吃-試吃辦法',
            'has_delivery_tasting'              => '是否提供宅配試吃',
            'delivery_tasting_min_fee'          => '宅配試吃-最低價格',
            'delivery_tasting_max_fee'          => '宅配試吃-最高價格',
            'delivery_tasting_min_delivery_fee' => '宅配試吃-運費最低價格',
            'delivery_tasting_max_delivery_fee' => '宅配試吃-運費最高價格',
            'delivery_tasting_content'          => '宅配試吃-試吃內容',
            'delivery_tasting_method'           => '宅配試吃-試吃辦法',
        ],
    ];

    // 延期類型 order:下訂日 wedding:婚期
    public $extensionTypeList = [
        'order'   => '下訂日',
        'wedding' => '婚期',
    ];

    // 拍婚紗-有無檔案全贈 0:無 1:有 2:限方案
    public $hasFilesList = ['無', '有', '限方案'];

    // relation Image 商家特色說明照
    public function featureImage()
    {
        return $this->hasOne(Image::class, 'target_id')->where('type', 'store_feature');
    }
}
