<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdminPermission extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    public $timestamps = false;

    protected $casts = [
        'store_types'  => 'array',
        'store_status' => 'array',
    ];

    // 詳細頁的類型
    public $infoPageTypes = [
        'member'     => '{member_id}',
        'album'      => '{album_id}',
        'video'      => '{video_id}',
        'service'    => '{service_id}',
        'venue-room' => '{room_id}',
        'invoice'    => '{invoice_id}',
        'cookie'     => '{cookie_id}',
        'contract'   => '{contract_id}',
    ];

    // relation Self 上一層菜單
    public function parentMenu()
    {
        return $this->belongsTo(self::class, 'parent_menu_id');
    }

    // relation Self 下一層菜單
    public function childrenMenu()
    {
        return $this->hasMany(self::class, 'parent_menu_id')->sort();
    }

    // relation Self 上一層麵包屑
    public function parentBreadcrumb()
    {
        return $this->belongsTo(self::class, 'parent_breadcrumb_id');
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('sequence');
    }

    // scope storeType 商家類型
    public function scopeStoreType($query, $storeType)
    {
        return $query->where(function($q) use ($storeType) {
                        $q->whereJsonContains('store_types', $storeType)
                            ->orWhereNull('store_types');
                    });
    }

    // scope storeStatus 商家狀態
    public function scopeStoreStatus($query, $storeStatus)
    {
        return $query->where(function($q) use ($storeStatus) {
                        $q->whereJsonContains('store_status', $storeStatus)
                            ->orWhereNull('store_status');
                    });
    }
}
