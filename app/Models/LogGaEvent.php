<?php

namespace App\Models;

use App\Traits\Model\ToJsonDateTrait;
use Illuminate\Database\Eloquent\Model;

class LogGaEvent extends Model
{
    use ToJsonDateTrait;

    public $timestamps = false;

    protected $guarded = [];

    // 類型
    public $typeList = [
        'store.email'   => '商家的Email連結點擊',
        'store.phone'   => '商家的電話連結點擊',
        'store.line'    => '商家的Line連結點擊',
        'store.address' => '商家的地址連結點擊',
        'store.site'    => '商家的官網連結點擊',
        'store.fans'    => '商家的粉專連結點擊',
        'quote'         => '主動報價的外站點擊',
    ];
}
