<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use App\Traits\ParseKeywordTrait;

class StoreAlbum extends Model
{
    use SoftDeletes;

    use ParseKeywordTrait;

    protected $guarded = [];

    protected $dates = ['edited_at'];

    // 狀態 hide:隱藏 show:顯示
    public $statusList = [
        'hide' => '隱藏',
        'show' => '顯示',
    ];

    public $orderDressTypeList = [
        'A' => '白紗',
        'B' => '晚禮服',
        'C' => '伴娘服',
        'D' => '配件',
        'E' => '飾品',
        'F' => '中大碼白紗',
        'G' => '中大碼晚禮服',
        'H' => '西服',
        'I' => '中式禮服',
        'J' => '孕婦禮服',
        'K' => '媽媽禮服'
    ];

    // relation Store 所屬商家
    public function allStore()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    // relation Store 包含刪除的所屬商家
    public function allStoreWithTrashed()
    {
        return $this->allStore()->withTrashed();
    }

    // relation Store 所屬上架商家
    public function store()
    {
        return $this->allStore()->published();
    }

    // relation Image 商家作品封面照
    public function cover()
    {
        return $this->belongsTo(Image::class, 'cover_id');
    }

    public function logo()
    {
        return $this->hasOne(Image::class, 'target_id', 'store_id')
                    ->where('type', 'store_logo');
    }

    // relation 拍婚紗的相本欄位
    public function studioAlbum()
    {
        return $this->hasOne(StudioAlbum::class, 'album_id');
    }

    // relation 婚紗禮服的相本欄位
    public function dressAlbum()
    {
        return $this->hasOne(DressAlbum::class, 'album_id');
    }

    // relation 婚攝/婚錄的相本欄位
    public function photographerAlbum()
    {
        return $this->hasOne(PhotographerAlbum::class, 'album_id');
    }

    // relation 婚禮佈置的相本欄位
    public function decorationAlbum()
    {
        return $this->hasOne(DecorationAlbum::class, 'album_id');
    }

    // relation 喜餅的禮盒欄位
    public function weddingcakeAlbum()
    {
        return $this->hasOne(WeddingcakeAlbum::class, 'album_id');
    }

    // relation 商家不同類型的相本欄位
    public function storeTypeAlbum()
    {
        // 驗證商家類型的作品資訊 Model 是否存在
        $storeTypeKey = $this->allStoreWithTrashed->typeKeyList[$this->allStoreWithTrashed->type];
        $classPath    = 'App\Models\\'.Str::studly($storeTypeKey).'Album';
        if (!class_exists($classPath)) {
            return NULL;
        }

        return $this->hasOne($classPath, 'album_id');
    }

    // relation StoreTag 作品集(禮服)標籤
    public function tags()
    {
        return $this->belongsToMany(StoreTag::class, 'store_tag_pivot', 'target_id')
                    ->wherePivot('target_type', 'album');
    }

    // relation Image 商家作品照
    public function images()
    {
        return $this->hasMany(StoreAlbumImage::class, 'album_id')->sort();
    }

    // relation DressOrder 商家禮服訂單
    public function orderAlbums()
    {
        return $this->belongsToMany(DressOrder::class, 'dress_album_order', 'album_id', 'dress_order_id');
    }

    // relation StoreMember
    public function members()
    {
        return $this->belongsToMany(StoreMember::class, 'store_album_member', 'album_id', 'member_id');
    }

    // relation StoreService 喜餅-適用方案
    public function allWeddingcakeServices()
    {
        return $this->belongsToMany(StoreService::class, 'weddingcake_album_service')
                    ->where('store_id', $this->store_id);
    }

    // relation StoreService 喜餅-適用方案(顯示中)
    public function weddingcakeServices()
    {
        return $this->allWeddingcakeServices()->status('show');
    }

    // relation User
    public function userCollects()
    {
        return $this->belongsToMany(User::class, (new UserCollect)->getTable(), 'target_id', 'user_id')
                    ->wherePivot('type', 'album')
                    ->where((new User)->getTable().'.status', '!=', 'delete'); // scopeLive()
    }

    // relation UserCollect 所有單一作品的收藏紀錄
    public function imageCollects()
    {
        return $this->hasManyThrough(UserCollect::class, StoreAlbumImage::class, 'album_id', 'target_id')
                    ->where('user_collects.type', 'album_image')
                    ->whereNull('user_collects.deleted_at');
    }

    // relation StoreFull 商家的檔期
    public function storeFull()
    {
        return $this->hasMany(Wdv2\StoreFull::class, 'store_id', 'store_id');
    }

    // relation LogGaPageView GA網頁瀏覽量
    public function logGaPageViews()
    {
        return $this->hasMany(LogGaPageView::class, 'target_id')->where('type', 'store_album');
    }

    // scope status 狀態
    public function scopeStatus($query, $status)
    {
        return $query->where($this->getTable().'.status', $status);
    }

    // scope storeHasPaid 限特定類型已付費的商家作品
    public function scopeStoreHasPaid($query, $storeType)
    {
        // 狀態 show:顯示
        $query = $query->status('show');

        // 商家類型 store_type (改用 join 提升效率)
        // $query = $query->whereHas('store', function ($q) use ($storeType) {
        //     $q = $q->published($storeType);
        // });
        $query = $query->join((new Store)->getTable(), function($join) use ($storeType) {
                            $join->on('stores.id', '=', 'store_albums.store_id')
                                    ->where('stores.type', $storeType)
                                    ->where('stores.status', 'published');
                        });

        return $query;
    }

    // scope storeUsageActive 商家的使用費限制內
    public function scopeStoreUsageActive($query)
    {
        return $query->leftJoin((new Wdv2\StoreOrderSet)->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('usage_notice', '100%');
                        })
                        ->whereNull('store_order_set.id');
    }

    // scope searchKeyword 關鍵字搜尋
    // public function scopeSearchKeyword($query, $keyword)
    // {
    //     $keywords = $this->splitToArray($keyword);
    //     return $query->where(function ($q) use ($keywords) {
    //         foreach ($keywords as $val) {
    //             $q = $q->orWhere('store_albums.name', 'like', '%' . $val . '%');
    //         }
    //     });
    // }

    // scope searchKeywordWithStore 關鍵字搜尋含商家
    public function scopeSearchKeywordWithStore($query, $keyword)
    {
        $keywords = $this->splitToArray($keyword);
        return $query->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('store_albums.name', 'like', '%' . $val . '%')
                        ->orWhere('stores.name', 'like', '%' . $val . '%');
            }
        });
    }

    // scope searchWeddingDate 婚期搜尋
    public function scopeSearchWeddingDate($query, $date)
    {
        return $query->where(function ($q1) use ($date) {
            // 上午沒有滿檔
            $q1 = $q1->whereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 1);
                    })
                    // 或下午沒有滿檔
                    ->orWhereDoesntHave('storeFull', function ($q2) use ($date) {
                        $q2 = $q2->where('full_date', $date)
                                ->where('type', 2);
                    });
        });
    }

    // scope storeHasAttendActivities 商家有參加活動方案
    public function scopeStoreHasAttendActivities($query, $activityIds)
    {
        // 商家有參加活動方案
        return $query->join('activity_store', function($join) use ($activityIds) {
                        $join->on('activity_store.store_id', '=', 'stores.id')
                                ->whereIn('activity_store.activity_id', $activityIds)
                                ->where('is_attend', 1);
                    })
                    // 活動方案正在進行中
                    ->join((new Activity)->getTable(), function($join) use ($activityIds) {
                        $join->on('activities.id', '=', 'activity_store.activity_id')
                                ->whereIn('activities.id', $activityIds)
                                ->where(function ($q) {
                                    $q->whereNull('start_date')->orWhere('start_date', '<=', now());
                                })
                                ->where(function ($q) {
                                    $q->whereNull('end_date')->orWhere('end_date', '>', now());
                                });
                    });
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy($this->getTable().'.sequence')
                    ->orderBy($this->getTable().'.created_at', 'DESC');
    }

    // scope reverseSort 反向排序
    public function scopeReverseSort($query)
    {
        return $query->orderBy($this->getTable().'.sequence', 'DESC')
                    ->orderBy($this->getTable().'.created_at');
    }

    /**
     * 取得作品近期的收藏數
     * @param string $afterAt 近期的時間點
     *
     *  @return void
     */
    public function getRecentlyCollectCount($afterAt)
    {
        // 婚紗禮服/喜餅
        if (in_array($this->store->type, [2, 10])) {
            return $this->userCollects()->wherePivot('created_at', '>=', $afterAt)->count();
        }

        // 其他類別
        return $this->imageCollects()->where('created_at', '>=', $afterAt)->count();
    }
}
