<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SeoStoreRoute extends Model
{
    protected $guarded = [];

    public $timestamps = false;

    protected $dates = ['updated_at'];

    // relation Store 商家
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    // relation SeoStoreRouteLog SEO商家路由客製記錄
    public function logs()
    {
        return $this->hasMany(SeoStoreRouteLog::class)->sort();
    }

    /**
     * 取得該商家路由最新的設定客製紀錄
     */
    public function getLatest($routeName, $storeId, $targetId)
    {
        $model = $this->where('route_name', $routeName)
                        ->where('store_id', $storeId)
                        ->where('target_id', $targetId)
                        ->first();

        return $model ? $model->logs->first() : NULL;
    }

    // scope sort 排序
    public function scopeSort($query)
    {
        return $query->orderBy('updated_at', 'DESC');
    }
}
