<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Migration Error: Specified key was too long
        Schema::defaultStringLength(191);

        // 全站強制 HTTPS
        URL::forceScheme('https');

        // 強制設定 umask，確保新檔案預設權限是 664
        umask(0002);
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
