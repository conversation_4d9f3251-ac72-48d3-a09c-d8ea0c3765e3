<?php

namespace App\Providers;

use Illuminate\Support\Facades\Route;
use App\Models\StoreUser;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //

        parent::boot();

        // 神之後台-商家管理 商家user管理包含軟刪除資料
        Route::bind('storeUser', function ($value) {
            return StoreUser::withTrashed()->findOrFail($value);
        });
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        /*
        |--------------------------------------------------------------------------
        | 活動
        |--------------------------------------------------------------------------
        */
        // 婚戒大賞
        $this->mapRingEventRoutes();

        /*
        |--------------------------------------------------------------------------
        | 新神之後台
        |--------------------------------------------------------------------------
        */
        // 神之後台-主要功能
        $this->mapMainYzcubeRoutes();
        // 神之後台-身份驗證
        $this->mapAuthYzcubeRoutes();
        // 神之後台-好婚聊聊(論壇)管理
        $this->mapForumYzcubeRoutes();
        // 神之後台-使用者管理
        $this->mapUserYzcubeRoutes();
        // 神之後台-商家管理
        $this->mapStoreYzcubeRoutes();
        // 神之後台-品牌管理
        $this->mapBrandYzcubeRoutes();
        // 神之後台-部落格文章管理
        $this->mapBlogArticleYzcubeRoutes();
        // 神之後台-意見回饋管理
        $this->mapFeedbackYzcubeRoutes();
        // 神之後台-廣告活動管理
        $this->mapAdCampaignYzcubeRoutes();
        // 神之後台-活動報名系統
        $this->mapEventYzcubeRoutes();
        // 神之後台-即時通訊管理
        $this->mapMessageYzcubeRoutes();
        // 神之後台-SEO設定
        $this->mapSeoSettingYzcubeRoutes();
        // 神之後台-活動方案
        $this->mapActivityYzcubeRoutes();

        /*
        |--------------------------------------------------------------------------
        | 商家後台
        |--------------------------------------------------------------------------
        */
        // 商家後台-商家設定
        $this->mapStoreAdminRoutes();
        // 商家後台-發票管理
        $this->mapInvoiceAdminRoutes();
        // 商家後台-主要功能
        $this->mapMainAdminRoutes();
        // 商家後台-身份驗證
        $this->mapAuthAdminRoutes();

        /*
        |--------------------------------------------------------------------------
        | 部落格後台
        |--------------------------------------------------------------------------
        */
        // 部落格後台-身份驗證
        $this->mapAuthBlogRoutes();

        /*
        |--------------------------------------------------------------------------
        | 前台
        |--------------------------------------------------------------------------
        */
        // 前台-主要功能
        $this->mapMainRoutes();
        // 前台-身份驗證
        $this->mapAuthRoutes();
        // 前台-商家系統
        $this->mapStoreRoutes();
        // 前台-論壇
        $this->mapForumRoutes();
        // 前台-婚禮中心
        $this->mapUserRoutes();
        // 前台-活動報名系統
        $this->mapEventRoutes();

        /*
        |--------------------------------------------------------------------------
        | 其他
        |--------------------------------------------------------------------------
        */
        $this->mapApiRoutes();
        $this->mapWebRoutes();
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
             ->namespace($this->namespace)
             ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
             ->middleware('api.public')
             ->namespace($this->namespace)
             ->group(base_path('routes/api.php'));
    }


    /*
    |--------------------------------------------------------------------------
    | 活動
    |--------------------------------------------------------------------------
    */

    /**
     * 前台-婚戒大賞
     * Define the "api/ring-event" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapRingEventRoutes()
    {
        Route::prefix('api/ring-event')
             ->middleware('api.private')
             ->namespace($this->namespace)
             ->group(base_path('routes/api/ring_event.php'));
    }


    /*
    |--------------------------------------------------------------------------
    | 前台
    |--------------------------------------------------------------------------
    */

    /**
     * 前台-主要功能
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapMainRoutes()
    {
        Route::prefix('api')
             ->middleware(['api.private', 'api.response.time'])
             ->namespace($this->namespace)
             ->group(base_path('routes/api/main.php'));
    }

    /**
     * 前台-身份驗證
     * Define the "api/auth" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapAuthRoutes()
    {
        Route::prefix('api/auth')
             ->middleware(['api.private', 'api.response.time'])
             ->namespace($this->namespace.'\Auth')
             ->group(base_path('routes/api/auth.php'));
    }

    /**
     * 前台-商家系統
     * Define the "api/store" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapStoreRoutes()
    {
        Route::prefix('api/store')
             ->middleware(['api.private', 'api.response.time'])
             ->namespace($this->namespace.'\Store')
             ->group(base_path('routes/api/store.php'));
    }

    /**
     * 前台-論壇
     * Define the "api/forum" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapForumRoutes()
    {
        Route::prefix('api/forum')
             ->middleware(['api.private', 'api.response.time'])
             ->namespace($this->namespace.'\Forum')
             ->group(base_path('routes/api/forum.php'));
    }

    /**
     * 前台-婚禮中心
     * Define the "api/user" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapUserRoutes()
    {
        Route::prefix('api/user')
             ->middleware(['api.private', 'user.token.forcibly', 'api.response.time'])
             ->namespace($this->namespace.'\User')
             ->group(base_path('routes/api/user.php'));
    }

    /**
     * 前台-活動報名系統
     * Define the "api/event" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapEventRoutes()
    {
        Route::prefix('api/event')
             ->middleware(['api.private', 'api.response.time'])
             ->namespace($this->namespace.'\Event')
             ->group(base_path('routes/api/event.php'));
    }


    /*
    |--------------------------------------------------------------------------
    | 新神之後台
    |--------------------------------------------------------------------------
    */

    /**
     * 神之後台-主要功能
     * Define the "api/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapMainYzcubeRoutes()
    {
        Route::prefix('api/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace)
             ->group(base_path('routes/api/yzcube/main.php'));
    }

    /**
     * 神之後台-身份驗證
     * Define the "api/auth/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapAuthYzcubeRoutes()
    {
        Route::prefix('api/auth/yzcube')
             ->middleware('api.private')
             ->namespace($this->namespace.'\Auth\Yzcube')
             ->group(base_path('routes/api/yzcube/auth.php'));
    }

    /**
     * 神之後台-好婚聊聊(論壇)管理
     * Define the "api/forum/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapForumYzcubeRoutes()
    {
        Route::prefix('api/forum/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Forum\Yzcube')
             ->group(base_path('routes/api/yzcube/forum.php'));
    }

    /**
     * 神之後台-使用者管理
     * Define the "api/user/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapUserYzcubeRoutes()
    {
        Route::prefix('api/user/yzcube')
            ->middleware(['api.private', 'yzcube.token'])
            ->namespace($this->namespace.'\User\Yzcube')
            ->group(base_path('routes/api/yzcube/user.php'));
    }

    /**
     * 神之後台-商家管理
     * Define the "api/store/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapStoreYzcubeRoutes()
    {
        Route::prefix('api/store/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Store\Yzcube')
             ->group(base_path('routes/api/yzcube/store.php'));
    }

    /**
     * 神之後台-品牌管理
     * Define the "api/brand/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapBrandYzcubeRoutes()
    {
        Route::prefix('api/brand/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Store\Yzcube')
             ->group(base_path('routes/api/yzcube/brand.php'));
    }

    /**
     * 神之後台-部落格文章管理
     * Define the "api/blog-article/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapBlogArticleYzcubeRoutes()
    {
        Route::prefix('api/blog-article/yzcube')
             ->middleware('api.private')
             ->namespace($this->namespace.'\Yzcube')
             ->group(base_path('routes/api/yzcube/blog_article.php'));
    }

    /**
     * 神之後台-意見回饋管理
     * Define the "api/feedback/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapFeedbackYzcubeRoutes()
    {
        Route::prefix('api/feedback/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Yzcube')
             ->group(base_path('routes/api/yzcube/feedback.php'));
    }

    /**
     * 神之後台-廣告活動管理
     * Define the "api/ad-campaign/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapAdCampaignYzcubeRoutes()
    {
        Route::prefix('api/ad-campaign/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Yzcube\AdCampaign')
             ->group(base_path('routes/api/yzcube/ad_campaign.php'));
    }

    /**
     * 神之後台-活動報名系統
     * Define the "api/event/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapEventYzcubeRoutes()
    {
        Route::prefix('api/event/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Event\Yzcube')
             ->group(base_path('routes/api/yzcube/event.php'));
    }

    /**
     * 神之後台-即時通訊管理
     * Define the "api/message/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapMessageYzcubeRoutes()
    {
        Route::prefix('api/message/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Yzcube\Message')
             ->group(base_path('routes/api/yzcube/message.php'));
    }

    /**
     * 神之後台-SEO設定
     * Define the "api/seo/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapSeoSettingYzcubeRoutes()
    {
        Route::prefix('api/seo-setting/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Yzcube')
             ->group(base_path('routes/api/yzcube/seo_setting.php'));
    }

    /**
     * 神之後台-活動方案
     * Define the "api/activity/yzcube" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapActivityYzcubeRoutes()
    {
        Route::prefix('api/activity/yzcube')
             ->middleware(['api.private', 'yzcube.token'])
             ->namespace($this->namespace.'\Yzcube')
             ->group(base_path('routes/api/yzcube/activity.php'));
    }


    /*
    |--------------------------------------------------------------------------
    | 商家後台
    |--------------------------------------------------------------------------
    */

    /**
     * 商家後台-商家設定
     * Define the "api/admin/{store_id}/store" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapStoreAdminRoutes()
    {
        Route::prefix('api/admin/{store}/store')
             ->middleware(['api.private', 'admin.token', 'admin.store'])
             ->namespace($this->namespace.'\Admin\Store')
             ->group(base_path('routes/api/admin/store.php'));
    }

    /**
     * 商家後台-發票管理
     * Define the "api/admin/{store_id}/invoice" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapInvoiceAdminRoutes()
    {
        Route::prefix('api/admin/{store}/invoice')
             ->middleware(['api.private', 'admin.token', 'admin.store', 'admin.invoiceSetting'])
             ->namespace($this->namespace.'\Admin\Invoice')
             ->group(base_path('routes/api/admin/invoice.php'));
    }

    /**
     * 商家後台-主要功能
     * Define the "api/admin" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapMainAdminRoutes()
    {
        Route::prefix('api/admin')
             ->middleware(['api.private', 'admin.token'])
             ->namespace($this->namespace)
             ->group(base_path('routes/api/admin/main.php'));
    }

    /**
     * 商家後台-身份驗證
     * Define the "api/auth/admin" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapAuthAdminRoutes()
    {
        Route::prefix('api/auth/admin')
             ->middleware('api.private')
             ->namespace($this->namespace.'\Auth\Admin')
             ->group(base_path('routes/api/admin/auth.php'));
    }

    /**
     * 部落格後台-身份驗證
     * Define the "api/auth/blog" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapAuthBlogRoutes()
    {
        Route::prefix('api/auth/blog')
             ->middleware('api.private')
             ->namespace($this->namespace.'\Auth\Blog')
             ->group(base_path('routes/api/blog/auth.php'));
    }
}
