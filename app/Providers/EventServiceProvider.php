<?php

namespace App\Providers;

use Illuminate\Support\Facades\Event;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

use App\Models\Store;
use App\Models\StoreAlbum;
use App\Models\StoreService;
use App\Models\StudioAlbumImage;
use App\Models\RingReserve;
use App\Models\EventReport;
use App\Observers\StoreObserver;
use App\Observers\StoreAlbumObserver;
use App\Observers\StoreServiceObserver;
use App\Observers\StudioAlbumImageObserver;
use App\Observers\RingReserveObserver;
use App\Observers\EventReportObserver;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'App\Events\Event' => [
            'App\Listeners\EventListener',
        ],
        'Illuminate\Mail\Events\MessageSent' => [
            'App\Listeners\LogSentMessage',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        // 商家後台-新增商家、方案、作品集：需要與 wdv2 同步 ID
        Store::observe(StoreObserver::class);
        StoreAlbum::observe(StoreAlbumObserver::class);
        StoreService::observe(StoreServiceObserver::class);

        // 商家後台-拍婚紗的作品照：需要統計拍攝地點
        StudioAlbumImage::observe(StudioAlbumImageObserver::class);

        // 婚戒大賞-新增預約：需要同步更新Google雲端試算表
        RingReserve::observe(RingReserveObserver::class);

        // 活動表單-儲存表單：修正報名紀錄資訊
        EventReport::observe(EventReportObserver::class);
    }
}
