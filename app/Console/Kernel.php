<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\Event\InviteRegisterCommand::class,
        Commands\Forum\ArticleCountCommand::class,
        Commands\Forum\TagCountCommand::class,
        Commands\Forum\ArticleHotScoreV2Command::class,
        Commands\Forum\ArticleStatusCheckCommand::class,
        Commands\Forum\FakeUserRandomLikeCommand::class,
        Commands\File\DeleteImageCommand::class,
        Commands\File\ReacquisitionImageCommand::class,
        Commands\Tools\CrawlerMarryStudioContactCommand::class,
        Commands\Tools\SitemapCommand::class,
        Commands\Tools\ImportBlogArticlesCommand::class,
        Commands\Tools\ExportQuoteDataCommand::class,
        Commands\User\UserCountCommand::class,
        Commands\User\GaSearchDateCommand::class,
        Commands\User\UserWeddingDateCommand::class,
        Commands\User\MessageDateCommand::class,
        Commands\User\ReserveDateCommand::class,
        Commands\User\UserQuoteDateCommand::class,
        Commands\User\WeddingDateHotLevelCommand::class,
        Commands\User\ExpiringDiscountCommand::class,
        Commands\User\DeleteDataByUserRemovedCommand::class,
        // Commands\Share\PostCountCommand::class,
        // Commands\Share\TagCountCommand::class,
        // Commands\Share\CategoryCountCommand::class,
        Commands\Store\SharePostPublishedCommand::class,
        Commands\Mail\NewsLeopardGroup::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        /** ===================================
         *   00:00 ~ 00:55 每日即時更新
         * ====================================*/
        // 【商家】活動方案狀態檢查
        $schedule->command('store:activity-status')->dailyAt('00:00');


        /** ===================================
         *   01:00 ~ 03:55 流量、金流、訂單 相關
         * ====================================*/
        // 【金流】Firestore 的訊息紀錄
        $schedule->command('payment:firestore-message-create')->dailyAt('01:00');
        $schedule->command('payment:firestore-message-settle')->dailyAt('01:30');
        // 【金流】更新發票的上傳狀態
        $schedule->command('payment:invoice-upload-status')->dailyAt('03:00');


        /** ===================================
         *   04:00 ~ 04:55 好婚聊聊、W姐妹、會員 相關
         * ====================================*/
        // 【好婚聊聊】文章相關的統計數據
        $schedule->command('forum:article-count')->dailyAt('04:00');
        // 【好婚聊聊】標籤的統計數據
        $schedule->command('forum:tag-count')->dailyAt('04:10');
        // 【好婚聊聊】文章熱門評分統計
        $schedule->command('forum:article-hot-v2')->dailyAt('04:30');

        // 【婚禮中心】使用者以人為本的統計數據
        $schedule->command('user:user-count')->monthlyOn(5, '04:00');
        // 【婚禮中心】計算GA搜尋婚期比例
        $schedule->command('user:ga-search-date')->dailyAt('04:10');
        // 【婚禮中心】計算新娘提供婚期比例
        $schedule->command('user:wedding-date')->dailyAt('04:15');
        // 【婚禮中心】計算即時通訊(詢問單)婚期比例
        $schedule->command('user:message-date')->dailyAt('04:20');
        $schedule->command('user:reserve-date')->dailyAt('04:20');
        // 【婚禮中心】計算主動報價婚期比例
        $schedule->command('user:quote-date')->dailyAt('04:25');
        // 【婚禮中心】婚期熱門評分統計
        $schedule->command('user:wedding-date-hot')->dailyAt('04:30');
        // 刪除user已被刪除的資料（W姐妹文章）
        $schedule->command('user:delete-data-by-user-removed')->dailyAt('04:35');

        // 【商家】取得價格區間
        $schedule->command('store:price-range')->dailyAt('04:00');
        // 【商家】取得收藏數
        $schedule->command('store:collect-count')->dailyAt('04:05');
        // 【商家】GA網頁瀏覽量
        $schedule->command('store:ga-page-view')->dailyAt('04:10');
        // 【商家】GA事件
        $schedule->command('store:ga-event')->dailyAt('04:15');
        // 【商家】統計商家排行積分
        $schedule->command('store:integral')->dailyAt('04:20');
        // 【商家】更新商家排行
        $schedule->command('store:rank')->dailyAt('04:25');
        // 【商家】更新商家相本排行
        $schedule->command('store:album-rank')->dailyAt('04:30');
        // 【商家】更新商家影片排行
        $schedule->command('store:video-rank')->dailyAt('04:35');
        // 【商家】更新商家方案排行
        $schedule->command('store:service-rank')->dailyAt('04:40');
        // 【商家】更新婚宴場地廳房排行
        $schedule->command('store:venue-room-rank')->dailyAt('04:45');
        // 【商家】新增商家的外站點擊紀錄
        $schedule->command('store:external-link')->dailyAt('04:50');


        /** ===================================
         *   05:00 ~ 05:55 其他 類型
         * ====================================*/
        // 【檔案系統】刪除圖片（包含將找出幽靈圖片移入待刪除區）
        $schedule->command('file:delete-image')->dailyAt('05:00');
        // 【檔案系統】重新處理圖片的寬高資訊
        $schedule->command('file:reacquisition-image')->dailyAt('05:05');
        // 【工具系統】產生網站地圖 Sitemap
        $schedule->command('sitemap:create')->dailyAt('05:10');
        // 【工具系統】匯出主動報價至Google試算表
        $schedule->command('tool:export-quote-data')->dailyAt('05:15');
        // 【EDM系統】上傳電子豹email
        $schedule->command('mail:news-leopard-group')->dailyAt('05:20');
        // 【好婚鑑定團】文章月份的瀏覽量
        $schedule->command('kol:article-month-page-view')->monthlyOn(1, '05:30');
        // 【好婚鑑定團】文章的GA網頁瀏覽量
        $schedule->command('kol:ga-page-view')->dailyAt('05:30');
        // 【好婚鑑定團】文章相關的統計數據
        $schedule->command('kol:article-count')->dailyAt('05:35');
        // 【工具系統】官網所有頁面的GA網頁瀏覽量
        $schedule->command('tool:all-ga-page-view')->dailyAt('05:40');


        /** ===================================
         *   06:00 ~ 07:55 其他 通知
         * ====================================*/
        // 【活動報名系統】邀請報名成功的使用者來註冊會員
        $schedule->command('event:invite-register')->dailyAt('06:00');
        // 【婚禮中心】收藏獨家優惠即將到期的使用者通知
        $schedule->command('user:expiring-discount')->dailyAt('06:00');
        // 【婚戒大賞】集點提醒通知
        $schedule->command('ring-event:collect-point-remind')->dailyAt('09:00');
        // 【商家】公開報價通知基本型商家
        $schedule->command('store:user-quote-notify-basic-level')->dailyAt('10:00');


        /** ===================================
         *   持續型的排程
         * ====================================*/
        // 【活動報名系統】新增紀錄至Google試算表
        $schedule->command('event:add-records-google-sheet')->everyFiveMinutes();
        // 【好婚聊聊】文章熱門評分統計
        // $schedule->command('forum:article-hot')->everyThreeHours();
        // 【好婚聊聊】文章狀態檢查
        $schedule->command('forum:article-status')->everyMinute();
        // 【好婚聊聊】假帳號隨機按讚
        $schedule->command('forum:fakeuser-like')->between('10:00', '24:00')
                                                ->everyFifteenMinutes()
                                                ->skip(function() {
                                                    return env('APP_DEBUG') || rand(0, 1);
                                                });
        // 【商家】Ｗ姐妹有新發佈分享文的商家通知
        $schedule->command('store:recommend-article-published')->weeklyOn(1, '09:30');
        // 【商家】公開報價通知品牌
        $schedule->command('store:user-quote-notify-brand')->weeklyOn(1, '10:30');

        // 【工具系統】排程全站推播
        $schedule->command('tool:push-message')->everyFiveMinutes();
        // 【工具系統】匯入部落格所有文章
        $schedule->command('tool:import-blog-articles')->twiceDaily(6, 18);
        // 【工具系統】Firestore的未讀訊息通知
        $schedule->command('tool:unread-message-notify')->cron('0 0,6,12,18 * * *');
        // 【工具系統】爬結婚吧的商家聯絡資訊
        $schedule->command('tool:crawler-marry-studio-contact')->monthlyOn(25, '06:30')
                                                                ->skip(function() {
                                                                    return env('APP_DEBUG');
                                                                });
        // 【工具系統】每日檢查Line通知
        $schedule->command('tool:check-line-notice')->dailyAt('10:00');

        // 【舊資料匯入】測試機的圖片處理隊列
        // $schedule->command('old-data:dev-image-queue')->everyTenMinutes();

        // 限測試環境
        // if (env('APP_DEBUG')) {
        //     $schedule->command('tool:crawler-marry-studio-contact --quantity=1000')->hourly(); // 預計每小時可執行7000筆...
        // }


        /** ===================================
         *   2023喜餅大賞-活動表單客製化
         * ====================================*/
        // 【2023喜餅大賞】山木島Shanmudao 宅配喜餅試吃
        // $schedule->command('CK_2023_sample_shanmu')->weeklyOn(0, '16:30');
        // 【2023喜餅大賞】koti koti 家家 宅配喜餅試吃
        // $schedule->command('CK_2023_sample_kotikoti')->weeklyOn(0, '16:30');
        // 【2023喜餅大賞】Bloom花神精品法式喜餅 宅配喜餅試吃
        // $schedule->command('CK_2023_sample_bloom')->weeklyOn(0, '16:30');
        // 【2023喜餅大賞】香貝里 Chambery 宅配喜餅試吃
        // $schedule->command('CK_2023_sample_chambery')->dailyAt('11:30');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
