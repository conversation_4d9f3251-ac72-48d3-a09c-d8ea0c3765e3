<?php

namespace App\Console\Commands\Event;

use Illuminate\Console\Command;
use App\Repositories\EventReportRepository;
use App\Services\Mail\Event\InviteRegisterService;
use App\Traits\CommandLogTrait;
class InviteRegisterCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'event:invite-register';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【活動報名系統】邀請報名成功的使用者來註冊會員';

    private $eventReportRepository;
    private $inviteRegisterService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        EventReportRepository $eventReportRepository,
        InviteRegisterService $inviteRegisterService
    ) {
        parent::__construct();

        $this->eventReportRepository = $eventReportRepository;
        $this->inviteRegisterService = $inviteRegisterService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 篩選出需要邀請註冊的名單
        $eventReports   = $this->eventReportRepository->getData(['need_invite' => 1]);
        $eventReportIds = $eventReports->pluck('id');

        // 去除重複Email
        $eventReports = $eventReports->unique('email');
        foreach ($eventReports as $eventReport) {

            // WeddingDay 邀請註冊信
            $this->inviteRegisterService->sendMail($eventReport);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        // 標記不需要重複寄信
        $this->eventReportRepository->updateData(function($q) use ($eventReportIds) {
            $q = $q->whereIn('id', $eventReportIds);
        }, [
            'need_invite' => 0,
        ]);

        $this->info('共邀請了'.$eventReports->count().'位活動報名成功的使用者來註冊會員！');
        $this->endLog();
    }
}
