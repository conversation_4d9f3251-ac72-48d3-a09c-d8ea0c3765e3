<?php

namespace App\Console\Commands\Event;

use Illuminate\Console\Command;
use App\Repositories\EventReportRepository;
use App\Services\Event\GoogleSheetsService;
use App\Traits\CommandLogTrait;
use Log;

class AddRecordsGoogleSheetCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'event:add-records-google-sheet';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【活動報名系統】新增紀錄至Google試算表';

    private $eventReportRepository;
    private $googleSheetsService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        EventReportRepository $eventReportRepository,
        GoogleSheetsService $googleSheetsService
    ) {
        parent::__construct();

        $this->eventReportRepository = $eventReportRepository;
        $this->googleSheetsService   = $googleSheetsService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 篩選出未新增的名單
        $eventReports = $this->eventReportRepository->getData(['add_google_sheet' => 0]);
        foreach ($eventReports as $eventReport) {

            // 訂購表單可能尚未完成付款流程，需延遲五分鐘後紀錄
            if (
                $eventReport->event->use_payment &&
                $eventReport->order &&
                $eventReport->order->payment_status == 'pending' &&
                $eventReport->created_at >= now()->subMinutes(5)
            ) {
                continue;
            }

            try {
                // Google試算表-新增一筆紀錄
                $this->googleSheetsService->addRecord($eventReport);

                // 標記已新增
                $eventReport->add_google_sheet = 1;
                $eventReport->save();

            } catch (\Exception $e) {
                Log::error($e->getMessage(), [
                    'class'     => class_basename(get_class($this)),
                    'event_id'  => $eventReport->event_id,
                    'report_id' => $eventReport->id,
                ]);
            }
        }

        $this->info('共新增了'.$eventReports->count().'筆活動報名成功的紀錄！');
        $this->endLog();
    }
}
