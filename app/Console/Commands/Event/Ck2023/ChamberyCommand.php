<?php

namespace App\Console\Commands\Event\Ck2023;

use Illuminate\Console\Command;
use App\Traits\CommandLogTrait;
use App\Traits\FormatDatetimeTrait;
use App\Models\Event;
use App\Models\NationalHoliday;
use Log;

class ChamberyCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;
    use FormatDatetimeTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'CK_2023_sample_chambery';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【2023喜餅大賞】香貝里 Chambery 宅配喜餅試吃';

    private $event;
    private $nationalHoliday;
    private $addColumnKey;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Event $event,
        NationalHoliday $nationalHoliday
    ) {
        parent::__construct();

        $this->event           = $event;
        $this->nationalHoliday = $nationalHoliday;
        $this->startLog();

        // 指定出貨日的欄位索引
        $this->addColumnKey = 'formb8d1b51f-22de-439a-8137-af2e4c3c5616';
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 找出活動表單
        $event = $this->event->where('path', $this->signature)->first();
        if (!$event) {
            $this->error('找不到 '.$this->description);
            return;
        }

        // 國定假日列表
        $nationalHolidays = $this->nationalHoliday->pluck('date')->toArray();

        // 找出指定出貨日的欄位設定
        $columns = json_decode($event->columns);
        $columns = collect($columns)->map(function($item) use ($nationalHolidays) {
            if ($item->key != $this->addColumnKey) {
                return $item;
            }

            // 驗證指定出貨日
            preg_match('/^\d+\/\d+/', $item->format_options[0]->value, $matches);
            if (!$matches) {
                $this->error($this->description.'，找不到指定出貨日！');
                exit;
            }

            // 直接清空指定出貨日
            $item->format_options = [];

            $idx   = 0;
            $today = now();
            while ($idx < 3) {
                $today   = $today->addDay();
                $weekday = $this->getChineseWeekday($today);

                // 例假日或國定假日，不更新指定出貨日
                if (in_array($weekday, ['六', '日']) || in_array($today->format('Y-m-d'), $nationalHolidays)) {
                    continue;
                }

                // 新增指定出貨日
                $addOption = $this->getSampleDate($today);
                $item->format_options[] = (object)[
                    'text'  => $addOption,
                    'value' => $addOption,
                ];
                $idx++;
            }

            return $item;
        });

        // 儲存活動表單
        $event->columns = json_encode($columns, JSON_UNESCAPED_UNICODE);
        $event->save();

        $this->info($this->description.'，成功更新指定出貨日！');
        $this->endLog();
    }
}
