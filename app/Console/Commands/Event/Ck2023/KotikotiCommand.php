<?php

namespace App\Console\Commands\Event\Ck2023;

use Illuminate\Console\Command;
use App\Traits\CommandLogTrait;
use App\Traits\FormatDatetimeTrait;
use App\Models\Event;
use Log;

class KotikotiCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;
    use FormatDatetimeTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'CK_2023_sample_kotikoti';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【2023喜餅大賞】koti koti 家家 宅配喜餅試吃';

    private $event;
    private $addColumnKey;
    private $addOption;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Event $event
    ) {
        parent::__construct();

        $this->event = $event;
        $this->startLog();

        // 指定出貨日的欄位索引
        $this->addColumnKey = 'forme5b5e929-efbc-457b-abd6-81a38dae15aa';

        // 指定出貨日
        $newDate = now()->addDays(31);
        $this->addOption = $this->getSampleDate($newDate);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 找出活動表單
        $event = $this->event->where('path', $this->signature)->first();
        if (!$event) {
            $this->error('找不到 '.$this->description);
            return;
        }

        // 找出指定出貨日的欄位設定
        $columns = json_decode($event->columns);
        $columns = collect($columns)->map(function($item) {
            if ($item->key != $this->addColumnKey) {
                return $item;
            }

            // 驗證指定出貨日
            preg_match('/^\d+\/\d+/', $item->format_options[0]->value, $matches);
            if (!$matches) {
                $this->error($this->description.'，找不到指定出貨日！');
                exit;
            }

            // 刪除第一個選項
            array_shift($item->format_options);

            // 新增最後一個選項
            array_push($item->format_options, (object)[
                'text'  => $this->addOption,
                'value' => $this->addOption,
            ]);

            return $item;
        });

        // 儲存活動表單
        $event->columns = json_encode($columns, JSON_UNESCAPED_UNICODE);
        $event->save();

        $this->info($this->description.'，成功新增 '.$this->addOption.' 出貨日！');
        $this->endLog();
    }
}
