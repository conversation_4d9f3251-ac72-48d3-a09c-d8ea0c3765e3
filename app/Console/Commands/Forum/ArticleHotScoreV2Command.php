<?php

namespace App\Console\Commands\Forum;

use Illuminate\Console\Command;
use App\Services\Google\BigQuery\BigQueryHandle;
use App\Models\ForumArticle as Forum;

class ArticleHotScoreV2Command extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forum:article-hot-v2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊+W姐妹】文章熱門評分統計';

    private $bigQueryHandle;
    protected $forum;
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        BigQueryHandle $bigQueryHandle,
        Forum $forum
    )
    {
        parent::__construct();
        $this->bigQueryHandle = $bigQueryHandle;
        $this->forum        = $forum;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 近30天的時間區間
        $activeDateRange = [
            now()->subDays(90)->startOfDay(),
            now()->subDays(1)->endOfDay(),
        ];
        $location = 'forum';
        $pvResults = [];

        // 撈出近90天有page view的文章id
        $activeResultsArr = $this->bigQueryHandle->handle('active_articles', [
            'eventName' => 'wd_pageview',
            'dateRange' => $activeDateRange,
            'location'  => $location,
        ], false);
        echo "\n" . $location . ' 近90天有page view的文章id: ';
        // 測試暫時只拿array前三個
        // $activeResultsArr = array_slice($activeResultsArr, 0, 3);

        if (!empty($activeResultsArr)) {
            $activeResultsStr = '(' . implode(',', $activeResultsArr) . ')';
            echo $activeResultsStr . "\n";

            // 拿到文章在GA的 UV & PV
            echo "\n" . $location . ' 近90天的UV & PV: ';
            $pvResults = $this->bigQueryHandle->handle('page_view_events', [
                'dateRange' => $activeDateRange,
                'eventName' => 'wd_pageview',
                'location'  => $location,
                'targetId'  => $activeResultsStr,
                'storeId'  => null,
            ], false);
        }

        // 排程錯誤通知
        if (empty($activeResultsArr)) {
            $this->sendErrorNotification('沒有近90天有page view的文章！請確認BigQuery資料。（forum:article-hot-v2）');
        }

        // forum 就拿 forum model
        $model = $this->forum;
        // 清掉全部文章的熱門評分
        get_class($model)::withTrashed()->where('hot_score', '>', 0)->update(['hot_score' => 0]);
        $articles = $model->release()->whereIn('id', $activeResultsArr)->get();

        // 把算hotscore需要的資料組成一個新陣列
        $articleData = $articles->map(function ($article) use ($pvResults) {

            $brandsCount = 0;
            $paidBrandsCount = 0;
            $brands = $article->brands()->with('paidStores')->get();
            if ($brands->isNotEmpty()) {
                $brandsCount = $brands->count();
                $paidBrandsCount = $brands->filter(fn($brand) => $brand->paidStores->isNotEmpty())->count();
            }

            echo "\n" . $article->id . ' ' . $article->title . ': ';
            echo 'PV: ' . ($pvResults[$article->id]['total_page_view'] ?? 0) . ', ';
            echo 'UV: ' . ($pvResults[$article->id]['unique_page_view'] ?? 0) . ', ';
            echo 'Like: ' . $article->recentLikes()->count() . ', ';
            echo 'Comment: ' . $article->recentComments()->count() . ', ';
            echo 'Collect: ' . $article->recentCollects()->count() . ', ';
            echo 'Share: ' . $article->recentShares()->count() . ', ';
            echo 'Has Brand: ' . ($brandsCount > 0 ? 1 : 0) . ', ';
            echo 'Paid Brand: ' . ($paidBrandsCount > 0 ? 1 : 0) . "\n";

            return [
                'id'            => $article->id,
                'pv'            => $pvResults[$article->id]['total_page_view'] ?? 0,
                'uv'            => $pvResults[$article->id]['unique_page_view'] ?? 0,
                'like_count'    => $article->recentLikes()->count(),
                'comment_count' => $article->recentComments()->count(),
                'collect_count' => $article->recentCollects()->count(),
                'share_count'   => $article->recentShares()->count(),
                'has_brand'     => $brandsCount > 0 ? 1 : 0,
                'paid_brand'    => $paidBrandsCount > 0 ? 1 : 0,
                'published_at'  => $article->published_at,
            ];
        });

        // 計算熱門分數 更新model hotscore
        foreach ($articleData as $article) {
            $hotScore = $this->calculateHotScore($article);
            $model->where('id', $article['id'])->update(['hot_score' => $hotScore]);
        }

    }

    // 計算熱門分數

    /**
     * 熱門指數 = (90天內的基礎熱度分數) × (90天內的互動品質加權) x 時間近期分數
     *
     * 基礎熱度分數
     * 指標名稱 / 權重
     * 不重複工作階段（UV）/ 3
     * 重複瀏覽量（PV）/ 1
     * 按讚數 / 3
     * 留言＆回覆數 / 7
     * 收藏數 / 5
     * 分享數 / 5
     * 是否有tag商家 / 3
     * tag的商家是否是付費商家 / 7
     *
     * 互動品質加權 = 1 + ((留言率 + 按讚率+ 收藏率) / 3) x 3，但為防止分數過高，上限僅到 1.7 倍，公式改為「防爆衝機制 = min(1 + 平均互動率 × 3, 1.7)」
     * 留言率 = 留言數 / UV
     * 按讚率 = 按讚數 / UV
     * 收藏率 = 收藏數 / UV
     *
     * 時間近期分數
     * 發布時間 30 天內 : 2
     * 發布時間 60 天內 : 1.5
     *
     */
    private function calculateHotScore($articleData)
    {
        $hotScore = 0;

        // 計算熱門分數的邏輯
        // 1. 基礎熱度分數
        $baseScore = ($articleData['uv'] * 3) + ($articleData['pv'] * 1) + ($articleData['like_count'] * 3) +
                     ($articleData['comment_count'] * 7) + ($articleData['collect_count'] * 5) +
                     ($articleData['share_count'] * 5) +
                     ($articleData['has_brand'] * 3) + ($articleData['paid_brand'] * 7);
        // 2. 互動品質加權
        if($articleData['uv'] == 0) {
            $interactionRate = 0;
        } else {
            $interactionRate = ($articleData['comment_count'] / $articleData['uv'] +
                                $articleData['like_count'] / $articleData['uv'] +
                                $articleData['collect_count'] / $articleData['uv']) / 3;
        }
        $interactionQuality = min(1 + ($interactionRate * 3), 1.7);
        // 3. 時間近期分數
        $recentTimeScore = 1;
        if ($articleData['published_at'] >= now()->subDays(30)) {
            $recentTimeScore = 2;
        } elseif ($articleData['published_at'] >= now()->subDays(60)) {
            $recentTimeScore = 1.5;
        }
        // 4. 計算熱門分數（小數第二位）
        $hotScore = round($baseScore * $interactionQuality * $recentTimeScore, 2);

        echo "\n" . $articleData['id'] . ' 熱門分數: ' . $hotScore . "\n";
        return $hotScore;
    }
}
