<?php

namespace App\Console\Commands\Forum;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\ForumArticle as Article;
use App\Services\Forum\Article\SaveService;

class ArticleStatusCheckCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forum:article-status {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊】文章狀態檢查';

    protected $article;
    protected $saveService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Article $article,
        SaveService $saveService
    ) {
        parent::__construct();

        $this->article     = $article;
        $this->saveService = $saveService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find article
            $article = $this->article->find($this->argument('id'));
            if (!$article) {
                $this->error('Article Not Find!');
                return;
            }

            $this->doCheck($article);

            $message  = $article->title.':'.PHP_EOL;
            $message .= $article->statusList[$article->status].'、';
            $message .= ($article->is_top ? '已置頂' : '未置頂').'！';
            $this->info($message);
            return;
        }

        // all articles
        $articles = $this->article->orWhere(function($q) {
                                        $q->where('status', 'pending')
                                            ->whereNotNull('set_publish_at')
                                            ->where('set_publish_at', '<=', now());
                                    })
                                    ->orWhere(function($q) {
                                        $q->where('is_top', 1)
                                            ->whereNotNull('top_stop_at')
                                            ->where('top_stop_at', '<=', now());
                                    })
                                    ->get();
        foreach ($articles as $article) {
            $this->doCheck($article);
        }
        $this->info('共更新'.$articles->count().'篇文章！');

        $this->endLog();
    }

    /**
     * 實作檢查
     */
    private function doCheck(Article $article)
    {
        // 草稿已設定排程時間
        if ($article->status == 'pending' && $article->set_publish_at && $article->set_publish_at <= now()) {
            $request = [
                'user'        => $article->author,
                'status'      => 'published',
                'is_schedule' => true,
            ];
            $this->saveService->run($request, $article);
        }

        // 置頂結束時間
        if ($article->is_top && $article->top_stop_at && $article->top_stop_at <= now()) {
            $article->is_top = 0;
            $article->save();
        }
    }
}
