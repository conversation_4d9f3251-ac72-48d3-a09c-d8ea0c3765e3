<?php

namespace App\Console\Commands\Forum;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\ForumArticle as Article;

class ArticleCountCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forum:article-count {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊】文章相關的統計數據';

    protected $article;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(Article $article)
    {
        parent::__construct();

        $this->article = $article;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find article
            $article = $this->article->find($this->argument('id'));
            if (!$article) {
                $this->error('Article Not Find!');
                return;
            }

            $this->doCount($article);

            $message  = $article->title.':'.PHP_EOL;
            $message .= $article->like_count.'個讚，';
            $message .= $article->comment_count.'個留言，';
            $message .= $article->track_count.'個追蹤！';
            $this->info($message);
            return;
        }

        // 所有好婚聊聊文章的數據統計
        $after_at = date('Y-m-d', strtotime('-1 day'));
        $articles = $this->article->release()
                                    ->where('forum_articles.updated_at', '>=', $after_at)
                                    ->get();
        foreach ($articles as $article) {
            $this->doCount($article);
        }
        $this->info('共統計'.$articles->count().'篇好婚聊聊文章！');
        $this->endLog();
    }

    /**
     * 實作統計
     */
    private function doCount(Article $article)
    {
        $article->like_count  = $article->likes->count();
        $article->track_count = $article->tracks->count();
        $article->totalCommentCount();
        $article->save();
    }
}
