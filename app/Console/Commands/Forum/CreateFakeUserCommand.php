<?php

namespace App\Console\Commands\Forum;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Forum\Yzcube\FakeUser\StoreService;

class CreateFakeUserCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forum:create-fakeuser {count}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊】建立假帳號';

    protected $storeService;
    protected $fake_user;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(StoreService $storeService)
    {
        parent::__construct();

        $this->storeService = $storeService;
        $this->fake_user    = config('params.fake_user');
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 請確認建立假帳號
        if (!$this->confirm('請確認建立'.$this->argument('count').'位假帳號('.$this->fake_user['email'].')？')) {
            return;
        }

        $request = [
            'email'    => $this->fake_user['email'],
            'password' => $this->fake_user['password'],
            'count'    => $this->argument('count'),
        ];

        $users = $this->storeService->run($request);

        foreach ($users as $user) {
            $this->info('假帳號「'.$user->name.'」已建立！');
        }
        $this->endLog();
    }
}
