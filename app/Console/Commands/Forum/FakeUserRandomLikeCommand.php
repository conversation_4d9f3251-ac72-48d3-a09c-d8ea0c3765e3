<?php

namespace App\Console\Commands\Forum;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\FakeUser;
use App\Models\ForumArticle as Article;
use App\Models\ForumComment as Comment;
use App\Services\Forum\Article\LikeService;

class FakeUserRandomLikeCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forum:fakeuser-like {times?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊】假帳號隨機按讚';

    protected $daily_limit = 20;
    protected $fakeUser;
    protected $article;
    protected $comment;
    protected $likeService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        FakeUser $fakeUser,
        Article $article,
        Comment $comment,
        LikeService $likeService
    ) {
        parent::__construct();

        $this->fakeUser    = $fakeUser;
        $this->article     = $article;
        $this->comment     = $comment;
        $this->likeService = $likeService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 半年內隨機按讚的假帳號們
        // $after_at  = now()->subMonths(6);
        $fakeUsers = $this->fakeUser->whereHas('user')
                                    ->where('is_random_like', 1)
                                    // ->where('created_at', '>=', $after_at)
                                    ->get();

        // 驗證是否有可用的假帳號
        if (!$fakeUsers->count()) {
            $this->error('沒有可用的假帳號可以隨機按讚！');
            return;
        }

        // 循環次數
        $times = $this->argument('times') ?: 1;
        for ($i=0; $i < $times; $i++) {

            // 限制每天最高讚數
            $count = $fakeUsers->sum(function ($item) {
                return $item->user->likes()
                                    ->where('created_at', '>=', date('Y-m-d'))
                                    ->count();
            });
            if ($count >= $this->daily_limit) {
                $this->error('已超過每日限制最高'.$this->daily_limit.'次讚！');
                return;
            }

            // 留言:文章 = 5:2
            $is_article       = (rand(1, 7) <= 2);
            $model            = $is_article ? $this->article : $this->comment;
            $relationCategory = $is_article ? 'category' : 'article.category';

            $model = $model->status('published')
                            // 公告&活動的分類排除
                            ->whereHas($relationCategory, function ($q) {
                                $q->publicUse();
                            })
                            // 真帳號:假帳號 = 9:1
                            ->whereHas('author', function ($q) {
                                $q->where('is_fake', (rand(1, 10) <= 1));
                            })
                            ->inRandomOrder()
                            ->first();

            $request = [
                'user'       => $fakeUsers->random()->user,
                'article'    => $is_article ? $model : $model->article,
                'comment_id' => $is_article ? NULL : $model->id,
                'like'       => true,
            ];
            $this->likeService->run($request);

            // 輸出
            $this->info($request['user']->name.'('.$request['user']->id.')隨機按讚！{article_id='.$request['article']->id.', comment_id='.$request['comment_id'].'}');
        }
        $this->endLog();
    }
}
