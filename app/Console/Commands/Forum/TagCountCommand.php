<?php

namespace App\Console\Commands\Forum;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\ForumTag as Tag;

class TagCountCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'forum:tag-count {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊】標籤相關的統計數據';

    protected $tag;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(Tag $tag)
    {
        parent::__construct();

        $this->tag = $tag;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find tag
            $tag = $this->tag->find($this->argument('id'));
            if (!$tag) {
                $this->error('Tag Not Find!');
                return;
            }

            $this->doCount($tag);

            $message  = $tag->name.': ';
            $message .= $tag->article_count.'篇文章！';
            $this->info($message);
            return;
        }

        // all tags
        $tags = $this->tag->all();
        foreach ($tags as $tag) {
            $this->doCount($tag);
        }
        $this->info('共統計'.$tags->count().'個標籤！');

        $this->endLog();
    }

    /**
     * 實作統計
     */
    private function doCount(Tag $tag)
    {
        $tag->article_count = $tag->articles->count();
        $tag->save();
    }
}
