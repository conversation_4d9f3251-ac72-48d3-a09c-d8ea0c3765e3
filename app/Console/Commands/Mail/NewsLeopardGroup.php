<?php

namespace App\Console\Commands\Mail;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\NewsLeopard\UploadMailService;

class NewsLeopardGroup extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:news-leopard-group';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【EDM系統】上傳電子豹群組email';

    /**
     * @var UploadMailService
     */
    private $uploadMailService;

    /**
     * Create a new command instance.
     *
     * @param UploadMailService $uploadMailService
     */
    public function __construct(
        UploadMailService $uploadMailService
    )
    {
        parent::__construct();
        $this->uploadMailService = $uploadMailService;
        $this->startLog(); //in trait
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //正式環境才真的去跑
        if(strtolower(env('LEOPARD_ENV')) != 'dev') {
            $this->uploadMailService->upload();
        }
        $this->info('Finish!');
        $this->endLog();
    }
}
