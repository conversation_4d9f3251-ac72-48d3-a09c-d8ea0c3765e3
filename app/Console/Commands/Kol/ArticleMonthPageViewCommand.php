<?php

namespace App\Console\Commands\Kol;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\LogGaPageView;
use App\Models\BlogArticlePageView;

class ArticleMonthPageViewCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kol:article-month-page-view';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚鑑定團】文章月份的瀏覽量';

    protected $logGaPageView;
    protected $blogArticlePageView;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LogGaPageView $logGaPageView,
        BlogArticlePageView $blogArticlePageView
    ) {
        parent::__construct();

        $this->logGaPageView       = $logGaPageView;
        $this->blogArticlePageView = $blogArticlePageView;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 取得上個月的GA網頁瀏覽量
        $startDate = date('Y-m-d', strtotime('first day of previous month'));
        $endDate   = date('Y-m-d', strtotime('last day of previous month'));
        $logGaPageViews = $this->logGaPageView->where('type', 'kol_article')
                                                ->whereBetween('created_date', [$startDate, $endDate])
                                                ->get();

        // 依鑑定團文章編號加總
        $temp = [];
        foreach ($logGaPageViews as $logGaPageView) {
            $blog_id = $logGaPageView->target_id;
            if (!isset($temp[$blog_id])) {
                $temp[$blog_id] = 0;
            }
            $temp[$blog_id] += $logGaPageView->pageViews;
        }

        // 新增鑑定團文章月份的瀏覽量
        $month = date('Y-m', strtotime($startDate));
        foreach ($temp as $blog_id => $count) {
            $this->blogArticlePageView->updateOrCreate([
                'type'    => 'kol',
                'blog_id' => $blog_id,
                'month'   => $month,
            ],[
                'count' => $count,
            ]);
        }

        $this->info('共新增'.count($temp).'篇上個月鑑定團的文章瀏覽量！');

        $this->endLog();
    }
}
