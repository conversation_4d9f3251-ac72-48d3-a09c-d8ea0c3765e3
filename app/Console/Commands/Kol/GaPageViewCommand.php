<?php

namespace App\Console\Commands\kol;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Google\BigQuery\LogPageViewService;

class GaPageViewCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kol:ga-page-view {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚鑑定團】文章的GA網頁瀏覽量';

    protected $logPageViewService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LogPageViewService $logPageViewService
    ) {
        parent::__construct();

        $this->logPageViewService = $logPageViewService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 紀錄鑑定團文章的GA網頁瀏覽量
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->logPageViewService->run('kol_article', $date);
        $this->info('共新增'.$total.'篇'.$dateString.'的鑑定團文章GA網頁瀏覽量！');

        // 排程錯誤通知
        if (!$total) {
            $this->sendErrorNotification('沒有新增'.$dateString.'的鑑定團文章GA網頁瀏覽量！');
        }

        // 更新一週前的紀錄，使用BigQuery的歷史總表
        if (!$this->argument('date')) {
            $date = now()->subDays(7)->format('Y-m-d');
            $this->logPageViewService->run('kol_article', $date, false);
        }

        $this->endLog();
    }
}
