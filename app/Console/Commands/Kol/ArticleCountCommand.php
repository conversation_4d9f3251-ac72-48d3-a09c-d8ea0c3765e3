<?php

namespace App\Console\Commands\Kol;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\Kol\Postmeta;
use App\Models\LogGaPageView;
use App\Models\BlogArticlePageView;
use App\Models\BlogArticle;

class ArticleCountCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'kol:article-count {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚鑑定團】文章相關的統計數據';

    protected $postmeta;
    protected $logGaPageView;
    protected $blogArticlePageView;
    protected $blogArticle;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Postmeta $postmeta,
        LogGaPageView $logGaPageView,
        BlogArticlePageView $blogArticlePageView,
        BlogArticle $blogArticle
    ) {
        parent::__construct();

        $this->postmeta            = $postmeta;
        $this->logGaPageView       = $logGaPageView;
        $this->blogArticlePageView = $blogArticlePageView;
        $this->blogArticle         = $blogArticle;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find postmeta
            $postmeta = $this->postmeta->where('post_id', $this->argument('id'))
                                        ->where('meta_key', 'penci_post_views_count')
                                        ->first();
            if (!$postmeta) {
                $this->error('PostMeta Not Find!');
                return;
            }

            $this->doCount($postmeta);

            $message  = 'PostMeta '.$postmeta->post_id.': '.$postmeta->meta_value.'次瀏覽！';
            $this->info($message);
            return;
        }

        // 所有鑑定團文章的數據統計
        $postmetas = $this->postmeta->where('meta_key', 'penci_post_views_count')->get();
        foreach ($postmetas as $postmeta) {
            $this->doCount($postmeta);
        }
        $this->info('共統計'.$postmetas->count().'篇鑑定團文章！');
        $this->endLog();
    }

    /**
     * 實作統計
     */
    private function doCount(Postmeta $postmeta)
    {
        $postmeta->meta_value = $this->logGaPageView->sumKolPageView($postmeta->post_id);
        $postmeta->save();

        // 更新blog_article的page_view
        $this->blogArticle->where('type', 'kol')
            ->where('blog_id', $postmeta->post_id)
            ->update(['page_view' => $postmeta->meta_value]);
    }
}
