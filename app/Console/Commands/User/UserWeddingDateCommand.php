<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\User\Date\GetUserWeddingRankService;

class UserWeddingDateCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:wedding-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】計算新娘提供婚期比例';

    private $getUserWeddingRankService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetUserWeddingRankService $getUserWeddingRankService
    ) {
        parent::__construct();

        $this->getUserWeddingRankService = $getUserWeddingRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $startMonth = date('Y-m');
        $endMonth   = date('Y-m', strtotime('+1 years +6 Months'));
        $this->getUserWeddingRankService->run($startMonth, $endMonth);
        $this->endLog();
    }
}
