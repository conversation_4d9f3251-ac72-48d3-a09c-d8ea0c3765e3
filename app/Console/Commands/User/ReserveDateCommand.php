<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\User\Date\GetReserveRankService;

class ReserveDateCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:reserve-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】計算詢問單婚期比例';

    private $getReserveRankService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetReserveRankService $getReserveRankService
    ) {
        parent::__construct();

        $this->getReserveRankService = $getReserveRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $startMonth = date('Y-m');
        $endMonth   = date('Y-m', strtotime('+1 years +6 Months'));
        $this->getReserveRankService->run($startMonth, $endMonth);
        $this->endLog();
    }
}
