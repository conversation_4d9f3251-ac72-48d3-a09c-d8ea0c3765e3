<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\Store;
use App\Services\Mail\User\ExpiringDiscountService;
use App\Jobs\Firebase\Notification;

class ExpiringDiscountCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:expiring-discount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】收藏獨家優惠即將到期的使用者通知';

    protected $notifyTypes;
    protected $store;
    protected $expiringDiscountService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Store $store,
        ExpiringDiscountService $expiringDiscountService
    ) {
        parent::__construct();

        // 設定即將到期的天數及通知類型
        $this->notifyTypes = [
            [
                'module'    => 'email',
                'countdown' => 10,
            ],
            [
                'module'    => 'notification',
                'countdown' => 5,
            ],
            [
                'module'    => 'notification',
                'countdown' => 3,
            ],
            [
                'module'    => 'notification',
                'countdown' => 1,
            ],
        ];

        $this->store                   = $store;
        $this->expiringDiscountService = $expiringDiscountService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 紀錄通知列表
        $notificationUserIds = [];

        // 即將到期的天數及通知類型
        foreach ($this->notifyTypes as $type) {
            $module    = $type['module'];
            $countdown = $type['countdown'];

            // 即將到期某天數的時間區間
            $dayRange  = [
                now()->addDays($countdown)->startOfDay(),
                now()->addDays($countdown)->endOfDay(),
            ];

            // 找出獨家優惠即將到期的已付費商家
            $stores = $this->store->published()
                                    ->whereHas('discounts', function ($q) use ($dayRange) {
                                        $q->whereBetween('end_date', $dayRange);
                                    })
                                    // 預載入：一年半之內有登入和收藏的user & 這些user的詢問單
                                    ->with(['userCollects' => function ($query) {
                                        $query->where('users.last_login_at', '>=', now()->subMonths(18))
                                                ->where('user_collects.created_at', '>=', now()->subMonths(18));
                                    }])
                                    ->get();

            // 收藏這些商家的user
            $userCount = 0;
            foreach ($stores as $store) {
                foreach ($store->userCollects as $user) {

                    // 通知類型
                    switch ($module) {

                        case 'email':

                            // WeddingDay 獨家優惠即期通知信
                            $this->expiringDiscountService->sendMail($user, $store);

                            // 測試環境寄一封意思意思就好拉，好嗎？
                            if (env('APP_DEBUG')) {
                                $userCount = $stores->sum(function($store) {
                                    return $store->userCollects->count();
                                });
                                break 2;
                            }
                            break;

                        case 'notification':

                            // 測試環境只送通知給管理員意思意思就好拉，好嗎？
                            if (env('APP_DEBUG') && !$user->is_admin) {
                                break;
                            }

                            // 紀錄小鈴鐺通知列表
                            $notificationUserIds[$countdown][$store->id][] = $user->id;
                            break;

                        default:
                            $this->error('此通知類型'.$module.'有誤！');
                            break 3;
                    }

                    $userCount++;
                }
            }
            $this->info('【'.ucfirst($module).'】共有'.$stores->count().'個商家的獨家優惠即將到期'.$countdown.'天，並通知了'.$userCount.'位有收藏的新娘！');
        }

        // 小鈴鐺通知使用者 use Job
        Notification::dispatch('collect_store_discount', $notificationUserIds);

        $this->endLog();
    }
}
