<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\WeddingDate;

class WeddingDateHotLevelCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:wedding-date-hot';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】婚期熱門評分統計';

    private $weddingDate;
    private $limitUserDateCount = 3500;
    private $bigDayLevel = [
        1 => ['ratio' => 1.5, 'limit' => 4],
        2 => ['ratio' => 0.5, 'limit' => 3],
    ];

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        WeddingDate $weddingDate
    ) {
        parent::__construct();

        $this->weddingDate = $weddingDate;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $startDate = date('Y-m-d', strtotime('-1 years'));
        $endDate   = date('Y-m-d', strtotime('+1 years +6 Months'));

        // 婚期紀錄
        $weddingDates = $this->weddingDate->whereBetween('date', [$startDate, $endDate])
                                            ->orderBy('date')
                                            ->get();

        // 新娘提供婚期數量陣列
        $userDateCounts = $weddingDates->pluck('user_wedding_count', 'date');

        foreach ($weddingDates as $weddingDate) {

            // 從今日開始往後計算
            if ($weddingDate->date < date('Y-m-d')) {
                continue;
            }

            // 找出以往一年內新娘提供婚期數量是否大於特定筆數
            $limit         = 0;
            $checkUserDate = false;
            foreach ($userDateCounts as $date => $count) {
                if ($date < date('Y-m-d', strtotime($weddingDate->date.' -1 years')) OR $weddingDate->date < $date) {
                    continue;
                }
                $limit += $count;
                if ($limit >= $this->limitUserDateCount) {
                    $checkUserDate = true;
                    break;
                }
            }

            // 計算熱門級距
            $hotLevel = 0;
            foreach ($this->bigDayLevel as $level => $condition) {
                $score = 0;
                $score += ($weddingDate->ga_search_rank >= $condition['ratio']);
                $score += (!$checkUserDate OR $weddingDate->user_wedding_rank >= $condition['ratio']);
                $score += ($weddingDate->message_rank >= $condition['ratio']);
                $score += ($weddingDate->reserve_rank >= $condition['ratio']);
                $score += ($weddingDate->user_quote_rank >= $condition['ratio']);

                if ($score >= $condition['limit']) {
                    $hotLevel = $level;
                    break;
                }
            }

            $weddingDate->hot_level = $hotLevel;
            $weddingDate->save();
        }
        $this->endLog();
    }
}
