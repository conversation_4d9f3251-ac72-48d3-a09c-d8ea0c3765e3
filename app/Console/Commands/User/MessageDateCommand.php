<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\User\Date\GetMessageRankService;

class MessageDateCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:message-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】計算即時通訊婚期比例';

    private $getMessageRankService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetMessageRankService $getMessageRankService
    ) {
        parent::__construct();

        $this->getMessageRankService = $getMessageRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $startMonth = date('Y-m');
        $endMonth   = date('Y-m', strtotime('+1 years +6 Months'));
        $this->getMessageRankService->run($startMonth, $endMonth);
        $this->endLog();
    }
}
