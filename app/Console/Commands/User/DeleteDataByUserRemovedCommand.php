<?php

namespace App\Console\Commands\User;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\ForumArticle;
use App\Models\ForumComment;
use App\Traits\CommandLogTrait;
class DeleteDataByUserRemovedCommand extends Command
{
     /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:delete-data-by-user-removed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '移除已刪除的使用者相關資料';

    protected $user;
    protected $forumArticle;
    protected $forumComment;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(User $user, ForumArticle $forumArticle, ForumComment $forumComment)
    {
        parent::__construct();
        $this->user         = $user;
        $this->forumArticle = $forumArticle;
        $this->forumComment = $forumComment;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $removeUsers = $this->user->onlyTrashed()
                          ->pluck('id');

        if (!$removeUsers->isEmpty()) {
            $forumArticleDel = $this->forumArticle->whereIn('user_id', $removeUsers)
                // ->whereNotIn('status', ['softdelete', 'delete'])
                ->delete();
                // ->update(['softdeleted_at' => now(), 'status' => 'softdelete']);
            if ($forumArticleDel) {
                $this->info('移除作者是' . $removeUsers->implode(', ') . '的好婚聊聊文章');
            }

            $forumCommentByPost = $this->forumComment->join('forum_articles', 'forum_articles.id', '=', 'forum_comments.article_id')
                ->whereIn('forum_articles.user_id', $removeUsers)
                ->delete();
                // ->whereNotIn('forum_comments.status', ['softdelete', 'delete'])
                // ->update(['forum_comments.softdeleted_at' => now(), 'forum_comments.status' => 'softdelete']);
            if ($forumCommentByPost) {
                $this->info('移除作者是' . $removeUsers->implode(', ') . '的好婚聊聊文章內留言');
            }

            $forumCommentDel = $this->forumComment->whereIn('user_id', $removeUsers)
                // ->whereNotIn('status', ['softdelete', 'delete'])
                ->delete();
                // ->update(['softdeleted_at' => now(), 'status' => 'softdelete']);
            if ($forumCommentDel) {
                $this->info('移除作者是' . $removeUsers->implode(', ') . '的好婚聊聊留言');
            }
            $this->info('移除已刪除的使用者相關資料完成');
        }
        $this->endLog();
    }
}
