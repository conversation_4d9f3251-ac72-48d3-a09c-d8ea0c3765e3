<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\User\Date\GetUserQuoteRankService;

class UserQuoteDateCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:quote-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】計算主動報價婚期比例';

    private $getUserQuoteRankService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetUserQuoteRankService $getUserQuoteRankService
    ) {
        parent::__construct();

        $this->getUserQuoteRankService = $getUserQuoteRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $startMonth = date('Y-m');
        $endMonth   = date('Y-m', strtotime('+1 years +6 Months'));
        $this->getUserQuoteRankService->run($startMonth, $endMonth);
        $this->endLog();
    }
}
