<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\User;

class UserCountCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:user-count {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】使用者以人為本的統計數據';

    protected $user;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        parent::__construct();

        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(1800);//時間設定在30分鐘

        $this->user = $user;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find user
            $user = $this->user->find($this->argument('id'));
            if (!$user) {
                $this->error('User Not Find!');
                return;
            }

            $this->doCount($user);

            $message  = $user->name.'('.$user->present()->anonymous_name.'):'.PHP_EOL;
            $message .= $user->forum_comment_count.'次熱心留言，';
            $message .= $user->forum_get_like_count.'位新娘給讚！'.PHP_EOL;
            $this->info($message);
            return;
        }

        // 所有使用者的數據統計
        $users = $this->user->all();
        foreach ($users as $user) {
            $this->doCount($user);
        }
        $this->info('共統計'.$users->count().'位使用者！');
        $this->endLog();
    }

    /**
     * 實作統計
     */
    private function doCount(User $user)
    {
        $user->totalGetLikeCount();
        $user->totalArticleCommentCount();
        $user->save();

        // 已停用帳號
        if ($user->status == 'delete') {
            $this->isDeleted($user);
        }
    }

    /**
     * 將停用作者的W姐妹&好婚聊聊文章和留言刪除
     */
    private function isDeleted(User $user)
    {
        // 好婚聊聊文章
        $user->allArticles()->status('published')->update([
            'status'         => 'delete',
            'softdeleted_at' => now(),
        ]);

        // 好婚聊聊留言
        $user->allComments()->status('published')->update([
            'status'         => 'delete',
            'softdeleted_at' => now(),
        ]);
    }
}
