<?php

namespace App\Console\Commands\User;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\User\Date\LogGaSearchService;
use App\Services\User\Date\GetGaSearchRankService;

class GaSearchDateCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:ga-search-date {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚禮中心】計算GA搜尋婚期比例';

    protected $logGaSearchService;
    protected $getGaSearchRankService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LogGaSearchService $logGaSearchService,
        GetGaSearchRankService $getGaSearchRankService
    ) {
        parent::__construct();

        $this->logGaSearchService     = $logGaSearchService;
        $this->getGaSearchRankService = $getGaSearchRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // GA搜尋婚期紀錄
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->logGaSearchService->run($date);
        $this->info('共新增'.$total.'筆'.$dateString.'的GA搜尋婚期紀錄！');

        // 排程錯誤通知
        if (!$total) {
            $this->sendErrorNotification('沒有新增'.$dateString.'的GA搜尋婚期紀錄！');
        }

        // 計算GA搜尋婚期比例
        $startMonth = date('Y-m');
        $endMonth   = date('Y-m', strtotime('+1 years +6 Months'));
        $this->getGaSearchRankService->run($startMonth, $endMonth);
        $this->info('計算GA搜尋婚期比例！');

        $this->endLog();
    }
}
