<?php

namespace App\Console\Commands\Tools;

use Illuminate\Console\Command;
use App\Models\MarryStudio;
use App\Models\Store;
use App\Traits\CommandLogTrait;
use App\Traits\CurlTrait;
use Google\Cloud\Vision\VisionClient;

class CrawlerMarryStudioContactCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:crawler-marry-studio-contact {studio_id?} {--quantity=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】爬結婚吧的商家聯絡資訊';

    protected $studioType = NULL;
    protected $studioId;
    protected $studioHtml;
    protected $studioName;
    protected $studioHasEmail;
    protected $vision;
    protected $marryStudio;
    protected $store;

    /**
     * Log run time trait
     */
    use CommandLogTrait;

    use CurlTrait;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        MarryStudio $marryStudio,
        Store $store
    ) {
        parent::__construct();

        // Google Cloud Vision for PHP. http://googleapis.github.io/google-cloud-php/#/docs/cloud-vision/v1.3.1/vision/readme
        $this->vision      = new VisionClient(['keyFilePath' => base_path(env('FIREBASE_CREDENTIALS'))]);
        $this->marryStudio = $marryStudio;
        $this->store       = $store;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定匯入的商家ID
        if ($this->argument('studio_id')) {

            // 更新商家資訊
            $this->updateMarryStudio($this->argument('studio_id'));

            $this->info($this->studioName.'('.$this->studioId.')已匯入完畢！');

        // 批次匯入特定數量
        } elseif ($this->option('quantity')) {

            // 找出最後一筆商家ID
            $lastMarryStudio = $this->marryStudio->orderBy('studio_id', 'DESC')->first();
            $min = $lastMarryStudio->studio_id ? $lastMarryStudio->studio_id + 1 : 1;
            $max = $min + $this->option('quantity');
            for ($id = $min; $id < $max; $id++) {

                // 執行過則略過
                if ($this->marryStudio->where('studio_id', $id)->exists()) {
                    continue;
                }

                // 更新商家資訊
                $this->updateMarryStudio($id);
            }

            $this->info('匯入商家ID至'.$max.'完畢！');

        // 從列表匯入
        } else {

            // 商家類別
            foreach ($this->marryStudio->typeArray as $type) {

                // 取得商家列表
                $this->studioType = $type;
                $this->getStudioList();
            }

            $this->info('從列表匯入所有商家完畢！');
        }

        $this->endLog();
    }

    /**
     * 取得商家列表
     *
     * @return json
     */
    protected function getStudioList($url = NULL)
    {
        // 商家列表
        $url = $url ?: 'https://www.marry.com.tw/'.$this->studioType.'-shop';
        $listHtml = $this->getCurlResponseContent($url);

        // 取得目前網址
        // $pattern = '/<input type=\"hidden\" id=\"now_link\" href=\"(.*)\">/Usi';
        $pattern = '/<a class=\"active\" href=\"(.*)\">/Usi';
        preg_match_all($pattern, $listHtml, $matches, PREG_SET_ORDER);
        $nowLink = $matches[0][1];
        if (!$nowLink) {
            $this->error($this->studioType.'-shop: 取得目前網址有誤！');
        }

        // 取得 marry_access_toke
        $pattern = '/<input type=\"hidden\" id=\"marry_access_toke\" value=\"(.*)\"\/>/Usi';
        preg_match_all($pattern, $listHtml, $matches, PREG_SET_ORDER);
        $accessToke = $matches[0][1];
        if (!$accessToke) {
            $this->error($this->studioType.'-shop: Access Token有誤！');
        }

        // Ajax 商家列表
        $ajaxUrl = 'https://www.marry.com.tw/ajax/?ma=business_basic-get_list';
        $data = [
            'href'              => $nowLink,
            'pagesize'          => 21,
            'filter_search_ref' => 1,
            'module'            => 'business_basic',
            'action'            => 'get_list',
            'marry_access_toke' => $accessToke,
        ];
        $headers = [
            "X-Requested-With: XMLHttpRequest",
        ];
        $resultJson = $this->getCurlResponseContent($ajaxUrl, $data, 'POST', $headers);
        $resultObj  = json_decode($resultJson);

        // 取得商家ID
        $pattern = '/href=\"https:\/\/www\.marry\.com\.tw\/studio-(\d*)\"/Usi';
        preg_match_all($pattern, $resultObj->item, $matches, PREG_SET_ORDER);
        $studioIds = collect($matches)->pluck(1)->unique();

        // 取得下一頁網址
        $pattern = '/<a id=\"next_pages\" href=\"(.*)\"/Usi';
        preg_match_all($pattern, $resultObj->pages_html, $matches, PREG_SET_ORDER);
        $nextLink = $matches[0][1] ?? NULL;

        // 更新商家資訊
        foreach ($studioIds as $studioId) {
            $this->updateMarryStudio($studioId);
        }

        // 重新執行下一頁
        if ($nextLink) {
            $this->getStudioList($nextLink);
        }
    }

    /**
     * 更新商家資訊
     *
     * @return json
     */
    protected function updateMarryStudio($studioId)
    {
        // 取得商家主頁的網站內容
        $this->studioId = $studioId;
        $this->getStudioHtml();
        if (!$this->studioHtml) {
            $this->comment('studio-'.$this->studioId.': 網站內容擷取失敗！');
            return;
        }

        // 取得商家名稱
        $this->getStudioName();

        // 取得商家是否有Email
        $this->getStudioHasEmail();

        // 取得商家的聯絡資訊
        $contactJson = $this->getStudioContact();
        $contactObj  = json_decode($contactJson);

        // Trying to get property 'info' of non-object...不知道為什麼會這樣，那就再抓一次吧～
        if (!isset($contactObj->info)) {
            $this->error('studio-'.$this->studioId.': 商家聯絡資訊有誤！');
            $contactJson = $this->getStudioContact();
            $contactObj  = json_decode($contactJson);

            // 再抓一次還是沒有!?...放棄是一種很好的選擇～
            if (!isset($contactObj->info)) {
                $this->error('studio-'.$this->studioId.': 商家聯絡資訊有誤！x2, Give up!!');
                return;
            }
        }
        $imageData = file_get_contents($contactObj->info->email_link);

        // Google Cloud Vision API 貴貴的，Email圖檔名又每次隨機亂數，所以爬過就算了吧～
        $marryStudio = $this->marryStudio->where('studio_id', $this->studioId)->first();
        if ($marryStudio) {
            $email       = $marryStudio->email;
            $emailVision = $marryStudio->email_vision;

        // 若商家主頁有顯示Email，就取得圖片辨識結果
        } elseif ($this->studioHasEmail && $imageData) {
            $visionObj   = $this->getVisionInfo($imageData);
            $email       = preg_replace('/[\n\r\t]/', '', $visionObj['fullTextAnnotation']['text']);
            $emailVision = json_encode($visionObj);
        } else {
            $email       = NULL;
            $emailVision = NULL;
            $this->comment('studio-'.$this->studioId.': 圖片網址有誤！');
        }

        // 儲存商家資訊
        $this->marryStudio->updateOrCreate([
            'studio_id' => $this->studioId,
        ], [
            'type'                    => $this->studioType,
            'name'                    => $this->studioName,
            'email'                   => $email,
            'has_store_email'         => $email ? $this->store->where('email', $email)->exists() : 0,
            'has_store_contact_email' => $email ? $this->store->where('contact_email', $email)->exists() : 0,
            'contact'                 => $contactJson,
            'email_vision'            => $emailVision,
        ]);
    }

    /**
     * 取得商家主頁的網站內容
     *
     * @return json
     */
    protected function getStudioHtml()
    {
        $url = 'https://www.marry.com.tw/studio-'.$this->studioId;
        $this->studioHtml = $this->getCurlResponseContent($url);
    }

    /**
     * 取得商家名稱
     *
     * @return json
     */
    protected function getStudioName()
    {
        $pattern = '/<a href=\"https:\/\/www\.marry\.com\.tw\/studio-'.$this->studioId.'" title=\"(.*)\"><\/a>/Usi';
        preg_match_all($pattern, $this->studioHtml, $matches, PREG_SET_ORDER);
        $this->studioName = $matches[0][1] ?? '';
        if (!$this->studioName) {
            $this->error('studio-'.$this->studioId.': 商家名稱有誤！');
        }
    }

    /**
     * 取得商家是否有Email
     *
     * @return json
     */
    protected function getStudioHasEmail()
    {
        $pattern = '/<span class="contacts_info"><img src=\"(.*)\" ajax_read=\"1\" rel=\"email\">/Usi';
        preg_match_all($pattern, $this->studioHtml, $matches, PREG_SET_ORDER);

        $this->studioHasEmail = (bool)$matches;
    }

    /**
     * 取得商家的聯絡資訊
     *
     * @return json
     */
    protected function getStudioContact()
    {
        // 取得 marry_access_toke
        $pattern = '/<input type=\"hidden\" id=\"marry_access_toke\" value=\"(.*)\"\/>/Usi';
        preg_match_all($pattern, $this->studioHtml, $matches, PREG_SET_ORDER);
        $accessToke = $matches[0][1] ?? '';
        if (!$accessToke) {
            $this->error('studio-'.$this->studioId.': Access Token有誤！');
        }

        // 商家主頁-聯絡資訊-點擊查看
        $url = 'https://www.marry.com.tw/ajax/?ma=studio-basic_info';
        $data = [
            'm_id'              => $this->studioId,
            'module'            => 'studio',
            'action'            => 'basic_info',
            'marry_access_toke' => $accessToke,
        ];
        $headers = [
            "X-Requested-With: XMLHttpRequest",
        ];
        return $this->getCurlResponseContent($url, $data, 'POST', $headers);
    }

    /**
     * 取得圖片辨識結果
     *
     * @return json
     */
    protected function getVisionInfo($imageData)
    {
        $image  = $this->vision->image($imageData, ['text']);
        $result = $this->vision->annotate($image);

        return $result->info();
    }
}
