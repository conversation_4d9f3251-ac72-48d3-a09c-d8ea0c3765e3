<?php

namespace App\Console\Commands\Tools;

use App\Services\Line\LineService;
use App\Traits\CommandLogTrait;
use App\Traits\FormatDatetimeTrait;
use Illuminate\Console\Command;

class CheckLineNoticeCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;
    use FormatDatetimeTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:check-line-notice {--F|force : 強制執行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】每日檢查Line通知';

    private $lineService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LineService $lineService
    ) {
        parent::__construct();

        $this->lineService = $lineService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 若是測試環境，則需要強制執行
        if (env('APP_DEBUG') && !$this->option('force')) {
            return;
        }

        // 送出訊息
        $weekday = $this->getChineseWeekday();
        $message = $this->getSampleDateTime()."\n".$this->getTongueTwister($weekday);
        $devices = explode(',', env('LINE_TESTERS'));
        $this->lineService->send($message, $devices);

        $this->info($message);
        $this->endLog();
    }

    // 取得繞口令
    private function getTongueTwister($weekday)
    {
        switch ($weekday) {
            case '一':
                return '今天星期一，猴子穿新衣～';
            case '二':
                return '今天星期二，猴子肚子餓～';
            case '三':
                return '今天星期三，猴子去爬山～';
            case '四':
                return '今天星期四，猴子去考試～';
            case '五':
                return '今天星期五，猴子去跳舞～';
            case '六':
                return '今天星期六，猴子去斗六～';
            case '日':
                return '今天星期日，猴子過生日～';
            default:
                return '';
        }
    }
}
