<?php

namespace App\Console\Commands\Tools;

use App\Traits\CommandLogTrait;
use App\Models\PushMessage;
use App\Jobs\Firebase\Message;
use Illuminate\Console\Command;

class PushMessageCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:push-message {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】排程全站推播';

    protected $pushMessage;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        PushMessage $pushMessage
    )
    {
        parent::__construct();

        $this->pushMessage = $pushMessage;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find 推播訊息
            $pushMessage = $this->pushMessage->find($this->argument('id'));
            if (!$pushMessage) {
                $this->error('找不到此推播訊息！');
                return;
            }

            // 實作推播
            $this->doPush($pushMessage);
            return;
        }

        // 找出排程中且發佈時間已到的推播訊息
        $pushMessages = $this->pushMessage->where('status', 'scheduling')->where('set_publish_at', '<=', now())->get();
        foreach ($pushMessages as $pushMessage) {
            $this->doPush($pushMessage);
        }

        $this->endLog();
    }

    /**
     * 實作推播
     */
    private function doPush(PushMessage $pushMessage)
    {
        // 驗證狀態及發佈時間
        if ($pushMessage->status != 'scheduling' || $pushMessage->set_publish_at > now()) {
            $this->error('此為非排程中或發佈時間未到的推播，無法執行「'.$pushMessage->title.'('.$pushMessage->id.')」');
            return;
        }

        // 即時通訊處理 use Job
        $messageType = 'push_'.$pushMessage->receiver_type;
        Message::dispatch($messageType, $pushMessage);

        $this->info('已完成推播訊息「'.$pushMessage->title.'('.$pushMessage->id.')」');
    }
}
