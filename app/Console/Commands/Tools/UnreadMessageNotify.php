<?php

namespace App\Console\Commands\Tools;

use App\Traits\CommandLogTrait;
use App\Traits\Model\SummaryTrait;
use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Store;
use App\Repositories\StoreUserRepository;
use App\Services\Google\Firebase\CloudMessaging\MessagingHandle;
use App\Services\Line\LineService;
use App\Services\Mail\Firebase\NewMessageStoreToUserService;
use App\Services\Mail\Firebase\NewMessageUserToStoreService;
use App\Jobs\Firebase\Notification;
use Google\Cloud\Core\Timestamp;
use DateTime;
use Carbon\Carbon;

class UnreadMessageNotify extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;
    use SummaryTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:unread-message-notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】Firestore的未讀訊息通知';

    // 通知頻率(小時)
    protected $frequencyHour = 6;

    // 通知類型
    protected $notifyTypes = [
        [
            'name'            => 'Email通知',
            'idleHours'       => 6,
            'from'            => 'user',
            'to'              => 'store',
            'lastMsgColumn'   => 'user_last_msg_text', // user最後留的訊息, firestore->messages->{document_id}->user_last_msg_textssages->{document_id}->user_last_msg_text
            'lastMsgAtColumn' => 'user_last_msg_updated_at', // user最後留訊息的時間戳記, firestore->messages->{document_id}->user_last_msg_updated_at
            'unreadColumn'    => 'store_unread',             // store的未讀訊息數量, firestore->messages->{document_id}->store_unread
        ],
        [
            'name'            => 'Email通知',
            'idleHours'       => 6,
            'from'            => 'store',
            'to'              => 'user',
            'lastMsgColumn'   => 'store_last_msg_text', // store最後留的訊息, firestore->messages->{document_id}->store_last_msg_text
            'lastMsgAtColumn' => 'store_last_msg_updated_at', // store最後留訊息的時間戳記, firestore->messages->{document_id}->store_last_msg_updated_at
            'unreadColumn'    => 'user_unread',             // user的未讀訊息數量, firestore->messages->{document_id}->user_unread
        ],
        [
            'name'            => 'Line通知',
            'idleHours'       => 12,
            'from'            => 'user',
            'to'              => 'store',
            'lastMsgColumn'   => 'user_last_msg_text', // user最後留的訊息, firestore->messages->{document_id}->user_last_msg_text
            'lastMsgAtColumn' => 'user_last_msg_updated_at', // user最後留訊息的時間戳記, firestore->messages->{document_id}->user_last_msg_updated_at
            'unreadColumn'    => 'store_unread',             // store的未讀訊息數量, firestore->messages->{document_id}->store_unread
        ],
        [
            'name'            => '小鈴鐺通知',
            'idleHours'       => 12,
            'from'            => 'store',
            'to'              => 'user',
            'lastMsgColumn'   => 'store_last_msg_text', // store最後留的訊息, firestore->messages->{document_id}->store_last_msg_text
            'lastMsgAtColumn' => 'store_last_msg_updated_at', // store最後留訊息的時間戳記, firestore->messages->{document_id}->store_last_msg_updated_at
            'unreadColumn'    => 'user_unread',             // user的未讀訊息數量, firestore->messages->{document_id}->user_unread
        ],
        [
            'name'            => 'Line通知',
            'idleHours'       => 24,
            'from'            => 'user',
            'to'              => 'store',
            'lastMsgColumn'   => 'user_last_msg_text', // user最後留的訊息, firestore->messages->{document_id}->user_last_msg_text
            'lastMsgAtColumn' => 'user_last_msg_updated_at', // user最後留訊息的時間戳記, firestore->messages->{document_id}->user_last_msg_updated_at
            'unreadColumn'    => 'store_unread',             // store的未讀訊息數量, firestore->messages->{document_id}->store_unread
        ],
        [
            'name'            => '小鈴鐺通知',
            'idleHours'       => 24,
            'from'            => 'store',
            'to'              => 'user',
            'lastMsgColumn'   => 'store_last_msg_text', // store最後留的訊息, firestore->messages->{document_id}->store_last_msg_text
            'lastMsgAtColumn' => 'store_last_msg_updated_at', // store最後留訊息的時間戳記, firestore->messages->{document_id}->store_last_msg_updated_at
            'unreadColumn'    => 'user_unread',             // user的未讀訊息數量, firestore->messages->{document_id}->user_unread
        ],
    ];

    protected $user;
    protected $store;
    protected $storeUserRepository;
    protected $messagingHandle;
    protected $lineService;
    protected $newMessageStoreToUserService;
    protected $newMessageUserToStoreService;
    protected $from;
    protected $to;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        User $user,
        Store $store,
        StoreUserRepository $storeUserRepository,
        MessagingHandle $messagingHandle,
        LineService $lineService,
        NewMessageStoreToUserService $newMessageStoreToUserService,
        NewMessageUserToStoreService $newMessageUserToStoreService
    ) {
        parent::__construct();

        $this->user                         = $user;
        $this->store                        = $store;
        $this->storeUserRepository          = $storeUserRepository;
        $this->messagingHandle              = $messagingHandle;
        $this->lineService                  = $lineService;
        $this->newMessageStoreToUserService = $newMessageStoreToUserService;
        $this->newMessageUserToStoreService = $newMessageUserToStoreService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 通知類型
        foreach ($this->notifyTypes as $notifyType) {

            // 閒置的時間區間(小時)
            $startDate = now()->subHours($notifyType['idleHours'])->subHours($this->frequencyHour)->format('Y-m-d H:00:00');
            $endDate   = Carbon::parse($startDate)->addHours($this->frequencyHour)->format('Y-m-d H:00:00');

            // use Google\Cloud\Core\Timestamp
            $startDate = new Timestamp(new DateTime($startDate));
            $endDate   = new Timestamp(new DateTime($endDate));

            // 取出時間區間內需通知的訊息
            $firestore = app('firebase.firestore');
            $roomSanps = $firestore->database()
                                    ->collection('messages')
                                    // [INVALID_ARGUMENT] Cannot have inequality filters on multiple properties: [store_unread, user_last_msg_updated_at]
                                    // ->where('store_unread', '>', 0)
                                    ->where($notifyType['lastMsgAtColumn'], '>=', $startDate)
                                    ->where($notifyType['lastMsgAtColumn'], '<', $endDate)
                                    ->documents();

            foreach ($roomSanps as $roomSanp) {
                $roomObj = $roomSanp->data();

                // 排除已讀
                $unreadColumn = $notifyType['unreadColumn'];
                if (!isset($roomObj[$unreadColumn]) || !$roomObj[$unreadColumn]) {
                    continue;
                }

                // 排除錯誤的使用者資訊
                if (!isset($roomObj['user_id']) || !$roomObj['user_id'] || !isset($roomObj['store_id']) || !$roomObj['store_id']) {
                    continue;
                }

                // 取得使用者資訊
                $this->getMessagePerson($notifyType['from'], $roomObj);
                if (!$this->from || !$this->to) {
                    continue;
                }

                // Line通知商家
                if ($notifyType['name'] == 'Line通知') {
                    $this->sendNotifyToStoreLine($roomObj);
                }

                // 小鈴鐺通知使用者 use Job
                if ($notifyType['name'] == '小鈴鐺通知') {
                    Notification::dispatch('unread_message', $roomObj);
                }

                // 瀏覽器推播通知 (通通有)
                $this->sendWebNotification($notifyType, $roomObj);

                // Email通知 (通通有)
                $this->sendNotifyEmail($notifyType, $roomObj);

                // 測試環境寄一封意思意思就好拉，好嗎？
                if (env('APP_DEBUG')) {
                    break;
                }
            }

            $this->info('經過'.$notifyType['idleHours'].'小時('.date('n/j,ga', strtotime($startDate)).'~'.date('n/j,ga', strtotime($endDate)).')的未讀訊息，共'.$notifyType['name'].$roomSanps->size().'個'.$notifyType['to']);
        }
        $this->endLog();
    }

    /**
     * 取得使用者資訊
     */
    private function getMessagePerson($from, $roomObj)
    {
        $user  = $this->user->live()->find($roomObj['user_id']);
        $store = $this->store->live()->find($roomObj['store_id']);

        $this->from = ($from == 'user') ? $user : $store;
        $this->to   = ($from == 'user') ? $store : $user;
    }

    /**
     * 實作通知至商家的Line
     */
    private function sendNotifyToStoreLine($roomObj)
    {
        // 取得須通知的商家Line IDs
        $devices = $this->storeUserRepository->getMessageLineIdsByStoreId($roomObj['store_id']);

        // 通知訊息
        $message = '【💌新人訊息】'."\n";
        $message .= '新人 '.$this->from->name.' 還在等待您的回覆，不要讓他等太久喔😊'."\n";
        $message .= "\n";
        $message .= '👇點連結回覆他👇'."\n";
        $message .= config('params.wdv2.admin_url').'/redirect/'.$roomObj['store_id'].'/message?user_id='.$roomObj['user_id'].'&openExternalBrowser=1';

        // 送出訊息
        $this->lineService->send($message, $devices);
    }

    /**
     * 實作通知Email
     */
    private function sendNotifyEmail($notifyType, $roomObj)
    {
        $lastMsgColumn   = $notifyType['lastMsgColumn'];
        $lastMsgAtColumn = $notifyType['lastMsgAtColumn'];
        $service         = ($notifyType['from'] == 'user') ? 'newMessageUserToStoreService' : 'newMessageStoreToUserService';
        $this->{$service}->sendMail($this->from, $this->to, $roomObj[$lastMsgColumn], $roomObj[$lastMsgAtColumn], $notifyType['idleHours']);
    }

    /**
     * 實作瀏覽器推播通知
     */
    private function sendWebNotification($notifyType, $roomObj)
    {
        // 訊息限制字數
        $lastMsgColumn = $notifyType['lastMsgColumn'];
        $message       = $this->getSummaryStripTags($roomObj[$lastMsgColumn], 60);

        // 商家傳給新人
        if ($notifyType['from'] == 'store') {
            if (!empty($this->to->liveWebNotification->tokens)) {
                $this->messagingHandle->handle('push_web_notification', [
                    'webNotification' => $this->to->liveWebNotification,
                    'title'           => '商家還在等待你回覆訊息，快看看！',
                    'message'         => '說「'.$message.'」',
                    'link'            => config('params.wdv3.user_url').'/message?store_id='.$this->from->id,
                ]);
            }

        // 新人傳給商家
        } else {
            $link = config('params.wdv2.admin_url').'/redirect/'.$this->to->id.'/message?user_id='.$this->from->id;

            foreach ($this->to->accounts as $account) {
                if (!empty($account->liveWebNotification->tokens)) {
                    $this->messagingHandle->handle('push_web_notification', [
                        'webNotification' => $account->liveWebNotification,
                        'title'           => '新人還在等待您回覆訊息，快看看！',
                        'message'         => '說「'.$message.'」',
                        'link'            => $link,
                    ]);
                }
            }
        }
    }
}
