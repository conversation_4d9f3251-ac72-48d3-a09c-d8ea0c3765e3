<?php

namespace App\Console\Commands\Tools;

use App\Services\Tools\Sitemap\SitemapLogicService;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;

class SitemapCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】建立網站地圖';


    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 好婚聊聊(論壇)的 Sitemap
        // 檔案路徑：https://wdv3.weddingday.com.tw/sitemap/forum_article.xml
        $forumSitemapService = new SitemapLogicService('ForumSitemapService');
        $forumSitemapService->create();
        $this->info('好婚聊聊-文章 Sitemap 建立完畢！');

        // 商家頁面的 Sitemap
        // 檔案路徑：https://wdv3.weddingday.com.tw/sitemap/store.xml
        $storeSitemapService = new SitemapLogicService('StoreSitemapService');
        $storeSitemapService->create();
        $this->info('商家頁面 Sitemap 建立完畢！');

        // 品牌主頁的 Sitemap
        // 檔案路徑：https://wdv3.weddingday.com.tw/sitemap/brand.xml
        $brandSitemapService = new SitemapLogicService('BrandSitemapService');
        $brandSitemapService->create();
        $this->info('品牌主頁 Sitemap 建立完畢！');

        $this->endLog();
    }
}
