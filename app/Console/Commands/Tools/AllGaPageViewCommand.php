<?php

namespace App\Console\Commands\Tools;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Google\BigQuery\LogPageViewService;

class AllGaPageViewCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:all-ga-page-view {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】官網所有頁面的GA網頁瀏覽量';

    protected $logPageViewService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LogPageViewService $logPageViewService
    ) {
        parent::__construct();

        $this->logPageViewService = $logPageViewService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 官網所有頁面的GA網頁瀏覽量
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->logPageViewService->run('all', $date);
        $this->info('新增'.$dateString.'的官網所有頁面GA網頁瀏覽量！');

        // 排程錯誤通知
        if (!$total) {
            $this->sendErrorNotification('沒有紀錄'.$dateString.'官網所有頁面GA網頁瀏覽量！');
        }

        // 更新一週前的紀錄，使用BigQuery的歷史總表
        if (!$this->argument('date')) {
            $date = now()->subDays(7)->format('Y-m-d');
            $this->logPageViewService->run('all', $date, false);
        }

        $this->endLog();
    }
}
