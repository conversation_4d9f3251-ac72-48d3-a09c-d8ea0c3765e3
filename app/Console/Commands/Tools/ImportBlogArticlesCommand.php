<?php

namespace App\Console\Commands\Tools;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Tools\GetWebData\ImportBlogAllPostsService;
use App\Services\Tools\GetWebData\GetBlogPostInfoService;
use App\Models\BlogArticle;

class ImportBlogArticlesCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:import-blog-articles {blog_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】匯入部落格文章';

    protected $importBlogAllPostsService;
    protected $getBlogPostInfoService;
    protected $blogArticle;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        ImportBlogAllPostsService $importBlogAllPostsService,
        GetBlogPostInfoService $getBlogPostInfoService,
        BlogArticle $blogArticle
    ) {
        parent::__construct();

        $this->importBlogAllPostsService = $importBlogAllPostsService;
        $this->getBlogPostInfoService    = $getBlogPostInfoService;
        $this->blogArticle               = $blogArticle;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定部落格ID
        if ($this->argument('blog_id')) {
            $blog_id = $this->argument('blog_id');
            $type    = $this->choice('請選擇部落格類型?', $this->blogArticle->typeList);

            $this->getBlogPostInfoService->getBlogPostById($type, $blog_id);
            $this->getBlogPostInfoService->saveBlogArticle();
            $blogArticle = $this->getBlogPostInfoService->getBlogArticle();

            $this->info($this->blogArticle->typeList[$type].'『'.$blogArticle->title.'』已匯入完畢！');
        } else {
            $this->importBlogAllPostsService->run();

            $this->info('匯入部落格所有文章完畢！');
        }
        $this->endLog();
    }

}
