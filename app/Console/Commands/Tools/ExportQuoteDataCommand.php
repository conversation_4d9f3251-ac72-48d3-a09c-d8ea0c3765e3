<?php

namespace App\Console\Commands\Tools;

use App\Services\User\Quote\ExportGoogleSheetService;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;

class ExportQuoteDataCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tool:export-quote-data {--R|refresh : 全部重匯} {date? : 匯出特定的日期}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【工具系統】匯出主動報價至Google試算表';

    protected $exportGoogleSheetService;
    protected $store;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        ExportGoogleSheetService $exportGoogleSheetService
    )
    {
        parent::__construct();

        $this->exportGoogleSheetService = $exportGoogleSheetService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 預設昨天
        $yesterday = now()->subDay();

        // 全部重匯
        if ($this->option('refresh')) {
            // $totalDays = $this->exportGoogleSheetService->runRefresh($yesterday);
            // $this->info('共匯出'.$totalDays.'天的主動報價至Google試算表！');
            $this->info('太多資料了啦～千萬別這麼做！');

        // 匯出特定的日期
        } elseif ($this->argument('date')) {
            $this->exportGoogleSheetService->runDaily($this->argument('date'));
            $this->info('已匯出'.$this->argument('date').'的主動報價至Google試算表！');

        // 預設僅匯出昨天
        } else {
            $this->exportGoogleSheetService->runDaily($yesterday);
            $this->info('已匯出昨日'.$yesterday->format('Y-m-d').'的主動報價至Google試算表！');
        }

        $this->endLog();
    }
}
