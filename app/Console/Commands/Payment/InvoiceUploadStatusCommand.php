<?php

namespace App\Console\Commands\Payment;

use App\Models\Invoice;
use App\Services\Payment\EzPay\InvoiceHandle;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;

class InvoiceUploadStatusCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:invoice-upload-status {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【金流】更新發票的上傳狀態';

    private $invoice;
    private $invoiceHandle;

    private $isExecute = false; // 是否執行

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Invoice $invoice,
        InvoiceHandle $invoiceHandle
    ) {
        parent::__construct();

        $this->invoice       = $invoice;
        $this->invoiceHandle = $invoiceHandle;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find invoice
            $invoice = $this->invoice->find($this->argument('id'));
            if (!$invoice) {
                $this->error('找不到此發票記錄!');
                return;
            }

            $this->updateUploadStatus($invoice);

            $this->info('發票號碼 '.$invoice->invoice_number.'，已更新上傳狀態為：'.$invoice->uploadList[$invoice->upload_status]);
            return;
        }

        // all invoices
        $invoices = $this->invoice->where('upload_status', '!=', 'completed')->get();
        foreach ($invoices as $invoice) {
            $this->updateUploadStatus($invoice);
        }

        // 排程錯誤通知
        if ($invoices->isNotEmpty() && !$this->isExecute) {
            $this->sendErrorNotification('沒有更新發票的上傳狀態！');
        }

        $this->info('共更新'.$invoices->count().'個發票的上傳狀態！');
        $this->endLog();
    }

    /**
     * 更新上傳狀態
     */
    private function updateUploadStatus(Invoice $invoice)
    {
        // ezPay 補印電子發票
        $invoiceResult = $this->invoiceHandle->handle('invoice_search', ['invoice' => $invoice]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->error('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
            return;
        }

        // 更新上傳狀態
        $invoice->upload_status = $invoice->ezPayUploadList[$invoiceResult->Result->UploadStatus];
        $invoice->save();

        $this->isExecute = true;
    }
}
