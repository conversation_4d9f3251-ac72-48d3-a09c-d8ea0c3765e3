<?php

namespace App\Console\Commands\Payment;

use App\Services\Tools\FirestoreMessageSettleService;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;

class FirestoreMessageSettleCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:firestore-message-settle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【金流】結算 Firestore 的訊息紀錄';

    protected $firestoreMessageSettleService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        FirestoreMessageSettleService $firestoreMessageSettleService
    ) {
        parent::__construct();

        $this->firestoreMessageSettleService = $firestoreMessageSettleService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 結算 Firestore 的訊息紀錄
        $isExecute = $this->firestoreMessageSettleService->run();

        // 排程錯誤通知
        if (!$isExecute) {
            $this->sendErrorNotification('沒有結算 Firestore 的訊息紀錄！');
        }

        $this->endLog();
    }
}
