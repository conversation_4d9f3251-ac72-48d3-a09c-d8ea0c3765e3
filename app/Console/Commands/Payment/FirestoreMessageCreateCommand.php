<?php

namespace App\Console\Commands\Payment;

use App\Services\Tools\FirestoreMessageCreateService;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;

class FirestoreMessageCreateCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:firestore-message-create {date? : 日期}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【金流】新增 Firestore 的訊息紀錄';

    protected $firestoreMessageCreateService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        FirestoreMessageCreateService $firestoreMessageCreateService
    ) {
        parent::__construct();

        $this->firestoreMessageCreateService = $firestoreMessageCreateService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 預設昨天
        $date = $this->argument('date') ?: date('Y-m-d', strtotime('-1 day'));

        // 新增 Firestore 的訊息紀錄
        $total = $this->firestoreMessageCreateService->run($date);

        // 排程錯誤通知
        if (!$total) {
            $this->sendErrorNotification('沒有新增 Firestore 的訊息紀錄！');
        }

        $this->endLog();
    }
}
