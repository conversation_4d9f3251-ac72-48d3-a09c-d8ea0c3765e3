<?php

namespace App\Console\Commands\File;

use App\Services\File\DeleteFileService;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\Image;

class DeleteImageCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'file:delete-image';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【檔案系統】處理刪除圖片';


    protected $maxDays = 30; // 軟刪除 +30天後，永久刪除圖片
    protected $ghostMaxDays = 15; // 幽靈圖片到期時間為 15天
    protected $deleteFileService;
    protected $image;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Image $image,
        DeleteFileService $deleteFileService
    )
    {
        parent::__construct();

        $this->image             = $image;
        $this->deleteFileService = $deleteFileService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 將幽靈圖片變成 等待刪除的圖片
        $this->ghostToDelete();

        // 取得要刪除的圖片
        $images = $this->image->withTrashed()
                                ->where('deleted_at', '<=', now()->subDays($this->maxDays))
                                ->get();
        foreach ($images as $image) {

            // 若沒有其他同檔名的圖片，則刪除S3圖檔
            $otherImage = $this->image->withTrashed()
                                        ->where('file_name', $image->file_name)
                                        ->where('id', '!=', $image->id)
                                        ->exists();
            if (!$otherImage) {
                $this->deleteFileService->delete([
                    'file_name' => $image->file_name,
                    'app_env'   => $image->app_env,
                ]);
            }

            // 刪除資料
            $image->forceDelete();
        }

        $this->info('共刪除了' . $images->count() . '張圖了');
        $this->endLog();
    }

    /**
     * 處理幽靈圖片
     *
     * @return void
     */
    private function ghostToDelete()
    {
        $this->image->whereNull('target_id')
                    ->where('created_at', '<=', now()->subDays($this->ghostMaxDays))
                    ->delete();
    }
}
