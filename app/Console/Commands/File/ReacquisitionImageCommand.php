<?php

namespace App\Console\Commands\File;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\Image;
use App\Models\Store;
use App\Models\Wdv2\StoreAlbum;
use App\Jobs\File\ReacquisitionImageSize;

class ReacquisitionImageCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'file:reacquisition-image';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【檔案系統】重新處理圖片的寬高資訊';

    private $maxTime = 86400; // 大多都是靠 CreateImageService 去觸發 ，排程只是排除掉例外而已 ，所以 create_at + 1天後 才會執行，
    protected $image;
    protected $store;
    protected $storeAlbum;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Image $image,
        Store $store,
        StoreAlbum $storeAlbum
    ) {
        parent::__construct();

        $this->image      = $image;
        $this->store      = $store;
        $this->storeAlbum = $storeAlbum;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 只取上架商家且有效的作品照
        $albumImages = $this->image->select('images.id')
                                    ->join($this->storeAlbum->getTable(), function($join) {
                                        $join->on('album.id', '=', 'images.target_id')
                                                ->where('album.show_flag', 2);
                                    })
                                    ->join($this->store->getTable(), function($join) {
                                        $join->on('stores.id', '=', 'album.store_id')
                                                ->where('stores.status', 'published');
                                    })
                                    ->where(function($query) {
                                        $query->whereNull('width')->orWhereNull('height');
                                    })
                                    ->where('images.created_at', '<=' , now()->subSeconds($this->maxTime))
                                    ->where('images.type', 'store_album_image');

        // 取得要執行的圖片
        $images = $this->image->select('id')
                                ->where(function($query) {
                                    $query->whereNull('width')->orWhereNull('height');
                                })
                                ->where('created_at', '<=' , now()->subSeconds($this->maxTime))
                                ->where('type', '!=', 'store_album_image')
                                ->union($albumImages)
                                ->get();

        // 用 Queue 重新處理圖片的寬高資訊
        foreach ($images as $image) {
            ReacquisitionImageSize::dispatch($image->id);
        }

        $this->info('共重新處理'.$images->count().'張圖片的寬高資訊！');
        $this->endLog();
    }

}
