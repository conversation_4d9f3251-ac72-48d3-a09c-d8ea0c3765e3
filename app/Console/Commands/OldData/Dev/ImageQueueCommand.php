<?php

namespace App\Console\Commands\OldData\Dev;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Models\ImageJob;
use App\Services\File\GetImageInfoService;
use Log;

class ImageQueueCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'old-data:dev-image-queue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【舊資料匯入】測試機的圖片處理隊列';

    // 每分鐘執行數量
    private $minuteLimit = 1000;

    private $imageJob;
    private $getImageInfoService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        ImageJob $imageJob,
        GetImageInfoService $getImageInfoService
    ) {
        parent::__construct();

        $this->imageJob            = $imageJob;
        $this->getImageInfoService = $getImageInfoService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 正式環境以下中斷
        // if (!env('APP_DEBUG')) {
        //     return true;
        // }

        // 取出所有要執行的ID
        $imageIds = $this->imageJob->select('id')
                                    ->where(function($query) {
                                        $query->whereNull('width')->orWhereNull('height');
                                    })
                                    ->pluck('id');

        // 沒有要執行的
        if ($imageIds->isEmpty()) {
            return true;

        // 剩下最後一次
        } else if ($imageIds->count() < $this->minuteLimit) {
            $randomId = $imageIds->first();

        // 移除最後一批，其餘取亂數
        } else {
            $imageIds->pop($this->minuteLimit - 1);
            $randomId = $imageIds->random();
        }

        // 取出所有要執行的ID
        $imageIds = $this->imageJob->select('id')
                                    ->where(function($query) {
                                        $query->whereNull('width')->orWhereNull('height');
                                    })
                                    ->pluck('id');

        // 沒有要執行的
        if ($imageIds->isEmpty()) {
            return true;

        // 剩下最後一次
        } else if ($imageIds->count() < $this->minuteLimit) {
            $randomId = $imageIds->first();

        // 移除最後一批，其餘取亂數
        } else {
            $imageIds->pop($this->minuteLimit - 1);
            $randomId = $imageIds->random();
        }

        // 取得要執行的圖片
        $imageJobs = $this->imageJob->where(function($query) {
                                        $query->whereNull('width')->orWhereNull('height');
                                    })
                                    ->where('id', '>=', $randomId)
                                    ->limit($this->minuteLimit)
                                    ->get();

        // 重新處理圖片的寬高資訊
        $this->withProgressBar($imageJobs, function ($imageJob) {

            // 取得S3上圖片的寬高
            $data = $this->getImageInfoService->run($imageJob->file_name);
            if (!$data) {
                $imageJob->delete();
                return false;
            }

            // 更新 ImageJob Model
            $imageJob->width  = $data['width'];
            $imageJob->height = $data['height'];
            $imageJob->save();
        });
        $this->info("\n");

        $this->info('共重新處理'.$imageJobs->count().'張圖片的寬高資訊！');
        $this->endLog();
    }
}
