<?php

namespace App\Console\Commands\OldData;

use Illuminate\Console\Command;
use App\Services\OldData\ShareMergeToForumService;

class ShareMergeToForumCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'old-data:share-merge-to-forum';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【資料合併】W姐妹合併至好婚聊聊';

    private $shareMergeToForumService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        ShareMergeToForumService $shareMergeToForumService
    ) {
        parent::__construct();

        $this->shareMergeToForumService = $shareMergeToForumService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $count = $this->shareMergeToForumService->run();

        $this->info('Done!!');
        $this->info('共計匯入'.$count['articles'].'篇好婚聊聊文章！');
        $this->info('共計匯入'.$count['images'].'張 Image！');
        $this->info('共計匯入'.$count['cover'].'張封面照！');
        $this->info('共計匯入'.$count['atUsers'].'個 @user！');
        $this->info('共計匯入'.$count['likes'].'個 Like！');
        $this->info('共計匯入'.$count['comments'].'篇留言！');
        $this->info('共計匯入'.$count['commentImages'].'張留言 Image！');
        $this->info('共計匯入'.$count['commentAtUsers'].'個留言 @user！');
        $this->info('共計匯入'.$count['commentLikes'].'個留言 Like！');
        $this->info('共計匯入'.$count['replies'].'篇回覆！');
        $this->info('共計匯入'.$count['replyImages'].'張回覆 Image！');
        $this->info('共計匯入'.$count['replyAtUsers'].'個回覆 @user！');
        $this->info('共計匯入'.$count['replyLikes'].'個回覆 Like！');
        $this->info('共計匯入'.$count['brands'].'個品牌！');
        $this->info('共計匯入'.$count['tracks'].'個使用者追蹤！');
        $this->info('共計匯入'.$count['tags'].'個話題標籤！');
        $this->info('共計匯入'.$count['logShares'].'個分享紀錄！');
    }
}
