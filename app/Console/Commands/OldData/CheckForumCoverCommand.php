<?php

namespace App\Console\Commands\OldData;

use Illuminate\Console\Command;
use App\Models\ForumArticle;
use App\Services\Image\CreateImageService;

class CheckForumCoverCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'old-data:check-forum-cover';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【好婚聊聊】檢查好婚聊聊的推薦文是否有封面';

    private $forumArticle;
    private $createImageService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        ForumArticle $forumArticle,
        CreateImageService $createImageService
    ) {
        parent::__construct();

        $this->forumArticle       = $forumArticle;
        $this->createImageService = $createImageService;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 橫板封面照優先
        $this->forumArticle->status('published')
                            ->recommend()
                            ->whereDoesntHave('cover')
                            ->whereHas('images', function($q) {
                                $q->whereColumn('width', '>=', 'height');
                            })
                            ->get()
                            ->map(function($article) {
                                echo ', '.$article->id;

                                foreach ($article->images as $image) {
                                    if ($image->width < $image->height) {
                                        continue;
                                    }

                                    // 更新上傳的圖檔的來源
                                    $this->createImageService->add([
                                        'file_name' => $image->file_name,
                                        'type'      => 'forum_cover',
                                        'target_id' => $article->id,
                                        'only'      => true,
                                    ]);
                                    break;
                                }
                            });

        // 直板封面照
        $this->forumArticle->status('published')
                            ->recommend()
                            ->whereDoesntHave('cover')
                            ->whereHas('images', function($q) {
                                $q->whereColumn('height', '>', 'width');
                            })
                            ->get()
                            ->map(function($article) {
                                echo ', '.$article->id;

                                // 更新上傳的圖檔的來源
                                $this->createImageService->add([
                                    'file_name' => $article->images[0]->file_name,
                                    'type'      => 'forum_cover',
                                    'target_id' => $article->id,
                                    'only'      => true,
                                ]);
                            });
    }
}
