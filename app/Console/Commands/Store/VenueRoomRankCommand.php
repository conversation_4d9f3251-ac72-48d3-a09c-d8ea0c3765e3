<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\UpdateVenueRoomRankRankService;

class VenueRoomRankCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:venue-room-rank {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】更新婚宴場地廳房排行';

    protected $updateVenueRoomRankRankService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UpdateVenueRoomRankRankService $updateVenueRoomRankRankService
    ) {
        parent::__construct();

        $this->updateVenueRoomRankRankService = $updateVenueRoomRankRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 更新婚宴場地廳房排行
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->updateVenueRoomRankRankService->run($date);
        $this->info('共更新'.$total.'個'.$dateString.'的婚宴場地廳房排行！');

        $this->endLog();
    }
}
