<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\Store;

class PriceRangeCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:price-range {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】取得價格區間';

    protected $store;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(Store $store)
    {
        parent::__construct();

        $this->store = $store;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find store
            $store = $this->store->find($this->argument('id'));
            if (!$store) {
                $this->error('Store Not Find!');
                return;
            }

            // 更新商家的價格區間
            $store->updatePriceRange();

            $message  = $store->name.': ';
            $message .= '服務價格區間為'.$store->min_price.'~'.$store->max_price;
            $this->info($message);
            return;
        }

        // 所有付費商家
        $stores = $this->store->published()->get();
        foreach ($stores as $store) {

            // 更新商家的價格區間
            $store->updatePriceRange();
        }
        $this->info('共統計'.$stores->count().'個商家！');
        $this->endLog();
    }
}
