<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\UpdateStoreAlbumRankService;

class AlbumRankCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:album-rank {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】更新商家相本排行';

    protected $updateStoreAlbumRankService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UpdateStoreAlbumRankService $updateStoreAlbumRankService
    ) {
        parent::__construct();

        $this->updateStoreAlbumRankService = $updateStoreAlbumRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 更新商家相本排行
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->updateStoreAlbumRankService->run($date);
        $this->info('共更新'.$total.'個'.$dateString.'的商家相本排行！');

        $this->endLog();
    }
}
