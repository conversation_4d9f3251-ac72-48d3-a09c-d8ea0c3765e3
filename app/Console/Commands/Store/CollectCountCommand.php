<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Repositories\StoreRepository;

class CollectCountCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:collect-count {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】取得收藏數';

    protected $after_at;
    protected $storeRepository;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(StoreRepository $storeRepository)
    {
        parent::__construct();

        $this->after_at = now()->subMonths(5); // 計算五個月以內的紀錄
        $this->storeRepository = $storeRepository;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find store
            $store = $this->storeRepository->getModel()->find($this->argument('id'));
            if (!$store) {
                $this->error('Store Not Find!');
                return;
            }

            $this->doCount($store);

            $message  = $store->name.': ';
            $message .= $store->unique_user_all_collect_count.'個不重複的新娘所有收藏數！';
            $this->info($message);
            return;
        }

        // 所有付費商家
        $stores = $this->storeRepository->getModel()->published()->get();
        foreach ($stores as $store) {
            $this->doCount($store);
        }
        $this->info('共統計'.$stores->count().'個商家！');
        $this->endLog();
    }

    /**
     * 實作統計
     */
    private function doCount($store)
    {
        $store->unique_user_all_collect_count = $this->storeRepository->getUniqueUserAllCollectCountByAfterAt($store, $this->after_at);
        $store->save();
    }
}
