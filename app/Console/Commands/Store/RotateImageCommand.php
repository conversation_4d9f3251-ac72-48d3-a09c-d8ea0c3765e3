<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\StoreAlbum;
use App\Models\StoreAlbumImage;
use App\Models\Image;
use App\Services\File\GetImageInfoService;

class RotateImageCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:rotate-image {store_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】轉正作品照';

    private $storeAlbum;
    private $storeAlbumImage;
    private $image;
    private $getImageInfoService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreAlbum $storeAlbum,
        StoreAlbumImage $storeAlbumImage,
        Image $image,
        GetImageInfoService $getImageInfoService
    ) {
        parent::__construct();

        $this->storeAlbum          = $storeAlbum;
        $this->storeAlbumImage     = $storeAlbumImage;
        $this->image               = $image;
        $this->getImageInfoService = $getImageInfoService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 正式環境以下中斷
        if (!env('APP_DEBUG')) {
            return true;
        }

        // 商家ID
        $storeId = $this->argument('store_id');

        // 商家作品集IDs
        $storeAlbumIds = $this->storeAlbum->where('store_id', $storeId)->pluck('id');

        // 商家作品照IDs
        $storeAlbumImageIds = $this->storeAlbumImage->whereIn('album_id', $storeAlbumIds)->pluck('id');

        // 取出所有要執行的圖片
        $images = $this->image->where('type', 'store_album_image')->whereIn('target_id', $storeAlbumImageIds)->get();

        // 重新處理圖片的寬高資訊
        $this->withProgressBar($images, function ($image) {

            // 取得S3上圖片的寬高
            $data = $this->getImageInfoService->run($image->file_name);

            // 更新 Image Model
            if ($data) {
                $image->width  = $data['width'];
                $image->height = $data['height'];
                $image->save();
            }
        });
        $this->info("\n");

        $this->info('共重新轉正'.$images->count().'張作品照！');
        $this->endLog();
    }
}
