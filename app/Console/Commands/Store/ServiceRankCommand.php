<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\UpdateStoreServiceRankService;

class ServiceRankCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:service-rank {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】更新商家方案排行';

    protected $updateStoreServiceRankService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UpdateStoreServiceRankService $updateStoreServiceRankService
    ) {
        parent::__construct();

        $this->updateStoreServiceRankService = $updateStoreServiceRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 更新商家方案排行
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->updateStoreServiceRankService->run($date);
        $this->info('共更新'.$total.'個'.$dateString.'的商家方案排行！');

        $this->endLog();
    }
}
