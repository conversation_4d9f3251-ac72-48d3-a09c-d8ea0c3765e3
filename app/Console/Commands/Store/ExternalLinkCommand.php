<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\LogExternalLinkService;

class ExternalLinkCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:external-link {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】新增商家的外站點擊紀錄';

    protected $logExternalLinkService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LogExternalLinkService $logExternalLinkService
    ) {
        parent::__construct();

        $this->logExternalLinkService = $logExternalLinkService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 新增商家的外站點擊紀錄
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->logExternalLinkService->run($date);
        $this->info('共新增'.$total.'家'.$dateString.'的商家外站點擊紀錄！');

        $this->endLog();
    }
}
