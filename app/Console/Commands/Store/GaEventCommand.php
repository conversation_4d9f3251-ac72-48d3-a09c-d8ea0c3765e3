<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Google\BigQuery\LogEventService;

class GaEventCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:ga-event {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】GA事件';

    protected $logEventService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        LogEventService $logEventService
    ) {
        parent::__construct();

        $this->logEventService = $logEventService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 紀錄商家GA事件
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->logEventService->run($date);
        $this->info('共新增'.$total.'筆'.$dateString.'的商家GA事件！');

        // 排程錯誤通知
        if (!$total) {
            $this->sendErrorNotification('沒有新增'.$dateString.'的商家GA事件！');
        }

        // 更新一週前的紀錄，使用BigQuery的歷史總表
        if (!$this->argument('date')) {
            $date = now()->subDays(7)->format('Y-m-d');
            $this->logEventService->run($date, false);
        }

        $this->endLog();
    }
}
