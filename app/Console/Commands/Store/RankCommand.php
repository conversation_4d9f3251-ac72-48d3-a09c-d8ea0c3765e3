<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\UpdateStoreRankService;

class RankCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:rank {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】更新商家排行';

    protected $updateStoreRankService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UpdateStoreRankService $updateStoreRankService
    ) {
        parent::__construct();

        $this->updateStoreRankService = $updateStoreRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 更新商家排行
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->updateStoreRankService->run($date);
        $this->info('共更新'.$total.'個'.$dateString.'的商家排行！');

        $this->endLog();
    }
}
