<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\ForumArticle as Article;
use App\Services\Mail\Store\SharePostPublishedService;

class SharePostPublishedCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:recommend-article-published';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】好婚聊聊 - 推薦板有新發佈的分享文通知';

    protected $brandArticles;
    protected $article;
    protected $sharePostPublishedService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Article $article,
        SharePostPublishedService $sharePostPublishedService
    ) {
        parent::__construct();

        $this->article                      = $article;
        $this->sharePostPublishedService = $sharePostPublishedService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 取得上週發布的分享文列表
        $after_at = date('Y-m-d H:i:s', strtotime('-1 week'));
        // dd($after_at);
        $articles    = $this->article->where('category_id', 5) // 5:推薦文
                                ->where('published_at', '>=', $after_at)
                                ->release()
                                ->select('forum_articles.id', 'user_id', 'title', 'published_at')
                                ->sortPublishedAt()
                                ->get();
        if(count($articles) === 0) {
            $this->info('沒有好婚聊聊 - 推薦板有新發佈的分享文！');
            $this->endLog();
            return;
        }
        foreach ($articles as $article) {

            // 區分分享文的所屬品牌
            foreach ($article->brands as $brand) {

                // 該品牌沒有email，就略過
                if (!$brand->present()->store_email) {
                    continue;
                }

                $this->brandArticles[$brand->id]['model']   = $brand;
                $this->brandArticles[$brand->id]['articles'][] = $article;
            };
        }

        if($this->brandArticles === null) {
            $this->info('沒有好婚聊聊 - 推薦板有新發佈的分享文！');
            $this->endLog();
            return;
        }
        // 分享文的所屬品牌列表
        foreach ($this->brandArticles as $brand) {

            // 好婚聊聊 - 推薦板有新發佈分享文的商家通知
            $this->sharePostPublishedService->sendMail($brand['model'], $brand['articles']);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        $this->info('好婚聊聊 - 推薦板有新發佈的分享文，共通知了'.count($this->brandArticles).'個商家！');
        $this->endLog();
    }
}
