<?php

namespace App\Console\Commands\Store;

use Illuminate\Console\Command;
use App\Models\Wdv2\UserQuote;
use App\Models\Brand;
use App\Models\MarryStudio;
use App\Repositories\UserRepository;
use App\Services\Mail\Store\UserQuoteNotifyBrandService;
use App\Services\Mail\Store\UserQuoteNotifyUnknownTypeMarryStudioService;
use App\Services\Mail\Store\UserQuoteNotifyBrandYzcubeService;
use App\Traits\CommandLogTrait;
use App\Traits\Job\GetSqsClientTrait;
use App\Traits\Job\DelayAllocationTrait;

class UserQuoteNotifyBrandCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    use GetSqsClientTrait;
    use DelayAllocationTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:user-quote-notify-brand';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】公開報價通知品牌';

    protected $userQuote;
    protected $brand;
    protected $marryStudio;
    protected $userRepository;
    protected $userQuoteNotifyBrandService;
    protected $userQuoteNotifyUnknownTypeMarryStudioService;
    protected $userQuoteNotifyBrandYzcubeService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UserQuote $userQuote,
        Brand $brand,
        MarryStudio $marryStudio,
        UserRepository $userRepository,
        UserQuoteNotifyBrandService $userQuoteNotifyBrandService,
        UserQuoteNotifyUnknownTypeMarryStudioService $userQuoteNotifyUnknownTypeMarryStudioService,
        UserQuoteNotifyBrandYzcubeService $userQuoteNotifyBrandYzcubeService
    ) {
        parent::__construct();

        $this->userQuote                                    = $userQuote;
        $this->brand                                        = $brand;
        $this->marryStudio                                  = $marryStudio;
        $this->userRepository                               = $userRepository;
        $this->userQuoteNotifyBrandService                  = $userQuoteNotifyBrandService;
        $this->userQuoteNotifyUnknownTypeMarryStudioService = $userQuoteNotifyUnknownTypeMarryStudioService;
        $this->userQuoteNotifyBrandYzcubeService            = $userQuoteNotifyBrandYzcubeService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 找出已靜置的時間區間
        $startDate = now()->subDays(7)->format('Y-m-d 00:00:00');
        $endDate   = now()->subDay()->format('Y-m-d 23:59:59');

        // 當天有效的主動報價
        $userQuotes = $this->userQuote->release()
                                        ->whereBetween('created_at', [$startDate, $endDate])
                                        ->get();

        // 沒有主動報價略過
        $userQuoteCount = $userQuotes->count();
        if (!$userQuoteCount) {
            $this->endLog();
            return;
        }

        // 通知品牌
        $brandCount = $this->notifyBrands($userQuotes);

        // 通知未知類型的結婚吧商家
        $studioCount = $this->notifyUnknownTypeMarryStudios($userQuotes);

        // 紀錄總覽
        $info = $this->getWeekDay($startDate).' ~ '.$this->getWeekDay($endDate).' 有 '.$userQuoteCount.' 個主動報價在找婚禮商家，共通知了 '.$brandCount.' 個品牌、以及 '.$studioCount.' 個結婚吧商家！';
        $this->info($info);

        // 額外寄信給管理者，並顯示統計數量
        $admins = $this->userRepository->getData(['is_admin' => 1]);
        foreach ($admins as $admin) {

            // 主動報價通知品牌 的管理者通知
            $this->userQuoteNotifyBrandYzcubeService->sendMail($admin, $userQuotes, $brandCount, $studioCount);
        }

        $this->endLog();
    }

    /**
     * 特製日期格式
     */
    private function getWeekDay($data)
    {
        $weekAry = ['日', '一', '二', '三', '四', '五', '六'];

        return date('m/d', strtotime($data)).'('.$weekAry[date('w', strtotime($data))].')';
    }

    /**
     * 通知品牌
     */
    private function notifyBrands($userQuotes)
    {
        // 主動報價須通知的品牌
        $brands = $this->brand->doesntHave('stores')
                                ->quoteSubscribers() // 排除取消訂閱-公開報價通知
                                ->whereNotNull('email')
                                ->inRandomOrder()
                                ->get();

        // 寄信通知品牌
        $total = $brands->count();
        $sqsArgs = [
            'client' => $this->getSqsClient(),
            'delay'  => 0,
        ];
        foreach ($brands as $index => $brand) {

            // 主動報價通知品牌
            $sqsArgs['delay'] = $this->getDelayTimeByIndex($index, $total);
            $this->userQuoteNotifyBrandService->sendMail($brand, $userQuotes, $sqsArgs);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        return $total;
    }

    /**
     * 通知未知類型的結婚吧商家
     */
    private function notifyUnknownTypeMarryStudios($userQuotes)
    {
        // 主動報價須通知的結婚吧商家
        $marryStudios = $this->marryStudio->emailNotEmpty()
                                            ->quoteSubscribers() // 排除取消訂閱-公開報價通知
                                            ->unknownStoreTypes()
                                            ->excludeStoreEmail()
                                            ->inRandomOrder()
                                            ->get();

        // 寄信通知結婚吧商家
        $total = $marryStudios->count();
        $sqsArgs = [
            'client' => $this->getSqsClient(),
            'delay'  => 0,
        ];
        foreach ($marryStudios as $index => $studio) {

            // 通知結婚吧商家
            $sqsArgs['delay'] = $this->getDelayTimeByIndex($index, $total);
            $this->userQuoteNotifyUnknownTypeMarryStudioService->sendMail($studio, $userQuotes, $sqsArgs);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        return $total;
    }
}
