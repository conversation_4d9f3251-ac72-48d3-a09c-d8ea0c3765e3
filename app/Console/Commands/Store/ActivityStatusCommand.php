<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Models\Activity;

class ActivityStatusCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:activity-status {id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】活動方案狀態檢查';

    protected $activity;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Activity $activity
    ) {
        parent::__construct();

        $this->activity = $activity;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 指定ID
        if ($this->argument('id')) {

            // find activity
            $activity = $this->activity->withTrashed()->find($this->argument('id'));
            if (!$activity) {
                $this->error('Activity Not Find!');
                return;
            }

            // 重置已結束的活動方案
            $activity->resetCompleted();

            $this->info($activity->title.': 活動方案狀態已檢查完畢！');
            return;
        }

        // 重置已結束的活動方案
        $activities = $this->activity->withTrashed()->get();
        foreach ($activities as $activity) {
            $activity->resetCompleted();
        }

        $this->info('共檢查'.$activities->count().'個商家活動方案狀態！');
        $this->endLog();
    }
}
