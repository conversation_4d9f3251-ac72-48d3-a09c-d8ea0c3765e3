<?php

namespace App\Console\Commands\Store;

use Illuminate\Console\Command;
use App\Models\Wdv2\UserQuote;
use App\Models\Store;
use App\Models\MarryStudio;
use App\Repositories\UserRepository;
use App\Services\Mail\Store\UserQuoteNotifyBasicLevelService;
use App\Services\Mail\Store\UserQuoteNotifyMarryStudioService;
use App\Services\Mail\Store\UserQuoteNotifyBasicLevelYzcubeService;
use App\Traits\CommandLogTrait;
use App\Traits\Job\GetSqsClientTrait;
use App\Traits\Job\DelayAllocationTrait;

class UserQuoteNotifyBasicLevelCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    use GetSqsClientTrait;
    use DelayAllocationTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:user-quote-notify-basic-level';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】公開報價通知基本型商家';

    protected $standingTime;

    protected $userQuote;
    protected $store;
    protected $marryStudio;
    protected $userRepository;
    protected $userQuoteNotifyBasicLevelService;
    protected $userQuoteNotifyMarryStudioService;
    protected $userQuoteNotifyBasicLevelYzcubeService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UserQuote $userQuote,
        Store $store,
        MarryStudio $marryStudio,
        UserRepository $userRepository,
        UserQuoteNotifyBasicLevelService $userQuoteNotifyBasicLevelService,
        UserQuoteNotifyMarryStudioService $userQuoteNotifyMarryStudioService,
        UserQuoteNotifyBasicLevelYzcubeService $userQuoteNotifyBasicLevelYzcubeService
    ) {
        parent::__construct();

        // [新娘秘書/婚攝/婚錄] 的主動報價：詢價單建立24小時後才能開始報價，每天寄送通知，所以得取前天新增的詢價單
        // [婚禮主持人/婚禮佈置] 的主動報價：詢價單建立24小時後才能開始報價，分禮拜一、禮拜五寄送通知，所以得取3~4天前新增的詢價單
        $this->standingTime = [
            3 => [
                0 => now()->subDay(),
                1 => now()->subDay(),
                2 => now()->subDay(),
                3 => now()->subDay(),
                4 => now()->subDay(),
                5 => now()->subDay(),
                6 => now()->subDay(),
            ],
            4 => [
                0 => now()->subDay(),
                1 => now()->subDay(),
                2 => now()->subDay(),
                3 => now()->subDay(),
                4 => now()->subDay(),
                5 => now()->subDay(),
                6 => now()->subDay(),
            ],
            6 => [
                1 => now()->subDays(3),
                5 => now()->subDays(4),
            ],
            8 => [
                1 => now()->subDays(3),
                5 => now()->subDays(4),
            ],
        ];

        $this->userQuote                              = $userQuote;
        $this->store                                  = $store;
        $this->marryStudio                            = $marryStudio;
        $this->userRepository                         = $userRepository;
        $this->userQuoteNotifyBasicLevelService       = $userQuoteNotifyBasicLevelService;
        $this->userQuoteNotifyMarryStudioService      = $userQuoteNotifyMarryStudioService;
        $this->userQuoteNotifyBasicLevelYzcubeService = $userQuoteNotifyBasicLevelYzcubeService;

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $storeData = [
            'userQuoteCount' => 0,
            'storeCount'     => 0,
            'studioCount'    => 0,
            'types'          => [],
        ];

        // 主動報價的商家類型(有檔期)
        foreach ($this->store->quoteTypeList as $type => $name) {

            // 驗證今天是否要通知
            if (!isset($this->standingTime[$type][date('w')])) {
                continue;
            }

            // 找出已靜置的時間區間
            $startDate = $this->standingTime[$type][date('w')]->format('Y-m-d 00:00:00');
            $endDate   = now()->subDay()->format('Y-m-d 23:59:59');

            // 當天有效的主動報價
            $userQuotes = $this->userQuote->release()
                                            ->where('type', $type)
                                            ->whereBetween('created_at', [$startDate, $endDate])
                                            ->get();

            // 沒有主動報價略過
            $userQuoteCount = $userQuotes->count();
            if (!$userQuoteCount) {
                continue;
            }

            // 通知基本型商家
            $storeCount = $this->notifyBasicLevelStores($type, $userQuotes);

            // 通知結婚吧商家
            $studioCount = $this->notifyMarryStudios($type, $userQuotes);

            // 紀錄總覽
            $info = $this->getWeekDay($startDate).' ~ '.$this->getWeekDay($endDate).' 有 '.$userQuoteCount.' 個主動報價在找'.$name.'，共通知了 '.$storeCount.' 個基本型商家、以及 '.$studioCount.' 個結婚吧商家！';
            $storeData['userQuoteCount'] += $userQuoteCount;
            $storeData['storeCount']     += $storeCount;
            $storeData['studioCount']    += $studioCount;
            $storeData['types'][]        = [
                'info'       => $info,
                'name'       => $name,
                'userQuotes' => $userQuotes,
            ];
            $this->info($info);
        }

        // 額外寄信給管理者，並顯示統計數量
        if ($storeData['userQuoteCount']) {
            $admins = $this->userRepository->getData(['is_admin' => 1]);
            foreach ($admins as $admin) {

                // 主動報價通知基本型商家 的管理者通知
                $this->userQuoteNotifyBasicLevelYzcubeService->sendMail($admin, $storeData);
            }
        }

        $this->endLog();
    }

    /**
     * 特製日期格式
     */
    private function getWeekDay($data)
    {
        $weekAry = ['日', '一', '二', '三', '四', '五', '六'];

        return date('m/d', strtotime($data)).'('.$weekAry[date('w', strtotime($data))].')';
    }

    /**
     * 通知基本型商家
     */
    private function notifyBasicLevelStores($type, $userQuotes)
    {
        // 主動報價須通知的基本型商家
        $stores = $this->store->emailNotEmpty()
                                ->quoteEmailSubscribers() // 排除取消訂閱-公開報價通知基本型商家
                                ->type($type)
                                ->leave()
                                ->inRandomOrder()
                                ->get();

        // 寄信通知商家
        $total = $stores->count();
        $sqsArgs = [
            'client' => $this->getSqsClient(),
            'delay'  => 0,
        ];
        foreach ($stores as $index => $store) {

            // 主動報價通知基本型商家
            $sqsArgs['delay'] = $this->getDelayTimeByIndex($index, $total);
            $this->userQuoteNotifyBasicLevelService->sendMail($store, $userQuotes, $sqsArgs);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        return $total;
    }

    /**
     * 通知結婚吧商家
     */
    private function notifyMarryStudios($type, $userQuotes)
    {
        // 主動報價須通知的結婚吧商家
        $marryStudios = $this->marryStudio->emailNotEmpty()
                                            ->quoteSubscribers() // 排除取消訂閱-公開報價通知
                                            ->storeType($type)
                                            ->excludeStoreEmail()
                                            ->inRandomOrder()
                                            ->get();

        // 寄信通知結婚吧商家
        $total = $marryStudios->count();
        $sqsArgs = [
            'client' => $this->getSqsClient(),
            'delay'  => 0,
        ];
        foreach ($marryStudios as $index => $studio) {

            // 通知結婚吧商家
            $sqsArgs['delay'] = $this->getDelayTimeByIndex($index, $total);
            $this->userQuoteNotifyMarryStudioService->sendMail($type, $studio, $userQuotes, $sqsArgs);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        return $total;
    }
}
