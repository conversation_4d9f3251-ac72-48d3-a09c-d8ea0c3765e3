<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\CreateStoreIntegralService;

class IntegralCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:integral {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】統計商家排行積分';

    protected $createStoreIntegralService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        CreateStoreIntegralService $createStoreIntegralService
    ) {
        parent::__construct();

        $this->createStoreIntegralService = $createStoreIntegralService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 紀錄商家排行積分
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->createStoreIntegralService->run($date);
        $this->info('共統計'.$total.'個'.$dateString.'的商家排行積分！');

        $this->endLog();
    }
}
