<?php

namespace App\Console\Commands\Store;

use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;
use App\Services\Store\UpdateStoreVideoRankService;

class VideoRankCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'store:video-rank {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【商家】更新商家影片排行';

    protected $updateStoreVideoRankService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        UpdateStoreVideoRankService $updateStoreVideoRankService
    ) {
        parent::__construct();

        $this->updateStoreVideoRankService = $updateStoreVideoRankService;
        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 更新商家影片排行
        $date       = $this->argument('date') ?: now()->subDay()->format('Y-m-d');
        $dateString = date('n/j', strtotime($date));
        $total      = $this->updateStoreVideoRankService->run($date);
        $this->info('共更新'.$total.'個'.$dateString.'的商家影片排行！');

        $this->endLog();
    }
}
