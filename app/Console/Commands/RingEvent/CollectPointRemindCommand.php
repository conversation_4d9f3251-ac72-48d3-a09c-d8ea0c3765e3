<?php

namespace App\Console\Commands\RingEvent;

use App\Models\RingUser;
use App\Services\Mail\RingEvent\CollectPointRemindService;
use App\Traits\CommandLogTrait;
use Illuminate\Console\Command;

class CollectPointRemindCommand extends Command
{
    /**
     * Log run time trait
     */
    use CommandLogTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ring-event:collect-point-remind';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '【婚戒大賞】集點提醒通知';

    // 靜置7天才提醒
    private $standingDay = 7;
    private $ringUser;
    private $collectPointRemindService;
    private $dateRange;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        RingUser $ringUser,
        CollectPointRemindService $collectPointRemindService
    ) {
        parent::__construct();

        $this->ringUser                  = $ringUser;
        $this->collectPointRemindService = $collectPointRemindService;

        // 找出已靜置的時間區間
        $this->dateRange = [
            now()->subDays($this->standingDay)->startOfDay(),
            now()->subDays($this->standingDay)->endOfDay(),
        ];

        $this->startLog();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // 尚未集滿點數
        $ringUsers = $this->ringUser->whereBetween('collect_count', [1, 4])

                                    // 七天前有集點
                                    ->whereHas('points', function($q) {
                                        $q->whereBetween('created_at', $this->dateRange);
                                    })

                                    // 連續七天沒有集點
                                    ->whereDoesntHave('points', function ($q) {
                                        $q->where('created_at', '>', $this->dateRange[1]);
                                    })

                                    // 連續七天沒有預約
                                    ->whereDoesntHave('reserves', function ($q) {
                                        $q->where('created_at', '>', $this->dateRange[1]);
                                    })

                                    // 連續七天沒有上傳訂單
                                    ->whereDoesntHave('orders', function ($q) {
                                        $q->where('created_at', '>', $this->dateRange[1]);
                                    })
                                    ->get()
                                    ->each(function($ringUser) {

                                        // Email通知
                                        $this->collectPointRemindService->sendMail($ringUser);
                                    });

        $this->info('共通知'.$ringUsers->count().'位尚未集滿點數的報名者！');
        $this->endLog();
    }
}
