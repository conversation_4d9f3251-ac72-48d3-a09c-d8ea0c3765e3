<?php
/*
 |--------------------------------------
 |  神之後台-儲存廣告素材 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\AdCampaign\Yzcube;

use App\Repositories\AdImageRepository;
use App\Services\Image\CreateImageService;
use App\Services\Image\DeleteImageService;

class SaveImageService
{
    private $adImageRepository;
    private $createImageService;
    private $deleteImageService;

    public function __construct(
        AdImageRepository $adImageRepository,
        CreateImageService $createImageService,
        DeleteImageService $deleteImageService
    ) {
        $this->adImageRepository  = $adImageRepository;
        $this->createImageService = $createImageService;
        $this->deleteImageService = $deleteImageService;
    }

    /**
     * 儲存廣告素材
     *
     */
    public function run($request)
    {
        $adImage = $this->adImageRepository->getFirst(['id' => $request['ad_image_id']]);
        if (!$adImage) {
            $adImage = $this->adImageRepository->getModel();
        }

        $adImage->name          = $request['name'];
        $adImage->campaign_type = $request['campaign_type'];
        $adImage->is_backup     = (!$adImage->campaign && $request['is_backup']) ? 1 : 0;
        $adImage->link_type     = $request['link_type'] ?: 'none';
        $adImage->url           = ($request['link_type'] == 'none') ? NULL : $request['url_'.$request['link_type']];
        $adImage->link_type_xs  = $request['link_type_xs'] ?: 'none';
        $adImage->url_xs        = ($request['link_type_xs'] == 'none') ? NULL : $request['url_xs_'.$request['link_type_xs']];
        $adImage->save();

        // 更新上傳的圖檔的來源
        if ($request['image']) {
            $this->createImageService->add([
                'file_name' => $request['image'],
                'type'      => 'ad_image',
                'target_id' => $adImage->id,
                'only'      => true,
            ]);

        // 移除圖片
        } elseif ($adImage->image) {
            $this->deleteImageService->delete(['id' => $adImage->image->id]);
        }

        // 更新上傳的圖檔的來源 (mobile版)
        if ($request['image_xs']) {
            $this->createImageService->add([
                'file_name' => $request['image_xs'],
                'type'      => 'ad_image_xs',
                'target_id' => $adImage->id,
                'only'      => true,
            ]);

        // 移除圖片 (mobile版)
        } elseif ($adImage->image_xs) {
            $this->deleteImageService->delete(['id' => $adImage->image_xs->id]);
        }

        return $adImage;
    }
}
