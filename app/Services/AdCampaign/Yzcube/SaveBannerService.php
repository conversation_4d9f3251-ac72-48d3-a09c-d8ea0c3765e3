<?php
/*
 |--------------------------------------
 |  神之後台-儲存首頁輪播 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\AdCampaign\Yzcube;

use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;

class SaveBannerService
{
    private $adCampaignRepository;
    private $adImageRepository;

    public function __construct(
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository
    ) {
        $this->adCampaignRepository = $adCampaignRepository;
        $this->adImageRepository    = $adImageRepository;
    }

    /**
     * 儲存首頁輪播
     *
     */
    public function run($request)
    {
        $adCampaign = $this->adCampaignRepository->getFirst(['type' => 'banner', 'id' => $request['banner_id']]);
        if (!$adCampaign) {
            $adCampaign = $this->adCampaignRepository->getModel();
        }

        $adCampaign->type       = 'banner';
        $adCampaign->name       = $request['name'];
        $adCampaign->start_date = $request['start_date'];
        $adCampaign->end_date   = $request['end_date'];
        $adCampaign->use_slots  = $request['use_slots'];
        $adCampaign->save();

        // 刪除關聯
        $adCampaign->bannerAdImages()
                    ->whereNotIn('id', $request['ad_image_ids'])
                    ->update(['ad_campaign_id' => NULL]);

        // 新增關聯
        $this->adImageRepository->getModel()
                                ->whereIn('id', $request['ad_image_ids'])
                                ->where(['ad_campaign_id' => NULL])
                                ->update(['ad_campaign_id' => $adCampaign->id]);

        return $adCampaign;
    }
}
