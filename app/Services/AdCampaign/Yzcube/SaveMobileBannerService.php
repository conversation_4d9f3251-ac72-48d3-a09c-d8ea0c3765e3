<?php
/*
 |--------------------------------------
 |  神之後台-儲存手機側邊欄Banner Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\AdCampaign\Yzcube;

use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;

class SaveMobileBannerService
{
    private $adCampaignRepository;
    private $adImageRepository;

    public function __construct(
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository
    ) {
        $this->adCampaignRepository = $adCampaignRepository;
        $this->adImageRepository    = $adImageRepository;
    }

    /**
     * 儲存手機側邊欄Banner
     *
     */
    public function run($request)
    {
        $adCampaign = $this->adCampaignRepository->getFirst(['type' => 'mobile_banner', 'id' => $request['mobile_banner_id']]);
        if (!$adCampaign) {
            $adCampaign = $this->adCampaignRepository->getModel();
        }

        $adCampaign->type       = 'mobile_banner';
        $adCampaign->name       = $request['name'];
        $adCampaign->start_date = $request['start_date'];
        $adCampaign->end_date   = $request['end_date'];
        $adCampaign->save();

        // 刪除關聯
        $adImage = $adCampaign->mobileBannerAdImage;
        if ($adImage && $adImage->id != $request['ad_image_id']) {
            $adImage->ad_campaign_id = NULL;
            $adImage->save();
        }

        // 新增關聯
        $this->adImageRepository->updateData([
            'id'             => $request['ad_image_id'],
            'ad_campaign_id' => NULL,
        ],[
            'ad_campaign_id' => $adCampaign->id,
        ]);

        return $adCampaign;
    }
}
