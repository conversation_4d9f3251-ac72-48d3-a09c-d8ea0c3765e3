<?php
/*
 |--------------------------------------
 |  神之後台-儲存浮動廣告 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\AdCampaign\Yzcube;

use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;
use App\Traits\ApiErrorTrait;

class SaveFloatService
{
    private $adCampaignRepository;
    private $adImageRepository;

    use ApiErrorTrait;

    public function __construct(
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository
    ) {
        $this->adCampaignRepository = $adCampaignRepository;
        $this->adImageRepository    = $adImageRepository;
    }

    /**
     * 儲存浮動廣告
     *
     */
    public function run($request)
    {
        $adCampaign = $this->adCampaignRepository->getFirst(['type' => 'float', 'id' => $request['float_id']]);
        if (!$adCampaign) {
            $adCampaign = $this->adCampaignRepository->getModel();
        }

        // 取得日期區間中有發佈中的版位
        $floats = $this->adCampaignRepository->getPublishedByDateRange('float', $request['start_date'], $request['end_date'], $adCampaign->id);
        if ($floats->count()) {
            $this->setException('選取的日期區間中，有已發佈中的版位！');
        }

        $adCampaign->type       = 'float';
        $adCampaign->name       = $request['name'];
        $adCampaign->start_date = $request['start_date'];
        $adCampaign->end_date   = $request['end_date'];
        $adCampaign->save();

        // 刪除關聯
        $adImage = $adCampaign->floatAdImage;
        if ($adImage && $adImage->id != $request['ad_image_id']) {
            $adImage->ad_campaign_id = NULL;
            $adImage->save();
        }

        // 新增關聯
        $this->adImageRepository->updateData([
            'id'             => $request['ad_image_id'],
            'ad_campaign_id' => NULL,
        ],[
            'ad_campaign_id' => $adCampaign->id,
        ]);

        return $adCampaign;
    }
}
