<?php
namespace App\Services\MetaScraper\Contract;

use App\Models\LogMetaScraper;
use Illuminate\Support\Facades\Log;
use App\Traits\ApiErrorTrait;

abstract class AbsScraperHandle
{
    use ApiErrorTrait;
    protected $url;
    protected $type;
    protected $target_id;

    public function handle($url, $type, $target_id)
    {
        $this->url = trim(strtolower($url));
        $this->type = $type;
        $this->target_id = $target_id;

        $result = $this->run();

        if (empty($result)) {
            return false;
        }

        $showImage = $result['is_self'] ? config('params.image_url') . '/original/' . $result['image'] : $result['image'];

        return [
            'success'      => true,
            'title'        => $result['title'] ?? '',
            'description'  => $result['description'] ?? '',
            'show_image'   => $showImage,
            'image'        => $result['image'] ?? null,
            'is_self'      => $result['is_self'],
            'favicon'      => $result['favicon'] ?? null,
            'scraper_type' => $result['scraper_type'] ?? '',
        ];
    }

    public function saveResult(string $url, array $result)
    {
        LogMetaScraper::create([
            'type'         => $this->type,
            'target_id'    => $this->target_id, // 這裡可以根據實際情況設置目標ID
            'scraper_type' => $result['scraper_type'],
            'url'          => $url,
            'title'        => $result['title'] ?? null,
            'description'  => $result['description'] ?? null,
            'image'        => $result['image'] ?? null,
            'is_self'      => $result['is_self'],
            'favicon'      => $result['favicon'] ?? null,
        ]);
    }

    abstract protected function run();
}
