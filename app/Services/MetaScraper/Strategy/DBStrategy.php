<?php
namespace App\Services\MetaScraper\Strategy;

use App\Services\MetaScraper\Contract\AbsScraperHandle;
use App\Models\LogMetaScraper;

class DBStrategy extends AbsScraperHandle
{
    protected function run()
    {
        $result = LogMetaScraper::where('url', $this->url)
            // ->where('type', $this->type)
            // ->where('target_id', $this->target_id)
            // created_at三個月內
            ->where('created_at', '>=', now()->subMonths(3))
            // 拿最新的一筆資料
            ->orderBy('created_at', 'desc')
            ->first();

        if(!$result) {
            return false;
        }

        return [
            'title'        => $result->title,
            'description'  => $result->description,
            'image'        => $result->image,
            'is_self'      => $result->is_self,
            'favicon'      => $result->favicon,
            'scraper_type' => 'db',
        ];

    }
}
