<?php
namespace App\Services\MetaScraper\Strategy;

use App\Services\MetaScraper\Contract\AbsScraperHandle;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use DOMDocument;
use DOMXPath;

class GuzzleStrategy extends AbsScraperHandle
{
    private $client;

    /**
     * GuzzleStrategy constructor.
     *
     * @param Client $client
     */
    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    protected function run()
    {
        // 抓取網頁 HTML
        try {

            // 如果是facebook、instagram 直接回傳false
            if (preg_match('/(facebook|instagram)\.com/', $this->url)) {
                $this->setException('不支援 Facebook 或 Instagram 網頁抓取');
                return false;
            }

            $response = $this->client->get($this->url);
        } catch (\Exception $e) {
            Log::error('Scraper Error in ' . static::class, [
                'message' => 'Guzzle HTTP request error: ' . $e->getMessage(),
                'url' => $this->url,
            ]);
            return false;
        }

        $html = (string) $response->getBody();

        // 強制轉換 HTML 為 UTF-8
        $html = mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8');

        // 解析 HTML
        libxml_use_internal_errors(true);
        $dom = new DOMDocument();
        $dom->loadHTML('<?xml encoding="UTF-8">' . $html); // 加上 XML 宣告，確保 UTF-8 解析
        libxml_clear_errors();
        $xpath = new DOMXPath($dom);

        // 提取 SEO 標籤
        $titleNode = $xpath->query('//title')->item(0);
        $title = $titleNode ? $titleNode->textContent : null;
        $descNode = $xpath->query("//meta[@name='description']")->item(0);
        $metaDescription = $descNode ? $descNode->getAttribute('content') : null;
        // $metaKeywords = $xpath->query("//meta[@name='keywords']")->item(0)?->getAttribute('content') ?? null;

        // 提取 SEO 縮圖
        // og:image
        $ogNode = $xpath->query("//meta[@property='og:image']")->item(0);
        $ogImage = $ogNode ? $ogNode->getAttribute('content') : null;
        // twitter:image
        $twNode = $xpath->query("//meta[@name='twitter:image']")->item(0);
        $twitterImage = $twNode ? $twNode->getAttribute('content') : null;
        $seoImage = $ogImage ?? $twitterImage ?? null;

        // 提取 favicon
        $faviconNode = $xpath->query("//link[@rel='shortcut icon']")->item(0);
        if (!$faviconNode) {
            $faviconNode = $xpath->query("//link[@rel='icon']")->item(0);
        }
        $favicon = $faviconNode ? $faviconNode->getAttribute('href') : null;

        if (!empty($favicon) && !preg_match('/^https?:\/\//', $favicon)) {
            $parsed_url = parse_url($this->url);
            $scheme = $parsed_url['scheme'] ?? 'https';
            $host = $parsed_url['host'] ?? '';
            $favicon = rtrim("$scheme://$host", '/') . '/' . ltrim($favicon, '/');
        }

        return [
            'title'        => $title,
            'description'  => $metaDescription,
            // 'keywords'  => $metaKeywords,
            'image'        => $seoImage,
            'is_self'      => false,
            'favicon'      => $favicon,
            'scraper_type' => 'guzzle',
        ];

    }
}
