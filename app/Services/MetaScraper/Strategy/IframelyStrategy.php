<?php
namespace App\Services\MetaScraper\Strategy;

use App\Services\MetaScraper\Contract\AbsScraperHandle;
use GuzzleHttp\Client;
use App\Services\Image\CreateImageService;
use App\Services\File\UploadImageFormService;
use Illuminate\Support\Facades\Log;

class IframelyStrategy extends AbsScraperHandle
{
    private $client;
    private $uploadImageFormService;
    private $createImageService;

    /**
     * GuzzleStrategy constructor.
     *
     * @param Client $client
     */
    public function __construct(
        Client $client,
        UploadImageFormService $uploadImageFormService,
        CreateImageService $createImageService
    ){
        $this->client = $client;
        $this->uploadImageFormService = $uploadImageFormService;
        $this->createImageService = $createImageService;
    }

    protected function run()
    {
        // encode url
        $this->url = urlencode($this->url);

        $api = 'https://iframe.ly/api/iframely?url=' . $this->url . '&api_key=' . env('IFRAMELY_API_KEY');

        try {
            $response = $this->client->get($api);
        } catch (\Exception $e) {
            Log::error('Scraper Error in ' . static::class, [
                'message' => 'Iframely API error: ' . $e->getMessage(),
                'url' => $this->url,
            ]);
            return false;
        }

        $data = json_decode((string) $response->getBody(), true);
        $title = $data['meta']['title'] ?? '';
        $description = $data['meta']['description'] ?? '';
        $icon = $data['links']['icon'][0]['href'] ?? null;
        $image = $data['links']['thumbnail'][0]['href'] ?? null;
        $is_self = false;

        // 如果是facebook、instagram，image要下載
        if (preg_match('/(facebook|instagram)\.com/', $this->url)) {
            $base64 = $this->uploadImageFormService->downloadImageAsBase64($image);
            $avatarResult = $this->uploadImageFormService->uploadBase64($base64);

            // 更新上傳的圖檔的來源
            $this->createImageService->add([
                'file_name' => $avatarResult['file_name'],
                'type'      => 'meta_scraper',
                'target_id' => $this->target_id,
                'only'      => false,
            ]);

            $is_self = true;
        }

        return [
            'title'        => $title,
            'description'  => $description,
            'favicon'      => $icon,
            'image'        => $avatarResult['file_name'],
            'is_self'      => $is_self,
            'scraper_type' => 'iframely',
        ];

    }
}
