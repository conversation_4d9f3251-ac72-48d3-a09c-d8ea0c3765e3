<?php
namespace App\Services\MetaScraper;

use App\Services\MetaScraper\Strategy\DBStrategy;
use App\Services\MetaScraper\Strategy\GuzzleStrategy;
use App\Services\MetaScraper\Strategy\IframelyStrategy;

class ScraperHandle
{
    protected array $strategies;

    public function __construct()
    {
        // 順序：拿資料庫 > php > Iframely
        $this->strategies = [
            DBStrategy::class,
            GuzzleStrategy::class,
            IframelyStrategy::class,
        ];
    }

    /**
     * 嘗試所有策略直到成功
     */
    public function handle($url, $type, $target_id)
    {
        foreach ($this->strategies as $strategyClass) {
            $strategy = app($strategyClass);
            $result = $strategy->handle($url, $type, $target_id);

            if ($result !== false) {
                // 如果不是DBStrategy，則儲存結果
                if ($result['scraper_type'] !== 'db') {
                    $strategy->saveResult($url, $result);
                }
                return [
                    'success'      => true,
                    'title'        => $result['title'] ?? '',
                    'description'  => $result['description'] ?? '',
                    'image'        => $result['show_image'] ?? null,
                    'favicon'      => $result['favicon'] ?? null,
                    'scraper_type' => $result['scraper_type'] ?? '',
                ];
            }
        }

        return [
            'success' => false,
        ];
    }
}
