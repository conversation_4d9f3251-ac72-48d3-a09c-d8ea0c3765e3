<?php
declare(strict_types=1);
/*------------------------------------
 | 判斷信件是否被阻檔
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Mail;


use App\Repositories\MailBaseRepository;

class CheckValidService
{
    /** @var MailBaseRepository  */
    private $mailBaseRepository;

    /**
     * CheckIsBlockService constructor.
     * @param MailBaseRepository $mailBaseRepository
     */
    public function __construct(MailBaseRepository $mailBaseRepository)
    {
        $this->mailBaseRepository = $mailBaseRepository;
    }

    /**
     * 判斷信件是否被阻檔
     * @param $address : 要判斷的email address
     * @return bool: 被阻檔 true, 沒阻檔 false
     */
    public function check($address)
    {
        //取出email資料
        $res = $this->mailBaseRepository->getFirst([
            'email' => $address
        ]);

        //沒資料的話就算沒阻檔
        if (is_null($res)) {
            return false;
        }

        return $res->valid != 1;
    }
}
