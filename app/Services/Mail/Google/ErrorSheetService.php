<?php

namespace App\Services\Mail\Google;

use App\Services\Mail\AbsSendMail;

class ErrorSheetService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'log_sheet_error';
        $this->campaignTitle = '【Google試算表 - 錯誤訊息】';
    }

    /**
     * 失敗通知寄信
     *
     * @param  string $title
     * @param  array $data
     * @return void
     */
    public function sendMail(string $title, array $data)
    {
        $params = [
            'address'      => env('MAIL_TEST'),
            'name'         => env('MAIL_FROM_NAME'),
            'subject'      => $title,
            'event' => [
                'campaign_id' => $this->getCampaignID(),
                'target_type' => 'it',
                'target_id'   => null
            ]
        ];

        $sheetUrl = config('params.google_sheet_url'). '/d/' . $data['spreadsheetId'] . '/edit#gid=' . $data['sheetId'];
        
        $messages = [
            'path'       => $data['path'],
            'action'     => $data['action'],
            'sheetUrl'   => $sheetUrl,
            'sheetTitle' => $data['sheetTitle'],
            'inputData'  => $data['inputData'],
            'errorData'  => json_encode(json_decode($data['errorData']), JSON_PRETTY_PRINT)
        ];

        if (!empty($data['logId']) && 
            !empty($data['spreadsheetId']) && 
            !empty($data['sheetId'])
        ) {
            $messages['button'] = [
                'text' => 'Google 試算表 - 重新新增一筆紀錄',
                'link' => config('params.wdv3.api_url') . '/process-google-sheet/' . $data['logId']
            ];
        }
        
        $this->mailService->send($params, 'emails.google.error_sheet', $messages);
    }
}
