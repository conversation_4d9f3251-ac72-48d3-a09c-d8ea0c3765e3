<?php

namespace App\Services\Mail\Invoice;

use App\Services\Mail\AbsSendMail;
use App\Models\Invoice;

class ResendNotificationService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType  = 'invoice_resend_notification';
        $this->campaignTitle = '電子發票開立通知(補發)';
    }

    /**
     * 寄信
     *
     * @param Invoice $invoice
     * @param  $pwd_token
     * @return void
     */
    public function sendMail(Invoice $invoice, $email)
    {
        $this->campaignTargetID = $invoice->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $email,
            'name'         => $invoice->buyer_name,
            'subject'      => $invoice->setting->seller_brand.'-'.$this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'guest',
                'target_id'   => NULL,
            ]
        ];

        $ezPayUrl = parse_url(env('EZPAY_API_URL'));
        $data = [
            'invoice'   => $invoice,
            'ezpay_url' => $ezPayUrl['scheme'].'://'.$ezPayUrl['host'].'/invoice_index/search_platform',
            'title'     => $this->campaignTitle,
        ];

        $this->mailService->send($params, 'emails.invoice.resend_notification', $data);
    }
}
