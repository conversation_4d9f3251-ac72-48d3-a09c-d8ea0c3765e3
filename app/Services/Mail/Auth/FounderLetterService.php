<?php

namespace App\Services\Mail\Auth;

use App\Services\Mail\AbsSendMail;
use App\Models\User;

class FounderLetterService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_founder_letter';
        $this->campaignTargetID = null;
        $this->campaignTitle = '來自於好婚市集共同創辦人的一封信';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @return void
     */
    public function sendMail(User $user)
    {
        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.founder.address'),
            'from_name'    => config('params.mail.founder.name'),
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'      => $user,
            'title'     => $this->campaignTitle,
            'can_reply' => true,
            'button' => [
                'text' => '前往 WeddingDay',
                'link' => config('params.wdv3.www_url'),
            ],
        ];

        $this->mailService->send($params, 'emails.auth.founder_letter', $data);
    }
}
