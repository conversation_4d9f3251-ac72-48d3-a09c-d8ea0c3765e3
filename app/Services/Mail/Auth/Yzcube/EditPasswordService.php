<?php

namespace App\Services\Mail\Auth\Yzcube;

use App\Services\Mail\AbsSendMail;
use App\Models\YzcubeUser;

class EditPasswordService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'yzcube_user_edit_password';
        $this->campaignTargetID = null;
        $this->campaignTitle = 'WeddingDay 神之後台-修改密碼信';
    }

    /**
     * 寄信
     *
     * @param YzcubeUser $yzcubeUser
     * @param  $pwd_token
     * @return void
     */
    public function sendMail(YzcubeUser $yzcubeUser, $pwd_token)
    {
        $edit_password_url = config('params.wdv3.yzcube_url').'/#/reset-password/'.$pwd_token;

        $params = [
            'address'      => $yzcubeUser->email,
            'name'         => $yzcubeUser->name,
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'yzcube_user',
                'target_id'   => $yzcubeUser->id
            ]
        ];
        $data = [
            'yzcube_user'       => $yzcubeUser,
            'title'             => $this->campaignTitle,
            'edit_password_url' => $edit_password_url,
            'button'            => [
                'text' => '點我修改密碼',
                'link' => $edit_password_url,
            ],
        ];

        $this->mailService->send($params, 'emails.auth.yzcube.edit_password', $data);
    }
}
