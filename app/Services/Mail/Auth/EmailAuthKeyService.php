<?php

namespace App\Services\Mail\Auth;

use App\Services\Mail\AbsSendMail;
use App\Models\UserEmailAuthKey;
use App\Models\EmailLegalize;
use App\Models\User;
use App\Models\StoreUser;
use App\Traits\ApiErrorTrait;

class EmailAuthKeyService extends AbsSendMail
{
    private $user;
    private $storeUser;

    use ApiErrorTrait;

    /**
     * __construct
     * @return void
     */
    public function __construct(
        User $user,
        StoreUser $storeUser
    ) {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_email_auth_key';
        $this->campaignTargetID = null;
        $this->campaignTitle = '好婚市集 信箱驗證碼';
        $this->user = $user;
        $this->storeUser = $storeUser;
    }

    /**
     * 會員註冊 - 檢查是否為可驗證信箱
     *
     * @param  array $request
     * @return void
     */
    public function checkSendEmail($request)
    {
        // 商家user
        if ($request['type'] == 'store_email') {
            // 1 商家註冊 2 fb登入後走註冊流程（有store_user_id & fb_id） 3 信箱驗證（有store_user_id）
            $emailUser = $this->storeUser->where(['email' => $request['email']])->first();
            // 第三方登入已存在資料庫的商家user 若重新註冊 只能用自己原本的信箱 或資料庫不存在的信箱
            if ($request['store_user_id'] && $request['fb_id']) {
                $storeUser = $this->storeUser->where(['fb_id' => $request['fb_id']])->first();
                if ($storeUser->email != $request['email']) {
                    if ($emailUser && $emailUser->fb_id != $request['fb_id']) {
                        $this->setException('此信箱已註冊過，請更換信箱 ', 4000, ['email' => '此信箱已註冊過，請更換信箱']);
                    }
                }
            // 新註冊的商家user 若信箱已存在資料庫 不能註冊
            } elseif (!$request['store_user_id']) {
                if ($emailUser) {
                    $this->setException('此信箱已註冊過，請更換信箱 ', 4000, ['email' => '此信箱已註冊過，請更換信箱']);
                }
            }
        }

        // 會員user
        // 驗證Email是否存在會員資料庫
        if ($request['type'] == 'email') {
            $user = $this->user->where(['email' => $request['email']])->first();
            if (!$user) {
                $this->setException('找不到此會員帳號，請重新輸入！ ');
            }
        }

        if (in_array($request['type'], ['fb', 'line', 'google'])) {
            // 驗證第三方id是否已綁定過別的帳號
            if ($user = $this->user->where([$request['type'].'_id' => $request['third_id']])->first()) {
                if ($user->email != $request['email']) {
                    switch ($request['type']) {
                        case 'fb':
                            $this->setException('無法綁定，此 Facebook 帳號已與其他登入信箱綁定 ', 4000, ['email' => '無法綁定，此 Facebook 帳號已與其他登入信箱綁定']);
                            break;
                        case 'line':
                            $this->setException('無法綁定，此 Line 帳號已與其他登入信箱綁定 ', 4000, ['email' => '無法綁定，此 Line 帳號已與其他登入信箱綁定']);
                            break;
                        case 'google':
                            $this->setException('無法綁定，此 Google 帳號已與其他登入信箱綁定 ', 4000, ['email' => '無法綁定，此 Google 帳號已與其他登入信箱綁定']);
                            break;
                    }
                }
            }

            // 檢查輸入的信箱是否已經有綁定過別的第三方帳號
            $third_column = $request['type'].'_id';
            if ($user = $this->user->where(['email' => $request['email']])->first()) {
                if ($user->$third_column && $user->$third_column != $request['third_id']) {
                    switch ($request['type']) {
                        case 'fb':
                            $this->setException('無法綁定，此信箱已與其他 Facebook 帳號綁定 ', 4000, ['email' => '無法綁定，此信箱已與其他 Facebook 帳號綁定']);
                            break;
                        case 'line':
                            $this->setException('無法綁定，此信箱已與其他 Line 帳號綁定 ', 4000, ['email' => '無法綁定，此信箱已與其他 Line 帳號綁定']);
                            break;
                        case 'google':
                            $this->setException('無法綁定，此信箱已與其他 Google 帳號綁定 ', 4000, ['email' => '無法綁定，此信箱已與其他 Google 帳號綁定']);
                            break;
                    }
                }
            }
        }
    }

    /**
     * 寄信
     *
     * @param  UserEmailAuthKey $authKey
     * @return void
     */
    public function sendMail(UserEmailAuthKey $authKey)
    {
        $params = [
            'address' => $authKey->user->email,
            'name'    => $authKey->user->name,
            'subject' => $this->campaignTitle,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $authKey->user->id
            ]
        ];

        $data = [
            'authKey' => $authKey,
        ];

        $this->mailService->send($params, 'emails.auth.email_auth_key', $data);
    }

    /**
     * 會員註冊 - 信箱驗證碼
     *
     * @param  EmailLegalize $authKey
     * @return void
     */
    public function sendAuthMail(EmailLegalize $authKey, $type)
    {
        if ($type == 'store_user_email') {
            $targetType = 'store_user';
        } elseif($type == 'store_notify_email') {
            $targetType = 'store';
        } else {
            $targetType = 'user';
        }
        if ($type == 'user_notify_email' || $type == 'store_notify_email'){
            $subject = '好婚市集 - 系統通知信箱驗證碼';
        } else {
            $subject = '好婚市集 - 登入信箱驗證碼';
        }

        $params = [
            'address' => $authKey->email,
            'name'    => $authKey->recipient,
            'subject' => $subject,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => $targetType,
                'target_id'   => null,
            ]
        ];
        $data = [
            'authKey' => $authKey,
        ];

        $this->mailService->send($params, 'emails.auth.email_auth_key_v2', $data);
    }

    /**
     * 後台設定已停用 新人 or 商家user 帳號重新啟用時，寄信通知
     *
     * @return void
     */
    public function sendReEnableMail($user, $type)
    {
        preg_match('/^(.*?)@/', $user->email, $matches);
        $mailName = $matches[1] ?? '';
        $recipient = null;
        if ($type == 'storeUser') {
            $recipient = StoreUser::where('id', $user->id)->with('liveStores')->orderBy('id', 'desc')->first();
            if (count($recipient->liveStores)) {
                $recipient = $recipient->liveStores->first()->name;
            }
        } else {
            $recipient = isset($user->name) ? $user->name : $mailName;
        }
        $recipient = $recipient ?: $mailName;
        $subject = $type == 'storeUser' ? '你的商家帳號已成功恢復使用｜歡迎回來！' : '你的帳號已成功恢復使用｜歡迎回來！';

        $params = [
            'address' => $user->email,
            'name'    => $recipient,
            'subject' => $subject,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => null,
            ]
        ];

        $data = [
            'user' => $user,
            'recipient' => $recipient,
            'type' => $type,
        ];

        $this->mailService->send($params, 'emails.auth.re_enable_user', $data);
    }

    /**
     * 驗證是否信箱是否已被其他使用者使用
     *
     * @param string $email
     * @param string $type
     * @param int $id
     * @return void
     */
    public function validateUniqueEmail($type, $email, $id){
        if ($type == 'store_user') {
            $storeUser = $this->storeUser->where(['email' => $email])->first();
            if ($storeUser && $storeUser->id != $id) {
                $this->setException('此信箱已註冊過，請更換信箱 ', 4000, ['email' => '此信箱已註冊過，請更換信箱']);
            }
        } elseif ($type == 'user') {
            $user = $this->user->where(['email' => $email])->first();
            if ($user && $user->id != $id) {
                $this->setException('此信箱已註冊過，請更換信箱 ', 4000, ['email' => '此信箱已註冊過，請更換信箱']);
            }
        }
    }
}
