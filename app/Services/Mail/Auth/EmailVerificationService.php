<?php

namespace App\Services\Mail\Auth;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use Illuminate\Support\Str;

class EmailVerificationService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_email_verification';
        $this->campaignTargetID = null;
        $this->campaignTitle = '好婚市集信箱驗證';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @return void
     */
    public function sendMail(User $user)
    {
        // 是否有驗證碼
        if (!$user->email_verified_code) {
            $user->email_verified_code = Str::random(45);
            $user->save();
        }

        $verified_url = config('params.wdv3.www_url').'/email/verified/'.$user->email_verified_code;

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'         => $user,
            'title'        => $this->campaignTitle,
            'verified_url' => $verified_url,
            'button'       => [
                'text' => '點我驗證信箱',
                'link' => $verified_url,
            ],
        ];

        $this->mailService->send($params, 'emails.auth.verification', $data);
    }
}
