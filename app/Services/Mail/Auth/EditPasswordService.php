<?php

namespace App\Services\Mail\Auth;

use App\Services\Mail\AbsSendMail;

class EditPasswordService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_edit_password';
        $this->campaignTargetID = null;
        $this->campaignTitle = '好婚市集 - 修改密碼信';
    }

    /**
     * 寄信 （會員user/商家storeUser 共用）
     *
     * @param  $pwd_token
     * @return void
     */
    public function sendMail($model, $pwd_token, $type = 'user')
    {
        $urlPath = $type == 'user' ? '/user/pwd/' : '/admin/pwd/';
        $edit_password_url = config('params.wdv3.www_url') . $urlPath . $pwd_token;

        $params = [
            'address'      => $model->email,
            'name'         => $model->name,
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => $type,
                'target_id'   => $model->id
            ]
        ];
        $data = [
            'user'              => $model,
            'title'             => $this->campaignTitle,
            'edit_password_url' => $edit_password_url,
            'button'            => [
                'text' => '點我修改密碼',
                'link' => $edit_password_url,
            ],
        ];

        $this->mailService->send($params, 'emails.auth.edit_password', $data);
    }
}
