<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\Wdv2\UserQuote;

class NewQuoteUserService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_new_quote_user';
        $this->campaignTitle = '你已經成功送出詢價囉！';
    }

    /**
     * 寄信
     *
     * @param  UserQuote $userQuote
     * @return void
     */
    public function sendMail(UserQuote $userQuote)
    {
        $this->campaignTargetID = $userQuote->id; //set campaign properties see AbsSendMail

        $params = [
            'address' => $userQuote->user->email,
            'name'    => $userQuote->user->name,
            'subject' => '你已經成功送出尋找'.date('Y/m/d', strtotime($userQuote->date)).'可服務的'.$userQuote->present()->store_type_name.'詢價單囉！',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $userQuote->user_id
            ]
        ];

        $data = [
            'userQuote' => $userQuote,
            'button'    => [
                'text' => '回我的詢價紀錄',
                'link' => config('params.wdv3.user_url').'/quote',
            ],
        ];

        $this->mailService->send($params, 'emails.user.new_quote_user', $data);
    }
}
