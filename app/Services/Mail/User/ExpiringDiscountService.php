<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\Store;

class ExpiringDiscountService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_expiring_discount';
        $this->campaignTargetID = null;
        $this->campaignTitle = '【倒數10天】詢問就有的優惠，你領取了嗎？';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  $recipient
     * @param  $total
     * @return void
     */
    public function sendMail(User $user, Store $store)
    {
        // 商家的第一個優惠
        $firstDiscount = $store->discounts->first();

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => '【倒數10天】詢問就『'.$firstDiscount->title.'』的優惠，你使用了嗎？',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'user'   => $user,
            'store'  => $store,
            'button' => [
                'text' => '點我立即線上詢問',
                'link' => config('params.wdv3.user_url').'/message?store_id='.$store->id,
            ],
        ];

        $this->mailService->send($params, 'emails.user.expiring_discount', $data);
    }
}
