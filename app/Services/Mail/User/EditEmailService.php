<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\User;

class EditEmailService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_edit_email';
        $this->campaignTargetID = null;
        $this->campaignTitle = '好婚市集變更信箱-驗證碼';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  $recipient
     * @param  $total
     * @return void
     */
    public function sendMail(User $user, $recipient, $token)
    {
        $data = [
            'content' => '好婚市集變更信箱-驗證碼：'.$token,
        ];

        $params = [
            'address' => $recipient,
            'name'    => $user->name,
            'subject' => $data['content'],
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $this->mailService->send($params, 'emails.user.edit_email', $data);
    }
}
