<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Services\Slack\IncomingWebhookService;
use App\Models\Feedback;

class FeedbackService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'feedback';
        $this->campaignTargetID = null;
        $this->campaignTitle = '[意見回饋] 新增一筆意見回饋/聯絡我們';

        // 實例化 IncomingWebhookService
        $this->incomingWebhookService = resolve(IncomingWebhookService::class);
    }

    /**
     * 寄信
     *
     * @param  Feedback $feedback
     * @return void
     */
    public function sendMail(Feedback $feedback)
    {
        $identity = $feedback->identityList[$feedback->identity];
        $name     = $feedback->present()->user_name;
        $userId   = $feedback->user ? '('.$feedback->user_id.')' : '';

        $params = [
            // 意見回饋寄信給service群組信
            'address'      => env('MAIL_FROM_ADDRESS'),
            'name'         => env('MAIL_FROM_NAME'),
            'from_address' => config('params.mail.feedback.address'),
            'from_name'    => config('params.mail.feedback.name'),
            'subject'      => '[意見回饋] '.$identity.$name.$userId.'新增一筆意見回饋/聯絡我們',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'service',
                'target_id'   => null
            ]
        ];

        $data = [
            'feedback' => $feedback,
            'button'   => [
                'text' => '回神之後台-意見回饋管理',
                'link' => config('params.wdv3.yzcube_url').'/#/www/feedback/list',
            ],
        ];

        $this->mailService->send($params, 'emails.user.feedback', $data);

        // Slack 發佈訊息
        $this->incomingWebhookService->sendMessage($this->campaignTitle, [
            [
                'color'  => 'good',
                'fields' => [
                    [
                        'title' => $identity.'-'.$name.' '.$userId,
                        'value' => $feedback->content."\n".'<'.$data['button']['link'].'|'.$data['button']['text'].'>',
                    ],
                ],
            ],
        ]);
    }
}
