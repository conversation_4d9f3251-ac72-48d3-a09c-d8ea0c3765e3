<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\Wdv2\UserQuote;

class NewQuoteYzcubeService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_new_quote_yzcube';
        $this->campaignTitle = '【公開詢價通知】新人正在找xxx for yzcube';
    }

    /**
     * 寄信
     *
     * @param  User $user
     * @param  UserQuote $userQuote
     * @param  $total
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail(User $user, UserQuote $userQuote, $total, $sqsArgs)
    {
        $this->campaignTargetID = $userQuote->id; //set campaign properties see AbsSendMail

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => '('.$total.'封, '.now()->format('H:i').')【公開詢價通知】新人'.$userQuote->user->name.'('.$userQuote->user_id.')正在找'.date('Y/m/d', strtotime($userQuote->date)).'的'.$userQuote->present()->store_type_name,
            'sqsArgs' => $sqsArgs,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'userQuote'  => $userQuote,
            'button'     => [
                'text' => '回神之後台-公開詢價單內容',
                'link' => config('params.wdv3.yzcube_url').'/#/quote/list/content/'.$userQuote->id,
            ],
        ];

        $this->mailService->send($params, 'emails.user.new_quote_yzcube', $data);
    }
}
