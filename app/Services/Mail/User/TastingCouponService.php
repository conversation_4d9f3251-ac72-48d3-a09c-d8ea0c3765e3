<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\CouponMultiple;

class TastingCouponService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_tasting_coupon';
        $this->campaignTargetID = null;
        $this->campaignTitle = '試吃優惠代碼';
    }

    /**
     * 寄信
     *
     * @param  User $user
     * @param  CouponMultiple $couponMultiple
     * @return void
     */
    public function sendMail(User $user, CouponMultiple $couponMultiple)
    {
        // 優惠代碼
        $coupon = $couponMultiple->coupon->code.$couponMultiple->code;

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => '恭喜獲得喜餅試吃禮盒百元折扣優惠碼『'.$coupon.'』，您使用了嗎？',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'user'   => $user,
            'coupon' => $coupon,
            'button' => [
                'info' => '* 優惠碼期限到 2024/12/31，記得盡快使用，免得忘記過期唷！',
                'text' => '前往喜餅宅配禮盒',
                'link' => config('params.wdv3.www_url').'/store-weddingcake/tasting',
            ],
        ];

        $this->mailService->send($params, 'emails.user.tasting_coupon', $data);
    }
}
