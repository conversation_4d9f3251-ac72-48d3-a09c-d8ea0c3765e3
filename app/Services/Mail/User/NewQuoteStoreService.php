<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\Store;
use App\Models\Wdv2\UserQuote;

class NewQuoteStoreService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'user_new_quote_store';
        $this->campaignTitle = '【公開詢價通知】新人正在找xxx for store';
    }

    /**
     * 寄信
     *
     * @param  Store $store
     * @param  UserQuote $userQuote
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail(Store $store, UserQuote $userQuote, $sqsArgs)
    {
        $this->campaignTargetID = $userQuote->id; //set campaign properties see AbsSendMail

        $params = [
            'address' => $store->email,
            'name'    => $store->name,
            'subject' => '【公開詢價通知】新人'.$userQuote->user->name.'正在找'.date('Y/m/d', strtotime($userQuote->date)).'的'.$userQuote->present()->store_type_name,
            'sqsArgs' => $sqsArgs,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'store',
                'target_id'   => $store->id
            ]
        ];

        // UTM 設置
        $utmLink = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=store_quote&utm_campaign=store_backside_quote';

        $data = [
            'store_name' => $store->name,
            'userQuote'  => $userQuote,
            'button'     => [
                'text' => '點這裡去報價',
                'link' => config('params.wdv2.admin_url').'/quote/'.$userQuote->id.'/'.$store->id.$utmLink,
            ],
        ];

        $this->mailService->send($params, 'emails.user.new_quote_store', $data);
    }
}
