<?php

namespace App\Services\Mail\User;

use App\Services\Mail\AbsSendMail;
use App\Models\FeedbackReply;

class FeedbackReplyService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'feedback_reply';
        $this->campaignTargetID = null;
        $this->campaignTitle = '問題 & 意見回覆通知 - WeddingDay好婚市集';
    }

    /**
     * 寄信
     *
     * @param  FeedbackReply $reply
     * @return void
     */
    public function sendMail(FeedbackReply $reply)
    {
        $params = [
            'address'      => $reply->feedback->present()->user_email,
            'name'         => $reply->feedback->present()->user_name,
            'from_address' => config('params.mail.feedback.address'),
            'from_name'    => config('params.mail.feedback.name'),
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => $reply->feedback->identity,
                'target_id'   => $reply->feedback->user_id,
            ]
        ];

        $data = [
            'reply'     => $reply,
            'can_reply' => true,
            'banner'    => [
                'text'  => 'WeddingDay好婚市集-問題與意見回覆',
                'image' => config('params.file_url').'/wedding-icon/email/feedback_191217.png',
            ],

            // TODO: 等婚禮中心的收信匣完成
            // 'button'    => [
            //     'info' => '如欲回覆訊息，請點選「立即回覆」並登入帳號',
            //     'text' => '立即回覆',
            //     'link' => config('params.wdv3.user_url'),
            // ],
        ];

        $this->mailService->send($params, 'emails.user.feedback_reply', $data);
    }
}
