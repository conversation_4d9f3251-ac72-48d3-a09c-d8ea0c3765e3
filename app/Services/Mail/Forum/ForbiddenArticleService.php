<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumArticle as Article;

class ForbiddenArticleService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_forbidden_article';
        $this->campaignTitle = '[禁言文章] 需審核一篇關於好婚聊聊文章';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Article  $article
     * @param  $forbiddenText
     * @return void
     */
    public function sendMail(User $user, Article $article, $forbiddenText)
    {
        $this->campaignTargetID = $article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => '[禁言文章] 需審核一篇關於「'.$forbiddenText.'」的好婚聊聊文章',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'       => $user,
            'article'    => $article,
            'forum_link' => config('params.wdv3.forum_url'),
            'button'  => [
                'text' => '回神之後台-好婚聊聊-文章內容',
                'link' => config('params.wdv3.yzcube_url').'/#/forum/article/content/'.$article->id,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.new_article', $data);
    }
}
