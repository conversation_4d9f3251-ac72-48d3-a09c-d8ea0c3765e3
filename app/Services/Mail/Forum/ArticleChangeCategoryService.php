<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumArticle as Article;
use App\Models\ForumCategory as Category;

class ArticleChangeCategoryService extends AbsSendMail
{
    /** @var Category  */
    private $category;

    /**
     * __construct
     * @param Category $category
     */
    public function __construct(Category $category)
    {
        parent::__construct();
        $this->category = $category;
        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_article_change_category';
        $this->campaignTitle = '[好婚聊聊] 您的發文分類已被更改囉！';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Article  $article
     * @param  $oldCategoryId
     * @return void
     */
    public function sendMail(User $user, Article $article, $oldCategoryId)
    {
        $this->campaignTargetID = $article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'        => $user,
            'article'     => $article,
            'oldCategory' => $this->category->find($oldCategoryId),
        ];

        $this->mailService->send($params, 'emails.forum.article_change_category', $data);
    }
}
