<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\ForumArticle as Article;
use Illuminate\Support\Str;

class LikeArticleService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_like_article';
        $this->campaignTitle = '有人對您的文章按讚囉';
    }

    /**
     * 寄信
     *
     * @param Article $article
     * @return void
     */
    public function sendMail(Article $article)
    {
        $this->campaignTargetID = $article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $article->author->email,
            'name'         => $article->author->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $article->author->name . '～有人對您的文章💗' . Str::limit($article->title, 20) . '💗按讚囉！',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $article->user_id
            ]
        ];
        $data = [
            'user'    => $article->author,
            'article' => $article,
            'button'  => [
                'text' => '點我看文章',
                'link' => $article->present()->frontend_url,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.like_article', $data);
    }
}
