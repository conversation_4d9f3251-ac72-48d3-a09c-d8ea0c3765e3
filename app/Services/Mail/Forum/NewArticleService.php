<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Models\User;
use App\Models\ForumArticle as Article;
use Illuminate\Support\Str;

class NewArticleService extends AbsSendMail
{
    /** trait */
    use ConvertPasswordTrait;

    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_new_article';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Article  $article
     * @param  $total
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail(User $user, Article $article, $total = NULL, $sqsArgs)
    {
        //set campaign properties see AbsSendMail
        $this->campaignTitle = $this->getSubject($article, $total);
        $this->campaignTargetID = $article->id;

        $forumLink      = config('params.wdv3.forum_url');
        $forumLink      .= env('APP_DEBUG') ? '' : '?utm_source=automail&utm_campaign=discussion_cnt&utm_content=forum_index';
        $buttonLink     = $article->present()->frontend_url;
        $buttonLink     .= env('APP_DEBUG') ? '' : '?utm_source=automail&utm_campaign=discussion_button&utm_content=article_'.$article->id;
        $unsubscribeUrl = config('params.wdv3.www_url').'/unsubscribe/user/forum_new_article/'.$this->feEncode($user->email);

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $this->campaignTitle,
            'sqsArgs'      => $sqsArgs,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'user'       => $user,
            'article'    => $article,
            'forum_link' => $forumLink,
            'button'     => [
                'text' => '點我看文章',
                'link' => $buttonLink,
            ],
            'unsubscribe' => [
                'item' => '好婚聊聊最新文章',
                'url'  => $unsubscribeUrl,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.new_article', $data);
    }

    private function getSubject($article, $total)
    {
        $subject = $total ? '('.$total.'封, '.now()->format('H:i').') ' : '';
        $article_title = Str::limit($article->title, 30);

        switch ($article->category_id) {

            // 發問
            case 2:
                $subject .= '新娘想問『'.$article_title.'』，來看看吧！';
                break;

            // 心情
            case 3:
                $subject .= '婚禮心情故事『'.$article_title.'』，來看看吧！';
                break;

            // 交易
            case 4:
                $subject .= '新娘想交易『'.$article_title.'』，你有興趣嗎？';
                break;

            // 避雷
            case 6:
                $subject .= '婚禮別踩雷！新娘說『'.$article_title.'』！';
                break;

            // 活動
            case 8:
                $subject .= '新娘專屬活動『'.$article_title.'』，來參加吧！';
                break;

            // 儀式
            case 9:
                $subject .= '婚禮流程怎麼安排？『'.$article_title.'』等你來分享！';
                break;

            // DIY
            case 10:
                $subject .= '婚禮手作分享『'.$article_title.'』，來看看吧！';
                break;

            // 揪團
            case 11:
                $subject .= '新娘想揪團『'.$article_title.'』，你要 +1 嗎？';
                break;

            default:
                $subject .= '新娘發了一篇新文章，來看看吧！';
                break;
        }

        return $subject;
    }
}
