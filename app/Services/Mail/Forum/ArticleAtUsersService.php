<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumArticle as Article;

class ArticleAtUsersService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_article_at_users';
        $this->campaignTitle = '有人在文章中提到您';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Article  $article
     * @return void
     */
    public function sendMail(User $user, Article $article)
    {
        $this->campaignTargetID = $article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $user->name.' 有人在文章中提到您',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'    => $user,
            'article' => $article,
            'button'  => [
                'text' => '點我看文章',
                'link' => $article->present()->frontend_url,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.article_at_user', $data);
    }
}
