<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumComment as Comment;

class DeleteCommentService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_delete_comment';
        $this->campaignTitle = '[好婚聊聊] 您的留言因違反板規已被永久刪除囉！';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Comment  $comment
     * @return void
     */
    public function sendMail(User $user, Comment $comment)
    {
        $this->campaignTargetID = $comment->article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'    => $user,
            'comment' => $comment,
        ];

        $this->mailService->send($params, 'emails.forum.delete_comment', $data);
    }
}
