<?php

namespace App\Services\Mail\Forum\NewComment;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumComment as Comment;
use Illuminate\Support\Str;

class ArticleAuthorByCommentService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_new_comment_article_author_by_comment';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Comment  $comment
     * @return void
     */
    public function sendMail(User $user, Comment $comment)
    {
        //set campaign properties see AbsSendMail
        $this->campaignTitle = '有人在您的文章💗'.Str::limit($comment->article->title, 20).'💗留言給您';
        $this->campaignTargetID = $comment->article->id;

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'    => $user,
            'comment' => $comment,
            'button'  => [
                'text' => '點我看留言',
                'link' => $comment->present()->frontend_url,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.new_comment.article_author_by_comment', $data);
    }
}
