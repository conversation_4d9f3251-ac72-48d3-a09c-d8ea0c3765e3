<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumArticle as Article;

class DeleteArticleService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_delete_article';
        $this->campaignTitle = '[好婚聊聊] 您的發文因違反板規已被永久刪除囉！';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Article  $article
     * @return void
     */
    public function sendMail(User $user, Article $article)
    {
        $this->campaignTargetID = $article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'    => $user,
            'article' => $article,
        ];

        $this->mailService->send($params, 'emails.forum.delete_article', $data);
    }
}
