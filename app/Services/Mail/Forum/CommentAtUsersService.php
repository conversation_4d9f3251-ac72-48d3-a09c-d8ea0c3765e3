<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\ForumComment as Comment;

class CommentAtUsersService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_comment_at_users';
        $this->campaignTitle = '有人在留言中提到您';
    }

    /**
     * 寄信
     *
     * @param  User  $user
     * @param  Comment  $comment
     * @return void
     */
    public function sendMail(User $user, Comment $comment)
    {
        $this->campaignTargetID = $comment->article->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $user->email,
            'name'         => $user->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $user->name.' 有人在留言中提到您',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];
        $data = [
            'user'    => $user,
            'comment' => $comment,
            'button'  => [
                'text' => '點我看留言',
                'link' => $comment->present()->frontend_url,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.comment_at_user', $data);
    }
}
