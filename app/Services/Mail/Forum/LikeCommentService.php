<?php

namespace App\Services\Mail\Forum;

use App\Services\Mail\AbsSendMail;
use App\Models\ForumComment as Comment;

class LikeCommentService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'forum_like_comment';
        $this->campaignTitle = '有人對您的留言按讚囉';
    }

    /**
     * 寄信
     *
     * @param  Comment  $comment
     * @return void
     */
    public function sendMail(Comment $comment)
    {
        $this->campaignTargetID = $comment->article_id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $comment->author->email,
            'name'         => $comment->author->name,
            'from_address' => config('params.mail.forum.address'),
            'from_name'    => config('params.mail.forum.name'),
            'subject'      => $comment->author->name.'～有人對您的留言💗'.$comment->present()->summary_text(20).'💗按讚囉！',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $comment->user_id
            ]
        ];
        $data = [
            'user'    => $comment->author,
            'comment' => $comment,
            'button'  => [
                'text' => '點我看留言',
                'link' => $comment->present()->frontend_url,
            ],
        ];

        $this->mailService->send($params, 'emails.forum.like_comment', $data);
    }
}
