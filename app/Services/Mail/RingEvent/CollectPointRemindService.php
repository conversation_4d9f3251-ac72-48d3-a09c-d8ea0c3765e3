<?php

namespace App\Services\Mail\RingEvent;

use App\Services\Mail\AbsSendMail;
use App\Models\RingUser;

class CollectPointRemindService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'ring_event_collect_point_remind';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '2022婚戒大賞 賞鑽集點拿好禮！';
    }

    /**
     * 寄信
     *
     * @param  RingUser $ringUser
     * @return void
     */
    public function sendMail(RingUser $ringUser)
    {
        // 累積點數
        $this->campaignType = 'ring_event_collect_point_remind_'.$ringUser->collect_count;
        if ($ringUser->collect_count <= 2) {
            $this->campaignTitle = '[重要通知] 請領取您的專屬頂級SPA 婚前保養課程... 婚戒大賞2022賞鑽集點拿好禮！';
            $view = 'collect_point_remind_3';
        } else {
            $this->campaignTitle = '立即領取BOBBI BROWN 人氣NO.1淨妝油＆免費試妝保養課程... 婚戒大賞 2022賞鑽集點拿好禮！';
            $view = 'collect_point_remind_5';
        }

        $params = [
            'address' => $ringUser->email,
            'name'    => $ringUser->name,
            'subject' => $this->campaignTitle,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $ringUser->user_id
            ]
        ];

        $data = [
            'ringUser' => $ringUser,
            'button'   => [
                'info' => '立即出發集點GO！',
                'text' => '前往預約',
                'link' => env('APP_DEBUG') ? config('params.wdv3.www_url').'/event/ring2022#booking' : 'https://lihi.weddingday.com.tw/XZ4YQ/edm_'.$ringUser->collect_count,
            ],
        ];

        $this->mailService->send($params, 'emails.ring_event.'.$view, $data);
    }
}
