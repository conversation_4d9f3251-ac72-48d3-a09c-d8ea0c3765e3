<?php

namespace App\Services\Mail\RingEvent;

use App\Services\Mail\AbsSendMail;
use App\Models\RingUser;

class CollectPointService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'ring_event_collect_point';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '【婚戒大賞- 集點好禮】';
    }

    /**
     * 寄信
     *
     * @param  RingUser $ringUser
     * @return void
     */
    public function sendMail(RingUser $ringUser)
    {
        // 累積點數
        $this->campaignType = 'ring_event_collect_point_'.$ringUser->collect_count;
        if ($ringUser->collect_count == 1) {
            $this->campaignTitle = '【婚戒大賞- 1點好禮】超實用婚禮表格，立即領取讓你的婚禮規劃事半功倍！';
        } elseif ($ringUser->collect_count == 3) {
            $this->campaignTitle = '【婚戒大賞- 3點好禮】頂級SPA折抵券，補給妳的美麗能量';
        } elseif ($ringUser->collect_count == 5) {
            $this->campaignTitle = '【婚戒大賞- 5點好禮】恭喜成功解鎖2022集點任務！立即領取VIP好禮 >>';
        } else {
            return false;
        }

        $params = [
            'address' => $ringUser->email,
            'name'    => $ringUser->name,
            'subject' => $this->campaignTitle,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $ringUser->user_id
            ]
        ];

        $data = [
            'ringUser' => $ringUser,
            'button'   => [
                'text' => '前往活動主頁',
                'link' => env('APP_DEBUG') ? config('params.wdv3.www_url').'/event/ring2022' : 'https://lihi.weddingday.com.tw/AZXyw/edm_'.$ringUser->collect_count.'point',
            ],
        ];

        $this->mailService->send($params, 'emails.ring_event.collect_point_'.$ringUser->collect_count, $data);
    }
}
