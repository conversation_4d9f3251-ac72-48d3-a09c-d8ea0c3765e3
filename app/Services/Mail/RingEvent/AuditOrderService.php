<?php

namespace App\Services\Mail\RingEvent;

use App\Services\Mail\AbsSendMail;
use App\Models\RingOrder;

class AuditOrderService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'ring_event_audit_order';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '【婚戒大賞-訂單審核通知】';
    }

    /**
     * 寄信
     *
     * @param  RingOrder $ringOrder
     * @return void
     */
    public function sendMail(RingOrder $ringOrder)
    {
        // 累積點數
        if ($ringOrder->status == 'approved') {
            $this->campaignTitle = '【婚戒大賞-審核成功】恭喜解鎖集點任務，超多好禮送給你！';
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event' : 'https://lihi.weddingday.com.tw/3UzeT/edm_orderpass';
        } else {
            $this->campaignTitle = '【婚戒大賞-訂單審核失敗】立即查看以確保您的權益';
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event#order' : 'https://lihi.weddingday.com.tw/ZLU3I/edm_orderfail';
        }

        $params = [
            'address' => $ringOrder->ringUser->email,
            'name'    => $ringOrder->ringUser->name,
            'subject' => $this->campaignTitle,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $ringOrder->ringUser->user_id
            ]
        ];

        $data = [
            'ringUser' => $ringOrder->ringUser,
            'button'   => [
                'text' => ($ringOrder->status == 'approved') ? '前往集點卡' : '查看訂單',
                'link' => $link,
            ],
        ];

        $this->mailService->send($params, 'emails.ring_event.audit_order_'.$ringOrder->status, $data);
    }
}
