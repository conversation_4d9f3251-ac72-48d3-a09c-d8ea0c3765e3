<?php

namespace App\Services\Mail\RingEvent;

use App\Services\Mail\AbsSendMail;
use App\Models\RingUser;

class AddReserveService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'ring_event_add_reserve';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '【婚戒大賞-預約成功】您預約的品牌有...';
    }

    /**
     * 寄信
     *
     * @param  RingUser $ringUser
     * @return void
     */
    public function sendMail(RingUser $ringUser)
    {
        $params = [
            'address' => $ringUser->email,
            'name'    => $ringUser->name,
            'subject' => $this->campaignTitle,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $ringUser->user_id
            ]
        ];

        // 按鈕連結
        if ($ringUser->user_id) {
            $buttonLink = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event' : 'https://lihi.weddingday.com.tw/3UzeT/edm_booking';
        } else {
            $buttonLink = config('params.wdv3.www_url').'/event/ring2022/'.$ringUser->auth_token.'?is_mail=1';
        }

        $data = [
            'ringUser' => $ringUser,
            'button'   => [
                'info' => $ringUser->user_id ? '' : '若您還未解鎖集點卡，請點擊下方按鈕，進行開卡喔！',
                'text' => $ringUser->user_id ? '前往集點' : '開啟集點卡',
                'link' => $buttonLink,
            ],
        ];

        $this->mailService->send($params, 'emails.ring_event.add_reserve', $data);
    }
}
