<?php

namespace App\Services\Mail\Event;

use App\Services\Mail\AbsSendMail;
use App\Models\Event;
use App\Models\EventReport;
use App\Services\Line\LineService;
use App\Repositories\EventStoreRepository;

class CompletedService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'event_completed';
    }

    /**
     * 寄信
     *
     * @param Event $event
     * @return void
     */
    public function sendMail(Event $event, EventReport $report)
    {
        //set campaign properties see AbsSendMail
        $this->campaignTitle = ($event->use_payment ? '【訂購完成】' : '【報名完成】') . $event->title;
        $this->campaignTargetID = $event->id;

        $contentHtml = '';
        $columns = json_decode($event->columns);
        $content = json_decode($report->content);

        foreach ($columns as $column) {

            // 排除已刪除的欄位
            if (!$column->status) {
                continue;
            }

            // 排除非表單欄位
            if (in_array($column->format, ['subheading', 'info', 'image', 'product', 'payment'])) {
                continue;
            }

            // 大標題
            if ($column->format == 'heading') {
                $contentHtml .= '<span style="color: #de3159; font-size: 12px; line-height: 18px;">' . $column->name . '</span><br />';
                continue;
            }

            // 上傳檔案
            if ($column->format == 'file') {
                $contentHtml .= $column->name . '：';
                if (isset($content->{$column->key}) && $content->{$column->key}) {
                    $filePath = env('APP_DEBUG') ? '/wdv3-dev/event/' . $event->path . '/' : '/wdv3/event/' . $event->path . '/';
                    $fileName = config('params.file_url') . $filePath . $content->{$column->key};
                    if ($column->file_limit == 'image') {
                        $contentHtml .= '<br /><img src="' . $fileName . '" style="max-height: 100px;"/>';
                    } else {
                        $contentHtml .= '<br /><a href="' . $fileName . '" target="_blank">' . $fileName . '</a>';
                    }
                }
                $contentHtml .= '<br />';
                continue;
            }

            // 單一項目，或回傳值不為陣列
            if (!$column->format_options || (isset($content->{$column->key}) && !is_array($content->{$column->key}))) {
                $contentHtml .= $column->name . '：' . $content->{$column->key} . '<br />';
                continue;
            }

            // 多選項目
            $contentHtml .= $column->name . '：' . '<br />';
            foreach ($column->format_options as $option) {
                $checkbox = (isset($content->{$column->key}) && in_array($option->text, $content->{$column->key})) ? '☑' : '☐';
                $contentHtml .= '<span style="font-size: 20px;">' . $checkbox . '</span> ' . $option->text . '<br />';
            }
        }

        // 社群分享連結
        $event->share_url = $event->share_url ?: config('params.wdv3.www_url') . '/event/' . $event->path . '/signup';

        $params = [
            'address' => $report->email,
            'name'    => $report->name,
            'subject' => $event->email_title ? str_replace('[title]', $event->title, $event->email_title) : $this->campaignTitle,
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $report->user_id
            ]
        ];

        $data = [
            'event'      => $event,
            'report'     => $report,
            'content'    => $contentHtml,
            'qrCode_url' => $event->use_qrcode ? config('params.wdv3.api_url') . '/event/' . $event->path . '/qrcode-image?token=' . $report->qrcode_token : NULL,
        ];

        // 訂購要有聯絡客服的按鈕
        if ($event->use_payment) {
            $data['contactBtn'] = [
                'text' => '聯絡客服',
                'link' => config('params.wdv3.www_url') . '/contacts',
            ];
        }

        // 一般活動表單與訂購選擇不同view
        $view = $event->use_payment ? 'completed_order' : 'completed';

        $this->mailService->send($params, 'emails.event.' . $view, $data);
    }

    /**
     * 表單活動 - 商店通知信
     *
     * @param string $type
     * @param string $storeEmail
     * @param array $data
     * @return void
     */
    public function sendEventMail(
        string $type,
        string $storeEmail,
        array $data
    ) {
        $messages = [
            'storeName' => $data['storeName'], // 商店名稱
            'eventName' => $data['eventName'], // 訂購者名稱
            'eventDate' => $data['eventDate'], // 訂購時間
        ];

        switch ($type) {
            case EventStoreRepository::SHOP_TASTING:
                $stringType = '新人';
                $messages['type'] = '門市試吃';
                $messages['textOne'] = '填寫';
                $messages['textTwo'] = '表單囉';
                $messages['textThree'] = '請盡快聯繫他，不要讓他等太久唷～';

                break;
            case EventStoreRepository::DELIVERY_TASTING:
                $stringType = '出貨';
                $messages['type'] = '宅配試吃';
                $messages['textOne'] = '付費購買';
                $messages['textTwo'] = '禮盒囉';
                $messages['textThree'] = '請盡快出貨，不要讓他等太久唷～';

                break;
            default:
                $stringType = '';
                $messages['type'] = '';
                $messages['textOne'] = '';
                $messages['textTwo'] = '';

                break;
        }

        $this->campaignTitle = "【通知】" . $messages['type'];
        $this->campaignTargetID = $data['storeId'] ?? '';

        $params = [
            'address' => env('APP_DEBUG') ? env('MAIL_TEST') : $storeEmail,
            'name'    => $data['storeName'],
            'subject' => "新人 {$data['eventName']} 預約 " . $messages['type'],
            'from_address' => config('params.mail.store.address'),
            'from_name'    => config('params.mail.store.name'),
            'event' => [
                'campaign_id' => $this->getCampaignID(),
                'target_type' => 'it',
                'target_id'   => null
            ]
        ];

        if (
            !empty($data['spreadsheetId']) &&
            !empty($data['sheetId'])
        ) {
            $messages['button'] = [
                'info' => "點擊下方按鈕查看{$stringType}資訊",
                'text' => '查看資訊',
                'link' => config('params.google_sheet_url') . '/d/' . $data['spreadsheetId'] . '/edit#gid=' . $data['sheetId']
            ];
        }

        $this->mailService->send($params, 'emails.event.wedding_cake', $messages);
    }

    /**
     * 表單活動 - 商店Line訊息
     *
     * @param string $type
     * @param array $devices
     * @param array $data
     * @return void
     */
    public function sendEventLine(
        string $type,
        array $devices,
        array $data
    ) {
        switch ($type) {
            case EventStoreRepository::SHOP_TASTING:
                /* 門市試吃 - 通知訊息 */
                $stringType = '新人';
                $message = '【📣預約門市試吃】' . "\n";
                $message .= "新人 {$data['eventName']} 已填寫門市試吃表單囉！\n";
                $message .= "請盡快聯繫他，不要讓他等太久唷～\n";

                break;
            case EventStoreRepository::DELIVERY_TASTING:
                /* 宅配試吃 - 通知訊息 */
                $stringType = '出貨';
                $message = '【🍪預約宅配試吃】' . "\n";
                $message .= "新人 {$data['eventName']} 已付費訂購宅配試吃禮盒囉！\n";
                $message .= "請盡快出貨，不要讓他等太久唷～\n";

                break;
            default:
                $stringType = '';
                $message = '【預約參數發生錯誤】' . "\n";

                break;
        }


        $message .= "\n";

        if (
            !empty($data['spreadsheetId']) &&
            !empty($data['sheetId'])
        ) {
            $message .= "👇點連結看{$stringType}資訊👇\n";
            $message .= config('params.google_sheet_url') . '/d/' . $data['spreadsheetId'] . '/edit#gid=' . $data['sheetId'];
        }

        (new LineService)->send($message, $devices);
    }
}
