<?php

namespace App\Services\Mail\Event;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\Event;

class EmailLoginService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'event_email_login';
    }

    /**
     * 寄信
     *
     * @param  User $user
     * @param  Event $event
     * @return void
     */
    public function sendMail(User $user, Event $event, $code)
    {
        //set campaign properties see AbsSendMail
        $this->campaignTitle = '【重要-活動登入異常】'.$event->title;
        $this->campaignTargetID = $event->id;

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => $this->campaignTitle,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'user'   => $user,
            'event'  => $event,
            'button' => [
                'text' => '點我登入活動',
                'link' => config('params.wdv3.www_url').'/event/'.$event->path.'/signup?code='.$code,
            ],
        ];

        $this->mailService->send($params, 'emails.event.login', $data);
    }
}
