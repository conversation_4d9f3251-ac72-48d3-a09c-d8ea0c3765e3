<?php

namespace App\Services\Mail\Event;

use App\Services\Mail\AbsSendMail;
use App\Models\EventReport;

class InviteRegisterService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'event_invite_register';
        $this->campaignTargetID = null;
        $this->campaignTitle = '你需要的『婚禮小工具』這裡有～～快來看看吧！';
    }

    /**
     * 寄信
     *
     * @param  EventReport $eventReport
     * @return void
     */
    public function sendMail(EventReport $eventReport)
    {
        $params = [
            'address' => $eventReport->email,
            'name'    => $eventReport->name,
            'subject' => $eventReport->name.'～你需要的『婚禮小工具』這裡有～～快來看看吧！',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $eventReport->user_id
            ]
        ];

        $data = [
            'user_name'    => $eventReport->name,
            'register_url' => env('APP_DEBUG') ? config('params.wdv3.www_url').'/register' : 'https://lihi.weddingday.com.tw/EzLOr',
        ];

        $this->mailService->send($params, 'emails.event.invite_register', $data);
    }
}
