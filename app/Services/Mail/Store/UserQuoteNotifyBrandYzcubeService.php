<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\User;
use App\Models\Store;

class UserQuoteNotifyBrandYzcubeService extends AbsSendMail
{
    private $store;

    /**
     * __construct
     * @return void
     */
    public function __construct(Store $store)
    {
        parent::__construct();

        $this->store = $store;

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'store_user_quote_notify_brand_yzcube';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '有新娘在尋找婚禮商家，快來報價給他們！ for yzcube';
    }

    /**
     * 寄信
     *
     * @param  User $user
     * @param  array $userQuotes
     * @return void
     */
    public function sendMail(User $user, $userQuotes, $brandCount, $studioCount)
    {
        $userQuoteCount = $userQuotes->count();
        $total  = $brandCount + $studioCount;

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => '('.$total.'封, '.now()->format('H:i').')有'.$userQuoteCount.'位新娘在尋找婚禮商家，快來報價給他們！',
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'brand'      => $user,
            'userQuotes' => [
                'total'    => $userQuoteCount,
                'group'    => $userQuotes->groupBy('type'),
                'link'     => config('params.wdv2.admin_url').'/quote',
                'typeList' => $this->store->quoteTypeList,
            ],
            'brandCount'  => $brandCount,
            'studioCount' => $studioCount,
            'price'       => 599,
            'button'      => [
                'text' => '回神之後台-公開詢價',
                'link' => config('params.wdv3.yzcube_url').'/#/quote/list/list',
            ],
        ];

        $this->mailService->send($params, 'emails.store.user_quote_notify_brand_yzcube', $data);
    }
}
