<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\Brand;

class SharePostPublishedService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'store_share_post_published';
        $this->campaignTitle = '新娘在『好婚聊聊 - 推薦板』寫下關於您的文章';
    }

    /**
     * 寄信
     *
     * @param  Brand $brand
     * @return void
     */
    public function sendMail(Brand $brand, $posts)
    {
        $this->campaignTargetID = $brand->id; //set campaign properties see AbsSendMail

        $total     = count($posts);
        $storeName = $brand->present()->store_name;
        $paidStore = $brand->paidStores->first();

        $params = [
            'address'      => $brand->present()->store_email,
            'name'         => $storeName,
            'from_address' => config('params.mail.store.address'),
            'from_name'    => config('params.mail.store.name'),
            'subject'      => '太棒了🎊 有 '.$total.' 位新娘在『好婚聊聊 - 推薦板』寫下關於'.$storeName.'的口碑推薦文章👍',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'brand',
                'target_id'   => $brand->id
            ]
        ];

        // UTM 設置
        $utmLink = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=wsister_newarticle&utm_campaign=store_payment_lp';

        $data = [
            'store_name' => $params['name'],
            'total'      => $total,
            'has_paid'   => (bool)$paidStore,
            'posts'      => $posts,
            'button'     => [
                'info' => '立即加入WeddingDay好婚市集的行列<br />還可以享有更多獲取新娘流量與詢問的服務唷😊',
                'text' => '了解更多',
                'link' => config('params.wdv2.admin_url').'/present'.$utmLink,
            ],
        ];

        // 若是已上架的付費商家
        if ($paidStore) {
            $params['subject'] = '太棒了🎊 在『好婚聊聊 - 推薦板』發現多了 '.$total.' 篇關於'.$storeName.'的文章👍';
            unset($data['button']);
        }

        $this->mailService->send($params, 'emails.store.share_post_published', $data);
    }
}
