<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\Wdv2\ServiceStopApply;

class ServiceStopCompletedService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType  = 'store_service_stop_completed';
        $this->campaignTitle = '【好婚市集-帳務中心】商家下架通知';
    }

    /**
     * 寄信
     *
     * @param  ServiceStopApply $apply
     * @return void
     */
    public function sendMail(ServiceStopApply $apply)
    {
        $this->campaignTargetID = $apply->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $apply->store->email,
            'name'         => $apply->store->name,
            'from_address' => config('params.mail.xiaoer.address'),
            'from_name'    => config('params.mail.xiaoer.name'),
            'subject'      => $this->campaignTitle,
            'event'        => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'store',
                'target_id'   => $apply->store->id,
            ]
        ];

        $data = [
            'store'  => $apply->store,
            'banner' => [
                'text'  => 'WeddingDay好婚市集-商家下架通知',
                'image' => config('params.file_url').'/wedding-icon/email/store_service_stop_approve_211026.png',
            ],
        ];

        $this->mailService->send($params, 'emails.store.service_stop_completed', $data);
    }
}
