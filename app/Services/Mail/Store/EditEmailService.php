<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\Store;

class EditEmailService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'store_edit_email';
        $this->campaignTargetID = null;
        $this->campaignTitle = '好婚市集變更信箱-驗證碼';
    }

    /**
     * 寄信
     *
     * @param  Store $store
     * @param  $recipient
     * @param  $total
     * @return void
     */
    public function sendMail(Store $store, $recipient, $token)
    {
        $data = [
            'content' => '好婚市集變更信箱-驗證碼：'.$token,
        ];

        $params = [
            'address' => $recipient,
            'name'    => $store->name,
            'subject' => $data['content'],
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'store',
                'target_id'   => $store->id
            ]
        ];

        $this->mailService->send($params, 'emails.store.edit_email', $data);
    }
}
