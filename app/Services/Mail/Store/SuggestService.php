<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\StoreSuggest;

class SuggestService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'store_suggest';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '[建議資訊] 新人提供一筆商家的建議資訊';
    }

    /**
     * 寄信
     *
     * @param  StoreSuggest $storeSuggest
     * @return void
     */
    public function sendMail(StoreSuggest $storeSuggest)
    {
        $params = [
            // 建議資訊寄信給service群組信
            'address'      => env('MAIL_FROM_ADDRESS'),
            'name'         => env('MAIL_FROM_NAME'),
            'from_address' => config('params.mail.feedback.address'),
            'from_name'    => config('params.mail.feedback.name'),
            'subject'      => '[建議資訊] 新人提供一筆'.$storeSuggest->store->name.'('.$storeSuggest->store_id.')的建議資訊',
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'service',
                'target_id'   => null
            ]
        ];

        $data = [
            'storeSuggest' => $storeSuggest,
            'button'       => [
                'text' => '回神之後台-商家管理',
                'link' => config('params.wdv3.yzcube_url').'/#/store/list',
            ],
        ];

        $this->mailService->send($params, 'emails.store.suggest', $data);
    }
}
