<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\User;

class UserQuoteNotifyBasicLevelYzcubeService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'store_user_quote_notify_basic_level_yzcube';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '有新娘在尋找婚禮商家，快來報價給他們！ for yzcube';
    }

    /**
     * 寄信
     *
     * @param  User $user
     * @param  array $storeData
     * @return void
     */
    public function sendMail(User $user, $storeData)
    {
        $total  = $storeData['storeCount'] + $storeData['studioCount'];
        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => '('.$total.'封, '.now()->format('H:i').')有新娘在尋找商家，快來報價給他們！',
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        $data = [
            'storeData' => $storeData,
            'button'    => [
                'text' => '回神之後台-公開詢價',
                'link' => config('params.wdv3.yzcube_url').'/#/quote/list/list',
            ],
        ];

        $this->mailService->send($params, 'emails.store.user_quote_notify_basic_level_yzcube', $data);
    }
}
