<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\MarryStudio;
use App\Models\Store;
use App\Models\Wdv2\UserQuote;
use App\Traits\Auth\ConvertPasswordTrait;

class UserQuoteNotifyMarryStudioService extends AbsSendMail
{
    private $store;
    private $userQuote;

    /** trait */
    use ConvertPasswordTrait;

    /**
     * __construct
     * @return void
     */
    public function __construct(
        Store $store,
        UserQuote $userQuote
    ) {
        parent::__construct();

        $this->store     = $store;
        $this->userQuote = $userQuote;

        //set campaign properties see AbsSendMail
        $this->campaignType  = 'store_user_quote_notify_marry_studio';
        $this->campaignTitle = '有新娘在尋找婚禮商家，快來報價給他們！';
    }

    /**
     * 寄信
     *
     * @param  int $type
     * @param  MarryStudio $studio
     * @param  UserQuote Collect $userQuotes
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail($type, MarryStudio $studio, $userQuotes, $sqsArgs)
    {
        $this->campaignTargetID = $studio->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $studio->email,
            'name'         => $studio->name,
            'from_address' => config('params.mail.xiaoer.address'),
            'from_name'    => config('params.mail.xiaoer.name'),
            'subject'      => '好婚市集今天為你帶來'.$userQuotes->count().'個成交機會唷！',
            'sqsArgs'      => $sqsArgs,
            'event'        => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'marry_studio',
                'target_id'   => $studio->id
            ]
        ];

        // 近30天的詢價量
        $recentlyCount = $this->userQuote->where('type', $type)
                                            ->where('created_at', '>=', now()->subDays(30)->startOfDay())
                                            ->count();

        // UTM 設置
        $utmLink_1 = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=marry_specificstore_quote&utm_campaign=store_login';
        $utmLink_2 = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=marry_specificstore_quote&utm_campaign=store_payment_lp';

        $data = [
            'studio'        => $studio,
            'userQuotes'    => $userQuotes,
            'userQuotelink' => config('params.wdv2.admin_url').'/quote',
            'storeTypeName' => $this->store->quoteTypeList[$type],
            'recentlyCount' => $recentlyCount,
            'price'         => $this->store->getBasicSettingFee($type),
            'button_1'      => [
                'info' => '如果您的檔期可配合的話，快透過下方連結報價給新人，提高成交的可能性吧！',
                'text' => '把握機會，立刻報價',
                'link' => config('params.wdv2.admin_url').'/login'.$utmLink_1,
            ],
            'button_2' => [
                'text' => '查看方案，加入我們',
                'link' => config('params.wdv2.admin_url').'/present'.$utmLink_2.'#pay-list',
            ],
            'unsubscribe' => [
                'item' => '公開報價',
                'url'  => config('params.wdv3.www_url').'/unsubscribe/marry_studio/user_quote/'.$this->feEncode($studio->email),
            ],
        ];

        $this->mailService->send($params, 'emails.store.user_quote_notify_marry_studio', $data);
    }
}
