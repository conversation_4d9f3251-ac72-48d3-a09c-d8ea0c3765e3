<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\Store;
use App\Models\Wdv2\UserQuote;
use App\Traits\Auth\ConvertPasswordTrait;

class UserQuoteNotifyBasicLevelService extends AbsSendMail
{
    /** trait */
    use ConvertPasswordTrait;

    private $userQuote;

    /**
     * __construct
     * @return void
     */
    public function __construct(UserQuote $userQuote)
    {
        parent::__construct();

        $this->userQuote = $userQuote;

        //set campaign properties see AbsSendMail
        $this->campaignType  = 'store_user_quote_notify_basic_level';
        $this->campaignTitle = '有新娘在尋找婚禮商家，快來報價給他們！';
    }

    /**
     * 寄信
     *
     * @param  Store $store
     * @param  UserQuote Collect $userQuotes
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail(Store $store, $userQuotes, $sqsArgs)
    {
        $this->campaignTargetID = $store->id; //set campaign properties see AbsSendMail

        $params = [
            'address'      => $store->email,
            'name'         => $store->name,
            'from_address' => config('params.mail.xiaoer.address'),
            'from_name'    => config('params.mail.xiaoer.name'),
            'subject'      => '好婚市集今天為你帶來'.$userQuotes->count().'個成交機會唷！',
            'sqsArgs'      => $sqsArgs,
            'event'        => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'store',
                'target_id'   => $store->id
            ]
        ];

        // 近30天的詢價量
        $recentlyCount = $this->userQuote->where('type', $store->type)
                                            ->where('created_at', '>=', now()->subDays(30)->startOfDay())
                                            ->count();

        // UTM 設置
        $utmLink_1 = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=store_nopay_quote&utm_campaign=store_backside_quote';
        $utmLink_2 = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=store_nopay_quote&utm_campaign=store_payment_lp';

        $data = [
            'store'         => $store,
            'userQuotes'    => $userQuotes,
            'storeTypeName' => $store->quoteTypeList[$store->type],
            'recentlyCount' => $recentlyCount,
            'price'         => $store->getBasicSettingFee($store->type),
            'button_1'      => [
                'info' => '如果您的檔期可配合的話，快透過下方連結報價給新人，提高成交的可能性吧！',
                'text' => '把握機會，立刻報價',
                'link' => config('params.wdv2.admin_url').'/'.$store->id.'/quote'.$utmLink_1,
            ],
            'button_2' => [
                'text' => '查看方案，加入我們',
                'link' => config('params.wdv2.admin_url').'/present'.$utmLink_2.'#pay-list',
            ],
            'unsubscribe' => [
                'item' => '公開報價',
                'url'  => config('params.wdv3.www_url').'/unsubscribe/store/email_quote/'.$this->feEncode($store->email),
            ],
        ];

        $this->mailService->send($params, 'emails.store.user_quote_notify_basic_level', $data);
    }
}
