<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\Brand;
use App\Models\Store;
use App\Traits\Auth\ConvertPasswordTrait;

class UserQuoteNotifyBrandService extends AbsSendMail
{
    private $store;

    /** trait */
    use ConvertPasswordTrait;

    /**
     * __construct
     * @return void
     */
    public function __construct(Store $store)
    {
        parent::__construct();

        $this->store = $store;

        //set campaign properties see AbsSendMail
        $this->campaignType  = 'store_user_quote_notify_brand';
        $this->campaignTitle = '有新娘在尋找婚禮商家，快來報價給他們！';
    }

    /**
     * 寄信
     *
     * @param  Brand $brand
     * @param  UserQuote Collect $userQuotes
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail(Brand $brand, $userQuotes, $sqsArgs)
    {
        $this->campaignTargetID = $brand->id; //set campaign properties see AbsSendMail
        $userQuoteCount = $userQuotes->count();

        $params = [
            'address'      => $brand->email,
            'name'         => $brand->name,
            'from_address' => config('params.mail.xiaoer.address'),
            'from_name'    => config('params.mail.xiaoer.name'),
            'subject'      => '有'.$userQuoteCount.'位新娘在尋找婚禮商家，快來報價給他們！',
            'sqsArgs'      => $sqsArgs,
            'event'        => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'brand',
                'target_id'   => $brand->id
            ]
        ];

        // UTM 設置
        $utmLink = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=store_brand_quote&utm_campaign=store_payment_lp';

        $data = [
            'brand'      => $brand,
            'userQuotes' => [
                'total'    => $userQuoteCount,
                'group'    => $userQuotes->groupBy('type'),
                'link'     => config('params.wdv2.admin_url').'/quote',
                'typeList' => $this->store->quoteTypeList,
            ],
            'price'  => 599,
            'button' => [
                'text' => '立即了解方案',
                'link' => config('params.wdv2.admin_url').'/present'.$utmLink.'#pay-list',
            ],
            'unsubscribe' => [
                'item' => '公開報價',
                'url'  => config('params.wdv3.www_url').'/unsubscribe/brand/user_quote/'.$this->feEncode($brand->email),
            ],
        ];

        $this->mailService->send($params, 'emails.store.user_quote_notify_brand', $data);
    }
}
