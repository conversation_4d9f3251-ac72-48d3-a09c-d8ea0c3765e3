<?php

namespace App\Services\Mail\Store;

use App\Services\Mail\AbsSendMail;
use App\Models\MarryStudio;
use App\Models\Store;
use App\Traits\Auth\ConvertPasswordTrait;

class UserQuoteNotifyUnknownTypeMarryStudioService extends AbsSendMail
{
    private $store;

    /** trait */
    use ConvertPasswordTrait;

    /**
     * __construct
     * @return void
     */
    public function __construct(Store $store)
    {
        parent::__construct();

        $this->store = $store;

        //set campaign properties see AbsSendMail
        $this->campaignType  = 'store_user_quote_notify_unknown_type_marry_studio';
        $this->campaignTitle = '有新娘在尋找婚禮商家，快來報價給他們！';
    }

    /**
     * 寄信
     *
     * @param  MarryStudio $studio
     * @param  UserQuote Collect $userQuotes
     * @param  $sqsArgs
     * @return void
     */
    public function sendMail(MarryStudio $studio, $userQuotes, $sqsArgs)
    {
        $this->campaignTargetID = $studio->id; //set campaign properties see AbsSendMail
        $userQuoteCount = $userQuotes->count();

        $params = [
            'address'      => $studio->email,
            'name'         => $studio->name,
            'from_address' => config('params.mail.xiaoer.address'),
            'from_name'    => config('params.mail.xiaoer.name'),
            'subject'      => '有'.$userQuoteCount.'位新娘在尋找婚禮商家，快來報價給他們！',
            'sqsArgs'      => $sqsArgs,
            'event'        => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'marry_studio',
                'target_id'   => $studio->id
            ]
        ];

        // UTM 設置
        $utmLink = env('APP_DEBUG') ? '' : '?utm_source=automail&utm_medium=marry_otherstore_quote&utm_campaign=store_payment_lp';

        $data = [
            'brand'      => $studio,
            'userQuotes' => [
                'total'    => $userQuoteCount,
                'group'    => $userQuotes->groupBy('type'),
                'link'     => config('params.wdv2.admin_url').'/quote',
                'typeList' => $this->store->quoteTypeList,
            ],
            'price'  => 599,
            'button' => [
                'text' => '立即了解方案',
                'link' => config('params.wdv2.admin_url').'/present'.$utmLink.'#pay-list',
            ],
            'unsubscribe' => [
                'item' => '公開報價',
                'url'  => config('params.wdv3.www_url').'/unsubscribe/marry_studio/user_quote/'.$this->feEncode($studio->email),
            ],
        ];

        $this->mailService->send($params, 'emails.store.user_quote_notify_brand', $data);
    }
}
