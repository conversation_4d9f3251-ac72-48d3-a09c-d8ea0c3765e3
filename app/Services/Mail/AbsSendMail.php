<?php
declare(strict_types=1);
/*------------------------------------
 | 發送信件的抽象
 |------------------------------------
 | 把共用的 properties 跟 trait 都提到這裡
 | 整理發信內容的Service都可繼承
 |
 */

namespace App\Services\Mail;

use App\Services\Mail\MailService;
use App\Traits\Mail\CampaignTrait;

abstract class AbsSendMail
{
    /**
     * trait
     */
    use CampaignTrait;

    /**
     * @var \Illuminate\Foundation\Application|mixed
     */
    protected $mailService;

    /**
     * @var string : 信件所屬的campaign type
     */
    protected $campaignType;

    /**
     * @var null | int : 信件的campaign 目標id
     * 內容是 article_id or store_id or event_id 之類的id
     * 其它的就用null
     */
    protected $campaignTargetID = null;

    /**
     * @var bool : campaign需不需要新建資料
     * true: 無條件新增資料
     * false: 以 $campaignType && $campaignTargetID 當 where 做 firstOrCreate. see CampaignTrait
     */
    protected $campaignCreate = false;

    /**
     * @var string : campaign title
     */
    protected $campaignTitle = '';

    /**
     * @return void
     */
    public function __construct()
    {
        //實例化MailService
        $this->mailService = resolve(MailService::class);
    }
}
