<?php
/**
 *--------------------------------------
 *  Mail Service
 *--------------------------------------
 * @param array $params : 必填欄位 (*)
 *  [
 *      'address'      => '收件地址 (*)',
 *      'name'         => '* 收件人 (*)',
 *      'subject'      => '* 信件標題 (*)',
 *      'from_address' => '寄件地址',
 *      'from_name'    => '寄件人',
 *      'sqsArgs'      => [
 *                          'client' => sqsClient,
 *                          'delay' => int,
 *                        ],
 *  ]
 *
 * @param string $view
 *
 * @param array $data :
 *  [
 *      'banner' => [
 *          'text'  => 'Banner標題',
 *          'image' => 'Banner圖片連結',
 *      ],
 *      'title'  => '信件內容標題',
 *      'button' => [
 *          'info' => '按鈕上方的說明文字',
 *          'text' => '按鈕文字',
 *          'link' => '按鈕連結',
 *      ],
 *      'can_reply' => true,
 *      'unsubscribe' => [
 *          'item' => '取消訂閱的項目',
 *          'url'  => '取消訂閱連結',
 *      ],
 *  ]
 *
 */

namespace App\Services\Mail;

use Illuminate\Mail\Mailer;
use App\Traits\Job\GetSqsClientTrait;
use App\Repositories\MailBaseRepository;
use App\Repositories\MailEventRepository;
use App\Services\Mail\CheckValidService;
use Log;

class MailService
{
    private $mailer;
    private $params;
    private $view;
    private $data;

    /** @var MailBaseRepository */
    private $mailBaseRepository;

    /** @var MailEventRepository */
    private $MailEventRepository;

    /** @var CheckValidService  */
    private $checkValidService;

    use GetSqsClientTrait;

    /**
     * __construct
     *
     * @param Mailer $mailer
     * @param MailBaseRepository $mailBaseRepository
     * @param MailEventRepository $MailEventRepository
     * @param CheckValidService $checkValidService
     */
    public function __construct(
        Mailer $mailer,
        MailBaseRepository $mailBaseRepository,
        MailEventRepository $MailEventRepository,
        CheckValidService $checkValidService
    )
    {
        $this->mailer = $mailer;
        $this->mailBaseRepository = $mailBaseRepository;
        $this->MailEventRepository = $MailEventRepository;
        $this->checkValidService = $checkValidService;
    }

    /**
     * 實作寄信
     * @param $params : 寄信參數
     * @param $view : 使用的view file name
     * @param $data : 顯示在view的資料
     */
    public function send($params, $view, $data)
    {
        // 判斷信件是否被阻檔..是的話就不寄了
        if ($this->checkValidService->check($params['address'])) {
            return;
        }

        // 測試環境 & 寄件者變更
        $params['address'] = env('APP_DEBUG') ? env('MAIL_TEST') : $params['address'];
        $params['subject'] = env('APP_DEBUG') ? '【' . env('APP_NAME') . '】' . $params['subject'] : $params['subject'];
        $params['from_address'] = isset($params['from_address']) ? $params['from_address'] : env('MAIL_FROM_ADDRESS');
        $params['from_name'] = isset($params['from_name']) ? $params['from_name'] : env('MAIL_FROM_NAME');

        $this->params = $params;
        $this->view = $view;
        $this->data = $data;

        //寄件前儲存郵件資料到mail base
        $this->saveMailBase();

        // 是否使用SQS Lambda 寄信
        if (env('SQS_MAIL')) {
            $this->useSqsLambda();
        } else {
            $this->useMailer();
        }
    }

    /**
     * Laravel Mailer 寄信
     *
     */
    private function useMailer()
    {
        $eventID = $this->saveMailEvents(); //mail_events id

        $this->mailer->send($this->view, $this->data, function ($message) use ($eventID) {
            $message->to($this->params['address'], $this->params['name'])
                ->subject($this->params['subject'])
                ->from($this->params['from_address'], $this->params['from_name']);

            $swiftMessage = $message->getSwiftMessage();
            $headers = $swiftMessage->getHeaders();
            $headers->addTextHeader('Event-Id', $eventID);
            $headers->addTextHeader('X-SES-CONFIGURATION-SET', 'weddingday');
        });

    }

    /**
     * SQS Lambda 寄信
     *
     */
    private function useSqsLambda()
    {
        // 載入 SQS Client
        $client = isset($this->params['sqsArgs']) ? $this->params['sqsArgs']['client'] : $this->getSqsClient();

        // MessageBody
        $messageBody = [
            'body'      => view($this->view, $this->data)->render(),
            'subject'   => $this->params['subject'],
            'from'      => $this->addressEncode($this->params['from_address'], $this->params['from_name']),
            'to'        => $this->addressEncode($this->params['address'], $this->params['name']),
            'mail_events_id' => $this->saveMailEvents(), //mail_events id
        ];

        // Send Message 參數
        $args = [
            'QueueUrl'     => env('SQS_PREFIX') . '/' . env('SQS_MAIL_QUEUE'),
            'MessageBody'  => json_encode($messageBody),
            'DelaySeconds' => isset($this->params['sqsArgs']) ? $this->params['sqsArgs']['delay'] : 0,
        ];

        // Send Message
        try {
            $result = $client->sendMessage($args);
            // var_dump($result);
        } catch (Aws\Exception\AwsException $e) {
            // output error message if fails
            error_log($e->getMessage());
        }
    }

    /**
     * SES 的郵件地址，需使用 MIME base64 編碼
     *
     */
    private function addressEncode($address, $name)
    {
        return '=?UTF-8?B?' . base64_encode($name) . '?= <' . $address . '>';
    }

    /**
     * 儲存寄件資料到mail_base
     * server收到sns通知後再完善其它資料
     */
    private function saveMailBase()
    {
        try {
            $this->mailBaseRepository->updateOrCreate(['email' => $this->params['address']], []);
        } catch (\Exception $e) {
            Log::error($e->getMessage(), [
                'class' => class_basename(get_class($this)),
            ]);
        }
    }

    /**
     * 儲存寄信通知的資料到mail_events
     * 整體流程：
     * - 使用 SQS Lambda
     * 1.寄信前先寫入campaign_id target_type target_id email..然後把資料id跟著送到lambda..
     * 2.lambda寄完信後會在DB更新ses_message_id.
     * 3.server收到sns通知後再完善其它資料
     *
     * ------------------
     *
     * - 使用 Mailer
     * 1.寄信前先寫入campaign_id target_type target_id email..然後把資料id塞在Header..
     * 2.App\Listeners\LogSentMessage會在DB更新ses_message_id
     * 3.server收到sns通知後再完善其它資料
     */
    private function saveMailEvents()
    {
        $res = $this->MailEventRepository->addData([
            'campaign_id' => $this->params['event']['campaign_id'],
            'target_id'   => $this->params['event']['target_id'],
            'target_type' => $this->params['event']['target_type'],
            'email'       => $this->params['address'],
        ]);

        return $res->id;
    }
}
