<?php

namespace App\Services\Mail\Firebase;

use App\Services\Mail\AbsSendMail;
use App\Models\Store;
use App\Models\User;
use App\Traits\Model\SummaryTrait;

class NewMessageStoreToUserService extends AbsSendMail
{
    use SummaryTrait;

    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType     = 'new_message_store_to_user';
        $this->campaignTargetID = null;
        $this->campaignTitle    = '商家傳訊息給您';
    }

    /**
     * 寄信
     *
     * @param  Store $store
     * @param  User $user
     * @param  string $message
     * @param  datetime $created_at
     * @param  int $idle_hours
     * @return void
     */
    public function sendMail(Store $store, User $user, $message, $created_at, $idle_hours = '')
    {
        // 未讀通知
        if ($idle_hours) {
            $this->campaignType  = 'unread_message_store_to_user';
            $this->campaignTitle = '商家還在等待您回覆訊息，快看看！';
        }

        // 訊息限制字數
        $message = $this->getSummaryStripTags($message, 60);

        $params = [
            'address' => $user->email,
            'name'    => $user->name,
            'subject' => $store->name.' 傳訊息給您：「'.$message.'」',
            'event'   => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'user',
                'target_id'   => $user->id
            ]
        ];

        // UTM 設置
        $utmLink = env('APP_DEBUG') ? '' : '&utm_source=automail&utm_medium=user_chat_inform&utm_campaign=user_center_chat';

        $data = [
            'from_title' => '',
            'from_name'  => $store->name,
            'to_name'    => $user->name,
            'short_msg'  => $message,
            'created_at' => date('Y-m-d H:i', strtotime($created_at)),
            'idle_hours' => $idle_hours,
            'button'     => [
                'text' => '立即回覆',
                'link' => config('params.wdv3.user_url').'/message?store_id='.$store->id.$utmLink,
            ],
        ];

        $view = $idle_hours ? 'emails.message.unread_message' : 'emails.message.new_message';
        $this->mailService->send($params, $view, $data);
    }
}
