<?php

namespace App\Services\Mail\Yzcube;

use App\Services\Mail\AbsSendMail;

class MessageNotificationService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'yzcube_message_notification';
        $this->campaignTargetID = null;
    }

    /**
     * 寄信
     *
     * @param  $title
     * @return void
     */
    public function sendMail($title, $content = '')
    {
        $params = [
            'address'      => env('MAIL_TEST'),
            'name'         => env('MAIL_FROM_NAME'),
            'subject'      => $title,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'it',
                'target_id'   => null
            ]
        ];

        $data = [
            'content' => $content ?: $title,
        ];

        $this->mailService->send($params, 'emails.yzcube.message_notification', $data);
    }
}
