<?php

namespace App\Services\Mail\Yzcube;

use App\Services\Mail\AbsSendMail;

class ErrorNotificationService extends AbsSendMail
{
    /**
     * __construct
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        //set campaign properties see AbsSendMail
        $this->campaignType = 'yzcube_error_notification';
        $this->campaignTargetID = null;
    }

    /**
     * 寄信
     *
     * @param  $title
     * @return void
     */
    public function sendMail($title, $messages = [])
    {
        $params = [
            'address'      => env('MAIL_TEST'),
            'name'         => env('MAIL_FROM_NAME'),
            'subject'      => $title,
            'event' => [
                'campaign_id' => $this->getCampaignID(), //in trait
                'target_type' => 'it',
                'target_id'   => null
            ]
        ];

        $data = [
            'title'    => $title,
            'messages' => $messages,
        ];

        $this->mailService->send($params, 'emails.yzcube.error_notification', $data);
    }
}
