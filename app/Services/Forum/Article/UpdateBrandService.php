<?php
/*
 |--------------------------------------
 |  儲存分享文的品牌 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Models\ForumArticle as Article;
use App\Models\Brand;
use App\Models\User;
use App\Models\Store;
use App\Services\User\Wedding\UpdateScheduleService;

class UpdateBrandService
{
    private $store;
    private $updateScheduleService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Store $store,
        UpdateScheduleService $updateScheduleService
    ) {
        $this->store                 = $store;
        $this->updateScheduleService = $updateScheduleService;
    }

    /**
     * 更新品牌
     */
    public function run($brands = [], Article $article, User $user)
    {
        $brandPivotArray = [];

        // foreach 品牌物件
        foreach ($brands as $brand) {

            // 紀錄品牌編號及評分資訊
            $brandPivotArray[$brand->id] = [
                'rank'             => $brand->rank,
                'is_booked'        => ($brand->is_booked !== '') ? $brand->is_booked : NULL,
                'wedding_type_ids' => ($brand->is_booked && $brand->wedding_type_ids) ? implode(',', $brand->wedding_type_ids) : NULL,
                'deleted_at'       => NULL,
            ];

            // 排除未下訂的體驗文
            if (!$brand->is_booked) {
                continue;
            }

            $store = Brand::find($brand->id)->primaryStores->first();
            if($store) {
                // 驗證分享文分類是否為黃金團隊進度的團隊類型
                if (!isset($this->store->teamTypeList[$store->type])) {
                    continue;
                }

                // 同步新增關聯黃金團隊商家
                foreach ($brand->wedding_type_ids as $wedding_type) {
                    $this->updateScheduleService->run([
                        'user'         => $user,
                        'wedding_type' => $wedding_type,
                        'status'       => 'booked',
                        'team_type'    => $store->type,
                        'brand_id'     => $brand->id,
                    ], false);
                }
            }
        }

        // 同步分享文的品牌關聯
        $article->allBrands()->syncWithoutDetaching($brandPivotArray);

        // 刪除分享文的品牌關聯
        $brandIds  = array_keys($brandPivotArray);
        $detachIds = $article->brands()->whereNotIn('brand_id', $brandIds)->pluck('brand_id');
        foreach ($detachIds as $detachId) {
            $article->brands()->updateExistingPivot($detachId, ['deleted_at' => now()]);
        }
    }
}
