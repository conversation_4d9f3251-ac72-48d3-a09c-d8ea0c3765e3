<?php
/*
 |--------------------------------------
 |  留言 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Services\Tools\Content\EncodeService;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\GetClientIpTrait;
use App\Jobs\Forum\SendEmailCommentAtUsers;
use App\Jobs\Forum\SendEmailNewCommentArticleTracks;
use App\Jobs\Forum\SendEmailNewComment;
use App\Models\ForumComment as Comment;

class CommentService
{
    private $encodeService;
    private $comment;

    use ApiErrorTrait;
    use GetClientIpTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        EncodeService $encodeService,
        Comment $comment
    ) {
        $this->encodeService = $encodeService;
        $this->comment       = $comment;
    }

    /**
     * 留言
     */
    public function run($request)
    {
        $user    = $request['user'];
        $article = $request['article'];
        $comment = '';

        // 取特定留言
        if ($request['comment_id']) {
            $comment = $article->liveComments()->find($request['comment_id']);
        }

        // 建立新留言
        $isNewComment = false;
        if (!$comment) {
            $comment = new Comment;
            $isNewComment = true;
        }

        // 儲存留言
        $comment->article_id   = $article->id;
        $comment->parent_id    = $request['parent_id'];
        $comment->is_anonymous = (int) filter_var($request['is_anonymous'], FILTER_VALIDATE_BOOLEAN);
        $comment->hide_type    = $request['hide_type'];
        $comment->hide_content = $request['hide_content'];
        $comment->auto_reply   = (bool)$request['auto_reply'];
        $comment->ip           = $request['auto_reply'] ? $article->ip : $this->getClientIp();
        $user->comments()->save($comment);

        // 解析content，並儲存圖片
        $contentObj = $this->encodeService->run($request['content'], 'forum_comment', $comment->id);

        // 沒有留言內容不可發佈
        if (!$contentObj->content || !$contentObj->summary) {
            $comment->delete();
            $this->setException('儲存留言內容有誤，請重新編輯留言！');
        }

        // 回覆(textarea)需要額外處理分行符號
        if ($comment->parent) {
            $contentObj->content = preg_replace('/[\n\r\t]/', "<br />\n", $contentObj->content);
        }

        // 更新content & summary
        $oldContent = $comment->content;
        $comment->content = $contentObj->content;
        $comment->summary = json_encode($contentObj->summary, JSON_UNESCAPED_UNICODE);
        $comment->save();

        // 更新At Users
        $pivotArray = [];
        foreach ($contentObj->atUsers as $atUser) {
            // 留言或回覆 At 上一層(文章或留言)的作者，則不寄「有人在留言中提到您」
            $pivotArray[$atUser['user_id']] = [
                'article_id' => $article->id,
                'send_email' => ($comment->previous->user_id == $atUser['user_id']),
            ];
        }
        $comment->atUsers()->sync($pivotArray);

        // 更新文章留言數
        $article->totalCommentCount();
        $article->save();

        // 更新作者的文章和留言總數
        $comment->author->totalArticleCommentCount();
        $comment->author->save();

        // WeddingDay 留言標記通知
        SendEmailCommentAtUsers::dispatch($comment);

        // 新留言
        if ($isNewComment) {

            // WeddingDay 文章新留言通知
            SendEmailNewComment::dispatch($comment);

            // 每日新增的前三則留言，需通知文章追蹤者
            $todayCommentCount = $article->comments()->where('forum_comments.created_at', '>=', date('Y-m-d'))->count();
            if (!$comment->parent_id && $todayCommentCount <= 3) {

                // WeddingDay 追蹤文章新留言通知
                SendEmailNewCommentArticleTracks::dispatch($comment, $user);
            }

        // 新增留言編輯紀錄，儲存編輯前的留言內容
        } else {
            $comment->logUpdateds()->create(['article_id' => $article->id, 'content' => $oldContent]);
        }

        // 更新images
        $imageIds = [];
        foreach ($contentObj->images as $image) {
            $imageIds[] = $image['imageID'];
        }

        // 包含編輯紀錄裡的images
        foreach ($comment->logUpdateds as $log) {
            preg_match_all('/\[image:([0-9]{1,})\]/Usi', $log->content, $match, PREG_SET_ORDER);
            foreach ($match as $tag) {
                $imageIds[] = $tag[1];
            }
        }

        // 更新已刪除的圖片
        $comment->images()->whereNotIn('id', $imageIds)->delete();

        return $comment;
    }
}
