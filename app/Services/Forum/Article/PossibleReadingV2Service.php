<?php
/*
 |--------------------------------------
 |  取得可能想要閱讀的文章 Service
 |--------------------------------------
 |
 | 取出文章的方法
 |
 |      取出熱門文章前 200 篇
 |      前 2 篇  排名前20%
 |      中 2 篇  近 7 天的新文章取 2（若沒有新文章，取排名後80%)
 |      後 2 篇   排名後 80%
 |
 */
namespace App\Services\Forum\Article;

use App\Models\ForumArticle as Article;
use App\Models\ForumCategory as Category;

class PossibleReadingV2Service
{
    private $outputCount    = 6; // 預設6筆
    private $notGetId       = []; // 要排除的文章，通常是當篇文章
    private $result;

    private $article;
    private $category;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Article $article,
        Category $category
    ) {
        $this->article  = $article;
        $this->category = $category;
        $this->result   = collect([]);
    }

    /**
     * 要排除的文章ID
     *
     * @param Array $ids
     * @return void
     */
    public function setNotGetId(Array $ids)
    {
        $this->notGetId = $ids;
    }

    /**
     * 取得可能想閱讀的文章
     *
     * @return void
     */
    public function get($targetCategory)
    {
        // 取得熱門文章前 200 篇
        $hotArticles = $this->article->release()
                ->where('category_id', $targetCategory)
                ->whereNotIn('id', $this->notGetId)
                ->where('hot_score', '>', 0)
                ->sortHotScore()
                ->limit(200)
                ->get();
        $total = $hotArticles->count();

        // 如果沒有熱門文章，用page_view排序
        if ($total <= 6) {
            $hotArticles = $this->article->release()
                ->where('category_id', $targetCategory)
                ->whereNotIn('id', $this->notGetId)
                ->orderby('page_view', 'DESC')
                ->limit(200)
                ->get();
            $total = $hotArticles->count();
        }

        // 前 20%
        $topCount = (int) ceil($total * 0.2);
        $topArticlesPool = $hotArticles->slice(0, $topCount);
        $topArticles = $topArticlesPool->random(min(2, $topArticlesPool->count()));

        // 後 80%
        $bottomArticlesPool = $hotArticles->slice($topCount);

        // 新文章 pool
        $newArticlesPool = $hotArticles->filter(function ($article) {
            return $article->published_at >= now()->subDays(7);
        });

        // 已選的 ID（先把 top 的加進來）
        $selectedIds = $topArticles->pluck('id');

        // 過濾掉已選過的
        $availableNewArticles = $newArticlesPool->whereNotIn('id', $selectedIds);
        $availableBottomArticles = $bottomArticlesPool->whereNotIn('id', $selectedIds);
        $newCount = $availableNewArticles->count();
        $middleArticles = collect();
        if ($newCount > 0) {
            $pickCount = min(2, $newCount);
            $pickedNew = $availableNewArticles->random($pickCount);
            $middleArticles = $middleArticles->merge($pickedNew);
            $selectedIds = $selectedIds->merge($pickedNew->pluck('id'));
        }

        // 算一下新文章缺幾篇
        $neededFromBottom = 2 - $middleArticles->count();

        // 最後拿 bottom（補滿 2 + 缺的數量）
        $finalBottomPickCount = 2 + max(0, $neededFromBottom);
        $finalBottomArticlesPool = $availableBottomArticles->whereNotIn('id', $selectedIds);

        if ($finalBottomArticlesPool->isNotEmpty()) {
            $finalBottomArticles = $finalBottomArticlesPool->random(
                min($finalBottomPickCount, $finalBottomArticlesPool->count())
            );
        } else {
            $finalBottomArticles = collect();
        }

        // 合併結果
        $this->result = $topArticles->merge($middleArticles)->merge($finalBottomArticles);

        return $this->result;
    }
}
