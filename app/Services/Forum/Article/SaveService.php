<?php
/*
 |--------------------------------------
 |  儲存文章 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Services\Forum\Article\CreateService;
use App\Services\Tools\Content\EncodeService;
use App\Services\Tools\ForbiddenCheckService;
use App\Services\File\UploadImageFormService;
use App\Services\Forum\Article\UpdateBrandService;
use App\Services\Image\CreateImageService;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\GetClientIpTrait;
use App\Jobs\Forum\SendEmailForbiddenArticle;
use App\Jobs\Forum\SendEmailArticleAtUsers;
use App\Jobs\Forum\SendEmailArticleTracks;
use App\Jobs\Forum\SendEmailNewArticle;
use Illuminate\Support\Arr;

class SaveService
{
    private $createService;
    private $encodeService;
    private $forbiddenCheckService;
    private $uploadImageFormService;
    private $updateBrandService;
    private $createImageService;

    use ApiErrorTrait;
    use GetClientIpTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        CreateService $createService,
        EncodeService $encodeService,
        ForbiddenCheckService $forbiddenCheckService,
        UploadImageFormService $uploadImageFormService,
        UpdateBrandService $updateBrandService,
        CreateImageService $createImageService
    ) {
        $this->createService         = $createService;
        $this->encodeService         = $encodeService;
        $this->forbiddenCheckService = $forbiddenCheckService;
        $this->uploadImageFormService = $uploadImageFormService;
        $this->updateBrandService     = $updateBrandService;
        $this->createImageService     = $createImageService;
    }

    /**
     * 儲存文章
     */
    public function run($request, $article = null)
    {
        $user = $request['user'];
        if (!$article) {
            $article = $this->createService->getArticle($request);
        }

        // 狀態變更
        $status       = ($request['status'] == 'pending' && $request['status'] != $article->status && $article->status) ? $article->status : $request['status'];
        $isNewPublish = ($request['status'] == 'published' && $request['status'] != $article->status);

        // 儲存文章
        if (isset($request['category_id'])) {
            $article->category_id  = $request['category_id'];
        }
        if (isset($request['title'])) {
            $article->title = $request['title'];
        }
        if (isset($request['is_anonymous'])) {
            $article->is_anonymous = $request['is_anonymous'];
        }
        if (!$request['is_schedule']) {
            $article->ip = $this->getClientIp();
        }
        // $article->cover_use_image_id = $request['cover_use_image_id'];
        $article->status = $status;
        $user->articles()->save($article);

        // 刪除封面照
        if (!$request['cover_use_image_id']) {
            $article->cover()->delete();
        }

        // 內容
        if (isset($request['content'])) {
            // 去掉頭尾空白（空行）
            $request['content'] = preg_replace(
                '/^(?:\s*<p>(\s|<br\s*\/?>)*<\/p>\s*)+|(?:\s*<p>(\s|<br\s*\/?>)*<\/p>\s*)+$/',
                '',
                $request['content']
            );

            // 解析content，並儲存圖片
            $contentObj = $this->encodeService->run($request['content'], 'forum_article', $article->id);

            // 更新content & summary
            $oldContent = $article->content;
            $article->content = $contentObj->content;
            $article->summary = json_encode($contentObj->summary, JSON_UNESCAPED_UNICODE);
            $article->save();

            // 更新At Users
            $atUserIDs = Arr::pluck($contentObj->atUsers, 'user_id');
            $article->atUsers()->sync($atUserIDs);

            // 重新編輯已發佈文章，需新增文章編輯紀錄，儲存編輯前的文章內容
            if ($article->status == 'published' && !$isNewPublish) {
                $article->logUpdateds()->create(['content' => $oldContent]);
            }

            // 取出所有images
            $imageIds = [];
            foreach ($contentObj->images as $image) {
                $imageIds[] = $image['imageID'];

                // 選擇封面照
                if ($request['cover_use_image_id'] == $image['imageID']) {

                    // 新增封面照來源
                    $this->createImageService->add([
                        'file_name' => $image['file_name'],
                        'type'      => 'forum_cover',
                        'target_id' => $article->id,
                        'only'      => true,
                    ]);
                }
            }

            // 包含編輯紀錄裡的images
            foreach ($article->logUpdateds as $log) {
                preg_match_all('/\[image:([0-9]{1,})\]/Usi', $log->content, $match, PREG_SET_ORDER);
                foreach ($match as $tag) {
                    $imageIds[] = $tag[1];
                }
            }

            // 更新已刪除的圖片
            $article->images()->whereNotIn('id', $imageIds)->delete();
        }

        // 儲存分享文的品牌
        $brands = json_decode($request['brands']);
        $this->updateBrandService->run($brands, $article, $user);

        // 已發佈文章
        if ($article->status == 'published') {

            // 沒有文章內容不可發佈
            if (!$article->content || !$article->summary) {
                $article->status = 'pending';
                $article->save();

                $this->setException('儲存文章內容有誤，請重新編輯文章！');
            }

            // 禁言文章需經過審核
            $isException      = ($article->category_id == 6) ? true : false; // 避雷(黑特)文預設不禁言
            $titleForbidden   = $this->forbiddenCheckService->text($article->title, $isException);
            $contentForbidden = $this->forbiddenCheckService->text($article->content, $isException);
            if ($titleForbidden || $contentForbidden) {
                $article->status = 'pending';
                $article->save();

                // WeddingDay 禁言文章審核通知
                $forbiddenText = $titleForbidden ?: $contentForbidden;
                SendEmailForbiddenArticle::dispatch($article, $forbiddenText);

                $message = [
                    'message'  => '發佈此文章需審核，若要發佈文章請先[contact_us]',
                    '[contact_us]' => [
                        'type'   => 'link',
                        'text'   => '聯絡客服',
                        'href'   => '/contacts',
                        'target' => '_self',
                    ]
                ];
                $this->setException(json_encode($message), 4100);
            }

            // 更新發佈時間
            if ($isNewPublish || !$article->published_at) {
                $article->published_at = now();
                $article->save();
            }

            // 更新作者的文章和留言總數
            $user->totalArticleCommentCount();
            $user->save();

            // WeddingDay 文章標記通知
            SendEmailArticleAtUsers::dispatch($article);

            // WeddingDay 追蹤文章更新通知
            SendEmailArticleTracks::dispatch($article, $user);

            // WeddingDay 發佈文章互動通知
            if ($isNewPublish && $article->category->is_promotion) {
                SendEmailNewArticle::dispatch($article);
            }
        }

        return $article;
    }
}
