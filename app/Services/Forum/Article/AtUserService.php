<?php
/*
 |--------------------------------------
 |  At User列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Repositories\UserRepository;
use App\Repositories\ForumArticleRepository as ArticleRepository;
use App\Repositories\ForumCommentRepository as CommentRepository;
use App\Models\ForumArticle as Article;

class AtUserService
{
    private $userRepository;
    private $articleRepository;
    private $commentRepository;
    private $article;
    private $result;
    private $author;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserRepository $userRepository,
        ArticleRepository $articleRepository,
        CommentRepository $commentRepository,
        Article $article
    ) {
        $this->userRepository    = $userRepository;
        $this->articleRepository = $articleRepository;
        $this->commentRepository = $commentRepository;
        $this->article           = $article;

        // 空集合
        $this->result = collect([]);
    }

    /**
     * At User列表
     */
    public function run($request, $limit = 10)
    {
        // 關鍵字 keyword
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            return $this->userRepository->getListByKeyword($keyword, $limit);
        }

        $this->author = $request['user'];

        if ($request['article_id']) {
            $article = $this->article->live()->find($request['article_id']);
            if (!$article) {
                return $this->result;
            }

            // 回應留言者
            if ($request['comment_parent_id']) {
                $parent = $article->comments()->find($request['comment_parent_id']);
                if ($parent) {
                    $this->result = $this->result->merge([$parent->author]);
                }
            }

            // 發文者
            if ($request['type'] == 'comment') {
                $this->result = $this->result->merge([$article->author]);
            }

            // 留言者
            foreach ($article->liveComments as $comment) {
                $this->result = $this->result->merge([$comment->author]);
            }
        }

        // 確認數量
        $this->checkLimit($limit);

        return $this->result;
    }

    /**
     * 確認數量
     */
    private function checkLimit($limit)
    {
        // 排除自己
        $this->result = $this->result->reject(function ($value) {
            return $value->id == $this->author->id;
        });

        // 去重複
        $this->result = $this->result->unique('id');

        // 取特定數量
        $this->result->splice($limit);
    }
}
