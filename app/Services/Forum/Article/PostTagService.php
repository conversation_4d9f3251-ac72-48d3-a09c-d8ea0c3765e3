<?php
/*
 |--------------------------------------
 |  儲存Tag列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Models\ForumTag as Tag;

class PostTagService
{
    private $tag;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(Tag $tag)
    {
        $this->tag = $tag;
    }

    /**
     * 儲存Tag列表
     */
    public function run($request)
    {
        $article = $request['article'];

        // 先清除Tag的文章數
        foreach ($article->tags as $tag) {
            if ($tag->article_count) {
                $tag->article_count--;
                $tag->save();
            }
        }

        // 再儲存文章Tag
        $tagIDs = [];
        if ($request['tags']) {
            foreach ($request['tags'] as $val) {

                // 僅留漢字、英文、數字、底線
                $val = preg_replace('/[^\x{4e00}-\x{9fa5}A-Za-z0-9_]/u', '', $val);
                if (!$val) {
                    continue;
                }

                // 更新Tag & 文章數
                $tag = $this->tag->where('name', $val)->first();
                if (!$tag) {
                    $tag = clone $this->tag;
                }
                $tag->name = $val;
                $tag->article_count++;
                $tag->save();

                // 紀錄tag_id
                $tagIDs[] = $tag->id;
            }
        }

        // 同步關聯
        $article->tags()->sync($tagIDs);

        return;
    }
}
