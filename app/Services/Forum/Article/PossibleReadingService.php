<?php
/*
 |--------------------------------------
 |  取得可能想要閱讀的文章 Service
 |--------------------------------------
 |
 | 取出文章的方法
 |
 |      方案一、取三個熱門文章 取三個隨機文章
 |      方案二、條件為（曝光量超過1500且總數大於文章數一半），取可能喜歡文章點擊率最佳前20％，以及隨機文章但排除前20％的文章
 |
 */

namespace App\Services\Forum\Article;

use App\Models\ForumArticle as Article;
use App\Models\ForumCategory as Category;

class PossibleReadingService
{
    private $outputCount    = 5;//預設5筆
    private $possibleCount;     //可能想閱讀的文章數
    private $randomCount;       //隨機取樣的文章數
    private $maxImpressions = 1500;//曝光量超過的數字，點擊率才算可信任
    private $getPossibleMaxCount;//紀錄大於1500點擊率排序前20％的總數應該是多少
    private $possibleCTR    = 0;//前20％的CTR
    private $notGetId       = [];//要排除的文章，通常是當篇文章
    private $searchMode     = null;//如果曝光量未滿1500的總數量 未超過 總文章數的一半，就用方案一

    private $result;

    private $article;
    private $category;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Article $article,
        Category $category
    ) {
        $this->article  = $article;
        $this->category = $category;
        $this->result   = collect([]);
    }

    /**
     * 設定回傳的筆數
     *
     * @param integer $count
     * @return void
     */
    public function setOutputCount(Int $count)
    {
        $this->outputCount = $count;
    }

    /**
     * 要排除的文章ID
     *
     * @param Array $ids
     * @return void
     */
    public function setNotGetId(Array $ids)
    {
        $this->notGetId = $ids;
    }


    /**
     * 修改模式， 1：方案一 2：方案二
     *
     * @param [type] $mode
     * @return void
     */
    public function setSearchMode(Int $mode)
    {
        if ($mode == 1 || $mode == 2) {
            $this->searchMode = $mode;
        }
    }

    /**
     * 取得可能想閱讀的文章
     *
     * @return void
     */
    public function get($targetCategory)
    {
        if ($this->searchMode == null || $this->searchMode == 2) {
            $totalCount = $this->article->where('status', 'published')
                                        ->where('category_id', $targetCategory)
                                        ->count() ;
        }

        if ($this->searchMode == null) {
            $isPossibleCount    = $this->article->where('status', 'published')
                                        ->where('possible_read_impressions', '>=', $this->maxImpressions)
                                        ->count();
            //如果滿足可能閱讀曝光量大於1500的筆數超過總文章數的一半時
            if ($isPossibleCount >= $totalCount / 2) {
                $this->searchMode = 2;
            } else {
                $this->searchMode = 1;
            }
        }

        if ($this->searchMode == 2) {
            //取得前20％的數量應該是多少
            $this->getPossibleMaxCount = intval($totalCount / 5) ;
            $this->getPossibleMaxCount = $this->getPossibleMaxCount >= 1 ? $this->getPossibleMaxCount : 1;

            //取得前20％的點擊率是多少
            $topPossibleCTR   = $this->article->where('status', 'published')
                                        ->where('possible_read_impressions', '>=', $this->maxImpressions)
                                        ->orderBy('possible_read_CTR', 'desc')
                                        ->offset($this->getPossibleMaxCount -1)
                                        ->limit(1)
                                        ->get();
            $this->possibleCTR = $topPossibleCTR[0]->possible_read_CTR;
        }

        //分配一下，可能閱讀的推薦與熱門推薦各半
        $this->randomCount      = intval($this->outputCount / 2) ;
        $this->possibleCount    = $this->outputCount - $this->randomCount;

        //先取得可能閱讀的內容
        if ($this->searchMode == 1) {
            //取得熱門文章
            $this->getHot($this->possibleCount, $targetCategory);
        } else {
            //取得可能想閱讀的文章
            $this->getPossible($this->possibleCount, $targetCategory);
        }

        //計算一下取出的可能還想閱讀文章數量 『實際的可能還想閱讀數量』
        $this->possibleCount    = count($this->result);
        $this->randomCount      = $this->outputCount - $this->possibleCount;

        //取得熱門的文章
        $this->getRandom($this->randomCount, $targetCategory);

        //打散推薦文章
        $this->result = $this->result->shuffle();

        return $this->result;
    }


    /**
     * 取得 可能想要閱讀的文章
     *
     * @return void
     */
    private function getPossible($count, $targetCategory)
    {
        $possible = $this->article->with('category')->where('status', 'published')
                        ->where('category_id', $targetCategory)
                        ->whereNotIn('id', $this->notGetId)
                        ->where('possible_read_impressions', '>=', $this->maxImpressions)
                        ->where('possible_read_CTR', '>=', $this->possibleCTR)
                        ->inRandomOrder()
                        ->limit($count)
                        ->get();

        if (!$possible->isEmpty() && count($possible) > 0) {
            foreach ($possible as $val) {
                //曝光量 +1
                $val->possible_read_impressions ++;
                $val->possible_read_CTR = ($val->possible_read_click > 0) ? round($val->possible_read_click / $val->possible_read_impressions, 5) : 0;
                $val->save();

                $this->result->push($val);
            }
        }
    }

    /**
     * 取得熱門文章
     *
     * @return void
     */
    private function getHot($count, $targetCategory)
    {
        $hot = $this->article->with('category')->where('status', 'published')
                        ->where('category_id', $targetCategory)
                        ->whereNotIn('id', $this->notGetId)
                        ->where('hot_score', '>', 0)
                        ->inRandomOrder()
                        ->limit($count)
                        ->get();

        if (!$hot->isEmpty() && count($hot) > 0) {
            foreach ($hot as $val) {
                //曝光量 +1
                $val->possible_read_impressions ++;
                $val->possible_read_CTR = ($val->possible_read_click > 0) ? round($val->possible_read_click / $val->possible_read_impressions, 5) : 0;
                $val->save();

                $this->result->push($val);
            }
        }
    }


    /**
     * 取得 隨機文章
     *
     * @param [type] $count
     * @return void
     */
    private function getRandom($count, $targetCategory)
    {
        $random = $this->article->with('category')->where('status', 'published')
                        ->where('category_id', $targetCategory);

        if (is_array($this->notGetId) && count($this->notGetId)) {
            $random = $random->whereNotIn('id', $this->notGetId);
        }

        if ($this->searchMode == 2) {
            // 新文章產數不足，改用一個月內發佈的新文章
            // $random = $random->where('possible_read_impressions', '<', $this->maxImpressions);
            $random = $random->where('published_at', '>=', now()->subMonth());
        }

        $random = $random->inRandomOrder()->limit($count)->get();

        if (!$random->isEmpty() && count($random) > 0) {
            foreach ($random as $val) {
                //曝光量 +1
                $val->possible_read_impressions ++;
                $val->possible_read_CTR = ($val->possible_read_click > 0) ? round($val->possible_read_click / $val->possible_read_impressions, 5) : 0;
                $val->save();

                $this->result->push($val);
            }
        }
    }

}
