<?php
/*
 |--------------------------------------
 |  取得上下篇的文章 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Models\LogQueryString;
use App\Repositories\ForumArticleRepository as ArticleRepository;
use App\Services\Tools\GetQueryKeyService;

class PreviousNextService
{
    private $logQueryString;
    private $articleRepository;
    private $getQueryKeyService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        LogQueryString $logQueryString,
        ArticleRepository $articleRepository,
        GetQueryKeyService $getQueryKeyService
    ) {
        $this->logQueryString     = $logQueryString;
        $this->articleRepository  = $articleRepository;
        $this->getQueryKeyService = $getQueryKeyService;
    }

    /**
     * 取得上下篇的文章
     *
     */
    public function get($request, $article_id)
    {
        // 上下篇文章
        $_request = clone $request;
        $logQueryString = $this->logQueryString->where('source', 'forum')
                                                ->where('key', $request['query_key'])
                                                ->first();
        if ($logQueryString) {
            $queryArray = json_decode($logQueryString->data, true);
            $_request->merge($queryArray);
        }

        $articles = $this->articleRepository->getListByRequest($_request);

        // 找出當頁的第幾篇
        $index = 0;
        foreach ($articles as $key => $_article) {
            if ($_article->id == $article_id) {
                $index = $key + 1;
                break;
            }
        }

        // 預設的QueryKey
        $_request->merge(['page' => 1]);
        $defaultQueryKey = $this->getQueryKeyService->run($_request, 'forum', false);

        // 每頁的第一篇
        if ($index == 1) {

            // 上ㄧ頁的最後一篇文章
            $prevPage = ($articles->currentPage() == 1) ? $articles->lastPage() : $articles->currentPage() - 1;
            $_request->merge(['page' => $prevPage]);
            $prevArticle  = $this->articleRepository->getListByRequest($_request)->last();
            $prevQueryKey = $this->getQueryKeyService->run($_request, 'forum', false);

        // 當頁的上一篇
        } else {
            $prevArticle  = $articles[$index - 2];
            $prevQueryKey = $logQueryString ? $request['query_key'] : $defaultQueryKey;
        }

        // 每頁的最後一篇
        if ($index == $articles->count()) {

            // 下ㄧ頁的第一篇文章
            $nextPage = $articles->hasMorePages() ? $articles->currentPage() + 1 : 1;
            $_request->merge(['page' => $nextPage]);
            $nextArticle  = $this->articleRepository->getListByRequest($_request)->first();
            $nextQueryKey = $this->getQueryKeyService->run($_request, 'forum', false);

        // 當頁的上一篇
        } else {
            $nextArticle  = $articles[$index];
            $nextQueryKey = $logQueryString ? $request['query_key'] : $defaultQueryKey;
        }

        $prevArticle = ($prevArticle && $prevArticle->id == $article_id) ? NULL : $prevArticle;
        $nextArticle = ($nextArticle && $nextArticle->id == $article_id) ? NULL : $nextArticle;

        return [
            'prevQueryKey' => $prevQueryKey,
            'prevArticle'  => $prevArticle,
            'nextQueryKey' => $nextQueryKey,
            'nextArticle'  => $nextArticle,
        ];
    }
}
