<?php
/*
 |--------------------------------------
 |  文章&留言按讚 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Traits\ApiErrorTrait;
use App\Services\Mail\Forum\LikeArticleService;
use App\Services\Mail\Forum\LikeCommentService;

class LikeService
{
    private $likeArticleService;
    private $likeCommentService;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        LikeArticleService $likeArticleService,
        LikeCommentService $likeCommentService
    ) {
        $this->likeArticleService = $likeArticleService;
        $this->likeCommentService = $likeCommentService;
    }

    /**
     * 文章&留言按讚
     */
    public function run($request)
    {
        // 取得使用者 & 文章
        $user    = $request['user'];
        $article = $request['article'];

        // 留言
        if ($request['comment_id']) {
            // 驗證留言
            $comment = $article->liveComments()->find($request['comment_id']);
            if (!$comment) {
                $this->setException('查無此留言！');
            }

            // 更新留言按讚
            $changes = $comment->likesWithTrashed()
                                ->syncWithoutDetaching([
                                    $user->id => [
                                        'article_id' => $comment->article_id,
                                        'deleted_at' => $request['like'] ? NULL : now(),
                                    ]
                                ]);

            // 首次按讚、排除自己
            if ($request['like'] && in_array($user->id, $changes['attached']) && $comment->user_id != $user->id) {

                // WeddingDay 留言按讚通知
                $this->likeCommentService->sendMail($comment);
            }

            // 更新留言作者得到的按讚數
            $comment->author->totalGetLikeCount();
            $comment->author->save();

            return $comment->likes->count();
        // 文章
        } else {

            // 更新文章按讚
            $changes = $article->likesWithTrashed()
                                ->syncWithoutDetaching([
                                    $user->id => [
                                        'deleted_at' => $request['like'] ? NULL : now()
                                    ]
                                ]);

            // 首次按讚、排除自己
            if ($request['like'] && in_array($user->id, $changes['attached']) && $article->user_id != $user->id) {

                // WeddingDay 文章按讚通知
                $this->likeArticleService->sendMail($article);
            }

            // 更新文章按讚數
            $article->like_count = $article->likes->count();
            $article->save();

            // 更新文章作者得到的按讚數
            $article->author->totalGetLikeCount();
            $article->author->save();

            return $article->like_count;
        }
    }
}
