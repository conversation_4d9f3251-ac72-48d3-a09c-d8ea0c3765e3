<?php
/*
 |--------------------------------------
 |  話題標籤列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Repositories\ForumTagRepository as TagRepository;
use App\Repositories\ForumArticleRepository as ArticleRepository;

class GetTagService
{
    private $tagRepository;
    private $articleRepository;
    private $result;
    private $excludes;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        TagRepository $tagRepository,
        ArticleRepository $articleRepository
    ) {
        $this->tagRepository     = $tagRepository;
        $this->articleRepository = $articleRepository;

        // 空集合
        $this->result   = collect([]);
        $this->excludes = collect([]);
    }

    /**
     * 話題標籤列表
     */
    public function run($request, $limit = 20)
    {
        // 要排除的話題標籤名稱，搜尋出id集合
        if ($request['excludes']) {
            $this->excludes = collect($request['excludes']);
        }

        // 若有搜尋關鍵字 keyword，直接回傳結果
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            return $this->tagRepository->getListBySearch($keyword, false, $limit, $this->excludes);
        }

        // 近30天內發文的話題標籤紀錄
        $after_at = now()->subDays(30);
        $articles = $this->articleRepository->getNewPublishedByAfterAt($after_at);
        foreach ($articles as $article) {
            $this->result = $this->result->merge($article->tags->makeHidden('pivot'));
        }

        // 去重複
        $this->result = $this->result->unique('id');

        // 要排除的項目
        $this->result = $this->result->reject(function($val, $key) {
            return in_array($val->id, $this->excludes->toArray());
        });

        // 熱門排序
        $this->result = $this->result->sortByDesc('article_count');

        // 取特定數量
        $this->result->splice($limit);

        // 若數量不足，用預設標籤列表補
        $limit = $limit - $this->result->count();
        if ($limit > 0) {
            $this->excludes = $this->excludes->merge($this->result->pluck('id'));
            $tags = $this->tagRepository->getDefaultList($limit, $this->excludes);
            $this->result = $this->result->merge($tags);
        }

        return $this->result;
    }
}
