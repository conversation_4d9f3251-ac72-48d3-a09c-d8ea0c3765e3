<?php
/*
 |--------------------------------------
 |  新增文章 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Models\ForumArticle as Article;
use App\Models\Brand;

class CreateService
{
    private $categoryRepository;
    private $article;
    private $storeType;
    private $brand;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        CategoryRepository $categoryRepository,
        Article $article,
        Brand $brand
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->article            = $article;
        $this->brand             = $brand;
    }

    /**
     * 我要發文/編輯文章
     */
    public function run($request)
    {
        $user   = $request['user'];
        $public = (!$user->is_admin && !$user->is_fake);

        // 若帶brand_id參數 自動帶入品牌ID
        $brand = NULL;
        if (!$request['post_id'] && $request['brand_id']) {
            $brand = $this->brand->find($request['brand_id']);
        }

        $result['categories'] = $this->categoryRepository->getPublicUseList($public);
        $result['article']    = $this->getArticle($request);
        $result['brand']      = $brand;

        return $result;
    }

    /**
     * 取得文章
     *
     * @return article model
     */
    public function getArticle($request)
    {
        $user = $request['user'];
        $article = '';

        // 取出使用者的特定文章
        if ($request['article_id']) {
            $article = $user->articles->find($request['article_id']);
        }

        // 若找不到文章，則取出使用者的草稿
        if (!$article) {
            $article = $user->getDraftArticle();
        }

        // 若還是找不到文章，則建立使用者的新文章
        if (!$article) {
            $article = clone $this->article;
        }

        return $article;
    }
}
