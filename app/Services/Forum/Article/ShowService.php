<?php
/*
 |--------------------------------------
 |  文章詳細內容頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Article;

use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Services\Tools\LogSearchKeywordService;
use App\Services\Forum\Article\PossibleReadingService;
use App\Services\Forum\Article\PossibleReadingV2Service;
use App\Services\Forum\Article\PreviousNextService;
use App\Traits\ApiErrorTrait;
use GuzzleHttp\Client;
use DOMDocument;
use DOMXPath;

class ShowService
{
    use ApiErrorTrait;

    private $categoryRepository;
    private $logSearchKeywordService;
    private $previousNextService;
    private $possibleReadingService;
    private $possibleReadingV2Service;
    private $client;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        CategoryRepository $categoryRepository,
        LogSearchKeywordService $logSearchKeywordService,
        PreviousNextService $previousNextService,
        PossibleReadingService $possibleReadingService,
        PossibleReadingV2Service $possibleReadingV2Service,
        Client $client
    ) {
        $this->categoryRepository      = $categoryRepository;
        $this->logSearchKeywordService = $logSearchKeywordService;
        $this->previousNextService     = $previousNextService;
        $this->possibleReadingService  = $possibleReadingService;
        $this->possibleReadingV2Service= $possibleReadingV2Service;
        $this->client                  = $client;
    }

    /**
     * 文章詳細內容頁
     */
    public function run($request)
    {
        // 取得使用者 & 文章
        $user    = $request['user'];
        $article = $request['article'];

        // 更新點擊數
        $article->page_view++;
        $article->save();

        // 更新關鍵字搜尋紀錄
        if ($request['keyword_id']) {
            $this->logSearchKeywordService->updateFoundById($request['keyword_id'], $article->id);
        }

        return [
            'article'          => $article,
            'user'             => $user,
            'brands'           => $this->getRank($article),
        ];
    }

    /**
     * 取得品牌評分
     */
    public function getRank($article)
    {
        // 品牌列表：判斷是否為付費商家
        $paidBrandIds = [];
        $paidBrands   = [];
        $unPaidBrands = [];
        foreach ($article->brands as $brand) {
            if ($brand->paidStores->count()) {
                $paidBrandIds[] = $brand->id;
                $paidBrands[]   = $brand;
            } else {
                $unPaidBrands[] = $brand;
            }
        }

        // 品牌列表：付費商家要排前面
        $brands = array_merge($paidBrands, $unPaidBrands);

        return $brands;
    }

    /**
     * 感興趣的文章
     */
    public function getPossibleReadings($article)
    {
        // 可能想要閱讀的文章
        $this->possibleReadingService->setNotGetId([$article->id]);
        $possibleReadings = $this->possibleReadingService->get($article->category_id);

        return $possibleReadings;
    }

    public function getPossibleReadingsV2($article)
    {
        // 可能想要閱讀的文章
        $this->possibleReadingV2Service->setNotGetId([$article->id]);
        $possibleReadings = $this->possibleReadingV2Service->get($article->category_id);

        return $possibleReadings;
    }
}
