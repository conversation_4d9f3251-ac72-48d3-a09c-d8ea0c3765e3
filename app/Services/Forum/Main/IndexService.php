<?php
/*
 |--------------------------------------
 |  首頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Main;

use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Repositories\ForumArticleRepository as ArticleRepository;
use App\Repositories\ForumTagRepository as TagRepository;
use App\Services\Tools\LogSearchKeywordService;
use App\Services\Tools\GetQueryKeyService;
use App\Traits\ApiErrorTrait;

class IndexService
{
    use ApiErrorTrait;

    private $categoryRepository;
    private $articleRepository;
    private $tagRepository;
    private $logSearchKeywordService;
    private $getQueryKeyService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        CategoryRepository $categoryRepository,
        ArticleRepository $articleRepository,
        TagRepository $tagRepository,
        LogSearchKeywordService $logSearchKeywordService,
        GetQueryKeyService $getQueryKeyService
    ) {
        $this->categoryRepository      = $categoryRepository;
        $this->articleRepository       = $articleRepository;
        $this->tagRepository           = $tagRepository;
        $this->logSearchKeywordService = $logSearchKeywordService;
        $this->getQueryKeyService      = $getQueryKeyService;
    }

    /**
     * 首頁 文章列表
     */
    public function run($request)
    {
        // 文章列表
        $articles = $this->articleRepository->getListByRequest($request);

        // 搜尋話題標籤
        $tag = $this->tagRepository->getFirst(['id' => $request['tag_id']]);

        // 使用者
        $user = $request['user'];

        // 查詢參數的索引值
        $query_key = $this->getQueryKeyService->run($request, 'forum');

        // 關鍵字搜尋紀錄
        $keyword_id = '';
        if ($request['keyword'] && $request['search']) {
            $this->logSearchKeywordService->create($request['keyword'], 'forum', $articles->total());
            $keyword_id = $this->logSearchKeywordService->getId();
        }

        return [
            'articles'   => $articles,
            'tag'        => $tag,
            'user'       => $user,
            'query_key'  => $query_key,
            'keyword_id' => $keyword_id,
        ];
    }
}
