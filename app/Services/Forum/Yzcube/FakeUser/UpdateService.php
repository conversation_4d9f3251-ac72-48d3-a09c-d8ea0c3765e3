<?php
/*
 |--------------------------------------
 |  更新假帳號 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\FakeUser;

use App\Models\UserWeddingType;

class UpdateService
{
    protected $weddingType;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(UserWeddingType $weddingType)
    {
        $this->weddingType = $weddingType;
    }

    /**
     * 更新假帳號
     */
    public function run($fakeUser, $request)
    {
        // 婚期
        if (isset($request['wedding_date'])) {
            $fakeUser->wedding_date = $request['wedding_date'];

            // 更新User婚禮中心的結婚婚期
            $type = array_search('結婚', $this->weddingType->typeList);
            $attributes = ['user_id' => $fakeUser->user_id, 'type' => $type];
            if ($request['wedding_date']) {
                $values = ['date' => $request['wedding_date'], 'deleted_at' => NULL];
            } else {
                $values = ['deleted_at' => now()];
            }
            $this->weddingType->withTrashed()->updateOrCreate($attributes, $values);
        }

        // 標籤
        if (isset($request['tags'])) {
            $fakeUser->tags = $request['tags'] ? json_encode($request['tags'], JSON_UNESCAPED_UNICODE) : NULL;
        }

        // 半年內隨機按讚
        if (isset($request['is_random_like'])) {
            $fakeUser->is_random_like = $request['is_random_like'];
        }

        // 備註訊息
        if (isset($request['note'])) {
            $fakeUser->note = $request['note'];
        }

        $fakeUser->save();
    }
}
