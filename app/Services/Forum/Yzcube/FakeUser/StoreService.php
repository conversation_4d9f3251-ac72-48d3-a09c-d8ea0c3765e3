<?php
/*
 |--------------------------------------
 |  新增假帳號 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\FakeUser;

use App\Repositories\UserRepository;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Traits\Auth\MakeAnonymousKeyTrait;
use App\Traits\Auth\CreateTokenTrait;

class StoreService
{
    protected $userRepository;

    use ConvertPasswordTrait;
    use MakeAnonymousKeyTrait;
    use CreateTokenTrait;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * 新增假帳號
     *
     * @return model $users
     */
    public function run($request)
    {
        // 統一密碼
        $feEncode = $this->feEncode($request['password']);
        $password = $this->getInsertHashPassword($feEncode);

        // 建立數量
        $users = [];
        for ($i=0; $i < $request['count']; $i++) {
            $anonymous_key = $this->getAnonymousKey();
            $email         = str_replace('@', '+'.$anonymous_key.'@', $request['email']);

            // create users
            $user = $this->userRepository->addData([
                'email'          => $email,
                'password'       => $password,
                'name'           => '匿名鬼鬼'.$anonymous_key,
                'real_name'      => '匿名鬼鬼'.$anonymous_key,
                'avatar'         => $this->userRepository->getModel()->present()->rand_avatar_image,
                'anonymous_key'  => $anonymous_key,
                'phone_legalize' => 1,
                'status'         => 'published',
                'email_legalize' => 1,
                'is_fake'        => 1,
            ]);

            // create fake_users
            $user->fake()->create();

            // create user_token
            $user->tokens()->create([
                'target_id' => $user->id,
                'type'      => 'user',
                'token'     => $this->getNewToken(),
            ]);

            $users[] = $user;
        }

        return $users;
    }
}
