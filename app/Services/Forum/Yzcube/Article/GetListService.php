<?php
declare(strict_types=1);
/*------------------------------------
 | 取得好婚聊聊的列表資料
 |------------------------------------
 | 總共要有六種分類..然後每種要有自己的分頁
 | 實名已發佈 實名草稿 實名刪除
 | 匿名已發佈 匿名草稿 匿名刪除
 |
 */

namespace App\Services\Forum\Yzcube\Article;
use App\Services\Tools\Content\DecodeService;

class GetListService
{
    const LIMIT = 10;

    private $decodeService;

    public function __construct(DecodeService $decodeService)
    {
        $this->decodeService = $decodeService;
    }

    /**
     * 取得文章跟留言的資料
     * @param $user
     * @return array
     */
    public function getBoth($user)
    {
        return [
            'articles' => $this->articles($user),
            'comments' => $this->comments($user)
        ];
    }

    /**
     * 取得文章資料
     * @param $user : User model
     * @param int[] $anonymous : 是否匿名
     * @param string[] $status : 文章狀態
     * @return array
     */
    public function articles($user, $anonymous = [0, 1], $status = ['published', 'pending', 'delete']): array
    {

        $articles = [];
        foreach ($anonymous as $a) {
            foreach ($status as $s) {
                $model = $user->allArticlesWithTrashed()->with(['category']);
                $model = $this->makeStatusWhere($model, $s);
                $model = $model->anonymous($a)
                    ->orderBy('created_at', 'desc')
                    ->paginate(self::LIMIT);
                $model = $this->decodeContent($model);
                $articles[$a][$s] = $model;
            }
        }

        return $articles;
    }

    /**
     * 取得留言資料
     * @param $user : User model
     * @param int[] $anonymous : 是否匿名
     * @param string[] $status : 文章狀態
     * @return array
     */
    public function comments($user, $anonymous = [0, 1], $status = ['published', 'delete']): array
    {
        $comments = [];
        foreach ($anonymous as $a) {
            foreach ($status as $s) {
                $model = $user->allComments()
                    ->with(['articleRel'])
                    ->withCount(['likes']);
                $model = $this->makeStatusWhere($model, $s);
                $model = $model->anonymous($a)
                    ->sortCreatedAt()
                    ->paginate(self::LIMIT);
                $model = $this->decodeContent($model);
                $comments[$a][$s] = $model;
            }
        }

        return $comments;
    }

    /**
     * 編制文章狀態的where語句
     * 因為刪除狀態比較特殊，他把刪除跟軟刪除放在一起..
     * @param $model : 文章model
     * @param $status : 文章狀態
     * @return array
     */
    private function makeStatusWhere($model, $status)
    {
        if ($status == 'delete') {
            $model = $model->whereIn('status', ['delete', 'softdelete']);
        } else {
            $model = $model->where('status', $status);
        }
        return $model;
    }

    /**
     * 轉換content格式..
     * 丟到transformer再轉的話又會多繞幾圈..在這先轉一轉
     * @param $model
     * @return mixed
     */
    private function decodeContent($model) {
        foreach($model as $m) {
            $m->content = $this->decodeService->decode($m->content);
        }
        return $model;
    }
}
