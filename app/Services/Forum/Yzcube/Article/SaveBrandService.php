<?php
/*
 |--------------------------------------
 |  儲存品牌標籤 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\Article;

class SaveBrandService
{
    /**
     * 儲存品牌標籤
     */
    public function run($brand_id, $request)
    {
        $article    = $request['article'];
        $yzcubeUser = $request['yzcube_user'];

        if (!$article->allBrands()->where('brand_id', $brand_id)->exists()) {
            $pivotArray['user_created']   = 0;
            $pivotArray['yzcube_user_id'] = $yzcubeUser->id;
        }
        $article->allBrands()->syncWithoutDetaching([$brand_id => $pivotArray]);

        return $article->allBrands()
                        ->where('brand_id', $brand_id)
                        ->first();
    }
}
