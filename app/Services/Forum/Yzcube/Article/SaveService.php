<?php
/*
 |--------------------------------------
 |  儲存文章 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\Article;

use App\Models\ForumArticle as Article;
use App\Models\ForumAutoReply as AutoReply;
use App\Services\Tools\Content\EncodeService;
use App\Services\Image\CreateImageService;
use App\Services\Forum\Article\PostTagService;
use App\Services\Mail\Forum\ArticleChangeCategoryService;
use App\Services\Mail\Forum\DeleteArticleService;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\GetClientIpTrait;
use App\Jobs\Forum\SendEmailArticleAtUsers;
use App\Jobs\Forum\SendEmailArticleTracks;
use App\Jobs\Forum\SendEmailNewArticle;
use Illuminate\Support\Arr;

class SaveService
{
    private $article;
    private $autoReply;
    private $encodeService;
    private $createImageService;
    private $postTagService;
    private $articleChangeCategoryService;
    private $deleteArticleService;

    use ApiErrorTrait;
    use GetClientIpTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Article $article,
        AutoReply $autoReply,
        EncodeService $encodeService,
        CreateImageService $createImageService,
        PostTagService $postTagService,
        ArticleChangeCategoryService $articleChangeCategoryService,
        DeleteArticleService $deleteArticleService
    ) {
        $this->article                      = $article;
        $this->autoReply                    = $autoReply;
        $this->encodeService                = $encodeService;
        $this->createImageService           = $createImageService;
        $this->postTagService               = $postTagService;
        $this->articleChangeCategoryService = $articleChangeCategoryService;
        $this->deleteArticleService         = $deleteArticleService;
    }

    /**
     * 儲存文章
     */
    public function run($request)
    {
        $article = $this->article->find($request['article_id']);
        if (!$article) {
            $article = clone $this->article;
        }

        // 狀態變更
        $status           = $article->status ?: 'pending';
        $isNewPublish     = ($request['status'] == 'published' && $request['status'] != $article->status);
        $isNewDelete      = (in_array($request['status'], ['softdelete', 'delete']) && $article->status == 'published');
        $isChangeCategory = ($request['category_id'] && $article->category_id != $request['category_id']) ? $article->category_id : '';

        // 作者
        if ($request['user_id']) {
            $article->user_id = $request['user_id'];
        }

        // 是否匿名
        if (isset($request['is_anonymous'])) {
            $article->is_anonymous = $request['is_anonymous'];
        }

        // 標題
        if ($request['title']) {
            $article->title = $request['title'];
        }

        // 分類
        if ($request['category_id']) {
            $article->category_id = $request['category_id'];
        }

        // 活動表單
        if (isset($request['event_id'])) {
            $article->event_id = $request['event_id'];
        }

        // 狀態
        if ($request['status']) {
            $article->status = $request['status'];
        }

        // 設定發佈時間
        if (isset($request['set_publish_at'])) {
            $article->set_publish_at = $request['set_publish_at'];
        }

        // 是否排除SEO
        if (isset($request['is_exclude_seo'])) {
            $article->is_exclude_seo = $request['is_exclude_seo'];
        }

        // 是否置頂
        if (isset($request['is_top'])) {
            $article->is_top = $request['is_top'];
        }

        // 置頂結束時間
        if (isset($request['top_stop_at'])) {
            $article->top_stop_at = $request['top_stop_at'];
        }

        // 是否為負評
        if (isset($request['is_complain'])) {
            $article->is_complain = $request['is_complain'];
        }

        // 儲存文章
        $article->ip             = $article->ip ?: $this->getClientIp();
        $article->softdeleted_at = $isNewDelete ? now() : $article->softdeleted_at;
        $article->save();

        // 內容
        if (isset($request['content'])) {

            // 解析content，並儲存圖片
            $contentObj = $this->encodeService->run($request['content'], 'forum_article', $article->id);

            // 更新content & summary
            $oldContent = $article->content;
            $article->content = $contentObj->content;
            $article->summary = json_encode($contentObj->summary, JSON_UNESCAPED_UNICODE);
            $article->save();

            // 更新At Users
            $atUserIDs = Arr::pluck($contentObj->atUsers, 'user_id');
            $article->atUsers()->sync($atUserIDs);

            // 重新編輯已發佈文章，需新增文章編輯紀錄，儲存編輯前的文章內容
            if ($article->status == 'published' && !$isNewPublish) {
                $article->logUpdateds()->create(['content' => $oldContent]);
            }

            // 取出所有images
            $imageIds = [];
            foreach ($contentObj->images as $image) {
                $imageIds[] = $image['imageID'];
            }

            // 包含編輯紀錄裡的images
            foreach ($article->logUpdateds as $log) {
                preg_match_all('/\[image:([0-9]{1,})\]/Usi', $log->content, $match, PREG_SET_ORDER);
                foreach ($match as $tag) {
                    $imageIds[] = $tag[1];
                }
            }

            // 更新已刪除的圖片
            $article->images()->whereNotIn('id', $imageIds)->delete();
        }

        // 封面照
        if (isset($request['cover_image'])) {
            $this->createImageService->add([
                'file_name' => $request['cover_image'],
                'type'      => 'forum_cover',
                'target_id' => $article->id,
                'only'      => true,
            ]);
        }

        // 自動回覆功能
        if (isset($request['auto_reply'])) {
            $autoReply = $article->autoReply;

            if (!$autoReply && $request['auto_reply'] == true) {
                $autoReply = $this->autoReply;
            } elseif ($autoReply && $request['auto_reply'] == false) {
                $autoReply->delete();
                unset($autoReply);
            }

            if ($autoReply) {
                $autoReply->article_id      = $article->id;
                $autoReply->type            = ($request['whisper'] == true) ? 'whisper' : 'normal';
                $autoReply->reply_content   = $request['reply_content'];
                $autoReply->whisper_content = ($request['whisper'] == true) ? $request['whisper_content'] : null;
                $autoReply->save();
            }
        }

        // 儲存Tag列表
        $request['article'] = $article;
        $this->postTagService->run($request);

        // 已發佈文章
        if ($article->status == 'published') {

            // 沒有文章內容不可發佈
            if (!$article->content || !$article->summary) {
                $article->status = 'pending';
                $article->save();

                $this->setException('儲存文章內容有誤，請重新編輯文章！');
            }

            // 更新發佈時間
            if ($isNewPublish || !$article->published_at) {
                $article->published_at = now();
                $article->save();
            }

            // WeddingDay 文章標記通知
            SendEmailArticleAtUsers::dispatch($article);

            // WeddingDay 追蹤文章更新通知
            SendEmailArticleTracks::dispatch($article, $article->author);

            // WeddingDay 文章異動分類通知
            if ($isChangeCategory) {
                $this->articleChangeCategoryService->sendMail($article->author, $article, $isChangeCategory);
            }

            // WeddingDay 發佈文章互動通知
            if ($isNewPublish && $article->category->is_promotion) {
                SendEmailNewArticle::dispatch($article);
            }
        }

        // WeddingDay 文章下架通知
        if ($isNewDelete && $article->author) {
            $this->deleteArticleService->sendMail($article->author, $article);
        }

        // 更新作者的文章和留言總數
        if ($article->author) {
            $article->author->totalArticleCommentCount();
            $article->author->save();
        }

        return $article;
    }
}
