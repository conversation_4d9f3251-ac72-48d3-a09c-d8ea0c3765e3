<?php
/*
 |--------------------------------------
 |  更新留言 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\Article;

use App\Services\Mail\Forum\DeleteCommentService;

class CommentService
{
    private $deleteCommentService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        DeleteCommentService $deleteCommentService
    ) {
        $this->deleteCommentService = $deleteCommentService;
    }

    /**
     * 更新留言
     */
    public function run($request)
    {
        // 取得文章及留言
        $article = $request['article'];
        $comment = $article->allComments()->find($request['comment_id']);

        // 狀態變更
        $isNewDelete = (in_array($request['status'], ['softdelete', 'delete']) && $comment->status == 'published');

        // 狀態
        $comment->status         = $request['status'];
        $comment->softdeleted_at = $isNewDelete ? now() : $comment->softdeleted_at;
        $comment->save();

        // WeddingDay 留言下架通知
        if ($isNewDelete) {
            $this->deleteCommentService->sendMail($comment->author, $comment);
        }

        // 更新文章留言數
        $article->totalCommentCount();
        $article->save();

        // 更新作者的文章和留言總數
        $comment->author->totalArticleCommentCount();
        $comment->author->save();
    }
}
