<?php
/*
 |--------------------------------------
 |  儲存分類 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\Category;

use App\Models\ForumCategory as Category;

class SaveService
{
    private $category;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(Category $category)
    {
        $this->category = $category;
    }

    /**
     * 儲存分類
     */
    public function run($request)
    {
        $category = $this->category->find($request['category_id']);
        if (!$category) {
            $category = clone $this->category;
        }

        $category->name                = $request['name'];
        $category->description         = $request['description'];
        $category->article_placeholder = $request['article_placeholder'];
        $category->is_public_use       = $request['is_public_use'];
        $category->save();

        return $category;
    }
}
