<?php
/*
 |--------------------------------------
 |  數據看板 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Forum\Yzcube\Report;

use App\Models\ForumArticle as Article;
use App\Models\ForumComment as Comment;

class DashboardService
{
    protected $article;
    protected $comment;
    protected $todayRange;
    protected $yesterdayRange;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        Article $article,
        Comment $comment
    ) {
        $this->article = $article;
        $this->comment = $comment;

        $this->todayRange = [
            date('Y-m-d 00:00:00'),
            date('Y-m-d 23:59:59')
        ];

        $this->yesterdayRange = [
            date('Y-m-d H:i:s', strtotime($this->todayRange[0].'-1 day')),
            date('Y-m-d H:i:s', strtotime($this->todayRange[1].'-1 day'))
        ];
    }

    /**
     * 數據看板
     *
     * @return array data
     */
    public function run($request)
    {
        // 報表
        $data = [
            // 昨日
            'yesterday' => [
                'article_count' => $this->article->release()
                                                    ->whereBetween('published_at', $this->yesterdayRange)
                                                    ->count(),
                'comment_count' => $this->comment->live()
                                                    ->whereNull('parent_id')
                                                    ->whereBetween('forum_comments.created_at', $this->yesterdayRange)
                                                    ->count(),
                'reply_count' => $this->comment->live()
                                                ->whereNotNull('parent_id')
                                                ->whereBetween('forum_comments.created_at', $this->yesterdayRange)
                                                ->count(),
            ],

            // 今日
            'today' => [
                'articles' => $this->article->release()
                                            ->select('is_anonymous', 'user_id') // 列表需要的欄位 (需覆蓋release()內的select())
                                            ->whereBetween('published_at', $this->todayRange)
                                            ->get(),
                'comments' => $this->comment->live()
                                            ->select('is_anonymous', 'user_id')
                                            ->whereNull('parent_id')
                                            ->whereBetween('forum_comments.created_at', $this->todayRange)
                                            ->get(),
                'replies' => $this->comment->live()
                                            ->select('is_anonymous', 'user_id')
                                            ->whereNotNull('parent_id')
                                            ->whereBetween('forum_comments.created_at', $this->todayRange)
                                            ->get(),
            ],

            // 所有
            'history' => [
                'articles' => $this->article->release()
                                            ->select('is_anonymous', 'user_id')
                                            ->get(),
                'comments' => $this->comment->live()
                                            ->select('is_anonymous', 'user_id')
                                            ->whereNull('parent_id')
                                            ->get(),
                'replies' => $this->comment->live()
                                            ->select('is_anonymous', 'user_id')
                                            ->whereNotNull('parent_id')
                                            ->get(),
            ],
        ];

        // 篩選報表
        if ($request['start_date'] && $request['end_date']) {
            $data['filter'] = $this->getFilterReport($request);
        }

        return $data;
    }

    /**
     * 篩選報表
     *
     * @return array data
     */
    protected function getFilterReport($request)
    {
        $articles = $this->article->release()->select('is_anonymous', 'user_id');
        $comments = $this->comment->live()->select('is_anonymous', 'user_id')->whereNull('parent_id');
        $replies  = $this->comment->live()->select('is_anonymous', 'user_id')->whereNotNull('parent_id');

        // 分類
        if ($request['category_id']) {
            $category_id = $request['category_id'];
            $articles = $articles->where('category_id', $category_id);
            $comments = $comments->whereHas('article', function($q) use ($category_id) {
                $q->where('category_id', $category_id);
            });
            $replies = $replies->whereHas('article', function($q) use ($category_id) {
                $q->where('category_id', $category_id);
            });
        }

        // 日期區間
        if ($request['start_date'] && $request['end_date']) {
            $filterRange = [
                date('Y-m-d 00:00:00', strtotime($request['start_date'])),
                date('Y-m-d 23:59:59', strtotime($request['end_date']))
            ];
            $articles = $articles->whereBetween('published_at', $filterRange);
            $comments = $comments->whereBetween('forum_comments.created_at', $filterRange);
            $replies  = $replies->whereBetween('forum_comments.created_at', $filterRange);
        }

        return [
            'articles' => $articles->get(),
            'comments' => $comments->get(),
            'replies'  => $replies->get(),
        ];
    }
}
