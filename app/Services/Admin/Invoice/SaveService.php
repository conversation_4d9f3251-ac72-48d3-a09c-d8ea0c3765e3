<?php
/*
 |--------------------------------------
 |  商家後台-發票管理-開立發票儲存
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Invoice;

use App\Services\Admin\Invoice\MakeOrderNumberService;
use App\Services\Payment\EzPay\InvoiceHandle;
use App\Models\Store;
use App\Models\EventOrder;
use App\Models\InvoiceSetting;
use App\Jobs\Invoice\AddGoogleSheets;
use App\Traits\ApiErrorTrait;

class SaveService
{
    private $eventOrder;
    private $makeOrderNumberService;
    private $invoiceHandle;

    use ApiErrorTrait;

    public function __construct(
        EventOrder $eventOrder,
        MakeOrderNumberService $makeOrderNumberService,
        InvoiceHandle $invoiceHandle
    ) {
        $this->eventOrder             = $eventOrder;
        $this->makeOrderNumberService = $makeOrderNumberService;
        $this->invoiceHandle          = $invoiceHandle;
    }

    /**
     * 開立發票儲存
     */
    public function run(Store $store, $request)
    {
        // 驗證商家發票設定
        $invoiceSetting = $store->invoiceSetting;
        if (!$invoiceSetting) {
            $this->setException('找不到商家的發票設定！');
        }

        // 若輸入的訂單編號，等於活動訂單的訂單編號
        $eventOrder = $this->eventOrder->where('order_no', $request['order_no'])->first();
        if ($eventOrder) {

            // 驗證發票設定必須是同一個商家
            if ($eventOrder->invoice_setting_id != $invoiceSetting->id) {
                $this->setException('活動訂單所設定的賣家企業實名認證，與此商家不同！');
            }

            // 驗證不得重複開立發票
            if ($eventOrder->invoice_status == 'success' OR $eventOrder->invoices()->where('invoice_status', 'success')->exists()) {
                $this->setException('此活動訂單已開立發票，不行重複開立！');
            }

            // 驗證活動訂單無須開立發票
            if ($eventOrder->invoice_status == 'unnecessary' OR !$eventOrder->amount) {
                $this->setException('無須開立發票！');
            }

            // 驗證發票總金額
            if ($eventOrder->amount != $request['total']) {
                $this->setException('發票總金額與活動訂單的總金額不一致！');
            }
        }

        // 製作自訂訂單編號
        $merchantOrderNo = $this->makeOrderNumberService->getUniqueNo($invoiceSetting);

        // ezPay 開立電子發票
        $invoiceResult = $this->invoiceHandle->handle('invoice_issue', [
            'merchantOrderNo' => $merchantOrderNo,
            'invoiceSetting'  => $invoiceSetting,
            'data'            => $request,
        ]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->setException('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
        }

        // 更新活動訂單的發票狀態
        if ($eventOrder) {
            $eventOrder->invoice_status = 'success';
            $eventOrder->save();
        }

        // 儲存發票記錄
        $result  = $invoiceResult->Result;
        $invoice = $this->createInvoice($invoiceSetting, $request, $result, true);

        // 發票管理-Google試算表-新增紀錄 use Job
        if ($invoiceSetting->spreadsheet_id) {
            AddGoogleSheets::dispatch($invoice);
        }

        // 回傳給前端轉址到發票的詳細內容頁
        $result->invoice_id = $invoice->id;

        return $result;
    }

    /**
     * 儲存發票記錄
     */
    public function createInvoice(InvoiceSetting $invoiceSetting, $request, $result, $addInvoiceProducts = false)
    {
        // 新增發票記錄
        $invoice = $invoiceSetting->invoices()->create([
            'seller_name'       => $invoiceSetting->seller_name,
            'seller_ubn'        => $invoiceSetting->seller_ubn,
            'merchant_order_no' => $result->MerchantOrderNo,
            'type'              => $request['type'],
            'buyer_ubn'         => $request['buyer_ubn'],
            'buyer_name'        => $request['buyer_name'],
            'buyer_email'       => $request['buyer_email'],
            'carrier_type'      => $request['carrier_type'],
            'carrier_number'    => $request['carrier_number'],
            'order_no'          => $request['order_no'],
            'note'              => $request['note'],
            'sales'             => $request['sales'],
            'tax'               => $request['tax'],
            'total'             => $result->TotalAmt,
            'invoice_number'    => $result->InvoiceNumber,
            'random_number'     => $result->RandomNum,
        ]);

        // 新增發票品項記錄
        $items = json_decode($request['items'], true);
        $invoice->items()->createMany($items);

        // 新增發票品項
        if ($addInvoiceProducts) {
            $data = [];
            $productNames = $invoiceSetting->products->pluck('name')->toArray();
            $productCount = count($productNames);
            foreach ($items as $item) {
                // 品項名稱一樣就不需要新增
                if (in_array($item['name'], $productNames)) {
                    continue;
                }
                $data[] = [
                    'name'     => $item['name'],
                    'sequence' => ++$productCount,
                ];
            }
            $invoiceSetting->products()->createMany($data);
        }

        // 新增電子發票通知紀錄
        if ($invoice->type == 'email') {
            $invoice->logEmails()->create(['email' => $invoice->buyer_email]);
        }

        return $invoice;
    }
}
