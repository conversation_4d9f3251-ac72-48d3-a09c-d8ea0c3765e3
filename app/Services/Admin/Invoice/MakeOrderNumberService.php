<?php
/*
 |--------------------------------------
 |  製作發票的自訂訂單編號
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Invoice;

use App\Models\InvoiceSetting;
use App\Models\Invoice;
use Illuminate\Support\Str;

class MakeOrderNumberService
{
    private $invoice;

    public function __construct(
        Invoice $invoice
    ) {
        $this->invoice = $invoice;
    }

    public function getUniqueNo(InvoiceSetting $invoiceSetting)
    {
        // 產生自訂訂單編號
        $orderNo = 'IV'.$invoiceSetting->id.'_'.date('ymdHi').Str::random(2);

        // 驗證唯一值
        while ($this->invoice->where('invoice_setting_id', $invoiceSetting->id)->where('merchant_order_no', $orderNo)->exists()) {
            $orderNo = 'IV'.$invoiceSetting->id.'_'.date('ymdHi').Str::random(2);
        }

        return $orderNo;
    }
}
