<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-儲存方案 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Image\CreateImageService;
use App\Services\Image\DeleteImageService;

class ServiceSaveService
{
    private $createImageService;
    private $deleteImageService;

    public function __construct(
        CreateImageService $createImageService,
        DeleteImageService $deleteImageService
    ) {
        $this->createImageService = $createImageService;
        $this->deleteImageService = $deleteImageService;
    }

    /**
     * 儲存方案
     */
    public function run(Store $store, $request)
    {
        // 更新或新增商家方案
        $storeService = $store->services()->updateOrCreate([
            'id' => $request['service_id'],
        ], [
            'activity_id' => $request['activity_id'],
            'status'      => $request['status'],
            'name'        => $request['name'],
            'min_price'   => $request['min_price'],
            'max_price'   => $request['max_price'],
            'is_private'  => $request['is_private'],
            'type'        => $request['type'],
            'description' => $request['description'],
            'edited_at'   => now(),
        ]);

        // 更新或新增婚宴場地的方案資訊
        if ($store->type == 5) {
            $storeService->venueInfo()->updateOrCreate([
                'service_id' => $storeService->id,
            ], [
                'has_tip'            => $request['has_tip'],
                'priced_by'          => $request['priced_by'],
                'seater'             => $request['seater'],
                'has_child_discount' => $request['has_child_discount'],
            ]);
        }

        // 更新或新增喜餅的方案資訊
        if ($store->type == 10) {
            $weddingcakeInfo = $storeService->weddingcakeInfo()->updateOrCreate([
                'service_id' => $storeService->id,
            ], [
                'limit_type'     => $request['limit_type'],
                'limit_value'    => $request['limit_value'] ?: NULL,
                'condition_type' => $request['condition_type'],
                'discount_type'  => $request['discount_type'],
            ]);

            // 更新喜餅方案的優惠內容
            $weddingcakeInfo->discounts()->delete();
            $discounts = json_decode($request['discounts']);
            foreach ($discounts as $discount) {
                $weddingcakeInfo->discounts()->create([
                    'full_value'     => $discount->full_value ?: NULL,
                    'discount_value' => $discount->discount_value ?: NULL,
                    'has_other'      => $discount->has_other,
                    'other_info'     => $discount->other_info ?: NULL,
                ]);
            }

            // 適用禮盒
            $storeService->allWeddingcakeAlbums()->sync($request['albums']);
        }

        // 方案封面照
        if ($request['cover']) {
            $this->createImageService->add([
                'file_name' => $request['cover'],
                'type'      => 'store_service_cover',
                'target_id' => $storeService->id,
                'only'      => true,
            ]);
        }

        // 方案包含的服務
        if ($request['tags']) {
            $pivotArray = [];
            $tags = json_decode($request['tags']);
            foreach ($tags as $tag) {
                $pivotArray[$tag->key] = [
                    'target_type' => 'service',
                    'value'       => is_numeric($tag->value) ? $tag->value : NULL,
                    'number'      => is_numeric($tag->number) ? $tag->number : NULL,
                ];
            }
            $storeService->tags()->sync($pivotArray);
        }

        // 更新方案說明照
        $images = json_decode($request['images']);
        $this->updateImages($storeService, $images);

        // 更新商家的服務價格區間、最後編輯時間
        $store->updatePriceRange();
        $store->edited_at = now();
        $store->save();

        return $storeService;
    }

    /**
     * 更新方案說明照
     */
    private function updateImages($storeService, $images)
    {
        // 方案說明照
        $serviceImageIds = [];
        foreach ($images as $key => $item) {

            // 更新或新增方案說明照 (store_service_images)
            $serviceImage = $storeService->images()->updateOrCreate([
                'id' => $item->id,
            ], [
                'description' => $item->description,
                'sequence'    => $key + 1,
            ]);

            // 更新或新增方案說明照 (images)
            $this->createImageService->add([
                'file_name' => $item->file_name,
                'type'      => 'store_service_image',
                'target_id' => $serviceImage->id,
            ]);

            // 記錄儲存的編號
            $serviceImageIds[] = $serviceImage->id;
        }

        // 刪除多餘的方案說明照
        $oldImages = $storeService->images()
                                    ->whereNotIn('id', $serviceImageIds)
                                    ->get();
        foreach ($oldImages as $item) {
            if ($item->image) {
                $this->deleteImageService->delete(['id' => $item->image->id]);
            }
            $item->delete();
        }
    }
}
