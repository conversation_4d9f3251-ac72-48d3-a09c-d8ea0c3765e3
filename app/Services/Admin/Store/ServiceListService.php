<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-方案列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Tools\KeywordSearch\SearchService;

class ServiceListService
{
    /**
     * 方案列表
     */
    public function run(Store $store, $request)
    {
        // 商家的所有方案
        $services = $store->sortServices();

        // 活動方案ID
        if ($request['activity_id']) {
            $services = $services->where('activity_id', $request['activity_id']);
        } else {
            $services = $services->whereNull('activity_id');
        }

        // 總數 (要區隔活動方案)
        $total = $services->count();

        // 預載入
        $services = $services->with(['cover:target_id,file_name']);

        // 方案類型
        if ($request['type']) {
            $services = $services->where('type', $request['type']);
        }

        // 狀態
        if ($request['status']) {
            $services = $services->where('status', $request['status']);
        }

        // 搜尋關鍵字
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $services = $keywordSearchService->search($services, $keyword);
        }

        // 分頁
        $services = $services->paginate(52);

        return [
            'storeType' => $store->type,
            'total'     => $total,
            'services'  => $services,
        ];
    }
}
