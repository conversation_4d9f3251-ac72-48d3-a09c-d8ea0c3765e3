<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-儲存影片 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;

class VideoSaveService
{
    private $storeTypeKey;

    /**
     * 儲存影片
     */
    public function run(Store $store, $request)
    {
        // 商家類別索引值
        $this->storeTypeKey = $store->typeKeyList[$store->type];

        // 取得影片嵌入資訊
        $oembedData = json_decode($request['oembed_data']);

        // 更新或新增商家影片
        $video = $store->videos()->updateOrCreate([
            'id' => $request['video_id'],
        ], [
            'status'      => $request['status'],
            'name'        => $request['name'],
            'url'         => $request['url'],
            'description' => $request['description'],
            'oembed_data' => $oembedData,
            'cover_url'   => $oembedData->thumbnail_url,
            'edited_at'   => now(),
        ]);

        // 更新不同商家類型的客製化欄位
        $this->updateCustomVideoForStoreType($video, $request);

        return $video;
    }

    /**
     * 更新不同商家類型的客製化作品欄位
     *
     * @param int $location_type 地點類型 (婚攝婚錄&婚佈)
     * @param int $brand_id 宴客地點 (婚攝婚錄)、佈置地點 (婚佈)
     * @param int $members 參與成員 (拍婚紗&婚攝婚錄)
     */
    private function updateCustomVideoForStoreType($video, $request)
    {
        $data       = NULL;
        $hasMembers = false;

        // 商家類別索引值
        switch ($this->storeTypeKey) {

            // 拍婚紗
            case 'studio':
                $hasMembers = true;
                break;

            // 婚攝婚錄
            case 'photographer':
                $data       = $request->only(['location_type', 'brand_id']);
                $hasMembers = true;
                break;

            // 婚禮佈置
            case 'decoration':
                $data = $request->only(['location_type', 'brand_id']);
                break;

            // 新娘秘書 & 婚禮主持人
            case 'makeup':
            case 'host':
                $hasMembers = true;
                break;

            // 其餘的商家類型直接跳出
            default:
                return;
        }

        // 更新不同商家類型的客製化作品欄位
        if (!is_null($data)) {
            $video->storeTypeVideo()->updateOrCreate(['video_id' => $video->id], $data);
        }

        // 參與成員 (拍婚紗/婚攝婚錄/新娘秘書/婚禮主持人)
        if ($hasMembers) {
            $video->members()->sync($request['members']);
        }
    }
}
