<?php
/*
 |--------------------------------------
 |  商家後台-禮服租借管理-新人與訂單資料列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Models\StoreAlbum;
use App\Services\Tools\KeywordSearch\SearchService;

class DressListService
{
    /**
     * 禮服合約列表(新人與訂單資料列表)
     */
    public function run(Store $store, $request)
    {
        // 商家的所有合約
        $dressContracts = $store->sortDressContracts();

        // 總數
        $total = $dressContracts->count();

        // 搜尋婚期
        $startDate = $request['start_date'];
        $endDate = $request['end_date'];
        if ($startDate && $endDate){
            $dressContracts = $dressContracts -> weddingDateRange($startDate, $endDate);
        }


        // 搜尋關鍵字
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $dressContracts = $keywordSearchService->search($dressContracts, $keyword);
        }

        // 分頁
        $dressContracts = $dressContracts->paginate(52);

        return [
            'total'      => $total,
            'contracts'  => $dressContracts,
        ];
    }

    /**
     * 查詢禮服檔期列表
     */
    public function scheduleList(StoreAlbum $album, $request)
    {
        // $request 參數
        $startDate = $request['start_date'] ?? now(-2)->format('Y-m-d');
        $endDate = $request['end_date'] ?? now()->addDays(3)->format('Y-m-d');
        $orderId = $request['order_id'] ?? null;

        // 搜尋婚期、整理日、歸還日、整理日
        // 從「準備天數～整理天數」這整段期間都不能跟其他新人重疊，一但重疊就為不可租借
        // 重疊的訂單
        $overlapOrder = $album->orderAlbums()
                            ->wherePivot('dress_order_id', '!=', $orderId)
                            ->where(function ($q1) use ($startDate, $endDate) {
                                $q1 = $q1->where('tidy_date', '>=', $startDate)
                                        ->where('prepare_date', '<=', $endDate);
                            })->get();

        return [
            'album'        => $album,
            'can_rental'   => $overlapOrder->isEmpty(),
            'orders'       => $album->orderAlbums,
        ];
    }

    /**
     * 禮服訂單列表(即將禮服出件)
     */
    public function orderList(Store $store, $request)
    {
        // $request 參數
        $startDate = $request['start_date'] ?? now()->format('Y-m-d');
        $endDate = $request['end_date'] ?? now()->addDays(7)->format('Y-m-d');

        $orders = $store->sortDressOrders()->orderHasAlbum()->with('contract')->where(function ($q1) use ($startDate, $endDate) {
            $q1 = $q1->orWhereBetween('prepare_date', [$startDate, $endDate])
                    ->orWhereBetween('wedding_date', [$startDate, $endDate])
                    ->orWhere(function ($q2) use ($startDate, $endDate) {
                        $q2 = $q2->where('prepare_date', '<=', $startDate)
                                ->where('wedding_date', '>=', $endDate);
                    });
        })->get();

        return $orders;
    }
}
