<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-儲存成員 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Image\CreateImageService;

class MemberSaveService
{
    private $createImageService;
    private $storeTypeKey;

    public function __construct(
        CreateImageService $createImageService
    ) {
        $this->createImageService = $createImageService;
    }

    /**
     * 儲存成員
     */
    public function run(Store $store, $request)
    {
        // 商家類別索引值
        $this->storeTypeKey = $store->typeKeyList[$store->type];

        // 更新或新增商家成員
        $member = $store->members()->updateOrCreate([
            'id' => $request['member_id'],
        ], [
            'name'        => $request['name'],
            'title'       => $request['title'],
            'description' => $request['description'],
        ]);

        // 成員照
        if ($request['cover']) {
            $this->createImageService->add([
                'file_name' => $request['cover'],
                'type'      => 'store_member',
                'target_id' => $member->id,
                'only'      => true,
            ]);
        }

        return $member;
    }
}
