<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-儲存婚宴場地廳房 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Image\CreateImageService;

class VenueRoomSaveService
{
    private $createImageService;

    public function __construct(
        CreateImageService $createImageService
    ) {
        $this->createImageService = $createImageService;
    }

    /**
     * 儲存婚宴場地廳房
     */
    public function run(Store $store, $request)
    {
        // 把修改前的合併廳房先記錄起來
        $newCombineIds = $request['combine_rooms'] ?: [];
        $oldCombineIds = $store->venueRooms->find($request['room_id'])->combine_rooms ?? [];

        // 更新或新增
        $venueRoom = $store->venueRooms()->updateOrCreate([
            'id' => $request['room_id'],
        ], [
            'status'             => $request['status'],
            'name'               => $request['name'],
            'narrates'           => $request['narrates'],
            'is_underground'     => $request['is_underground'],
            'floor'              => $request['floor'],
            'min_number'         => $request['min_number'],
            'max_number'         => $request['max_number'],
            'has_banquet'        => $request['has_banquet'],
            'has_witness'        => $request['has_witness'],
            'has_ceremony'       => $request['has_ceremony'],
            'tables'             => $request['tables'],
            'guest_table_number' => $request['guest_table_number'],
            'main_table_number'  => $request['main_table_number'],
            'has_bridal_room'    => $request['has_bridal_room'],
            'combine_rooms'      => $newCombineIds,
            'combine_min_number' => $request['combine_min_number'],
            'combine_max_number' => $request['combine_max_number'],
            'has_wifi'           => $request['has_wifi'],
            'has_projection'     => $request['has_projection'],
            'has_led'            => $request['has_led'],
            'has_sound'          => $request['has_sound'],
            'has_light'          => $request['has_light'],
            'has_stage'          => $request['has_stage'],
            'has_pillar'         => $request['has_pillar'],
            'backplane'          => $request['backplane'],
            'description'        => $request['description'],
            'edited_at'          => now(),
        ]);

        // 同步移除要取消的合併廳房
        $removeIds = array_diff($oldCombineIds, $newCombineIds);
        foreach ($removeIds as $removeId) {
            $other = $store->venueRooms->find($removeId);
            $otherRooms = $other->combine_rooms ?: [];
            if (($key = array_search($venueRoom->id, $otherRooms)) !== false) {
                unset($otherRooms[$key]);
                $other->combine_rooms = $otherRooms;
                $other->save();
            }
        }

        // 同步加入要新增的合併廳房
        foreach ($newCombineIds as $addId) {
            $other = $store->venueRooms->find($addId);
            $otherRooms = $other->combine_rooms ?: [];
            if (array_search($venueRoom->id, $otherRooms) === false) {
                $otherRooms[] = $venueRoom->id;
                $other->combine_rooms = $otherRooms;
                $other->save();
            }
        }

        // 先刪除所有的廳房照片 (為了不想只因為排序欄位多開一個table)
        $venueRoom->images()->delete();

        // 新增廳房照片
        foreach ($request['images'] as $image) {
            $data = $this->createImageService->add([
                'file_name' => $image,
                'type'      => 'venue_room',
                'target_id' => $venueRoom->id,
            ]);

            // 更新廳房封面照編號
            if ($image == $request['cover']) {
                $venueRoom->cover_id = $data['id'];
                $venueRoom->save();
            }
        }

        // 更新商家的最後編輯時間
        $store->edited_at = now();
        $store->save();

        return $venueRoom;
    }
}
