<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-相本列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Models\StoreTag;
use App\Models\UserCollect;
use App\Services\Tools\KeywordSearch\SearchService;

class AlbumListService
{
    private $storeTag;
    private $userCollect;

    public function __construct(
        StoreTag $storeTag,
        UserCollect $userCollect
    ) {
        $this->storeTag    = $storeTag;
        $this->userCollect = $userCollect;
    }

    /**
     * 相本列表
     */
    public function run(Store $store, $request)
    {
        // 數據統計-30天內
        $afterAt      = now()->subDays(30);
        $storeTypeKey = $store->typeKeyList[$store->type];

        // 商家的所有相本
        $albums = $store->sortAlbums();
        $total  = $albums->count();

        // 取得數據
        $albums = $albums->withCount([
                            'images',
                            'orderAlbums'
                            // (in_array($storeTypeKey, ['dress', 'weddingcake'])) ? 'userCollects as collect_count' : 'imageCollects as collect_count' =>
                            // function($q) use ($afterAt) {
                            //     $q->where($this->userCollect->getTable().'.created_at', '>=', $afterAt);
                            // }, // 收藏數
                        ]);

        // 預載入
        $albums = $albums->with([
                            'cover:id,file_name',
                            'tags' => function($q) use ($storeTypeKey) {
                                        $q->select($this->storeTag->getTable().'.id')
                                            ->simpleTypes($storeTypeKey);
                                    },
                            // 'logGaPageViews' => function($q) use ($afterAt) {
                            //                         $q->select('target_id', 'pageviews')
                            //                             ->where('created_date', '>=', $afterAt);
                            //                     }, // 瀏覽數
                        ]);

        // 作品類型 (拍婚紗)、佈置類型 (婚佈)
        if ($request['type'] && in_array($storeTypeKey, ['studio', 'decoration'])) {
            $albums = $albums->whereHas($storeTypeKey.'Album', function($q) use ($request) {
                                $q->where('type', $request['type']);
                            });
        }

        // 禮服標籤 (禮服)、禮盒類型標籤 (喜餅)
        if ($request['tag'] && in_array($storeTypeKey, ['dress', 'weddingcake'])) {
            $albums = $albums->whereHas('tags', function($q) use ($request) {
                                $q->where($this->storeTag->getTable().'.id', $request['tag']);
                            });
        }

        // 狀態
        if ($request['status']) {
            $albums = $albums->where('status', $request['status']);
        }

        // 搜尋關鍵字
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $albums = $keywordSearchService->search($albums, $keyword);
        }

        // 分頁
        $albums = $albums->paginate(52);

        // 簡易類型標籤：禮服類型標籤 (禮服)、禮盒類型標籤 (喜餅)
        $simpleTags = $this->storeTag->simpleTypes($storeTypeKey)->sort()->pluck('name', 'id');

        return [
            'storeTypeKey' => $storeTypeKey,
            'total'        => $total,
            'albums'       => $albums,
            'simpleTags'   => $simpleTags,
        ];
    }
}
