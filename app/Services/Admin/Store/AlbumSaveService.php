<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-儲存作品 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Image\CreateImageService;
use App\Services\Image\DeleteImageService;

class AlbumSaveService
{
    private $createImageService;
    private $deleteImageService;
    private $storeTypeKey;

    public function __construct(
        CreateImageService $createImageService,
        DeleteImageService $deleteImageService
    ) {
        $this->createImageService = $createImageService;
        $this->deleteImageService = $deleteImageService;
    }

    /**
     * 儲存作品
     */
    public function run(Store $store, $request)
    {
        // 商家類別索引值
        $this->storeTypeKey = $store->typeKeyList[$store->type];

        // 如果商家類別是禮服，有禮服類型，但沒有名稱時，要自動產生名字
        if($store->type === 2 && !$request['name'] && $request['order_dress_type']) {
            // 取得最新一筆禮服
            $lastAlbum = $store->albums()
                                ->whereYear('created_at', date('Y'))
                                ->join('dress_albums', function($join) use ($request) {
                                    $join->on('store_albums.id', '=', 'dress_albums.album_id')
                                        ->where('order_dress_type', $request['order_dress_type']);
                                })
                                ->orderBy('dress_albums.serial_name', 'DESC')
                                ->first();
            
            // 禮服類型+年份(兩位數)+系統流水號(補0三位數)
            $orderDressTypeYear = $request['order_dress_type'] . date('y');
            $serialNumber = $lastAlbum ? ($lastAlbum->serial_name ?? 0) + 1 : 1;
            $serialNumberStr = str_pad($serialNumber, 3, '0', STR_PAD_LEFT);
            
            $request['name'] = $orderDressTypeYear . $serialNumberStr;
            $request['serial_name'] =  $serialNumber;
        }

        // 更新或新增商家作品
        $album = $store->albums()->updateOrCreate([
            'id' => $request['album_id'],
        ], [
            'status'      => $request['status'],
            'name'        => $request['name'],
            'description' => $request['description'],
            'cover_id'    => $request['cover_id'],
            'edited_at'   => now(),
        ]);

        // 更新不同商家類型的客製化欄位
        $this->updateCustomAlbumForStoreType($album, $request);

        // 更新作品照
        $albumImageIds = [];
        $images = json_decode($request['images']);
        foreach ($images as $key => $item) {

            // 更新或新增商家作品照 (store_album_images)
            $albumImage = $album->images()->updateOrCreate([
                'id' => $item->id,
            ], [
                'description' => $item->description ?? NULL,
                'sequence'    => $key + 1,
            ]);

            // 更新或新增商家作品照 (images)
            $this->createImageService->add([
                'file_name' => $item->file_name,
                'type'      => 'store_album_image',
                'target_id' => $albumImage->id,
            ]);

            // 作品照標籤 (拍婚紗&婚攝婚錄&新娘秘書)
            if (isset($item->tags)) {
                $pivotArray = [];
                foreach ($item->tags as $tag) {
                    $pivotArray[$tag] = ['target_type' => 'album_image'];
                }
                $albumImage->tags()->sync($pivotArray);
            }

            // 拍婚紗商家的照片欄位
            if ($this->storeTypeKey == 'studio') {
                if (empty($item->city_id) && empty($item->location)) {
                    $albumImage->studioImage()->delete();
                } else {
                    $albumImage->studioImage()->updateOrCreate([], [
                        'city_id'  => $item->city_id,
                        'location' => $item->location,
                    ]);
                }
            }

            // 記錄儲存的編號
            $albumImageIds[] = $albumImage->id;
        }

        // 刪除多餘的商家作品照
        $album->images()
                    ->whereNotIn('id', $albumImageIds)
                    ->get()
                    ->each(function($item) {
                        if ($item->image) {
                            $this->deleteImageService->delete(['id' => $item->image->id]);
                        }
                        $item->delete();
                    });

        // 更新商家的服務價格區間、最後編輯時間
        $store->updatePriceRange();
        $store->edited_at = now();
        $store->save();

        return $album;
    }

    /**
     * 更新不同商家類型的客製化作品欄位
     *
     * @param int $type 作品類型 (拍婚紗)、佈置類型 (婚佈)
     * @param int $location_type 地點類型 (婚攝婚錄&婚佈)
     * @param int $brand_id 宴客地點 (婚攝婚錄)、佈置地點 (婚佈)
     * @param string $order_dress_type 禮服訂單類型 (禮服)
     * @param bool $is_watermark 是否顯示浮水印 (禮服)
     * @param int $price 租金 (禮服)
     * @param int $min_price 最低金額 (婚佈&喜餅)
     * @param int $max_price 最高金額 (婚佈&喜餅)
     * @param int $is_private 價格不公開 (禮服&婚佈)
     * @param int $narrates 特色標語 (婚佈)
     * @param int $tags 禮服標籤 (禮服)、作品集標籤 (婚佈)、禮盒標籤 (喜餅)
     * @param bool $can_customized 是否可以客製化 (喜餅)
     * @param int $min_category 最少種類數 (喜餅)
     * @param int $max_category 最多種類數 (喜餅)
     * @param int $min_quantity 最少數量 (喜餅)
     * @param int $max_quantity 最多數量 (喜餅)
     * @param int $box_length 禮盒長度 (喜餅)
     * @param int $box_width 禮盒寬度 (喜餅)
     * @param int $box_high 禮盒高度 (喜餅)
     * @param string $preservation_method 保存方式 (喜餅)
     * @param int $members 參與成員 (拍婚紗&婚攝婚錄)
     */

    private function updateCustomAlbumForStoreType($album, $request)
    {
        $data       = NULL;
        $hasTags    = false;
        $hasMembers = false;

        // 商家類別索引值
        switch ($this->storeTypeKey) {

            // 拍婚紗
            case 'studio':
                $data       = $request->only(['type']);
                $hasMembers = true;
                break;

            // 婚紗禮服
            case 'dress':
                $data    = $request->only(['price', 'is_private', 'order_dress_type', 'serial_name', 'is_watermark', 'private_note']);
                $hasTags = true;
                break;

            // 婚攝婚錄
            case 'photographer':
                $data       = $request->only(['location_type', 'brand_id']);
                $hasMembers = true;
                break;

            // 婚禮佈置
            case 'decoration':
                $data    = $request->only(['type', 'location_type', 'brand_id', 'min_price', 'max_price', 'is_private', 'narrates']);
                $hasTags = true;
                break;

            // 喜餅
            case 'weddingcake':
                $data               = $request->only(['min_price', 'max_price', 'can_customized', 'min_category', 'max_category', 'min_quantity', 'max_quantity', 'box_length', 'box_width', 'box_high', 'is_meat', 'is_lacto_vegetarian', 'is_ovo_lacto_vegetarian', 'is_vegetarian', 'preservation_method']);
                $data['box_volume'] = $data['box_length'] * $data['box_width'] * $data['box_high'];
                $hasTags            = true;

                // 適用方案
                $album->allWeddingcakeServices()->sync($request['services']);
                break;

            // 新娘秘書 & 婚禮主持人
            case 'makeup':
            case 'host':
                $hasMembers = true;
                break;

            // 其餘的商家類型直接跳出
            default:
                return;
        }

        // 更新不同商家類型的客製化作品欄位
        if (!is_null($data)) {
            $album->storeTypeAlbum()->updateOrCreate(['album_id' => $album->id], $data);
        }

        // 禮服標籤 (禮服)、作品集標籤 (婚佈)、禮盒標籤 (喜餅)
        if ($hasTags) {
            $pivotArray = [];
            foreach ($request['tags'] as $tag) {
                $pivotArray[$tag] = ['target_type' => 'album'];
            }
            $album->tags()->sync($pivotArray);
        }

        // 參與成員 (拍婚紗/婚攝婚錄/新娘秘書/婚禮主持人)
        if ($hasMembers) {
            $album->members()->sync($request['members']);
        }

        // 婚紗禮服 更新不公開備註欄位的圖片
        $private_note_images = [];
        if ($this->storeTypeKey === 'dress') {
            foreach ($request['private_note_images'] as $item) {
                $this->createImageService->add([
                    'file_name' => $item,
                    'type'      => 'dress_album_private_note_image',
                    'target_id' => $album->storeTypeAlbum->id,
                ]);

                $private_note_images[] = $item;
            }

            // 刪除多餘的不公開照片
            $album->storeTypeAlbum->privateNoteImages()
                                    ->whereNotIn('file_name', $private_note_images)
                                    ->get()
                                    ->each(function($item) {
                                        $this->deleteImageService->delete(['id' => $item->id]);
                                        $item->delete();
                                    });
        }
    }
}
