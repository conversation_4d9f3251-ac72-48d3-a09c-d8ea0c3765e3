<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-影片列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Models\UserCollect;
use App\Services\Tools\KeywordSearch\SearchService;

class VideoListService
{
    private $userCollect;

    public function __construct(
        UserCollect $userCollect
    ) {
        $this->userCollect = $userCollect;
    }

    /**
     * 影片列表
     */
    public function run(Store $store, $request)
    {
        // 數據統計-30天內
        $afterAt = now()->subDays(30);

        // 商家的所有影片
        $videos = $store->sortVideos();
        $total  = $videos->count();

        // // 取得數據
        // $videos = $videos->withCount([
        //                     'userCollects as collect_count' =>
        //                     function($q) use ($afterAt) {
        //                         $q->where($this->userCollect->getTable().'.created_at', '>=', $afterAt);
        //                     }, // 收藏數
        //                 ]);

        // // 預載入
        // $videos = $videos->with([
        //                     'logGaPageViews' => function($q) use ($afterAt) {
        //                                             $q->select('target_id', 'pageviews')
        //                                                 ->where('created_date', '>=', $afterAt);
        //                                         }, // 瀏覽數
        //                 ]);

        // 狀態
        if ($request['status']) {
            $videos = $videos->where('status', $request['status']);
        }

        // 搜尋關鍵字
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $videos = $keywordSearchService->search($videos, $keyword);
        }

        // 分頁
        $videos = $videos->paginate(52);

        return [
            'total'  => $total,
            'videos' => $videos,
        ];
    }
}
