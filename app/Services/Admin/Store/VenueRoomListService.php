<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-廳房列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Tools\KeywordSearch\SearchService;

class VenueRoomListService
{
    /**
     * 廳房列表
     */
    public function run(Store $store, $request)
    {
        // 商家的所有廳房
        $rooms = $store->sortVenueRooms();
        $total = $rooms->count();

        // 取得數據
        $rooms = $rooms->withCount(['images']);

        // 預載入
        $rooms = $rooms->with(['cover:id,file_name']);

        // 狀態
        if ($request['status']) {
            $rooms = $rooms->where('status', $request['status']);
        }

        // 搜尋關鍵字
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $rooms = $keywordSearchService->search($rooms, $keyword);
        }

        // 分頁
        $rooms = $rooms->paginate(52);

        return [
            'total' => $total,
            'rooms' => $rooms,
        ];
    }
}
