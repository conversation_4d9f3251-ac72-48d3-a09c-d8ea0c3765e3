<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-成員列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Tools\KeywordSearch\SearchService;

class MemberListService
{
    /**
     * 成員列表
     */
    public function run(Store $store, $request)
    {
        // 數據統計-30天內
        $afterAt = now()->subDays(30);

        // 商家的所有成員
        $members = $store->members();
        $total   = $members->count();

        // 取得數據
        $members = $members->withCount(['albums', 'videos']);

        // 預載入
        $members = $members->with([
                            'cover:target_id,file_name',
                            // 'logGaPageViews' => function($q) use ($afterAt) {
                            //                         $q->select('target_id', 'pageviews')
                            //                             ->where('created_date', '>=', $afterAt);
                            //                     }, // 瀏覽數
                        ]);

        // 搜尋關鍵字
        $keyword = trim($request['keyword'] ?? '');
        if ($keyword != '') {
            $keywordSearchService = resolve(SearchService::class);
            $members = $keywordSearchService->search($members, $keyword);
        }

        // 分頁
        $members = $members->paginate(52);

        return [
            'total'   => $total,
            'members' => $members,
        ];
    }
}
