<?php
/*
 |--------------------------------------
 |  商家後台-商家設定-更新商家資訊 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin\Store;

use App\Models\Store;
use App\Services\Image\CreateImageService;
use App\Services\Image\DeleteImageService;
use Illuminate\Support\Arr;

class UpdateService
{
    private $createImageService;
    private $deleteImageService;

    public function __construct(
        CreateImageService $createImageService,
        DeleteImageService $deleteImageService
    ) {
        $this->createImageService = $createImageService;
        $this->deleteImageService = $deleteImageService;
    }

    /**
     * 更新商家資訊
     */
    public function run(Store $store, $request)
    {
        // 商家基本資訊
        $store->name          = $request['name'];
        $store->city_id       = $request['city_id'];
        $store->area_id       = $request['area_id'];
        $store->address       = $request['address'];
        $store->tel           = $request['tel'];
        $store->phone         = $request['phone'];
        $store->contact_email = $request['contact_email'];
        $store->website       = $request['website'];
        $store->fb_page       = $request['fb_page'];
        $store->instagram     = $request['instagram'];
        $store->edited_at     = now();

        // 商家LOGO
        $this->createImageService->add([
            'file_name' => $request['logo'],
            'type'      => 'store_logo',
            'target_id' => $store->id,
            'only'      => true,
        ]);

        // 下架商家只有簡單這幾個欄位
        if ($store->present()->is_leave) {
            $store->save();
            return;
        }

        // 商家基本資訊
        $store->extra_type      = $request['extra_type'];
        $store->address_info    = $request['address_info'];
        $store->map_title       = $request['map_title'];
        $store->open_time       = $request['open_time'];
        $store->close_time      = $request['close_time'];
        $store->round_clock     = $request['round_clock'];
        $store->public_holidays = $request['public_holidays'];
        $store->tel_info        = $request['tel_info'];
        $store->phone_info      = $request['phone_info'];
        $store->line            = $request['line'];
        $store->min_price       = $request['min_price'];
        $store->max_price       = $request['max_price'];
        $store->save();

        // 更新商家的描述內容
        $this->updateDescription($store, 'about', $request['about']); // 關於你們
        $this->updateDescription($store, 'discount', $request['discount']); // 最新優惠
        $this->updateDescription($store, 'traffic', $request['traffic']); // 交通資訊
        $this->updateDescription($store, 'device', $request['device']); // 設備工具
        $this->updateDescription($store, 'experience', $request['experience']); // 資歷介紹
        $this->updateDescription($store, 'other', $request['other']); // 想對新娘說的話

        // 拍婚紗
        if ($store->type == 1) {
            $this->updateDescription($store, 'has_files', $request['has_files']); // 有無檔案全贈
        }

        // 婚紗禮服
        if ($store->type == 2) {
            $this->updateDescription($store, 'look_time', $request['look_time']); // 租借日多久前可參觀禮服
            $this->updateDescription($store, 'reserve_time', $request['reserve_time']); // 租借日多久前可試穿禮服
            $this->updateDescription($store, 'has_trial_fee', $request['has_trial_fee']); // 有無試穿費
            $this->updateDescription($store, 'min_trial_fee', $request['min_trial_fee']); // 試穿費金額-最小
            $this->updateDescription($store, 'max_trial_fee', $request['max_trial_fee']); // 試穿費金額-最大
            $this->updateDescription($store, 'trial_fee_info', $request['trial_fee_info']); // 試穿費說明
            $this->updateDescription($store, 'pieces', $request['pieces']); // 店內婚紗件數
            $this->updateDescription($store, 'rooms', $request['rooms']); // 試衣間數
            $this->updateDescription($store, 'frequency', $request['frequency']); // 試穿件數
            $this->updateDescription($store, 'use_time', $request['use_time']); // 試穿時數
            $this->updateDescription($store, 'people', $request['people']); // 可攜伴數
            $this->updateDescription($store, 'is_photograph', $request['is_photograph']); // 可否拍照
            $this->updateDescription($store, 'is_pet', $request['is_pet']); // 攜帶寵物
            $this->updateDescription($store, 'is_eat', $request['is_eat']); // 可否飲食
        }

        // 新娘秘書
        if ($store->type == 4) {
            $this->updateDescription($store, 'has_trial_fee', $request['has_trial_fee']); // 試妝服務
            $this->updateDescription($store, 'min_trial_fee', $request['min_trial_fee']); // 試妝費金額-最小
            $this->updateDescription($store, 'max_trial_fee', $request['max_trial_fee']); // 試妝費金額-最大
            $this->updateDescription($store, 'trial_fee_info', $request['trial_fee_info']); // 試妝優惠與內容說明
        }

        // 婚宴場地
        if ($store->type == 5) {
            $this->updateDescription($store, 'room_count', $request['room_count']); // 廳房總數
            $this->updateDescription($store, 'min_number', $request['min_number']); // 最小建議人數
            $this->updateDescription($store, 'max_number', $request['max_number']); // 最大建議人數
            $this->updateDescription($store, 'dish_tasting', $request['dish_tasting']); // 試菜優惠說明
        }

        // 喜餅
        if ($store->type == 10) {
            $this->updateDescription($store, 'countdown_wedding_days', $request['countdown_wedding_days']); // 建議於婚期多久前下訂
            $this->updateDescription($store, 'additional_orders', $request['additional_orders']); // 喜餅追加及調整說明
            $this->updateDescription($store, 'free_delivery_method', $request['free_delivery_method']); // 免運計算方式
            $this->updateDescription($store, 'logistics_info', $request['logistics_info']); // 物流配送說明
            $this->updateDescription($store, 'logistics_date_info', $request['logistics_date_info']); // 指定配送日與時段說明
            $this->updateDescription($store, 'logistics_main_island', $request['logistics_main_island']); // 運費計算方式-台灣本島
            $this->updateDescription($store, 'logistics_outer_island', $request['logistics_outer_island']); // 運費計算方式-外島地區
            $this->updateDescription($store, 'logistics_overseas', $request['logistics_overseas']); // 運費計算方式-海外地區
            $this->updateDescription($store, 'has_multiple_shops', $request['has_multiple_shops']); // 有無多間門市

            // 更新商家的營業時間
            $openingHours = json_decode($request['opening_hours'], true);
            $openingHours = data_set($openingHours, '*.target_type', 'store');
            $store->openingHours()->delete();
            $store->openingHours()->createMany($openingHours);

            // 更新喜餅商家的門市資訊
            $this->updateWeddingcakeShops($store, $request['shops']);
        }

        // 商家封面照
        $this->createImageService->add([
            'file_name' => $request['cover'],
            'type'      => 'store_cover',
            'target_id' => $store->id,
            'only'      => true,
        ]);

        // 更新特色標語
        $store->descriptions()->where('key', 'narrate')->delete();
        foreach ($request['narrates'] as $item) {
            $store->descriptions()->create([
                'key'   => 'narrate',
                'value' => $item,
            ]);
        }

        // 商家提供的服務
        $pivotArray = [];
        $tags = json_decode($request['tags']);
        foreach ($tags as $tag) {
            $pivotArray[$tag->key] = [
                'target_type' => 'store',
                'value'       => $tag->value,
                'note'        => $tag->note,
            ];
        }
        $store->tags()->sync($pivotArray);

        // 更新特色說明
        $this->updateFeatures($store, $request['features']);
    }

    /**
     * 更新試吃資訊設定 (喜餅)
     */
    public function runTasting(Store $store, $request)
    {
        // 門市試吃
        $this->updateDescription($store, 'has_shop_tasting', $request['has_shop_tasting']); // 是否提供門市試吃
        $this->updateDescription($store, 'shop_tasting_min_fee', $request['shop_tasting_min_fee']); // 門市試吃-最低價格
        $this->updateDescription($store, 'shop_tasting_max_fee', $request['shop_tasting_max_fee']); // 門市試吃-最高價格
        $this->updateDescription($store, 'shop_tasting_time', $request['shop_tasting_time']); // 門市試吃-預約時間
        $this->updateDescription($store, 'shop_tasting_content', $request['shop_tasting_content']); // 門市試吃-試吃內容
        $this->updateDescription($store, 'shop_tasting_method', $request['shop_tasting_method']); // 門市試吃-試吃辦法

        // 門市店內照片
        if ($request['shop_tasting_image']) {
            $this->createImageService->add([
                'file_name' => $request['shop_tasting_image'],
                'type'      => 'store_shop_tasting',
                'target_id' => $store->id,
                'only'      => true,
            ]);
        }

        // 宅配試吃
        $this->updateDescription($store, 'has_delivery_tasting', $request['has_delivery_tasting']); // 是否提供宅配試吃
        $this->updateDescription($store, 'delivery_tasting_min_fee', $request['delivery_tasting_min_fee']); // 宅配試吃-最低價格
        $this->updateDescription($store, 'delivery_tasting_max_fee', $request['delivery_tasting_max_fee']); // 宅配試吃-最高價格
        $this->updateDescription($store, 'delivery_tasting_min_delivery_fee', $request['delivery_tasting_min_delivery_fee']); // 宅配試吃-運費最低價格
        $this->updateDescription($store, 'delivery_tasting_max_delivery_fee', $request['delivery_tasting_max_delivery_fee']); // 宅配試吃-運費最高價格
        $this->updateDescription($store, 'delivery_tasting_content', $request['delivery_tasting_content']); // 宅配試吃-試吃內容
        $this->updateDescription($store, 'delivery_tasting_method', $request['delivery_tasting_method']); // 宅配試吃-試吃辦法

        // 宅配禮盒照片
        if ($request['delivery_tasting_image']) {
            $this->createImageService->add([
                'file_name' => $request['delivery_tasting_image'],
                'type'      => 'store_delivery_tasting',
                'target_id' => $store->id,
                'only'      => true,
            ]);
        }
    }

    /**
     * 更新商家的描述內容
     */
    private function updateDescription($store, $key, $value)
    {
        // 有值 OR 是數值(包含0)，就儲存
        if ($value OR is_numeric($value)) {
            $store->descriptions()->updateOrCreate(['key' => $key], ['value' => $value]);

        // 空值 OR NULL，就刪除
        } else {
            $store->descriptions()->where('key', $key)->delete();
        }
    }

    /**
     * 更新特色說明
     */
    private function updateFeatures($store, $featuresJson)
    {
        $featureIds = [];
        $features   = json_decode($featuresJson);
        foreach ($features as $item) {

            // 更新特色說明 (改用每次儲存都是新增的方式，排序才不會有問題)
            $feature = $store->descriptions()->create([
                'key'   => 'feature',
                'value' => $item->value,
            ]);

            // 有給照片，則儲存商家特色說明照
            if ($item->image) {
                $this->createImageService->add([
                    'file_name' => $item->image,
                    'type'      => 'store_feature',
                    'target_id' => $feature->id,
                    'only'      => true,
                ]);

            // 沒有給照片，則刪除商家特色說明照
            } elseif ($feature->featureImage) {
                $this->deleteImageService->delete(['id' => $feature->featureImage->id]);
            }

            // 記錄儲存的編號
            $featureIds[] = $feature->id;
        }

        // 刪除多餘的特色說明＆特色說明照
        $oldFeatures = $store->descriptions()
                            ->where('key', 'feature')
                            ->whereNotIn('id', $featureIds)
                            ->get();
        foreach ($oldFeatures as $item) {
            if ($item->featureImage) {
                $this->deleteImageService->delete(['id' => $item->featureImage->id]);
            }
            $item->delete();
        }
    }

    /**
     * 更新喜餅商家的門市資訊
     */
    private function updateWeddingcakeShops($store, $shopsJson)
    {
        // 更新門市資訊
        $weddingcakeShopIds = [];
        $shops              = json_decode($shopsJson, true);
        foreach ($shops as $key => $shop) {
            $shop                 = Arr::add($shop, 'sequence', $key);
            $shop_id              = Arr::pull($shop, 'id');
            $openingHours         = Arr::pull($shop, 'opening_hours');
            $openingHours         = data_set($openingHours, '*.target_type', 'weddingcake_shop');
            $weddingcakeShop      = $store->weddingcakeShops()->updateOrCreate(['id' => $shop_id], $shop);
            $weddingcakeShopIds[] = $weddingcakeShop->id;

            // 更新門市的營業時間
            $weddingcakeShop->openingHours()->delete();
            $weddingcakeShop->openingHours()->createMany($openingHours);
        }

        // 移除多餘的門市資訊
        $store->weddingcakeShops()->whereNotIn('id', $weddingcakeShopIds)->delete();
    }
}
