<?php
/*
 |--------------------------------------
 |  商家中心 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Admin;

use App\Models\Wdv2\UserQuote;
use App\Models\Wdv2\StoreQuote;
use App\Models\StoreNotify;
use App\Models\LogGaPageView;
use App\Models\UserCollect;
use App\Models\LogRedirectUrl;
use App\Models\LogRedirectQuote;
use App\Models\FirestoreMessage;
use App\Models\ForumArticle;
use App\Models\OldData\SharePost;
use App\Services\Google\BigQuery\BigQueryHandle;
use DB;

class IndexService
{
    private $store;
    private $storeUser;
    private $dateRange;

    private $userQuote;
    private $storeQuote;
    private $storeNotify;
    private $logGaPageView;
    private $userCollect;
    private $logRedirectUrl;
    private $logRedirectQuote;
    private $firestoreMessage;
    private $forumArticle;
    private $sharePost;
    private $bigQueryHandle;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserQuote $userQuote,
        StoreQuote $storeQuote,
        StoreNotify $storeNotify,
        LogGaPageView $logGaPageView,
        UserCollect $userCollect,
        LogRedirectUrl $logRedirectUrl,
        LogRedirectQuote $logRedirectQuote,
        FirestoreMessage $firestoreMessage,
        ForumArticle $forumArticle,
        SharePost $sharePost,
        BigQueryHandle $bigQueryHandle
    ) {
        $this->userQuote        = $userQuote;
        $this->storeQuote       = $storeQuote;
        $this->storeNotify      = $storeNotify;
        $this->logGaPageView    = $logGaPageView;
        $this->userCollect      = $userCollect;
        $this->logRedirectUrl   = $logRedirectUrl;
        $this->logRedirectQuote = $logRedirectQuote;
        $this->firestoreMessage = $firestoreMessage;
        $this->forumArticle     = $forumArticle;
        $this->sharePost        = $sharePost;
        $this->bigQueryHandle   = $bigQueryHandle;
    }

    /**
     * 商家中心
     *
     * @param object $request
     * @return array
     */
    public function run($request)
    {
        $this->store     = $request['store'];
        $this->storeUser = $this->store->accounts->firstWhere('id', $request['store_user']->id);

        // 近30天的時間區間
        $this->dateRange = [
            now()->subDays(30)->startOfDay(),
            now()->endOfDay(),
        ];

        // 新人詢價區塊 (限有檔期的商家類型)
        $userQuoteCount  = NULL;
        $storeQuoteQuota = NULL;
        if ($this->store->hasScheduleDateByType($this->store->type)) {

            // 新人詢價數 (15天內)
            $userQuoteCount = $this->userQuote->release()
                                                ->where('type', $this->store->type)
                                                ->where('created_at', '>=', now()->subDays(15))
                                                ->whereDoesntHave('storeQuotes', function ($q) {
                                                    $q->where('store_id', $this->store->id);
                                                })
                                                ->count();

            // 今日報價剩餘數 (下架商家最多3次)
            if ($this->store->present()->is_leave) {
                $storeQuoteQuota = 3 - $this->store->storeQuotes()
                                                    ->where('created_at', '>=', now()->startOfDay())
                                                    ->count();
            }
        }

        // WeddingDay好婚市集近30天成效數據
        $data = $this->getWeddingDayStatistics();
        $weddingDayStatistics = [
            'page_view'      => $data['pageViews'], // 瀏覽量
            'collect_count'  => $data['userCollects'], // 收藏數
            'redirect_count' => $data['redirectUrls'] + $data['redirectQuotes'], // 關注數 = 商家的外站點擊 + 主動報價的外站點擊
            'match_count'    => $data['userQuotes'] + $data['storeQuotes'] + $data['messages'], // 媒合數 = 新人詢價 + 商家報價 + 有效訊息
        ];

        // 商家近30天成效數據
        $data = $this->getSelfStatistics();
        $selfStatistics = $data ? [
            'page_view'      => $data['storePageViews'] + $data['articlePageViews'], // 瀏覽量 = 商家 + 分享文
            'collect_count'  => $data['storeCollects'] + $data['albumCollects'] + $data['serviceCollects'] + $data['articleTracks'], // 收藏數 = 商家 + 作品 + 方案 + 分享文
            'redirect_count' => $data['redirectUrls'] + $data['redirectQuotes'], // 關注數 = 商家的外站點擊 + 主動報價的外站點擊
            'match_count'    => $data['storeQuotes'] + $data['messages'], // 媒合數 = 商家報價 + 有效訊息
            'post_count'     => $data['articleCount'], // 評價數
        ] : NULL;

        return [
            'store'                => $this->store,
            'storeUser'            => $this->storeUser,
            'dateRange'            => $this->dateRange,
            'userQuoteCount'       => $userQuoteCount,
            'storeQuoteQuota'      => $storeQuoteQuota,
            'weddingDayStatistics' => $weddingDayStatistics,
            'selfStatistics'       => $selfStatistics,
        ];
    }

    /**
     * 更多數據
     *
     * @param object $request
     * @return array
     */
    public function detail($request)
    {
        $this->store = $request['store'];

        // 近30天的時間區間
        $this->dateRange = [
            now()->subDays(30)->startOfDay(),
            now()->endOfDay(),
        ];

        // 同類別商家的排行最高分 maxRank
        $maxRank = $this->store->published($this->store->type)->max('rank');

        // 同類別沒有排行的商家數量 nullRank
        $notRankCount = $this->store->published($this->store->type)->whereNull('rank')->count();

        // 同類別的商家排行/同類別的商家數量
        $typeStoreRank  = $maxRank - $this->store->rank + 1; // maxRank - rank + 1
        $typeStoreCount = $maxRank + $notRankCount; // maxRank + nullRank

        // 評價分數
        $postAvgRank = $this->store->present()->avg_rank;

        // 所有作品總收藏數 (婚紗禮服/喜餅禮盒是收藏作品集，其他商家類型是收藏單一作品)
        // $collectType   = ($this->store->type == 2) ? 'album' : 'album_image';
        // $albumIds      = ($this->store->type == 2) ? $this->store->showAlbums->pluck('id') : $this->store->albumImages->pluck('id');
        // $albumCollects = $this->userCollect->where('type', $collectType)
        //                                         ->whereIn('target_id', $albumIds)
        //                                         ->count();
        $albumCollects = $this->store->allWorkCollects()->count();

        // 所有方案收藏數
        // $serviceIds      = $this->store->showServices->pluck('id');
        // $serviceCollects = $this->userCollect->where('type', 'service')
        //                                         ->whereIn('target_id', $serviceIds)
        //                                         ->count();
        $serviceCollects = $this->store->allServiceCollects()->count();

        // 訊息點擊數
        $messageClicks = $this->bigQueryHandle->handle('store_total_events', [
                            'dateRange'   => $this->dateRange,
                            'eventName'   => 'wd_message',
                            'eventParams' => [
                                'EXACT' => ['action' => 'open'],
                                'INT'   => ['store_id' => $this->store->id],
                            ],
                        ], false);

        // 商家近30天成效數據
        $data = $this->getSelfStatistics();
        $selfStatistics = $data ? [
            'store_collects'   => $data['storeCollects'], // 商家收藏數
            'album_collects'   => $data['albumCollects'], // 作品收藏數
            'service_collects' => $data['serviceCollects'], // 方案收藏數
            'page_view'        => $data['storePageViews'] + $data['articlePageViews'], // 瀏覽量 = 商家 + 分享文
            'redirect_count'   => $data['redirectUrls'] + $data['redirectQuotes'], // 關注數 = 商家的外站點擊 + 主動報價的外站點擊
            'messages'         => $data['messages'], // 有效訊息
            'message_clicks'   => $messageClicks, // 訊息點擊數
            'post_count'       => $data['articleCount'], // 評價數
        ] : NULL;

        return [
            'store'           => $this->store,
            'dateRange'       => $this->dateRange,
            'typeStoreRank'   => $typeStoreRank,
            'typeStoreCount'  => $typeStoreCount,
            'postAvgRank'     => $postAvgRank,
            'albumCollects'   => $albumCollects,
            'serviceCollects' => $serviceCollects,
            'selfStatistics'  => $selfStatistics,
        ];
    }

    /**
     * WeddingDay好婚市集近30天成效數據
     *
     * @return array
     */
    private function getWeddingDayStatistics()
    {
        // 瀏覽量
        $pageViews = $this->logGaPageView->where('type', 'all')
                                            ->whereBetween('created_date', $this->dateRange)
                                            ->get()
                                            ->sum('pageViews');

        // 收藏數
        $userCollects = $this->userCollect->whereBetween('created_at', $this->dateRange)
                                            ->count();

        // 關注數 (商家的外站點擊)
        $redirectUrls = $this->logRedirectUrl->whereBetween('created_at', $this->dateRange)
                                                ->count();

        // 關注數 (主動報價的外站點擊)
        $redirectQuotes = $this->logRedirectQuote->whereBetween('created_at', $this->dateRange)
                                                    ->count();

        // 媒合數 (新人詢價)
        $userQuotes = $this->userQuote->whereBetween('created_at', $this->dateRange)
                                        ->count();

        // 媒合數 (商家報價)
        $storeQuotes = $this->storeQuote->whereBetween('created_at', $this->dateRange)
                                        ->count();

        // 媒合數 (有效訊息)
        $messages = $this->firestoreMessage->where('settle_status', 'effective')
                                            ->whereBetween('created_at', $this->dateRange)
                                            ->count();

        return [
            'pageViews'      => $pageViews,
            'userCollects'   => $userCollects,
            'redirectUrls'   => $redirectUrls,
            'redirectQuotes' => $redirectQuotes,
            'userQuotes'     => $userQuotes,
            'storeQuotes'    => $storeQuotes,
            'messages'       => $messages,
        ];
    }

    /**
     * 商家近30天成效數據
     *
     * @return array
     */
    private function getSelfStatistics()
    {
        // 限「上線中」的商家
        if (!$this->store->present()->is_published) {
            return;
        }

        // 瀏覽量 (商家)
        $storePageViews = $this->store->logGaPageViews()
                                        ->where('type', 'store')
                                        ->whereBetween('created_date', $this->dateRange)
                                        ->get()
                                        ->sum('pageViews');

        // 瀏覽量 (分享文)
        $articlePageViews = 0;
        $articleIds = $this->store->sharePostBrands->pluck('article_id');
        if ($articleIds->isNotEmpty()) {
            $forumPageViews = $this->bigQueryHandle->handle('page_view_events', [
                'dateRange' => $this->dateRange,
                'eventName' => 'wd_pageview',
                'location'  => 'forum',
                'targetId'  => '('.$articleIds->implode(',').')',
                'storeId'   => null,
            ], false);
            $articlePageViews += collect($forumPageViews)->sum('total_page_view');
        }
        // TODO: 上線後30天可刪除
        $postIds = $this->sharePost->whereIn('change_article_id', $articleIds)->pluck('id');
        if ($postIds->isNotEmpty()) {
            $sharePageViews = $this->bigQueryHandle->handle('page_view_events', [
                'dateRange' => $this->dateRange,
                'eventName' => 'wd_pageview',
                'location'  => 'share',
                'targetId'  => '('.$postIds->implode(',').')',
                'storeId'   => null,
            ], false);
            $articlePageViews += collect($sharePageViews)->sum('total_page_view');
        }

        // 收藏數 (商家)
        $storeCollects = $this->userCollect->where('type', 'store')
                                            ->where('target_id', $this->store->id)
                                            ->whereBetween('created_at', $this->dateRange)
                                            ->count();

        // 收藏數 (作品), (婚紗禮服是收藏作品集，其他商家類型是收藏單一作品)
        // $collectType   = ($this->store->type == 2) ? 'album' : 'album_image';
        // $albumIds      = ($this->store->type == 2) ? $this->store->showAlbums->pluck('id') : $this->store->albumImages->pluck('id');
        // $albumCollects = $this->userCollect->where('type', $collectType)
        //                                     ->whereIn('target_id', $albumIds)
        //                                     ->whereBetween('created_at', $this->dateRange)
        //                                     ->count();
        $albumCollects = $this->store->allWorkCollects()
                                        ->whereBetween($this->userCollect->getTable().'.created_at', $this->dateRange)
                                        ->count();

        // 收藏數 (方案)
        // $serviceIds = $this->store->showServices->pluck('id');
        // $serviceCollects = $this->userCollect->where('type', 'service')
        //                                         ->whereIn('target_id', $serviceIds)
        //                                         ->whereBetween('created_at', $this->dateRange)
        //                                         ->count();
        $serviceCollects = $this->store->allServiceCollects()
                                        ->whereBetween($this->userCollect->getTable().'.created_at', $this->dateRange)
                                        ->count();

        // 收藏數 (分享文)
        $articleTracks = DB::table('user_article_track')
                            ->whereIn('article_id', $articleIds)
                            ->count();

        // 關注數 (商家的外站點擊)
        $redirectUrls = $this->store->logGaEvents()
                                    ->whereBetween('created_date', $this->dateRange)
                                    ->get()
                                    ->sum('totalEvents');

        // 關注數 (主動報價的外站點擊)
        $redirectQuotes = $this->store->logGaEventsQuotes()
                                        ->whereBetween('created_date', $this->dateRange)
                                        ->get()
                                        ->sum('totalEvents');

        // 媒合數 (商家報價)
        $storeQuotes = $this->store->storeQuotes()
                                    ->whereBetween('created_at', $this->dateRange)
                                    ->count();

        // 媒合數 (有效訊息)
        $messages = $this->store->firestoreMessages()
                                ->whereBetween('created_at', $this->dateRange)
                                ->count();

        // 評價數
        $articleCount = $this->forumArticle->whereIn('id', $articleIds)
                                            ->whereBetween('published_at', $this->dateRange)
                                            ->count();

        return [
            'storePageViews'   => $storePageViews,
            'articlePageViews' => $articlePageViews,
            'storeCollects'    => $storeCollects,
            'albumCollects'    => $albumCollects,
            'serviceCollects'  => $serviceCollects,
            'articleTracks'    => $articleTracks,
            'redirectUrls'     => $redirectUrls,
            'redirectQuotes'   => $redirectQuotes,
            'storeQuotes'      => $storeQuotes,
            'messages'         => $messages,
            'articleCount'     => $articleCount,
        ];
    }

    /**
     * 檢查商家可否上架 (建立商家專屬主頁)
     *
     * @param object $store
     * @return array
     */
    public function checkLaunched($store)
    {
        // 限「等待上架」的商家
        if ($store->status != 'pending') {
            return;
        }

        // 檢查商家是否更新過基本資料
        $result['profile'] = [
            'limit' => 1,
            'count' => $this->storeNotify->where('key', 'profile_update_202208')
                                            ->published()
                                            ->whereJsonContains('type_values', $store->type)
                                            // ->whereJsonContains('status_values', $store->status) // 等待上架也加入判斷是否有更新基本資料
                                            ->whereHas('notReminds', function($q) use ($store) {
                                                $q->where('store_id', $store->id);
                                            })
                                            ->count(),
        ];

        // 檢查商家是否有顯示中的作品 (除了婚宴場地、婚禮小物、動態婚錄)
        if (!in_array($store->type, [5, 7]) && $store->extra_type != 302) {
            if ($store->type == 6) { // 婚禮佈置至少12本作品
                $albumLimit = 12;
            }
            if ($store->type == 8) { // 主持人至少3本相簿
                $albumLimit = 3;
            }
            if ($store->type == 10) { // 喜餅至少3款禮盒
                $albumLimit = 3;
            }
            $result['album'] = [
                'limit' => $albumLimit ?? 1,
                'count' => $store->showAlbums->count(),
            ];
        }

        // 檢查商家是否有顯示中的影片 (動態婚錄、婚攝+婚錄)
        if (in_array($store->extra_type, [302, 303])) {
            $result['video'] = [
                'limit' => 1,
                'count' => $store->showVideos->count(),
            ];
        }

        // 檢查商家是否有顯示中的方案 (除了婚禮佈置、婚禮小物)
        if (!in_array($store->type, [6, 7])) {
            $result['service'] = [
                'limit' => ($store->type == 8) ? 2 : 1, // 主持人至少2個方案
                'count' => $store->showServicesWithoutActivity->count(),
            ];
        }

        // 檢查商家是否有設定車馬費/服務地區 (有檔期的商家類型)
        if ($store->hasScheduleDateByType($store->type)) {
            $result['fare'] = [
                'limit' => 1,
                'count' => $store->fares->count(),
            ];
        }

        // 檢查商家是否有設定廳房 (婚宴場地)
        if ($store->type == 5) {
            $result['venue_room'] = [
                'limit' => 1,
                'count' => $store->showVenueRooms->count(),
            ];
        }

        // 檢查商家是否有設定試吃資訊/喜餅管理/自動回覆訊息 (喜餅)
        if ($store->type == 10) {
            $result['weddingcake_tasting'] = [
                'limit' => 1,
                'count' => is_numeric($store->present()->description('has_shop_tasting')) && is_numeric($store->present()->description('has_delivery_tasting')) ? 1 : 0,
            ];
            $result['weddingcake_cookie']  = [
                'limit' => 6,
                'count' => $store->weddingcakeCookieItems->count(),
            ];
            $result['message_autoreply']   = [
                'limit' => 1,
                'count' => $store->autoReply ? 1 : 0,
            ];
        }

        return $result;
    }
}
