<?php
/*
 |--------------------------------------
 |  網站首頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Main;

use App\Models\Wdv2\UserQuote;
use App\Models\FirestoreMessage;
use App\Models\Wdv2\Reserve;
use App\Models\UserWeddingType;
use App\Models\ForumArticle;
use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;
use App\Repositories\StoreRepository;
use App\Repositories\BlogArticleRepository;
use App\Repositories\ForumArticleRepository as ArticleRepository;
use App\Traits\Model\TransformerTrait;

class IndexService
{
    private $userQuote;
    private $firestoreMessage;
    private $reserve;
    private $weddingType;
    private $forumArticle;
    private $adCampaignRepository;
    private $adImageRepository;
    private $storeRepository;
    private $blogArticleRepository;
    private $articleRepository;

    use TransformerTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserQuote $userQuote,
        FirestoreMessage $firestoreMessage,
        Reserve $reserve,
        UserWeddingType $weddingType,
        ForumArticle $forumArticle,
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository,
        StoreRepository $storeRepository,
        BlogArticleRepository $blogArticleRepository,
        ArticleRepository $articleRepository
    ) {
        $this->userQuote             = $userQuote;
        $this->firestoreMessage      = $firestoreMessage;
        $this->reserve               = $reserve;
        $this->weddingType           = $weddingType;
        $this->forumArticle          = $forumArticle;
        $this->adCampaignRepository  = $adCampaignRepository;
        $this->adImageRepository     = $adImageRepository;
        $this->storeRepository       = $storeRepository;
        $this->blogArticleRepository = $blogArticleRepository;
        $this->articleRepository     = $articleRepository;
    }

    /**
     * 網站首頁
     */
    public function run()
    {
        // 首頁輪播
        $banners = $this->adCampaignRepository->getPublishedList('banner');
        $bannerBackupImages = $this->adImageRepository->getBackupList();

        // 分享文數
        $postCount = $this->forumArticle->recommend()->select('id')->count();
        // 主動報價數
        $quoteCount = $this->userQuote->select('id')->count();
        // 即時通訊
        $messageCount = $this->firestoreMessage->select('id')->count();
        // 詢問單數
        $reserveCount = $this->reserve->select('id')->count();
        // 使用數 (幾位新娘找到了命定商家：分享+主動報價+即時通訊+詢問單)
        $usageCount = $postCount + $quoteCount + $messageCount + $reserveCount;
        // 婚紗禮服數
        $weddingdressCount = $this->storeRepository->getAlbumCountByType(2);
        // 婚紗照作品數
        $weddingstudioWorkCount = $this->storeRepository->getWorkCountByType(1);
        // 新秘作品數
        $makeupartistWorkCount = $this->storeRepository->getWorkCountByType(4);
        // 婚攝 & 婚錄作品數
        $photographerWorkCount = $this->storeRepository->getWorkCountByType(3);

        // 好婚鑑定團
        $kolArticles = $this->blogArticleRepository->getRandomNewSampleList('kol', 6);

        // 好婚聊聊-新娘正在討論的文章列表
        $articles = $this->articleRepository->getHotScoreSampleList(10);

        // 好婚聊聊 推薦文（category_id = 5）
        $posts = $this->forumArticle->status('published')->recommend()->whereHas('cover')->sortHotScore()->sortPageView()->limit(6)->get();

        // 好婚專欄
        $blogArticles = $this->blogArticleRepository->getRandomNewSampleList('blog', 6);

        // 主動報價區塊的下拉選單
        $storeTypes   = $this->formatAttributeList($this->storeRepository->getModel()->quoteTypeList);
        $weddingTypes = $this->formatAttributeList($this->weddingType->typeList);

        // 拍婚紗 ($type, $limit)
        $weddingstudio = $this->storeRepository->getPopularAlbums(1, 6);
        // 喜餅 ($type, $limit)
        $weddingcake = $this->storeRepository->getPopularStores(10, 6);
        // 新娘秘書 ($type, $limit)
        $makeupartist = $this->storeRepository->getPopularAlbums(4, 9);
        // 婚紗禮服 ($type, $limit)
        $weddingdress = $this->storeRepository->getPopularAlbums(2, 9);
        // 婚攝婚錄 ($type, $limit)
        $photographer = $this->storeRepository->getPopularAlbums(3, 6);
        // 婚宴場地 ($type, $limit)
        $weddingvenue = $this->storeRepository->getPopularStores(5, 6);
        // 婚禮主持人 ($type, $limit)
        $weddinghost = $this->storeRepository->getPopularStores(8, 6);
        // 婚禮佈置 ($type, $limit)
        $weddingdecoration = $this->storeRepository->getPopularAlbums(6, 6);
        // 婚禮小物 ($type, $limit)
        $weddingmall = $this->storeRepository->getPopularMallItems(7, 6);

        return [
            'usageCount'             => $usageCount,
            'weddingdressCount'      => $weddingdressCount,
            'weddingstudioWorkCount' => $weddingstudioWorkCount,
            'makeupartistWorkCount'  => $makeupartistWorkCount,
            'photographerWorkCount'  => $photographerWorkCount,
            'banners'                => $banners,
            'bannerBackupImages'     => $bannerBackupImages,
            'kolArticles'            => $kolArticles,
            'articles'               => $articles,
            'posts'                  => $posts,
            'blogArticles'           => $blogArticles,
            'quoteCount'             => $quoteCount,
            'storeTypes'             => $storeTypes,
            'weddingTypes'           => $weddingTypes,
            'weddingstudio'          => $weddingstudio,
            'weddingcake'            => $weddingcake,
            'makeupartist'           => $makeupartist,
            'weddingdress'           => $weddingdress,
            'photographer'           => $photographer,
            'weddingvenue'           => $weddingvenue,
            'weddinghost'            => $weddinghost,
            'weddingdecoration'      => $weddingdecoration,
            'weddingmall'            => $weddingmall,
        ];
    }

}
