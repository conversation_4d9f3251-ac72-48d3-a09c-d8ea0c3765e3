<?php
declare(strict_types=1);
/*------------------------------------
 | 記錄前端傳來的錯誤
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Main;

use App\Repositories\ErrorRecordRepository;
use App\Traits\RandStringTrait;
use Log;

class FrontendErrorLogService
{
    /** trait */
    use RandStringTrait;

    /** @var ErrorRecordRepository  */
    private $errorRecordRepository;

    /**
     * FrontendErrorLogService constructor.
     * @param ErrorRecordRepository $errorRecordRepository
     */
    public function __construct(
        ErrorRecordRepository $errorRecordRepository
    )
    {
        $this->errorRecordRepository = $errorRecordRepository;
    }

    /**
     * 儲存前端錯誤到DB
     * @param $data
     * @return string|null
     */
    public function run(array $data)
    {
        $data['error_code'] = $this->getRandString();
        try {
            $this->errorRecordRepository->addData($data);
            return $data['error_code'];
        } catch (\Exception $e) {
            Log::debug('FrontendErrorLogService@run', ['data' => $e]);
            return null;
        }
    }

}
