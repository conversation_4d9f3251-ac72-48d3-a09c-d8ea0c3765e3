<?php
/*
 |--------------------------------------
 |  W姐妹合併至好婚聊聊 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\OldData;

use App\Models\OldData\SharePost as Post;
use App\Models\ForumArticle as Article;
use App\Models\ForumComment;
use App\Models\Image;
use App\Services\Image\CreateImageService;
use App\Services\Forum\Article\PostTagService as ForumTagService;

class ShareMergeToForumService
{
    private $post;
    private $article;
    private $forumComment;
    private $image;
    private $createImageService;
    private $forumTagService;

    private $forumCategoryId = 5; // 好婚聊聊的推薦分類
    private $count = [
        'articles'       => 0,
        'images'         => 0,
        'cover'          => 0,
        'atUsers'        => 0,
        'likes'          => 0,
        'comments'       => 0,
        'commentImages'  => 0,
        'commentAtUsers' => 0,
        'commentLikes'   => 0,
        'replies'        => 0,
        'replyImages'    => 0,
        'replyAtUsers'   => 0,
        'replyLikes'     => 0,
        'brands'         => 0,
        'tracks'         => 0,
        'tags'           => 0,
        'logShares'      => 0,
    ];

    public function __construct(
        Post $post,
        Article $article,
        ForumComment $forumComment,
        Image $image,
        CreateImageService $createImageService,
        ForumTagService $forumTagService
    ) {
        // 能夠背景執行到結束
        ignore_user_abort(true);
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->post               = $post;
        $this->article            = $article;
        $this->forumComment       = $forumComment;
        $this->image              = $image;
        $this->forumTagService    = $forumTagService;
        $this->createImageService = $createImageService;
    }

    /**
     * 好婚聊聊-匯入W姐妹的分享文
     *
     */
    public function run()
    {
        // W姐妹分享文
        $this->post
            // ->whereBetween($this->post->getTable().'.id', [0,20000])
            // ->where($this->post->getTable().'.id', '>=', 104000)
            ->live()
            ->chunk(1000, function($posts) {
                foreach ($posts as $post) {

                    echo $post->id.", ";

                    //=================================================
                    //  好婚聊聊-文章
                    //=================================================
                    $article = NULL;
                    if ($post->change_article_id) {
                        $article = $this->article->find($post->change_article_id);
                    }
                    if (!$article) {
                        $article = clone $this->article;
                    }

                    // 儲存文章
                    $article->user_id        = $post->user_id;
                    $article->category_id    = $this->forumCategoryId;
                    $article->title          = $post->title;
                    $article->content        = $post->content;
                    $article->summary        = $post->summary;
                    $article->is_exclude_seo = $post->is_exclude_seo;
                    $article->page_view      = $post->page_view;
                    $article->like_count     = $post->allCollects->count();
                    $article->comment_count  = $post->comment_count;
                    $article->track_count    = $post->collect_count;
                    $article->hot_score      = $post->hot_score;
                    $article->ip             = $post->ip;
                    $article->status         = $post->status;
                    $article->published_at   = $post->published_at ?? $post->created_at;
                    $article->created_at     = $post->created_at;
                    $article->updated_at     = $post->updated_at;
                    $article->save();

                    // 記錄轉換ID
                    if (!$post->change_article_id) {
                        $post->change_article_id = $article->id;
                        $post->save();
                    }

                    // 統計數量
                    $this->count['articles']++;

                    //=================================================
                    //  好婚聊聊-Images
                    //=================================================
                    if ($post->images->count()) {
                        $post->images()->update([
                            'type'      => 'forum_article',
                            'target_id' => $article->id,
                        ]);

                        // 統計數量
                        $this->count['images'] += $article->images->count();
                    }

                    //=================================================
                    //  好婚聊聊-封面照
                    //=================================================
                    if ($post->cover_use_image_id) {
                        $cover = $this->image->find($post->cover_use_image_id);
                        if ($cover) {
                            // 更新上傳的圖檔的來源
                            $this->createImageService->add([
                                'file_name' => $cover->file_name,
                                'type'      => 'forum_cover',
                                'target_id' => $article->id,
                                'only'      => true,
                            ]);
                        }
                    } elseif ($post->cover) {
                        $post->cover->type      = 'forum_cover';
                        $post->cover->target_id = $article->id;
                        $post->cover->save();

                        // 統計數量
                        $this->count['cover']++;
                    }

                    //=================================================
                    //  好婚聊聊-文章-@user
                    //=================================================
                    if ($post->atUsers->count()) {
                        $pivotArray = [];
                        $post->atUsers()
                                ->withPivot('send_email')
                                ->get()
                                ->map(function($user) use (&$pivotArray) {
                                    $pivotArray[$user->id] = [
                                        'send_email' => $user->pivot->send_email,
                                    ];
                                });
                        $article->atUsers()->sync($pivotArray);

                        // 統計數量
                        $this->count['atUsers'] += $article->atUsers->count();
                    }

                    //=================================================
                    //  好婚聊聊-文章-Like
                    //=================================================
                    if ($post->likesWithTrashed->count()) {
                        $pivotArray = [];
                        $post->likesWithTrashed()
                                ->withPivot(['created_at', 'deleted_at'])
                                ->get()
                                ->map(function($user) use (&$pivotArray, $article) {
                                    $pivotArray[$user->id] = [
                                        'article_id' => $article->id,
                                        'created_at' => $user->pivot->created_at,
                                        'deleted_at' => $user->pivot->deleted_at,
                                    ];
                                });
                        $article->likesWithTrashed()->sync($pivotArray);

                        // 統計數量
                        $this->count['likes'] += $article->likesWithTrashed->count();
                    }

                    //=================================================
                    //  好婚聊聊-留言
                    //=================================================
                    foreach ($post->comments as $shareComment) {
                        $forumComment = NULL;
                        if ($shareComment->change_comment_id) {
                            $forumComment = $this->forumComment->find($shareComment->change_comment_id);
                        }
                        if (!$forumComment) {
                            $forumComment = clone $this->forumComment;
                        }

                        // 儲存留言
                        $forumComment->user_id    = $shareComment->user_id;
                        $forumComment->article_id = $article->id;
                        $forumComment->parent_id  = NULL;
                        $forumComment->content    = $shareComment->content;
                        $forumComment->summary    = $shareComment->summary;
                        $forumComment->ip         = $shareComment->ip;
                        $forumComment->status     = $shareComment->status;
                        $forumComment->created_at = $shareComment->created_at;
                        $forumComment->updated_at = $shareComment->updated_at;
                        $forumComment->save();

                        // 記錄轉換ID
                        if (!$shareComment->change_comment_id) {
                            $shareComment->change_comment_id = $forumComment->id;
                            $shareComment->save();
                        }

                        // 統計數量
                        $this->count['comments']++;

                        //=================================================
                        //  好婚聊聊-留言-Images
                        //=================================================
                        if ($shareComment->images->count()) {
                            $shareComment->images()->update([
                                'type'      => 'forum_comment',
                                'target_id' => $forumComment->id,
                            ]);

                            // 統計數量
                            $this->count['commentImages'] += $forumComment->images->count();
                        }

                        //=================================================
                        //  好婚聊聊-留言-@user
                        //=================================================
                        if ($shareComment->atUsers->count()) {
                            $pivotArray = [];
                            $shareComment->atUsers()
                                            ->withPivot('send_email')
                                            ->get()
                                            ->map(function($user) use (&$pivotArray, $article) {
                                                $pivotArray[$user->id] = [
                                                    'article_id' => $article->id,
                                                    'send_email' => $user->pivot->send_email,
                                                ];
                                            });
                            $forumComment->atUsers()->sync($pivotArray);

                            // 統計數量
                            $this->count['commentAtUsers'] += $forumComment->atUsers->count();
                        }

                        //=================================================
                        //  好婚聊聊-留言-Like
                        //=================================================
                        if ($shareComment->likesWithTrashed->count()) {
                            $pivotArray = [];
                            $shareComment->likesWithTrashed()
                                            ->withPivot(['created_at', 'deleted_at'])
                                            ->get()
                                            ->map(function($user) use (&$pivotArray, $article) {
                                                $pivotArray[$user->id] = [
                                                    'article_id' => $article->id,
                                                    'created_at' => $user->pivot->created_at,
                                                    'deleted_at' => $user->pivot->deleted_at,
                                                ];
                                            });
                            $forumComment->likesWithTrashed()->sync($pivotArray);

                            // 統計數量
                            $this->count['commentLikes'] += $forumComment->likesWithTrashed->count();
                        }

                        //=================================================
                        //  好婚聊聊-留言-回覆
                        //=================================================
                        foreach ($shareComment->replies as $shareReply) {
                            $forumReply = NULL;
                            if ($shareReply->change_comment_id) {
                                $forumReply = $this->forumComment->find($shareReply->change_comment_id);
                            }
                            if (!$forumReply) {
                                $forumReply = clone $this->forumComment;
                            }

                            // 儲存留言
                            $forumReply->user_id    = $shareReply->user_id;
                            $forumReply->article_id = $article->id;
                            $forumReply->parent_id  = $forumComment->id;
                            $forumReply->content    = $shareReply->content;
                            $forumReply->summary    = $shareReply->summary;
                            $forumReply->ip         = $shareReply->ip;
                            $forumReply->status     = $shareReply->status;
                            $forumReply->created_at = $shareReply->created_at;
                            $forumReply->updated_at = $shareReply->updated_at;
                            $forumReply->save();

                            // 記錄轉換ID
                            if (!$shareReply->change_comment_id) {
                                $shareReply->change_comment_id = $forumReply->id;
                                $shareReply->save();
                            }

                            // 統計數量
                            $this->count['replies']++;

                            //=================================================
                            //  好婚聊聊-留言-Images
                            //=================================================
                            if ($shareReply->images->count()) {
                                $shareReply->images()->update([
                                    'type'      => 'forum_comment',
                                    'target_id' => $forumReply->id,
                                ]);

                                // 統計數量
                                $this->count['replyImages'] += $forumReply->images->count();
                            }

                            //=================================================
                            //  好婚聊聊-留言-回覆-@user
                            //=================================================
                            if ($shareReply->atUsers->count()) {
                                $pivotArray = [];
                                $shareReply->atUsers()
                                            ->withPivot('send_email')
                                            ->get()
                                            ->map(function($user) use (&$pivotArray, $article) {
                                                $pivotArray[$user->id] = [
                                                    'article_id' => $article->id,
                                                    'send_email' => $user->pivot->send_email,
                                                ];
                                            });
                                $forumReply->atUsers()->sync($pivotArray);

                                // 統計數量
                                $this->count['replyAtUsers'] += $forumReply->atUsers->count();
                            }

                            //=================================================
                            //  好婚聊聊-留言-回覆-Like
                            //=================================================
                            if ($shareReply->likesWithTrashed->count()) {
                                $pivotArray = [];
                                $shareReply->likesWithTrashed()
                                                ->withPivot(['created_at', 'deleted_at'])
                                                ->get()
                                                ->map(function($user) use (&$pivotArray, $article) {
                                                    $pivotArray[$user->id] = [
                                                        'article_id' => $article->id,
                                                        'created_at' => $user->pivot->created_at,
                                                        'deleted_at' => $user->pivot->deleted_at,
                                                    ];
                                                });
                                $forumReply->likesWithTrashed()->sync($pivotArray);

                                // 統計數量
                                $this->count['replyLikes'] += $forumReply->likesWithTrashed->count();
                            }
                        }
                    }

                    // 更新留言數
                    $article->comment_count = $article->allComments->count();
                    $article->save();

                    //=================================================
                    //  好婚聊聊-品牌
                    //=================================================
                    if ($post->brands->count()) {
                        $pivotArray = [];
                        $post->brands->map(function($brand) use (&$pivotArray) {
                                        $pivotArray[$brand->id] = [
                                            'user_created'     => $brand->pivot->user_created,
                                            'rank'             => $brand->pivot->rank,
                                            'is_booked'        => $brand->pivot->is_booked,
                                            'wedding_type_ids' => $brand->pivot->wedding_type_ids,
                                            'yzcube_user_id'   => $brand->pivot->yzcube_user_id,
                                            'created_at'       => $brand->pivot->created_at,
                                            'deleted_at'       => $brand->pivot->deleted_at,
                                        ];
                                    });
                        $article->brands()->sync($pivotArray);

                        // 統計數量
                        $this->count['brands'] += $article->brands->count();
                    }

                    //=================================================
                    //  好婚聊聊-使用者追蹤
                    //=================================================
                    if ($post->userCollects->count()) {
                        $pivotArray = [];
                        $post->userCollects->map(function($user) use (&$pivotArray) {
                                                $pivotArray[$user->id] = [
                                                    'created_at' => $user->pivot->created_at,
                                                ];
                                            });
                        $article->tracks()->sync($pivotArray);

                        // 更新追蹤數
                        $article->track_count = $article->tracks->count();
                        $article->save();

                        // 統計數量
                        $this->count['tracks'] += $article->track_count;
                    }

                    //=================================================
                    //  好婚聊聊-話題標籤
                    //=================================================
                    if ($post->tags->count()) {
                        $this->forumTagService->run([
                            'article' => $article,
                            'tags'    => $post->tags->pluck('name'),
                        ]);

                        // 強制重新載入 tags
                        $article->load('tags');

                        // 統計數量
                        $this->count['tags'] += $article->tags->count();
                    }

                    //=================================================
                    //  好婚聊聊-分享紀錄
                    //=================================================
                    if ($post->logShares->count()) {
                        $logData = $post->logShares->map(function($log) {
                                                        return $log->only(['user_id', 'type', 'created_at']);
                                                    });
                        $article->logShares()->delete();
                        $article->logShares()->createMany($logData);

                        // 統計數量
                        $this->count['logShares'] += $article->logShares->count();
                    }
                }
            });

        return $this->count;
    }
}
