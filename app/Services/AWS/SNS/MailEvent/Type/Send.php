<?php
declare(strict_types=1);
/*------------------------------------
 | 信件傳送
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Type;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class Send extends AbsEventTypeHandle
{
    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    public function organizeBase(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->mail->destination as $recipient) {
            $recipients[] = [
                'email' => $recipient,
                'valid' => 1
            ];
        }

        return $recipients;
    }

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    public function organizeEvent(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->mail->destination as $recipient) {
            $recipients[] = [
                'email' => $recipient,
                'eventType' => $this->snsMessage->eventType,
            ];
        }

        return $recipients;
    }
}
