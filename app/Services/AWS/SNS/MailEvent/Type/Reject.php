<?php
declare(strict_types=1);
/*------------------------------------
 | 拒絕
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Type;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class Reject extends AbsEventTypeHandle
{
    /*
     |--------------------------------------
     | reason
     |--------------------------------------
     | 唯一的可能值為 Bad content，表示 Amazon SES 偵測到電子郵件包含病毒
     |
     */

    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    public function organizeBase(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->mail->commonHeaders->to as $recipient) {
            $recipients[] = [
                'email' => $recipient,
                'valid' => 0
            ];
        }

        return $recipients;
    }

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    public function organizeEvent(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->mail->commonHeaders->to as $recipient) {
            $recipients[] = [
                'email' => $recipient,
                'eventType' => $this->snsMessage->eventType,
                'reason' => $this->snsMessage->reject->reason,
            ];
        }

        return $recipients;
    }
}
