<?php
declare(strict_types=1);
/*------------------------------------
 | 交付延遲
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Type;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class DeliveryDelay extends AbsEventTypeHandle
{
    /*
     |--------------------------------------
     | delayType
     |--------------------------------------
     | InternalFailure: 內部 Amazon SES 問題造成訊息延遲。
     | General: 在 SMTP 對話期間發生一般失敗。
     | MailboxFull: 收件人的信箱已滿，無法接收其他訊息。
     | SpamDetected: 收件人的郵件伺服器偵測到來自您帳戶大量來路不明的電子郵件。
     | RecipientServerError: 收件人的電子郵件伺服器暫時發生問題，導致無法傳遞訊息。
     | IPFailure: 傳送訊息的 IP 地址遭到收件人的電子郵件提供者的封鎖或節流。
     | TransientCommunicationGeneral: 在與收件人的電子郵件提供者的 SMTP 對話期間發生暫時性的通訊失敗。
     | BYOIPHostNameLookupUnavailable: Amazon SES 無法查詢您的 IP 地址的 DNS 主機名稱。只有當您使用自有 IP時，才會發生這種類型的延遲。
     | Undetermined: Amazon SES 無法判斷傳送延遲的原因。
     |
     | ref: https://docs.aws.amazon.com/zh_tw/ses/latest/DeveloperGuide/event-publishing-retrieving-sns-contents.html
     */

    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    public function organizeBase(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->deliveryDelay->delayedRecipients as $recipient) {
            $recipients[] = [
                'email' => $recipient->emailAddress,
                'valid' => 1
            ];
        }

        return $recipients;
    }

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    public function organizeEvent(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->deliveryDelay->delayedRecipients as $recipient) {
            $recipients[] = [
                'email'          => $recipient->emailAddress,
                'eventType'      => $this->snsMessage->eventType,
                'delayType'      => $this->snsMessage->deliveryDelay->delayType,
                'expirationTime' => $this->snsMessage->deliveryDelay->expirationTime
            ];
        }

        return $recipients;
    }
}
