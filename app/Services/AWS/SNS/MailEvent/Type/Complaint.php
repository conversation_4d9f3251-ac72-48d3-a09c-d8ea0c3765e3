<?php
declare(strict_types=1);
/*------------------------------------
 | 抱怨
 |------------------------------------
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Type;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class Complaint extends AbsEventTypeHandle
{
    /*
     |--------------------------------------
     | complaintFeedbackType
     |--------------------------------------
     | x abuse: 指出自動發出的電子郵件或其他形式的電子郵件濫用。
     | x auth-failure: 電子郵件身份驗證故障報告。
     | x fraud: 指示某些形式的詐騙或網路釣魚活動。
     | x not-spam: 指示提供報告的實體不會將訊息視為垃圾郵件。這可能會用於修正內含不正確標籤或者被歸類為垃圾郵件的訊息。
     | x other: 指示不符合其他註冊類型的任何其他意見回饋。
     | x virus: 回報在原始訊息中找到病毒。
     |
     | ref: https://docs.aws.amazon.com/zh_tw/ses/latest/DeveloperGuide/event-publishing-retrieving-sns-contents.html
     */
    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    public function organizeBase(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->complaint->complainedRecipients as $recipient) {

            $recipients[] = [
                'email' => $recipient->emailAddress,
                'valid' => 0
            ];
        }

        return $recipients;
    }

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    public function organizeEvent(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->complaint->complainedRecipients as $recipient) {

            $recipients[] = [
                'email'                 => $recipient->emailAddress,
                'eventType'             => $this->snsMessage->eventType,
                'complaintSubType'      => $this->snsMessage->complaint->complaintSubType ?? null, // OnAccountSuppressionList 表示SES並沒真的發信.
                'complaintFeedbackType' => $this->snsMessage->complaint->complaintFeedbackType ?? null
            ];
        }

        return $recipients;
    }
}
