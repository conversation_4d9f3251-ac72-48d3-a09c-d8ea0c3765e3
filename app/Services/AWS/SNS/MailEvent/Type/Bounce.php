<?php
declare(strict_types=1);
/*------------------------------------
 | 退信
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Type;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class Bounce extends AbsEventTypeHandle
{
    /*
     |--------------------------------------
     | bounceType & bounceSubType
     |--------------------------------------
     | x Undetermined + Undetermined: Amazon SES 無法判斷特定退信原因。
     | x Permanent + General: Amazon SES 收到一般硬退信。如果您收到此類退信，應該從您的郵寄清單中移除該收件人的電子郵件地址。
     | x Permanent + NoEmail: Amazon SES 收到永久硬退信，因為目標電子郵件地址不存在。如果您收到此類退信，應該從您的郵寄清單中移除該收件人的電子郵件地址。
     | x Permanent + Suppressed: Amazon SES 已抑制寄至此地址的傳送，因為它最近因無效地址而出現退信歷程記錄。如需自抑制清單中移除地址的詳細資訊，請參閱 使用 Amazon SES 全域禁止名單。
     | x Permanent + OnAccountSuppressionList: Amazon SES 已禁止傳送至此地址，因為它列於帳戶層級禁止名單中。
     | v Transient + General: Amazon SES 收到一般退信。未來您也許可成功傳送給此收件人。
     | v Transient + MailboxFull: Amazon SES 收到信箱完整退信。未來您也許可成功傳送給此收件人。
     | v Transient + MessageTooLarge: Amazon SES 收到訊息過大退信。若您減少訊息大小，也許可成功傳送給此收件人。
     | v Transient + ContentRejected: Amazon SES 收到內容遭拒退信。若您更改訊息內容，也許可成功傳送給此收件人。
     | v Transient + AttachmentRejected: Amazon SES 收到附件遭拒退信。若您移除或更改附件，也許可成功傳送給此收件人。
     |
     | ref: https://docs.aws.amazon.com/zh_tw/ses/latest/DeveloperGuide/event-publishing-retrieving-sns-contents.html
     */

    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    public function organizeBase(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->bounce->bouncedRecipients as $recipient) {
            $recipients[] = [
                'email' => $recipient->emailAddress,

                //bounceType = Transient 代表有可能傳送成功..就不阻擋寄信...其它type一律阻擋
                //bounceType = Undetermined 不明的退信原因..就不阻擋寄信...其它type一律阻擋
                'valid' => in_array($this->snsMessage->bounce->bounceType, ['Transient', 'Undetermined']) ? 1 : 0 //0 => 阻擋, 1 => 開放
            ];
        }

        return $recipients;
    }

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    public function organizeEvent(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->bounce->bouncedRecipients as $recipient) {
            $recipients[] = [
                'email'         => $recipient->emailAddress,
                'eventType'     => $this->snsMessage->eventType,
                'bounceType'    => $this->snsMessage->bounce->bounceType,
                'bounceSubType' => $this->snsMessage->bounce->bounceSubType,
            ];
        }

        return $recipients;
    }
}
