<?php
declare(strict_types=1);
/*------------------------------------
 | 渲染失敗
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Type;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class RenderingFailure extends AbsEventTypeHandle
{
    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    public function organizeBase(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->mail->destination as $recipient) {
            $recipients[] = [
                'email' => $recipient,
                'valid' => 1
            ];
        }

        return $recipients;
    }

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    public function organizeEvent(): array
    {
        $recipients = [];

        foreach ($this->snsMessage->mail->destination as $recipient) {
            $recipients[] = [
                'email'        => $recipient,
                'eventType'    => $this->snsMessage->eventType,
                'errorMessage' => $this->snsMessage->failure->errorMessage,
                'templateName' => $this->snsMessage->failure->templateName
            ];
        }

        return $recipients;
    }
}
