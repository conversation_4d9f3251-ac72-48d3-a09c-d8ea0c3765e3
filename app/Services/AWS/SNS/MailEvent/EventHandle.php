<?php
declare(strict_types=1);
/*
 |------------------------------------
 | SNS信件事件處理
 |------------------------------------
 |
 |
 */

namespace App\Services\AWS\SNS\MailEvent;

use App\Services\AWS\SNS\MailEvent\Contract\AbsEventTypeHandle;

class EventHandle
{
    /**
     * 通知信處理邏輯
     * @param $message
     * @return array
     */
    public function handle($message): array
    {
//        logger('EVENT-MSG', ['data' => $message]);
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\AWS\SNS\MailEvent\Type\Bounce
         |  App\Services\AWS\SNS\MailEvent\Type\Complaint
         |  App\Services\AWS\SNS\MailEvent\Type\Delivery
         |  App\Services\AWS\SNS\MailEvent\Type\Click
         |  App\Services\AWS\SNS\MailEvent\Type\Open
         |  App\Services\AWS\SNS\MailEvent\Type\Reject
         |  App\Services\AWS\SNS\MailEvent\Type\RenderingFailure
         |  App\Services\AWS\SNS\MailEvent\Type\Send
         |  App\Services\AWS\SNS\MailEvent\Type\DeliveryDelay
         |
         */
        $eventType = preg_replace("/\s+/",'', $message->eventType); //去空白..
        $classPath = 'App\Services\AWS\SNS\MailEvent\Type\\' . $eventType;

        if(!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if(!$typeHandle instanceof AbsEventTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($message);
    }
}
