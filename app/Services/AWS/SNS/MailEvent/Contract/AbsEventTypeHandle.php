<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | SNS Type 處理的 abstract class
 |--------------------------------------
 | template pattern
 |
 */

namespace App\Services\AWS\SNS\MailEvent\Contract;

use App\Repositories\MailBaseRepository;
use App\Repositories\MailEventRepository;
use Log;

abstract class AbsEventTypeHandle
{
    /** @var: sns發來的訊息 */
    protected $snsMessage;

    /** @var : ses message id */
    protected $sesID;

    /** @var MailBaseRepository */
    private $mailBaseRepository;

    /** @var MailEventRepository */
    private $mailEventRepository;

    /**
     * AbsEventTypeHandle constructor.
     * @param MailBaseRepository $mailBaseRepository
     * @param MailEventRepository $mailEventRepository
     */
    public function __construct(
        MailBaseRepository $mailBaseRepository,
        MailEventRepository $mailEventRepository
    )
    {
        $this->mailBaseRepository = $mailBaseRepository;
        $this->mailEventRepository = $mailEventRepository;
    }

    /**
     * Handler logic
     * @param $snsMessage:
     * @return array
     */
    public function handle($snsMessage): array
    {
        //取得SES id
        $this->snsMessage = $snsMessage;
        $this->sesID = $snsMessage->mail->messageId;

        //寫入mail_base表
        $baseData = $this->organizeBase();
        $this->saveMailBase($baseData);

        //寫入mail_events表
        $eventData = $this->organizeEvent();
        $this->saveMailEvents($eventData);

        return [
            'base' => $baseData,
            'event' => $eventData
        ];
    }

    /**
     * 整理要存入mail_base表的資料
     * @return array
     */
    abstract public function organizeBase(): array;

    /**
     * 整理要存入mail_events表的資料
     * @return array
     */
    abstract public function organizeEvent(): array;

    /**
     * 更新通知資料到mail_base表
     * email target_id target_type在MailService已經寫入..所以更新資料就行
     * @param array $data : 要儲存的資料
     */
    protected function saveMailBase(array $data)
    {
        foreach ($data as $value) {
            // 過濾Email特殊格式
            $value['email'] = $this->filterEmail($value['email']);

            // 更新資料
            try {
                $this->mailBaseRepository->updateOrCreate(['email' => $value['email']], $value);
            } catch (\Exception $e) {
                Log::error($e->getMessage(), [
                    'class' => class_basename(get_class($this)),
                ]);
            }
        }
    }

    /**
     * 更新通知資料到mail_notifications表
     * @param array $data : 要儲存的資料
     */
    protected function saveMailEvents(array $data)
    {
        foreach ($data as $value) {
            // 過濾Email特殊格式
            $value['email'] = $this->filterEmail($value['email']);

            // 紀錄 SES messageId
            $value['ses_message_id'] = $this->sesID;

            // 新增資料
            $this->mailEventRepository->addData($value);
        }
    }

    /**
     * 過濾Email特殊格式
     * sns來的資料..有些前面有使用者的prefix..要過濾掉
     * @param $email
     * @return string
     */
    protected function filterEmail($email):string
    {
        preg_match_all('/.*<(.*)>/', $email, $matches);
        return $matches[1] ? $matches[1][0] : $email;
    }
}
