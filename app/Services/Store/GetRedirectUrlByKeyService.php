<?php
/*
 |--------------------------------------
 |  用短網址取得轉址目的地 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\RedirectUrl;
use App\Traits\ApiErrorTrait;

class GetRedirectUrlByKeyService
{
    private $redirectUrl;
    private $googleMapLink = 'https://maps.google.com/?q=';

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        RedirectUrl $redirectUrl
    ) {
        $this->redirectUrl = $redirectUrl;
    }

    /**
     * 用短網址取得轉址目的地
     */
    public function run($request)
    {
        // 找出已建立的轉址資訊
        $redirectUrl = $this->redirectUrl->where('key', $request->route()->key)->first();
        if (!$redirectUrl) {
            $this->setException('找不到此轉址資訊！');
        }

        $hasPaid = false;
        $model   = $redirectUrl->{$redirectUrl->target_type};

        // 驗證商家有沒有欄位資料
        if ($redirectUrl->target_type == 'store') {
            $targetModel = clone $model;
            $hasPaid     = $targetModel->present()->is_published;

            // 轉換門市Model
            if ($redirectUrl->shop_id) {
                $relation    = $targetModel->typeKeyList[$targetModel->type].'Shops';
                $targetModel = $targetModel->{$relation}->find($redirectUrl->shop_id);
            }

            // 商家/門市-地址連結
            if ($redirectUrl->column == 'address') {
                $destination = $this->googleMapLink.($targetModel->city->title ?? '').($targetModel->area->title ?? '').$targetModel->address.($targetModel->map_title ?? '');
            } else {
                $destination = $targetModel->{$redirectUrl->column} ?? '';
            }
            if (!$destination) {
                $this->setException('此商家轉址資訊有誤！');
            }
        }

        // 驗證品牌有沒有欄位資料
        if ($redirectUrl->target_type == 'brand') {
            // 品牌-地址連結
            if ($redirectUrl->column == 'address') {
                $destination = $this->googleMapLink.$model->address.$model->name;
            } else {
                $destination = $model->{$redirectUrl->column} ?? '';
            }
            if (!$destination) {
                $this->setException('此品牌轉址資訊有誤！');
            }
        }

        // 新增轉址資訊紀錄，需排除機器人
        if (!preg_match('/(Googlebot|PetalBot)/', $request['user_agent'])) {
            $redirectUrl->logs()->create([
                'route_name' => $request['route_name'],
                'label'      => $request['label'],
                'user_id'    => $request['user']->id ?? NULL,
                'client_id'  => $request['cid'],
                'user_agent' => $request['user_agent'],
                'created_at' => now(),
            ]);
        }

        return [
            'has_paid'    => $hasPaid,
            'target_id'   => $hasPaid ? $model->id : '',
            'column'      => $redirectUrl->column,
            'destination' => $destination,
        ];
    }
}
