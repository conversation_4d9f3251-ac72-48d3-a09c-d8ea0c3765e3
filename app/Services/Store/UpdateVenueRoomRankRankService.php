<?php
/*
 |--------------------------------------
 |  更新婚宴場地廳房排行 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\VenueRoom;
use App\Models\LogGaPageView;
use App\Models\StoreIntegral;
use App\Models\UserCollect;
use Carbon\Carbon;

class UpdateVenueRoomRankRankService
{
    private $venueRoom;
    private $logGaPageView;
    private $storeIntegral;
    private $userCollect;

    private $runDays   = 10; // 統計範圍的天數
    private $dateRange = [];

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Carbon $carbon,
        VenueRoom $venueRoom,
        LogGaPageView $logGaPageView,
        StoreIntegral $storeIntegral,
        UserCollect $userCollect
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->carbon        = $carbon;
        $this->venueRoom     = $venueRoom;
        $this->logGaPageView = $logGaPageView;
        $this->storeIntegral = $storeIntegral;
        $this->userCollect   = $userCollect;
    }

    /**
     * 計算婚宴場地廳房的排名積分
     */
    public function run($date)
    {
        // 統計範圍的時間區間
        $this->dateRange = [
            $this->carbon->parse($date)->subDays($this->runDays - 1)->format('Y-m-d 00:00:00'),
            $this->carbon->parse($date)->format('Y-m-d 23:59:59'),
        ];

        // 先清空所有婚宴場地廳房的熱門排行
        $this->venueRoom->whereNotNull('rank')
                        ->update(['rank' => NULL]);

        // 取得造假項目的商家ID
        $imitationIds = $this->storeIntegral->where('created_date', $date)
                                            ->whereNotNull('imitation')
                                            ->pluck('store_id');

        // 取得婚宴場地廳房的流量積分
        $logs = $this->logGaPageView->selectRaw('
                                        target_id AS room_id,
                                        parent_id AS store_id,
                                        SUM(pageViews + totalUsers * 5) AS score
                                    ')
                                    ->where('type', 'store_venue_room')
                                    ->whereBetween('created_date', $this->dateRange)
                                    ->groupBy('target_id')
                                    ->get();

        // 若沒有任何婚宴場地廳房的流量積分就先跳出，避免下面壞掉
        if (!$logs->count()) {
            return 0;
        }

        foreach ($logs as $log) {

            // 婚宴場地廳房的流量積分 + 收藏積分
            $score = $log->score + $this->getCollectScore($log->room_id);

            // 判定是否為造假項目的商家
            if ($imitationIds->contains($log->store_id)) {
                $score *= 0.1;
            }

            // 紀錄總分
            $this->score[$log->room_id] = $score;
        }

        // 婚宴場地廳房的總分排行榜，由高至低排序
        arsort($this->score);
        $this->score = array_keys($this->score);

        // 更新婚宴場地廳房的熱門排行
        $total = count($this->score);
        foreach ($this->score as $key => $roomId) {
            $this->venueRoom->where('id', $roomId)
                            ->update(['rank' => $total - $key]);
        }

        return $total;
    }

    /**
     * 取得收藏婚宴場地廳房的積分
     *
     * @return int
     */
    private function getCollectScore($roomId)
    {
        // 收藏婚宴場地廳房
        $roomCollect = $this->userCollect->where('type', 'venue_room')
                                            ->where('target_id', $roomId)
                                            ->whereBetween('created_at', $this->dateRange);

        $total        = $roomCollect->count();
        $uniqueMember = $roomCollect->groupBy('user_id')->count();

        //==================================================================
        //  重複收藏婚宴場地廳房，平均每個人收藏個5個很厲害了，超過就讓他GG吧
        //  處置：超過就以不重複人數計算，並給折扣
        //==================================================================
        if ($total > $uniqueMember * 5) {
            return $uniqueMember * 25;
        } else {
            return $uniqueMember * 50 + $total * 3;
        }
    }
}
