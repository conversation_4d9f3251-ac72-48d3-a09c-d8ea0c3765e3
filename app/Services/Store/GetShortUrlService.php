<?php
/*
 |--------------------------------------
 |  取得短網址 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\RedirectUrl;
use App\Services\Store\MakeRedirectKeyService;

class GetShortUrlService
{
    private $redirectUrl;
    private $makeRedirectKeyService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        RedirectUrl $redirectUrl,
        MakeRedirectKeyService $makeRedirectKeyService
    ) {
        $this->redirectUrl            = $redirectUrl;
        $this->makeRedirectKeyService = $makeRedirectKeyService;
    }

    /**
     * 取得短網址
     */
    public function run($targetType, $model, $column, $shopModel = Null, $fullUrl = false)
    {
        // 驗證商家有沒有欄位資料
        if ($targetType == 'store' && !$shopModel && !$model->{$column}) {
            return '';
        }

        // 驗證門市有沒有欄位資料
        if ($shopModel && !$shopModel->{$column}) {
            return '';
        }

        // 驗證品牌有沒有欄位資料
        if ($targetType == 'brand' && !$shopModel && (!isset($model->{$column}) || !$model->{$column})) {
            return '';
        }

        // 找出已建立的轉址資訊
        $shopId      =  $shopModel->id ?? Null;
        $redirectUrl = $this->redirectUrl->where('target_type', $targetType)
                                        ->where('target_id', $model->id)
                                        ->where('shop_id', $shopId)
                                        ->where('column', $column)
                                        ->first();

        if (!$redirectUrl) {
            $redirectUrl = $this->makeRedirectKeyService->run($targetType, $model, $shopModel, $column);
        }

        // 生成短網址
        return $fullUrl ? config('params.wdv3.www_url').'/redirect/'.$redirectUrl->key : $redirectUrl->key;
    }
}
