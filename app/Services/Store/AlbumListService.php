<?php
/*
 |--------------------------------------
 |  商家作品列表頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Repositories\StoreRepository;
use App\Repositories\StoreAlbumRepository;
use App\Models\CityData;
use App\Models\StudioAlbum;
use App\Models\DressAlbum;
use App\Models\DecorationAlbum;
use App\Models\WeddingcakeAlbum;
use App\Models\StoreTag;

class AlbumListService
{
    private $storeRepository;
    private $storeAlbumRepository;
    private $cityData;
    private $studioAlbum;
    private $dressAlbum;
    private $decorationAlbum;
    private $weddingcakeAlbum;
    private $storeTag;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreRepository $storeRepository,
        StoreAlbumRepository $storeAlbumRepository,
        CityData $cityData,
        StudioAlbum $studioAlbum,
        DressAlbum $dressAlbum,
        DecorationAlbum $decorationAlbum,
        WeddingcakeAlbum $weddingcakeAlbum,
        StoreTag $storeTag
    ) {
        $this->storeRepository      = $storeRepository;
        $this->storeAlbumRepository = $storeAlbumRepository;
        $this->cityData             = $cityData;
        $this->studioAlbum          = $studioAlbum;
        $this->dressAlbum           = $dressAlbum;
        $this->decorationAlbum      = $decorationAlbum;
        $this->weddingcakeAlbum     = $weddingcakeAlbum;
        $this->storeTag             = $storeTag;
    }

    /**
     * 商家作品列表頁
     */
    public function run($request)
    {
        $store        = $request['store'];
        $storeTypeKey = $store->typeKeyList[$store->type];

        // 商家總數 (供前端SEO MetaData Description)
        $storeCount = $this->storeRepository->getListTotalByType($store->type);

        // 拍攝地點列表 (拍婚紗)
        // $imageLocations = collect([]);
        // if ($store->type == 1) {
        //     $imageLocations = $this->cityData->region()
        //                                 ->with([
        //                                     'children' => function($q1) use ($store) {
        //                                         // 限特定類型的已付費商家數
        //                                         $q1->select('id', 'title', 'parent_id')
        //                                             ->with(['studioAlbumImages.storeAlbumImage.album.store' => function($q2) use ($store) {
        //                                                 $q2->find($store->id);
        //                                             }]);
        //                                     }
        //                                 ])
        //                                 ->get();
        // }

        // 價格區間列表 (婚紗禮服/婚禮佈置/喜餅需要)
        $priceRange = [];
        if (in_array($store->type, [2, 6, 10])) {
            $relationTable = $storeTypeKey.'Album';
            foreach ($this->{$relationTable}->priceRangeList as $key => $item) {
                $priceRange[] = [
                    'key'       => $key,
                    'value'     => $item['label'],
                    'is_exists' => $this->storeAlbumRepository->getModel()
                                                                ->select('store_albums.id')
                                                                ->status('show')
                                                                ->where('store_id', $store->id)
                                                                ->whereHas($relationTable, function ($q) use ($key) {
                                                                    $q->priceRangeByKey($key);
                                                                })
                                                                ->exists(),
                ];
            }
        }

        // 類型列表 (拍婚紗/婚禮佈置需要)
        $types = [];
        if (in_array($store->type, [1, 6])) {
            $relationTable = $storeTypeKey.'Album';
            foreach ($this->{$relationTable}->typeList as $key => $value) {
                $types[] = [
                    'key'       => $key,
                    'value'     => $value,
                    'is_exists' => $this->storeAlbumRepository->getModel()
                                                                ->select('store_albums.id')
                                                                ->status('show')
                                                                ->where('store_id', $store->id)
                                                                ->whereHas($relationTable, function ($q) use ($key) {
                                                                    $q->where('type', $key);
                                                                })
                                                                ->exists(),
                ];
            }
        }

        // 作品集標籤列表 (婚紗禮服/婚禮佈置/喜餅需要)
        $albumTags = collect([]);
        if (in_array($store->type, [2, 6, 10])) {
            $albumTags = $this->storeTag->getSearchListByStore($store, 'album');
        }

        // 作品照標籤列表 (拍婚紗/婚攝婚錄/新娘秘書需要)
        // $imageTags = collect([]);
        // if (in_array($store->type, [1, 3, 4])) {
        //     $imageTags = $this->storeTag->getSearchListByStore($store, 'album_image');
        // }

        return [
            'store'          => $store,
            'storeCount'     => $storeCount,
            // 'imageLocations' => $imageLocations,
            'priceRange'     => $priceRange,
            'types'          => $types,
            'albumTags'      => $albumTags,
            // 'imageTags'      => $imageTags,
            'albums'         => $store->getReleaseAlbumsByRequest($request),
        ];
    }
}
