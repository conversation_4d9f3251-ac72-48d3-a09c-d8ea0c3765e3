<?php
/*
 |--------------------------------------
 |  計算商家的排名積分 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\LogGaPageView;
use App\Models\LogGaEvent;
use App\Models\Store;
use App\Models\StoreAlbum;
use App\Models\StoreAlbumImage;
use App\Models\Wdv2\SetTag;
use App\Models\Wdv2\OrderProduct;
use App\Models\UserCollect;
use Carbon\Carbon;

class CreateStoreIntegralService
{
    private $carbon;
    private $logGaPageView;
    private $logGaEvent;
    private $store;
    private $storeAlbum;
    private $storeAlbumImage;
    private $setTag;
    private $orderProduct;
    private $userCollect;

    private $runDays   = 10; // 統計範圍的天數
    private $dateRange = [];
    private $priceList = [];
    private $imitation = [];
    private $statistic = [];

    private $weights = [
        'storePageViews'    => 0.5,
        'storeTotalUsers'   => 1,
        'organicPageViews'  => 0.5,
        'organicTotalUsers' => 1,
        'sharePageViews'    => 0.5,
        'shareTotalUsers'   => 1,
        'clickTotalUsers'   => 40, // 不重複外站點擊事件
        'message'           => 600, // 即時通訊
        'reserve'           => 600, // 詢問單
        'collectStore'      => 10, // 收藏商家
        'collectWork'       => 5, // 收藏作品
        'collectService'    => 5, // 收藏方案
        'collectVenueRoom'  => 5, // 收藏廳房
    ];

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Carbon $carbon,
        LogGaPageView $logGaPageView,
        LogGaEvent $logGaEvent,
        Store $store,
        StoreAlbum $storeAlbum,
        StoreAlbumImage $storeAlbumImage,
        SetTag $setTag,
        OrderProduct $orderProduct,
        UserCollect $userCollect
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->carbon          = $carbon;
        $this->logGaPageView   = $logGaPageView;
        $this->logGaEvent      = $logGaEvent;
        $this->store           = $store;
        $this->storeAlbum      = $storeAlbum;
        $this->storeAlbumImage = $storeAlbumImage;
        $this->setTag          = $setTag;
        $this->orderProduct    = $orderProduct;
        $this->userCollect     = $userCollect;

        // 取得產品單價列表
        $this->priceList = $this->getProductUnitPriceList();
    }

    /**
     * 計算商家的排名積分
     */
    public function run($date)
    {
        // 統計範圍的時間區間
        $this->dateRange = [
            $this->carbon->parse($date)->subDays($this->runDays - 1)->format('Y-m-d 00:00:00'),
            $this->carbon->parse($date)->format('Y-m-d 23:59:59'),
        ];

        // 需計算排名的所有付費商家
        $stores = $this->store->whereIn('type', array_keys($this->store->needRankList))
                                ->published()
                                ->get();
        foreach ($stores as $store) {

            // 清空造假項目
            $this->imitation = [];

            // 商家統計數據
            $this->store     = $store;
            $this->statistic = [
                'store'   => $this->getStorePageViews(), // 取得商家的PageViews
                'organic' => $this->getStoreOrganicPageViews(), // 取得商家自然流量的PageViews
                'share'   => $this->getSharePageViews(), // 取得商家分享文的PageViews
                'click'   => $this->getClickEvents(), // 商家的外站點擊
                'message' => $this->getMessageCount(), // 取得商家即時通訊的數量
                'reserve' => $this->getReserveCount(), // 取得商家詢問單的數量
                'collect' => [
                    'store'     => $this->getStoreCollectCount(), // 取得收藏商家的數量
                    'work'      => $this->getWorkCollectCount(), // 取得收藏商家作品的數量
                    'service'   => $this->getServiceCollectCount(), // 取得收藏商家方案的數量
                    'venueRoom' => $this->getVenueRoomCollectCount(), // 取得收藏商家廳房的數量
                ],
            ];

            // 檢查是否有商家標記-黑名單(偷雞/假流量)
            $this->checkeStoreTagGuarded();

            // 儲存計算積分
            $this->saveComputingIntegral();
        }

        return $stores->count();
    }

    /**
     * 取得產品單價列表
     *
     * @return array
     */
    private function getProductUnitPriceList()
    {
        return [
            'click'   => $this->orderProduct->getClickUnitPrice(), // 取得外站連結點擊單價
            'message' => $this->orderProduct->getAllMessageUnitPrice(), // 取得所有類型的詢問單單價
            'reserve' => $this->orderProduct->getAllReserveUnitPrice(), // 取得所有類型的詢問單單價
        ];
    }

    /**
     * 取得商家的PageViews
     *
     * @return array
     */
    private function getStorePageViews()
    {
        $result = [
            'pageViews'  => 0,
            'totalUsers' => 0,
        ];

        $logs = $this->logGaPageView->where('type', 'store')
                                    ->where('target_id', $this->store->id)
                                    ->whereBetween('created_date', $this->dateRange)
                                    ->get();
        foreach ($logs as $log) {

            //==================================================================
            //  瀏覽量 > 100 and 瀏覽量 > 不重複瀏覽量10倍
            //  處置：以不重複流量為主，再打個一折
            //==================================================================
            if ($log->pageViews > 100 && $log->pageViews >= $log->totalUsers * 10) {
                $log->pageViews = $log->totalUsers *= 0.1;
                $this->imitation[] = 'StorePageViews_1';
            }

            //==================================================================
            //  瀏覽量 > 500 and 平均停留時間低於5秒
            //  處置：惡劣，直接歸零吧
            //==================================================================
            if ($log->pageViews > 500 && $log->avgEngagementTime < 5) {
                $log->pageViews = $log->totalUsers = 0;
                $this->imitation[] = 'StorePageViews_2';
            }

            //==================================================================
            //  瀏覽量 > 500 and 平均停留時間低於10秒
            //  處置：以不重複流量為主，再打到骨折
            //==================================================================
            if ($log->pageViews > 500 && $log->avgEngagementTime < 10) {
                $log->pageViews = $log->totalUsers *= 0.01;
                $this->imitation[] = 'StorePageViews_3';
            }

            $result['pageViews']  += $log->pageViews;
            $result['totalUsers'] += $log->totalUsers;
        }

        return $result;
    }

    /**
     * 取得商家自然流量的PageViews
     *
     * @return array
     */
    private function getStoreOrganicPageViews()
    {
        $logs = $this->logGaPageView->where('type', 'store_organic')
                                    ->where('target_id', $this->store->id)
                                    ->whereBetween('created_date', $this->dateRange)
                                    ->get();
        return [
            'pageViews'  => $logs->sum('pageViews'),
            'totalUsers' => $logs->sum('totalUsers'),
        ];
    }

    /**
     * 取得商家分享文的PageViews
     *
     * @return array
     */
    private function getSharePageViews()
    {
        $postIds = $this->store->sharePostBrands->pluck('post_id');
        $logs    = $this->logGaPageView->where('type', 'share_post')
                                        ->whereIn('target_id', $postIds)
                                        ->whereBetween('created_date', $this->dateRange)
                                        ->get();
        return [
            'pageViews'  => $logs->sum('pageViews'),
            'totalUsers' => $logs->sum('totalUsers'),
        ];
    }

    /**
     * 取得商家外站點擊的Events
     *
     * @return array
     */
    private function getClickEvents()
    {
        $logs = $this->logGaEvent->where('type', '!=', 'quote')
                                ->where('target_id', $this->store->id)
                                ->whereBetween('created_date', $this->dateRange)
                                ->get();
        return [
            'totalEvents' => $logs->sum('totalEvents'),
            'totalUsers'  => $logs->sum('totalUsers'),
        ];
    }

    /**
     * 取得商家即時通訊的數量
     *
     * @return int
     */
    private function getMessageCount()
    {
        return $this->store->firestoreMessages()
                            ->whereBetween('created_at', $this->dateRange)
                            ->count();
    }

    /**
     * 取得商家詢問單的數量
     *
     * @return int
     */
    private function getReserveCount()
    {
        return $this->store->reserves()
                            ->whereBetween('created_at', $this->dateRange)
                            ->count();
    }

    /**
     * 取得收藏商家的數量
     *
     * @return int
     */
    private function getStoreCollectCount()
    {
        return $this->store->userCollects()
                            ->whereBetween($this->userCollect->getTable().'.created_at', $this->dateRange)
                            ->count();
    }

    /**
     * 取得收藏商家作品的數量
     *
     * @return int
     */
    private function getWorkCollectCount()
    {
        // 收藏作品集(婚紗禮服)
        if (in_array($this->store->type, [2, 10])) {
            $collectType = 'album';
            $workCollect = $this->userCollect->join($this->storeAlbum->getTable(), function($join) {
                                                    $join->on('store_albums.id', '=', 'user_collects.target_id')
                                                            ->where('store_albums.status', 'show')
                                                            ->whereNotNull('store_albums.cover_id'); // scopeRelease()
                                                });

        // 收藏單一作品(其他商家類型)
        } else {
            $collectType = 'album_image';
            $workCollect = $this->userCollect->join($this->storeAlbumImage->getTable(), function($join) {
                                                    $join->on('store_album_images.id', '=', 'user_collects.target_id'); // scopeRelease()
                                                })
                                                ->join($this->storeAlbum->getTable(), function($join) {
                                                    $join->on('store_albums.id', '=', 'store_album_images.album_id')
                                                            ->where('store_albums.status', 'show')
                                                            ->whereNotNull('store_albums.cover_id'); // scopeRelease()
                                                });
        }

        // 收藏作品
        $workCollect  = $workCollect->select('user_collects.id')
                                    ->where('user_collects.type', $collectType)
                                    ->where('store_albums.store_id', $this->store->id)
                                    ->whereBetween('user_collects.created_at', $this->dateRange);

        $total        = $workCollect->count();
        $uniqueMember = $workCollect->groupBy('user_collects.user_id')->count();

        //==================================================================
        //  重複收藏作品，平均每個人收藏個30個很厲害了，超過就讓他GG吧
        //  處置：超過就以不重複人數*10筆計算
        //==================================================================
        if ($total > $uniqueMember * 30) {
            $total = $uniqueMember * 10;
            $this->imitation[] = 'WorkCollectCount';
        }

        return $total;
    }

    /**
     * 取得收藏商家方案的數量
     *
     * @return int
     */
    private function getServiceCollectCount()
    {
        $serviceIds  = $this->store->showServicesWithActivity->pluck('id');

        // 收藏方案
        $serviceCollect = $this->userCollect->where('type', 'service')
                                            ->whereIn('target_id', $serviceIds)
                                            ->whereBetween('created_at', $this->dateRange);

        $total        = $serviceCollect->count();
        $uniqueMember = $serviceCollect->groupBy('user_id')->count();

        //==================================================================
        //  重複收藏方案，平均每個人收藏個5個很厲害了，超過就讓他GG吧
        //  處置：超過就以不重複人數*3筆計算
        //==================================================================
        if ($total > $uniqueMember * 5) {
            $total = $uniqueMember * 3;
            $this->imitation[] = 'ServiceCollectCount';
        }

        return $total;
    }

    /**
     * 取得收藏商家廳房的數量
     *
     * @return int
     */
    private function getVenueRoomCollectCount()
    {
        // 非婚宴場地則直接回傳0
        if ($this->store->type != 5) {
            return 0;
        }

        $venueRoomsIds    = $this->store->venueRooms->pluck('id');
        $venueRoomCollect = $this->userCollect->where('type', 'venue_room')
                                                ->whereIn('target_id', $venueRoomsIds)
                                                ->whereBetween('created_at', $this->dateRange);

        $total        = $venueRoomCollect->count();
        $uniqueMember = $venueRoomCollect->groupBy('user_id')->count();

        //==================================================================
        //  重複收藏廳房，平均每個人收藏個5個很厲害了，超過就讓他GG吧
        //  處置：超過就以不重複人數*3筆計算
        //==================================================================
        if ($total > $uniqueMember * 5) {
            $total = $uniqueMember * 3;
            $this->imitation[] = 'VenueRoomCollectCount';
        }

        return $total;
    }

    /**
     * 檢查是否有商家標記-黑名單(偷雞/假流量)
     *
     * @return voit
     */
    private function checkeStoreTagGuarded()
    {
        //==================================================================
        //  檢查是否擁有 黑名單(偷雞/假流量)的標籤
        //  處置：如果兩個都有就應該重懲
        //==================================================================
        $guardedCount = $this->store->marks->whereIn('id', $this->setTag->storeBlackIds)->count();
        if (!$guardedCount) {
            return;
        }

        $this->statistic['store']['pageViews'] = $this->statistic['store']['totalUsers'] *= 0.01;
        if ($guardedCount > 1) {
            $this->statistic['store'] = ['pageViews' => 0, 'totalUsers' => 0];
            $this->statistic['share'] = ['pageViews' => 0, 'totalUsers' => 0];
            $this->statistic['click'] = ['totalEvents' => 0, 'totalUsers' => 0];
            $this->statistic['message'] = 0;
            $this->statistic['reserve'] = 0;
            $this->statistic['collect']['store']     = 0;
            $this->statistic['collect']['work']      = 0;
            $this->statistic['collect']['service']   = 0;
            $this->statistic['collect']['venueRoom'] = 0;
        }
        $this->imitation[] = 'StoreTagGuarded';
    }

    /**
     * 儲存計算積分
     *
     * @return voit
     */
    private function saveComputingIntegral()
    {
        // 基礎分數
        $basic = 0;
        $basic += $this->statistic['store']['pageViews'] * $this->weights['storePageViews'];
        $basic += $this->statistic['store']['totalUsers'] * $this->weights['storeTotalUsers'];
        $basic += $this->statistic['organic']['pageViews'] * $this->weights['organicPageViews'];
        $basic += $this->statistic['organic']['totalUsers'] * $this->weights['organicTotalUsers'];
        $basic += $this->statistic['share']['pageViews'] * $this->weights['sharePageViews'];
        $basic += $this->statistic['share']['totalUsers'] * $this->weights['shareTotalUsers'];
        $basic += $this->statistic['click']['totalUsers'] * $this->weights['clickTotalUsers'];
        $basic += $this->statistic['message'] * $this->weights['message'];
        $basic += $this->statistic['reserve'] * $this->weights['reserve'];
        $basic += $this->statistic['collect']['store'] * $this->weights['collectStore'];
        $basic += $this->statistic['collect']['work'] * $this->weights['collectWork'];
        $basic += $this->statistic['collect']['service'] * $this->weights['collectService'];
        $basic += $this->statistic['collect']['venueRoom'] * $this->weights['collectVenueRoom'];

        //==================================================================
        //  檢查是否擁有 白名單(小編/培養/大師)的標籤
        //  處置：加分囉～
        //==================================================================
        foreach ($this->store->marks as $setTag) {
            if (!in_array($setTag->id, $this->setTag->storeWhiteIds)) {
                continue;
            }
            if ($setTag->id == $this->setTag->storeMasterId) { // 大師
                // $basic += $basic * 1.4;
                $basic += $basic * 50; // 20250610 大師加分暫時調整為50倍
            } else { // 培養/小編
                $basic += $basic * 1.2;
            }
        }

        // 計算收益 (目前婚宴場地沒有收費，所以也沒有產品單價，先用基本設定費來判斷有沒有品項)
        $income = 0;
        if (in_array($this->store->type, array_keys($this->orderProduct->SETTING_FEE))) {
            $income += $this->statistic['click']['totalUsers'] * $this->priceList['click'];
            $income += $this->statistic['message'] * $this->priceList['message'][$this->store->type];
            $income += $this->statistic['reserve'] * $this->priceList['reserve'][$this->store->type];
        }

        // 計算轉換率
        $conversion = 0;
        if ($income > 0 && $this->statistic['store']['pageViews'] > 0) {
            $conversion = round(($income / $this->statistic['store']['pageViews']), 2);
        }

        // 討論度的積分
        $discussion = 0;
        $discussion += $this->statistic['organic']['pageViews'] * $this->weights['organicPageViews'];
        $discussion += $this->statistic['organic']['totalUsers'] * $this->weights['organicTotalUsers'];

        // 儲存積分
        $this->store->integrals()->updateOrCreate([
            'created_date' => date('Y-m-d', strtotime($this->dateRange[1])),
        ], [
            'basic'      => $basic,
            'income'     => $income,
            'conversion' => $conversion,
            'discussion' => $discussion,
            'imitation'  => $this->imitation ? implode(',', $this->imitation) : NULL,
        ]);
    }
}
