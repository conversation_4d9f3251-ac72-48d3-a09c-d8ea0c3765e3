<?php
/*
 |--------------------------------------
 |  相本一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main;

use App\Repositories\StoreAlbumRepository;
use App\Repositories\StoreAlbumImageRepository;
use App\Repositories\StoreRepository;
use App\Models\CityData;
use App\Models\StudioAlbum;
use App\Models\DressAlbum;
use App\Models\DecorationAlbum;
use App\Models\WeddingcakeAlbum;
use App\Models\StoreTag;
use App\Services\Tools\GetQueryKeyService;
use App\Services\Store\Main\ActivityListService;

class AlbumListService
{
    private $storeAlbumRepository;
    private $storeAlbumImageRepository;
    private $storeRepository;
    private $store;
    private $storeAlbum;
    private $cityData;
    private $studioAlbum;
    private $dressAlbum;
    private $decorationAlbum;
    private $weddingcakeAlbum;
    private $storeTag;
    private $getQueryKeyService;
    private $activityListService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreAlbumRepository $storeAlbumRepository,
        StoreAlbumImageRepository $storeAlbumImageRepository,
        StoreRepository $storeRepository,
        CityData $cityData,
        StudioAlbum $studioAlbum,
        DressAlbum $dressAlbum,
        DecorationAlbum $decorationAlbum,
        WeddingcakeAlbum $weddingcakeAlbum,
        StoreTag $storeTag,
        GetQueryKeyService $getQueryKeyService,
        ActivityListService $activityListService
    ) {
        $this->storeAlbumRepository      = $storeAlbumRepository;
        $this->storeAlbumImageRepository = $storeAlbumImageRepository;
        $this->storeRepository           = $storeRepository;
        $this->store                     = $storeRepository->getModel();
        $this->storeAlbum                = $storeAlbumRepository->getModel();
        $this->cityData                  = $cityData;
        $this->studioAlbum               = $studioAlbum;
        $this->dressAlbum                = $dressAlbum;
        $this->decorationAlbum           = $decorationAlbum;
        $this->weddingcakeAlbum          = $weddingcakeAlbum;
        $this->storeTag                  = $storeTag;
        $this->getQueryKeyService        = $getQueryKeyService;
        $this->activityListService       = $activityListService;
    }

    /**
     * 相本一覽
     */
    public function run($request)
    {
        $storeType    = $request['store_type'];
        $storeTypeKey = $request['store_type_key'];
        $dataType     = $request['data_type'];
        $keyword      = $request['keyword'];
        $weddingDate  = $request['wedding_date'];

        // 存取cache
        $cacheKey = 'store:albums-filter-list-'.$storeType;
        if (!env('APP_DEBUG') && cache($cacheKey) && !$keyword && !$weddingDate) {
            $filterList = cache($cacheKey);
        } else {

            // 篩選器的下拉式選單
            $filterList = $this->getFilterSelectElementsList($storeType, $storeTypeKey, $dataType, $keyword, $weddingDate);

            // 沒有關鍵字&婚期搜尋才cache，有效時間30分鐘
            if (!env('APP_DEBUG') && !$keyword && !$weddingDate) {
                cache([$cacheKey => $filterList], 60 * 30);
            }
        }

        // 商家相本列表
        if (in_array($dataType, ['album', 'tasting'])) {
            $list = $this->storeAlbumRepository->getListByRequest($request);

        // 商家照片列表
        } else {
            // $list = $this->storeAlbumImageRepository->getListByRequest($request);
        }

        // 使用者收藏的商家禮服ID集合
        $user = $request['user'];
        $userCollects = $user ? $user->storeWorkCollects($storeType)->pluck('target_id') : [];

        // 記錄查詢參數
        $this->getQueryKeyService->run($request, 'store_album');

        return [
            'dataType'           => $dataType,
            'total'              => $filterList['total'],
            'storeCount'         => $filterList['storeCount'],
            'locations'          => $filterList['locations'],
            // 'imageLocations'  => $filterList['imageLocations'],
            'freeFares'          => $filterList['freeFares'],
            'priceRange'         => $filterList['priceRange'],
            'types'              => $filterList['types'],
            'albumTags'          => $filterList['albumTags'],
            // 'imageTags'       => $filterList['imageTags'],
            'weddingcakeDemands' => $filterList['weddingcakeDemands'],
            'activityList'       => $this->activityListService->run($storeType),
            'list'               => $list,
            'userCollects'       => $userCollects,
        ];
    }

    /**
     * 取得篩選器的下拉式選單
     */
    protected function getFilterSelectElementsList($storeType, $storeTypeKey, $dataType, $keyword, $weddingDate)
    {
        // 相本/照片 一覽總數 (供前端重設篩選時，顯示總數量)
        $repository = (in_array($dataType, ['album', 'tasting'])) ? 'storeAlbumRepository' : 'storeAlbumImageRepository';
        $total = $this->{$repository}->getListTotalByStoreType($storeType, $keyword, $weddingDate);

        // 商家總數 (供前端SEO MetaData Description)
        $storeCount = $this->storeRepository->getListTotalByType($storeType, $keyword, $weddingDate);

        // 商家地點列表 (拍婚紗/婚紗禮服/婚攝婚錄/新娘秘書/主持人/喜餅)
        $locations = collect([]);
        if (in_array($storeType, [1, 2, 3, 4, 8, 10])) {
            $locations = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate) {

                                                // 喜餅商家的搜尋地點，是搜尋喜餅門市的所在地
                                                $relation = ($storeType == 10) ? 'weddingcakeShopStores' : 'stores';

                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with([$relation => function($q2) use ($storeType, $keyword, $weddingDate) {
                                                        $q2->select('stores.id', 'stores.city_id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeywordForAlbum($keyword)
                                                            ->searchWeddingDate($weddingDate);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        // 拍攝地點列表 (拍婚紗)
        // $imageLocations = collect([]);
        // if ($storeType == 1) {
        //     $imageLocations = $this->cityData->region()
        //                                 ->with([
        //                                     'children' => function($q1) use ($storeType, $keyword) {
        //                                         // 限特定類型的已付費商家數
        //                                         $q1->select('id', 'title', 'parent_id')
        //                                             ->with(['studioAlbumImages.storeAlbumImage.album.store' => function($q2) use ($storeType, $keyword) {
        //                                                 $q2->published($storeType)
        //                                                     ->usageActive()
        //                                                     ->searchKeywordForAlbum($keyword);
        //                                             }]);
        //                                     }
        //                                 ])
        //                                 ->get();
        // }

        // 免車馬費列表 (限有檔期的商家類型)
        $freeFares = collect([]);
        if ($this->store->hasScheduleDateByType($storeType)) {
            $freeFares = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate) {
                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with(['freeFareStores' => function($q2) use ($storeType, $keyword, $weddingDate) {
                                                        $q2->select('stores.id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeywordForAlbum($keyword)
                                                            ->searchWeddingDate($weddingDate);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        // 價格區間列表 (婚紗禮服/婚禮佈置/喜餅)
        $priceRange = [];
        if (in_array($storeType, [2, 6, 10])) {
            $relationTable = $storeTypeKey.'Album';
            foreach ($this->{$relationTable}->priceRangeList as $key => $item) {
                $priceRange[] = [
                    'key'       => $key,
                    'value'     => $item['label'],
                    'is_exists' => $this->storeAlbumRepository->existsStoreAlbumByPriceRangeKey($relationTable, $key, $storeType, $keyword),
                ];
            }
        }

        // 類型列表 (拍婚紗/婚禮佈置)
        $types = [];
        if (in_array($storeType, [1, 6])) {
            $relationTable = $storeTypeKey.'Album';
            foreach ($this->{$relationTable}->typeList as $key => $value) {
                $types[] = [
                    'key'       => $key,
                    'value'     => $value,
                    'is_exists' => $this->storeAlbumRepository->existsStoreAlbumByType($relationTable, $key, $storeType, $keyword),
                ];
            }
        }

        // 作品集標籤列表 (婚紗禮服/婚禮佈置/喜餅)
        $albumTags = collect([]);
        if (in_array($storeType, [2, 6, 10])) {
            $albumTags = $this->storeTag->getSearchListByType($storeType, 'album', $keyword);
        }

        // 作品照標籤列表 (拍婚紗/婚攝婚錄/新娘秘書)
        // $imageTags = collect([]);
        // if (in_array($storeType, [1, 3, 4])) {
        //     $imageTags = $this->storeTag->getSearchListByType($storeType, 'album_image', $keyword, $weddingDate);
        // }

        // 禮盒需求:可客製化/葷/奶素/蛋奶素/全素 (喜餅)
        $weddingcakeDemands = [
            'can_customized'          => false,
            'is_meat'                 => false,
            'is_lacto_vegetarian'     => false,
            'is_ovo_lacto_vegetarian' => false,
            'is_vegetarian'           => false,
        ];
        if ($storeType == 10) {
            foreach ($weddingcakeDemands as $item => $val) {
                $weddingcakeDemands[$item] = $this->storeAlbum->storeHasPaid($storeType)
                                                                ->storeUsageActive()
                                                                ->searchKeywordWithStore($keyword)
                                                                ->whereHas('weddingcakeAlbum', function ($q) use ($item) {
                                                                    $q->where($item, 1);
                                                                })
                                                                ->exists();
            }
        }

        return [
            'total'              => $total,
            'storeCount'         => $storeCount,
            'locations'          => $locations,
            // 'imageLocations'  => $imageLocations,
            'freeFares'          => $freeFares,
            'priceRange'         => $priceRange,
            'types'              => $types,
            'albumTags'          => $albumTags,
            // 'imageTags'       => $imageTags,
            'weddingcakeDemands' => $weddingcakeDemands,
        ];
    }
}
