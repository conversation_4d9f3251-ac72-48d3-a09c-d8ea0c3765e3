<?php
/*
 |--------------------------------------
 |  活動方案一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main;

use App\Models\Activity;

class ActivityListService
{
    private $activity;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Activity $activity
    ) {
        $this->activity = $activity;
    }

    /**
     * 活動方案一覽
     */
    public function run($storeType)
    {
        return $this->activity->storeType((int)$storeType)
                                ->published()
                                ->get()
                                ->map(function($item) {
                                    return $item->only(['id', 'title', 'name', 'link']);
                                });
    }
}
