<?php
/*
 |--------------------------------------
 |  小物一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main\Wdv2;

use App\Repositories\Wdv2\MallItemRepository;
use App\Services\Tools\GetQueryKeyService;

class MallItemListService
{
    private $mallItemRepository;
    private $getQueryKeyService;
    private $storeType;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        MallItemRepository $mallItemRepository,
        GetQueryKeyService $getQueryKeyService
    ) {
        $this->mallItemRepository = $mallItemRepository;
        $this->getQueryKeyService = $getQueryKeyService;

        $this->storeType = $this->mallItemRepository->getModel()->storeType;
    }

    /**
     * 小物一覽
     */
    public function run($request)
    {
        $keyword = $request['keyword'];

        // 存取cache
        $cacheKey = 'store:mall-items-filter-list';
        if (!env('APP_DEBUG') && cache($cacheKey) && !$keyword) {
            $filterList = cache($cacheKey);
        } else {

            // 篩選器的下拉式選單
            $filterList = $this->getFilterSelectElementsList($keyword);

            // 沒有關鍵字搜尋才cache，有效時間30分鐘
            if (!env('APP_DEBUG') && !$keyword) {
                cache([$cacheKey => $filterList], 60 * 30);
            }
        }

        // 婚禮小物列表
        $items = $this->mallItemRepository->getListByRequest($request);

        // 記錄查詢參數
        $this->getQueryKeyService->run($request, 'store_mall_item');

        return [
            'total'        => $filterList['total'],
            'priceRange'   => $filterList['priceRange'],
            'items'        => $items,
        ];
    }

    /**
     * 取得篩選器的下拉式選單
     */
    protected function getFilterSelectElementsList($keyword)
    {
        // 小物一覽總數 (供前端重設篩選時，顯示總數量)
        $total = $this->mallItemRepository->getListTotal($keyword);

        // 價格區間列表
        $priceRange = [];
        foreach ($this->mallItemRepository->priceRangeList as $key => $item) {
            $priceRange[] = [
                'key'        => $key,
                'value'      => $item['label'],
                'item_count' => $this->mallItemRepository->countItemsByPriceRangeKey($key, $keyword),

            ];
        }

        return [
            'total'      => $total,
            'priceRange' => $priceRange,
        ];
    }
}
