<?php
/*
 |--------------------------------------
 |  影片一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main;

use App\Repositories\StoreVideoRepository;
use App\Repositories\StoreRepository;
use App\Models\CityData;
use App\Services\Tools\GetQueryKeyService;

class VideoListService
{
    private $storeVideoRepository;
    private $storeRepository;
    private $cityData;
    private $getQueryKeyService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreVideoRepository $storeVideoRepository,
        StoreRepository $storeRepository,
        CityData $cityData,
        GetQueryKeyService $getQueryKeyService
    ) {
        $this->storeVideoRepository = $storeVideoRepository;
        $this->storeRepository      = $storeRepository;
        $this->cityData             = $cityData;
        $this->getQueryKeyService   = $getQueryKeyService;
    }

    /**
     * 影片一覽
     */
    public function run($request)
    {
        $storeType    = $request['store_type'];
        $storeTypeKey = $request['store_type_key'];
        $keyword      = $request['keyword'];
        $weddingDate  = $request['wedding_date'];

        // 存取cache
        $cacheKey = 'store:videos-filter-list-'.$storeType;
        if (!env('APP_DEBUG') && cache($cacheKey) && !$keyword && !$weddingDate) {
            $filterList = cache($cacheKey);
        } else {

            // 篩選器的下拉式選單
            $filterList = $this->getFilterSelectElementsList($storeType, $storeTypeKey, $keyword, $weddingDate);

            // 沒有關鍵字&婚期搜尋才cache，有效時間30分鐘
            if (!env('APP_DEBUG') && !$keyword && !$weddingDate) {
                cache([$cacheKey => $filterList], 60 * 30);
            }
        }

        // 商家影片列表
        $list = $this->storeVideoRepository->getListByRequest($request);

        // 使用者收藏的商家禮服ID集合
        // $user = $request['user'];
        // $userCollects = $user ? $user->storeWorkCollects($storeType)->pluck('target_id') : [];

        // 記錄查詢參數
        $this->getQueryKeyService->run($request, 'store_video');

        return [
            'total'      => $filterList['total'],
            'storeCount' => $filterList['storeCount'],
            'locations'  => $filterList['locations'],
            'freeFares'  => $filterList['freeFares'],
            'list'       => $list,
            // 'userCollects'   => $userCollects,
        ];
    }

    /**
     * 取得篩選器的下拉式選單
     */
    protected function getFilterSelectElementsList($storeType, $storeTypeKey, $keyword, $weddingDate)
    {
        // 影片 一覽總數 (供前端重設篩選時，顯示總數量)
        $total = $this->storeVideoRepository->getListTotalByStoreType($storeType, $keyword, $weddingDate);

        // 商家總數 (供前端SEO MetaData Description)
        $storeCount = $this->storeRepository->getListTotalByType($storeType, $keyword, $weddingDate);

        // 商家地點列表 (婚攝婚錄/主持人)
        $locations = collect([]);
        if (in_array($storeType, [3, 8])) {
            $locations = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate) {
                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with(['stores' => function($q2) use ($storeType, $keyword, $weddingDate) {
                                                        $q2->select('stores.id', 'stores.city_id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeywordForVideo($keyword)
                                                            ->searchWeddingDate($weddingDate);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        // 免車馬費列表 (限有檔期的商家類型)
        $freeFares = collect([]);
        if ($this->storeRepository->getModel()->hasScheduleDateByType($storeType)) {
            $freeFares = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate) {
                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with(['freeFareStores' => function($q2) use ($storeType, $keyword, $weddingDate) {
                                                        $q2->select('stores.id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeywordForVideo($keyword)
                                                            ->searchWeddingDate($weddingDate);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        return [
            'total'      => $total,
            'storeCount' => $storeCount,
            'locations'  => $locations,
            'freeFares'  => $freeFares,
        ];
    }
}
