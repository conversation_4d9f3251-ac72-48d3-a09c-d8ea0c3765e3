<?php
/*
 |--------------------------------------
 |  方案一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main;

use App\Repositories\StoreServiceRepository;
use App\Models\CityData;
use App\Models\StoreTag;
use App\Models\Store;
use App\Services\Tools\GetQueryKeyService;
use App\Services\Store\Main\ActivityListService;

class ServiceListService
{
    private $storeServiceRepository;
    private $cityData;
    private $storeTag;
    private $store;
    private $getQueryKeyService;
    private $activityListService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreServiceRepository $storeServiceRepository,
        CityData $cityData,
        StoreTag $storeTag,
        Store $store,
        GetQueryKeyService $getQueryKeyService,
        ActivityListService $activityListService
    ) {
        $this->storeServiceRepository = $storeServiceRepository;
        $this->cityData               = $cityData;
        $this->storeTag               = $storeTag;
        $this->store                  = $store;
        $this->getQueryKeyService     = $getQueryKeyService;
        $this->activityListService    = $activityListService;
    }

    /**
     * 方案一覽
     */
    public function run($request)
    {
        $storeType   = $request['store_type'];
        $keyword     = $request['keyword'];
        $weddingDate = $request['wedding_date'];

        // 存取cache
        $cacheKey = 'store:services-filter-list-'.$storeType;
        if (!env('APP_DEBUG') && cache($cacheKey) && !$keyword && !$weddingDate) {
            $filterList = cache($cacheKey);
        } else {

            // 篩選器的下拉式選單
            $filterList = $this->getFilterSelectElementsList($storeType, $keyword, $weddingDate);

            // 沒有關鍵字搜尋才cache，有效時間30分鐘
            if (!env('APP_DEBUG') && !$keyword && !$weddingDate) {
                cache([$cacheKey => $filterList], 60 * 30);
            }
        }

        // 商家方案列表
        $services = $this->storeServiceRepository->getListByRequest($request);

        // 使用者收藏的商家方案ID集合
        $user = $request['user'];
        $userCollects = $user ? $user->storeServiceCollects($storeType)->pluck('target_id') : [];

        // 記錄查詢參數
        $this->getQueryKeyService->run($request, 'store_service');

        return [
            'total'        => $filterList['total'],
            'locations'    => $filterList['locations'],
            'freeFares'    => $filterList['freeFares'],
            'demandList'   => $filterList['demandList'],
            'serviceTags'  => $filterList['serviceTags'],
            'storeTags'    => $filterList['storeTags'],
            'hasDiscount'  => $filterList['hasDiscount'],
            'activityList' => $this->activityListService->run($storeType),
            'services'     => $services,
            'userCollects' => $userCollects,
        ];
    }

    /**
     * 取得篩選器的下拉式選單
     */
    protected function getFilterSelectElementsList($storeType, $keyword, $weddingDate)
    {
        $storeService = $this->storeServiceRepository->getModel();

        // 方案一覽總數 (供前端重設篩選時，顯示總數量)
        $total = $this->storeServiceRepository->getListTotalByStoreType($storeType, $keyword, $weddingDate);

        // 商家地點列表
        $locations = $this->cityData->select('id', 'title')
                                    ->region()
                                    ->with([
                                        'children' => function($q1) use ($storeType, $keyword, $weddingDate) {

                                            // 喜餅商家的搜尋地點，是搜尋喜餅門市的所在地
                                            $relation = ($storeType == 10) ? 'weddingcakeShopStores' : 'stores';

                                            // 限特定類型的已付費商家數
                                            $q1->select('id', 'title', 'parent_id')
                                                ->with([$relation => function($q2) use ($storeType, $keyword, $weddingDate) {
                                                    $q2->select('stores.id', 'stores.city_id')
                                                        ->published($storeType)
                                                        ->usageActive()
                                                        ->searchKeywordForService($keyword)
                                                        ->searchWeddingDate($weddingDate);
                                                }]);
                                        }
                                    ])
                                    ->get();

        // 免車馬費列表 (限有檔期的商家類型)
        $freeFares = collect([]);
        if ($this->store->hasScheduleDateByType($storeType)) {
            $freeFares = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate) {
                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with(['freeFareStores' => function($q2) use ($storeType, $keyword, $weddingDate) {
                                                        $q2->select('stores.id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeywordForService($keyword)
                                                            ->searchWeddingDate($weddingDate);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        // 方案需求(含價格區間)架構
        $demandList = [];
        foreach ($storeService->demandStructure[$storeType] as $demand => $data) {

            // 多選項目
            if (is_array($data['list'])) {
                $demandList[$demand] = [];
                foreach ($data['list'] as $key => $value) {
                    $demandList[$demand][] = [
                        'key'         => $key,
                        'value'       => is_array($value) ? $value['label'] : $value,
                        'service_ids' => $this->storeServiceRepository->getStoreServiceIdsByScope($demand, $key, $storeType, $keyword, $weddingDate),
                    ];
                }

            // 單選項目
            } else {
                $demandList[$demand] = $this->storeServiceRepository->getStoreServiceIdsByScope($demand, $data['list'], $storeType, $keyword, $weddingDate);
            }
        }

        // 商家標籤列表 (婚宴場地) & 方案標籤列表 (婚宴場地/主持人)
        $serviceTags = collect([]);
        $storeTags   = collect([]);
        if ($storeType == 5) {
            $serviceTags = $this->storeTag->getSearchListByType($storeType, 'service', $keyword, $weddingDate); // 方案包含的服務-列表
            $storeTags   = $this->storeTag->getSearchListByType($storeType, 'store', $keyword, $weddingDate, NULL, true); // 商家提供的服務-列表
        } elseif ($storeType == 8) {
            $serviceTags = $this->storeTag->getSearchListByType($storeType, 'service', $keyword, $weddingDate); // 方案包含的服務-列表
        }

        // 是否有獨家優惠的商家
        $hasDiscount = $storeService->storeHasPaid($storeType)
                                    ->storeUsageActive()
                                    ->searchKeywordWithStore($keyword)
                                    ->searchWeddingDate($weddingDate)
                                    ->storeHasDiscount()
                                    ->exists();

        return [
            'total'       => $total,
            'locations'   => $locations,
            'freeFares'   => $freeFares,
            'demandList'  => $demandList,
            'serviceTags' => $serviceTags,
            'storeTags'   => $storeTags,
            'hasDiscount' => $hasDiscount,
        ];
    }
}
