<?php
/*
 |--------------------------------------
 |  廳房一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main;

use App\Repositories\VenueRoomRepository;
// use App\Repositories\StoreRepository;
use App\Models\CityData;
use App\Services\Tools\GetQueryKeyService;

class VenueRoomListService
{
    private $venueRoomRepository;
    // private $storeRepository;
    private $cityData;
    private $getQueryKeyService;
    private $storeType;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        VenueRoomRepository $venueRoomRepository,
        // StoreRepository $storeRepository,
        CityData $cityData,
        GetQueryKeyService $getQueryKeyService
    ) {
        $this->venueRoomRepository = $venueRoomRepository;
        // $this->storeRepository     = $storeRepository;
        $this->cityData            = $cityData;
        $this->getQueryKeyService  = $getQueryKeyService;

        $this->storeType = $this->venueRoomRepository->getModel()->storeType;
    }

    /**
     * 廳房一覽
     */
    public function run($request)
    {
        $keyword   = $request['keyword'];

        // 存取cache
        $cacheKey = 'store:venue-rooms-filter-list';
        if (!env('APP_DEBUG') && cache($cacheKey) && !$keyword) {
            $filterList = cache($cacheKey);
        } else {

            // 篩選器的下拉式選單
            $filterList = $this->getFilterSelectElementsList($keyword);

            // 沒有關鍵字搜尋才cache，有效時間30分鐘
            if (!env('APP_DEBUG') && !$keyword) {
                cache([$cacheKey => $filterList], 60 * 30);
            }
        }

        // 商家廳房列表
        $rooms = $this->venueRoomRepository->getListByRequest($request);

        // 使用者收藏的商家方案ID集合
        $user = $request['user'];
        $userCollects = $user ? $user->storeVenueRoomCollects($this->storeType)->pluck('target_id') : [];

        // 記錄查詢參數
        $this->getQueryKeyService->run($request, 'store_venue_room');

        return [
            'total'        => $filterList['total'],
            // 'storeCount'   => $filterList['storeCount'],
            'locations'    => $filterList['locations'],
            'types'        => $filterList['types'],
            'numbers'      => $filterList['numbers'],
            'devices'      => $filterList['devices'],
            'rooms'        => $rooms,
            'userCollects' => $userCollects,
        ];
    }

    /**
     * 取得篩選器的下拉式選單
     */
    protected function getFilterSelectElementsList($keyword)
    {
        // 廳房一覽總數 (供前端重設篩選時，顯示總數量)
        $total = $this->venueRoomRepository->getListTotal($keyword);

        // 商家總數 (供前端SEO MetaData Description)
        // $storeCount = $this->storeRepository->getListTotalByType($this->storeType, $keyword);

        // 商家地點列表
        $locations = $this->cityData->select('id', 'title')
                                    ->region()
                                    ->with([
                                        'children' => function($q1) use ($keyword) {
                                            // 限特定類型的已付費商家數
                                            $q1->select('id', 'title', 'parent_id')
                                                ->with(['stores' => function($q2) use ($keyword) {
                                                    $q2->select('stores.id', 'stores.city_id')
                                                        ->published($this->storeType)
                                                        ->usageActive()
                                                        ->searchKeywordForVenueRoom($keyword);
                                                }]);
                                        }
                                    ])
                                    ->get();

        // 廳房類型
        $types = [];
        foreach ($this->venueRoomRepository->getModel()->typeList as $type => $value) {
            $types[] = [
                'key'      => $type,
                'value'    => $value,
                'room_ids' => $this->venueRoomRepository->getVenueRoomIdsByType($type, $keyword),
            ];
        }

        // 容納人數
        $numbers = [];
        foreach ($this->venueRoomRepository->getModel()->numberList as $key => $data) {
            $numbers[] = [
                'key'      => $key,
                'value'    => $data['label'],
                'room_ids' => $this->venueRoomRepository->getVenueRoomIdsByNumber($key, $keyword),
            ];
        }

        // 廳房設備
        $devices = [];
        foreach ($this->venueRoomRepository->getModel()->deviceList as $column => $data) {
            $devices[] = [
                'key'      => $column,
                'value'    => $data['label'],
                'room_ids' => $this->venueRoomRepository->getVenueRoomIdsByDevice($column, $keyword),
            ];
        }

        return [
            'total'      => $total,
            // 'storeCount' => $storeCount,
            'locations'  => $locations,
            'types'      => $types,
            'numbers'    => $numbers,
            'devices'    => $devices,
        ];
    }
}
