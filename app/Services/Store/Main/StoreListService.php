<?php
/*
 |--------------------------------------
 |  商家一覽 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Main;

use App\Repositories\StoreRepository;
use App\Models\CityData;
use App\Models\StoreTag;
use App\Models\Store;
use App\Services\Tools\GetQueryKeyService;
use App\Services\Store\Main\ActivityListService;

class StoreListService
{
    private $storeRepository;
    private $cityData;
    private $storeTag;
    private $store;
    private $getQueryKeyService;
    private $activityListService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreRepository $storeRepository,
        CityData $cityData,
        StoreTag $storeTag,
        Store $store,
        GetQueryKeyService $getQueryKeyService,
        ActivityListService $activityListService
    ) {
        $this->storeRepository     = $storeRepository;
        $this->cityData            = $cityData;
        $this->storeTag            = $storeTag;
        $this->store               = $store;
        $this->getQueryKeyService  = $getQueryKeyService;
        $this->activityListService = $activityListService;
    }

    /**
     * 商家一覽
     */
    public function run($request)
    {
        $storeType       = $request['store_type'];
        $keyword         = $request['keyword'];
        $weddingDate     = $request['wedding_date'];
        $freeShopTasting = $request['has_free_shop_tasting'];

        // 存取cache
        $cacheKey = 'store:stores-filter-list-'.$storeType;
        if ($freeShopTasting) {
            $cacheKey .= '-freeShopTasting';
        }
        if (!env('APP_DEBUG') && cache($cacheKey) && !$keyword && !$weddingDate) {
            $filterList = cache($cacheKey);
        } else {

            // 篩選器的下拉式選單
            $filterList = $this->getFilterSelectElementsList($storeType, $keyword, $weddingDate, $freeShopTasting);

            // 沒有關鍵字&婚期搜尋才cache，有效時間30分鐘
            if (!env('APP_DEBUG') && !$keyword && !$weddingDate) {
                cache([$cacheKey => $filterList], 60 * 30);
            }
        }

        // 商家列表
        $stores = $this->storeRepository->getListByRequest($request);

        // 使用者收藏的商家ID集合
        $user = $request['user'];
        $userCollects = $user ? $user->storeCollects($storeType)->pluck('target_id') : [];

        // 記錄查詢參數
        $this->getQueryKeyService->run($request, 'store');

        return [
            'total'         => $filterList['total'],
            'locations'     => $filterList['locations'],
            'storeTags'     => $filterList['storeTags'],
            'freeFares'     => $filterList['freeFares'],
            'extraServices' => $filterList['extraServices'],
            'hasFiles'      => $filterList['hasFiles'],
            'hasTrialFee'   => $filterList['hasTrialFee'],
            'hasDiscount'   => $filterList['hasDiscount'],
            'activityList'  => $this->activityListService->run($storeType),
            'stores'        => $stores,
            'userCollects'  => $userCollects,
        ];
    }

    /**
     * 取得篩選器的下拉式選單
     */
    protected function getFilterSelectElementsList($storeType, $keyword, $weddingDate, $freeShopTasting)
    {
        // 商家一覽總數 (供前端重設篩選時，顯示總數量)
        $total = $this->storeRepository->getListTotalByType($storeType, $keyword, $weddingDate, $freeShopTasting);

        // 所在地列表 (拍婚紗/婚紗禮服/婚攝婚錄/新娘秘書/婚宴場地/主持人/喜餅)
        $locations = collect([]);
        if (in_array($storeType, [1, 2, 3, 4, 5, 8, 10])) {
            $locations = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate, $freeShopTasting) {

                                                // 喜餅商家的搜尋地點，是搜尋喜餅門市的所在地
                                                $relation = ($storeType == 10) ? 'weddingcakeShopStores' : 'stores';

                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with([$relation => function($q2) use ($storeType, $keyword, $weddingDate, $freeShopTasting) {
                                                        $q2->select('stores.id', 'stores.city_id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeyword($keyword)
                                                            ->searchWeddingDate($weddingDate)
                                                            ->freeShopTasting($freeShopTasting);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        // 商家標籤列表
        $storeTags = $this->storeTag->getSearchListByType($storeType, 'store', $keyword, $weddingDate, $freeShopTasting); // 商家提供的服務-列表

        // 免車馬費列表 (限有檔期的商家類型)
        $freeFares = collect([]);
        if ($this->store->hasScheduleDateByType($storeType)) {
            $freeFares = $this->cityData->select('id', 'title')
                                        ->region()
                                        ->with([
                                            'children' => function($q1) use ($storeType, $keyword, $weddingDate, $freeShopTasting) {
                                                // 限特定類型的已付費商家數
                                                $q1->select('id', 'title', 'parent_id')
                                                    ->with(['freeFareStores' => function($q2) use ($storeType, $keyword, $weddingDate, $freeShopTasting) {
                                                        $q2->select('stores.id')
                                                            ->published($storeType)
                                                            ->usageActive()
                                                            ->searchKeyword($keyword)
                                                            ->searchWeddingDate($weddingDate)
                                                            ->freeShopTasting($freeShopTasting);
                                                    }]);
                                            }
                                        ])
                                        ->get();
        }

        // 服務類型 (婚攝婚錄)
        $extraServices = [];
        $extraServiceList = $this->store->extraServiceList;
        if (isset($extraServiceList[$storeType])) {
            foreach ($extraServiceList[$storeType] as $key => $value) {
                $extraServices[] = [
                    'key'       => $key,
                    'value'     => $value,
                    'store_ids' => $this->store
                                        ->select('stores.id')
                                        ->published($storeType)
                                        ->usageActive()
                                        ->searchKeyword($keyword)
                                        ->searchWeddingDate($weddingDate)
                                        ->freeShopTasting($freeShopTasting)
                                        ->where('extra_type', $key)
                                        ->pluck('id'),
                ];
            }
        }

        // 檔案全贈 (拍婚紗)
        $hasFiles = [];
        if ($storeType == 1) {
            $hasFiles = $this->store
                                ->select('stores.id')
                                ->published($storeType)
                                ->usageActive()
                                ->searchKeyword($keyword)
                                ->searchWeddingDate($weddingDate)
                                ->freeShopTasting($freeShopTasting)
                                ->whereHas('descriptions', function ($q) {
                                    $q = $q->where('key', 'has_files')
                                            ->whereIn('value', [1, 2]);
                                })
                                ->pluck('id');
        }

        // 免試穿費 (婚紗禮服) & 試妝服務 (新娘秘書)
        $hasTrialFee = [];
        if (in_array($storeType, [2, 4])) {
            $hasTrialFee = $this->store
                                ->select('stores.id')
                                ->published($storeType)
                                ->usageActive()
                                ->searchKeyword($keyword)
                                ->searchWeddingDate($weddingDate)
                                ->freeShopTasting($freeShopTasting);

            // 婚紗禮服要篩選免試穿費，所以找有開放試穿(reserve_time > 0)、且無試穿費(has_trial_fee = 0)
            if ($storeType == 2) {
                $hasTrialFee = $hasTrialFee->whereHas('descriptions', function ($q) {
                                                $q = $q->where('key', 'reserve_time')
                                                        ->where('value', '>', 0);
                                            });
            }
            $hasTrialFee = $hasTrialFee->whereHas('descriptions', function ($q) use ($storeType) {
                                            $value = ($storeType == 2) ? 0 : 1; // 婚紗禮服要篩選免試穿費
                                            $q = $q->where('key', 'has_trial_fee')
                                                    ->where('value', $value);
                                        })
                                        ->pluck('id');
        }

        // 是否有獨家優惠的商家
        $hasDiscount = $this->store
                            ->published($storeType)
                            ->usageActive()
                            ->searchKeyword($keyword)
                            ->searchWeddingDate($weddingDate)
                            ->freeShopTasting($freeShopTasting)
                            ->whereHas('discounts')
                            ->exists();

        return [
            'total'         => $total,
            'locations'     => $locations,
            'storeTags'     => $storeTags,
            'freeFares'     => $freeFares,
            'extraServices' => $extraServices,
            'hasFiles'      => $hasFiles,
            'hasTrialFee'   => $hasTrialFee,
            'hasDiscount'   => $hasDiscount,
        ];
    }
}
