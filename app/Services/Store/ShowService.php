<?php
/*
 |--------------------------------------
 |  商家主頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Repositories\ForumArticleRepository as ArticleRepository;
use App\Models\StoreTag;
use App\Models\CityData;
use App\Models\StoreFare;

class ShowService
{
    private $articleRepository;
    private $storeTag;
    private $cityData;
    private $storeFare;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        ArticleRepository $articleRepository,
        StoreTag $storeTag,
        CityData $cityData,
        StoreFare $storeFare
    ) {
        $this->articleRepository = $articleRepository;
        $this->storeTag       = $storeTag;
        $this->cityData       = $cityData;
        $this->storeFare      = $storeFare;
    }

    /**
     * 商家主頁
     */
    public function run($request)
    {
        $store = $request['store'];

        // 正在考慮的新娘數
        $yesterday      = now()->subDay()->format('Y-m-d');
        $storeAnalytics = $store->analytics()->where('created_at', $yesterday)->first();
        $thinkCount     = ceil(($storeAnalytics->statistic->analytics->store->users ?? 0) * 0.3);

        // 作品列表 (預設橫版6筆, 婚紗禮服/新娘秘書 直版8筆, 婚攝/婚錄/婚禮主持人 有動態作品橫版6筆)
        $perPage    = in_array($store->type, [2, 4]) ? 8 : 6;
        $albums     = $store->getReleaseAlbums($perPage);
        $videos     = $store->getReleaseVideos($perPage);
        $services   = $store->getReleaseServices(6);
        $venueRooms = $store->getReleaseVenueRooms(6);

        // 車馬費的所有區域
        $fareCities = $this->cityData->pluck('title', 'id');
        $fareValues = $this->storeFare->valueList[$store->type] ?? [];

        // W姐妹-分享文
        $total = 5;
        $sharePosts = collect([]);
        foreach ($store->brands as $brand) {
            $limit = $total - $sharePosts->count();
            $_temp = $this->articleRepository->getPublishedListByBrand($brand, $sharePosts->pluck('id'), $limit);
            $sharePosts = $sharePosts->merge($_temp);
            if ($sharePosts->count() >= $total) {
                break;
            }
        }

        return [
            'store'      => $store,
            'thinkCount' => $thinkCount,
            'storeTags'  => $this->storeTag->getSettingListByType($store->type, 'store'),
            'albums'     => $albums,
            'videos'     => $videos,
            'services'   => $services,
            'venueRooms' => $venueRooms ?? [],
            'fareCities' => $fareCities,
            'fareValues' => $fareValues,
            'sharePosts' => $sharePosts,
        ];
    }
}
