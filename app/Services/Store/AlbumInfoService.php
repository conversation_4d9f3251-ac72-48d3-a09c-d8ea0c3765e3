<?php
/*
 |--------------------------------------
 |  商家作品內容頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\StoreTagCategory;
use App\Models\StoreTag;

class AlbumInfoService
{
    private $storeTagCategory;
    private $storeTag;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        StoreTagCategory $storeTagCategory,
        StoreTag $storeTag
    ) {
        $this->storeTagCategory = $storeTagCategory;
        $this->storeTag         = $storeTag;
    }

    /**
     * 商家作品內容頁
     */
    public function run($request, $album)
    {
        $store = $request['store'];

        // 作品集標籤列表 (婚紗禮服/婚禮佈置/喜餅需要)
        $albumTags = [];
        if (in_array($store->type, [2, 6, 10])) {
            $storeTagCategory = $this->storeTagCategory->where('store_type', $store->type)
                                                        ->where('type', 'album')
                                                        ->first();
            $storeTagIds = $album->tags->pluck('id');
            $albumTags   = $this->storeTag->select('id', 'name')
                                            ->where('category_id', $storeTagCategory->id)
                                            ->whereNull('parent_id')
                                            ->with('children')
                                            ->get()
                                            ->map(function($storeTag) use ($storeTagIds) {
                                                return [
                                                    'id'       => $storeTag->id,
                                                    'name'     => $storeTag->name,
                                                    'children' => $storeTag->children->map(function($children) use ($storeTagIds) {
                                                                                            if ($storeTagIds->contains($children->id)) {
                                                                                                return $children->only('id', 'name');
                                                                                            }
                                                                                        })
                                                                                        ->filter()
                                                                                        ->values(),
                                                ];
                                            });
        }

        // 作品照片列表 (婚紗禮服/喜餅不用分頁)
        if (in_array($store->type, [2, 10])) {
            $images = $album->images->map(function($albumImage) {
                                        return array_merge(
                                            $albumImage->image->only(['file_name', 'width', 'height']),
                                            ['description' => $albumImage->description]
                                        );
                                    });
        } else {
            $images = $album->images()->paginate(50);
        }

        // 其他推薦作品列表 (預設12筆, 婚攝婚錄&婚禮主持人(有平面+動態作品)/婚宴場地(有廳房)/喜餅 各6筆)
        $lastAlbum       = $store->getLastReleaseAlbum($album);
        $perPage         = in_array($store->type, [3, 5, 8, 10]) ? 6 : 12;
        $otherAlbums     = $store->getOtherReleaseAlbums($perPage, $album);
        $otherVideos     = in_array($store->type, [3, 8]) ? $store->getReleaseVideos($perPage) : [];
        $otherVenueRooms = $store->getReleaseVenueRooms($perPage);

        return [
            'store'           => $store,
            'album'           => $album,
            'albumTags'       => $albumTags,
            'images'          => $images,
            'lastAlbum'       => $lastAlbum,
            'otherAlbums'     => $otherAlbums,
            'otherVideos'     => $otherVideos,
            'otherVenueRooms' => $otherVenueRooms,
        ];
    }
}
