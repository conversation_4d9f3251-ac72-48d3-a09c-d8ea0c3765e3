<?php
/*
 |--------------------------------------
 |  更新商家相本排行 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\StoreAlbum;
use App\Models\LogGaPageView;
use App\Models\StoreIntegral;
use App\Models\Store;
use App\Models\Wdv2\SetTag;
use Carbon\Carbon;

class UpdateStoreAlbumRankService
{
    private $store;
    private $storeAlbum;
    private $setTag;
    private $logGaPageView;
    private $storeIntegral;

    private $runDays   = 10; // 統計範圍的天數
    private $dateRange = [];

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Carbon $carbon,
        Store $store,
        StoreAlbum $storeAlbum,
        SetTag $setTag,
        LogGaPageView $logGaPageView,
        StoreIntegral $storeIntegral
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->carbon        = $carbon;
        $this->store         = $store;
        $this->storeAlbum    = $storeAlbum;
        $this->setTag        = $setTag;
        $this->logGaPageView = $logGaPageView;
        $this->storeIntegral = $storeIntegral;
    }

    /**
     * 計算商家相本的排名積分
     */
    public function run($date)
    {
        // 統計範圍的時間區間
        $this->dateRange = [
            $this->carbon->parse($date)->subDays($this->runDays - 1)->format('Y-m-d 00:00:00'),
            $this->carbon->parse($date)->format('Y-m-d 23:59:59'),
        ];

        // 先清空所有商家相本的熱門排行
        $this->storeAlbum->whereNotNull('rank')
                            ->update(['rank' => NULL]);

        // 有白名單(小編/培養/大師)＆黑名單(偷雞/假流量)標籤的所有付費商家
        $tagGroups = $this->setTag->whereIn('id', array_merge($this->setTag->storeWhiteIds, $this->setTag->storeBlackIds))
                                    ->whereHas('stores', function ($q) {
                                        $q->whereIn('stores.type', array_keys($this->store->needRankList))
                                            ->published();
                                    })
                                    ->with('stores:id')
                                    ->get()
                                    ->groupBy('id')
                                    ->map(function ($setTags) {
                                        foreach ($setTags as $setTag) {
                                            return $setTag->stores->pluck('id');
                                        }
                                    });

        // 取得造假項目的商家ID
        $imitationIds = $this->storeIntegral->where('created_date', $date)
                                            ->whereNotNull('imitation')
                                            ->pluck('store_id');

        // 取得商家相本的流量積分
        $storeTypeGroups = $this->logGaPageView->selectRaw('
                                                    target_id AS album_id,
                                                    stores.id AS store_id,
                                                    stores.type AS store_type,
                                                    SUM(pageViews + totalUsers * 5) AS score
                                                ')
                                                ->join($this->store->getTable(), function($join) {
                                                    $join->on('stores.id', '=', 'log_ga_page_views.parent_id');
                                                })
                                                ->where('log_ga_page_views.type', 'store_album')
                                                ->whereBetween('created_date', $this->dateRange)
                                                ->groupBy('target_id')
                                                ->get()
                                                ->groupBy('store_type');

        // 依序商家分類
        $total = 0;
        foreach ($storeTypeGroups as $storeType => $logs) {

            // 初始化總分數據
            $this->score        = [];
            $this->scoreNew     = [];
            $this->scoreTop     = [];
            $this->albumStoreId = [];
            $this->rank         = [];
            foreach ($logs as $log) {

                $storeAlbum = $this->storeAlbum->find($log->album_id);
                if (!$storeAlbum) {
                    continue;
                }

                // 相本的流量積分 + 收藏積分
                $score = $log->score + $this->getCollectScore($storeType, $storeAlbum);

                // 新上架的相本
                $log->is_new = false;
                if ($storeAlbum->onlined_at >= date('Y-m-d', strtotime($date.'-15 days'))) {
                    $log->is_new = true;
                }

                // 商家標記-大師
                $log->is_top = false;
                if ($tagGroups->contains(function ($value, $key) use ($log) {
                    return $key == $this->setTag->storeMasterId && $value->contains($log->store_id);
                })) {
                    $score *= 1.3;
                    $log->is_top = true;
                }

                // 商家標記-白名單(小編/培養，不用包含大師)
                if ($tagGroups->contains(function ($value, $key) use ($log) {
                    return in_array($key, $this->setTag->storeWhiteIds) && $key != $this->setTag->storeMasterId && $value->contains($log->store_id);
                })) {
                    $score *= 1.2;
                }

                // 商家標記-黑名單(偷雞/假流量)
                if ($tagGroups->contains(function ($value, $key) use ($log) {
                    return in_array($key, $this->setTag->storeBlackIds) && $value->contains($log->store_id);
                })) {
                    $score *= 0.5;
                }

                // 商家標記-黑名單(偷雞/假流量)：若同時擁有，就再懲罰一下
                $badCount = 0;
                foreach ($this->setTag->storeBlackIds as $storeBlackId) {
                    if ($tagGroups->contains(function ($value, $key) use ($storeBlackId, $log) {
                        return $key == $storeBlackId && $value->contains($log->store_id);
                    })) {
                        $badCount++;
                    }
                }
                if ($badCount >= 2) {
                    $score *= 0.1;
                }

                // 判定是否為造假項目的商家
                if ($imitationIds->contains($log->store_id)) {
                    $score *= 0.1;
                }

                // 紀錄相本的總分
                $this->score[$log->album_id] = $score;

                // 紀錄新上架相本的總分
                if ($log->is_new) {
                    $this->scoreNew[$log->album_id] = $score;
                }

                // 紀錄大師標籤相本的總分
                if ($log->is_top) {
                    $this->scoreTop[$log->album_id] = $score;
                }

                // 相本與商家的索引
                $this->albumStoreId[$log->album_id] = $log->store_id;
            }

            // 相本的總分排行榜，由高至低排序
            arsort($this->score);
            arsort($this->scoreNew);
            arsort($this->scoreTop);

            // 取得相本的分佈架構
            $distributed = $this->getDistributed();
            $idx = 1;
            $storeList = [];
            foreach ($this->score as $albumId => $score) {
                $storeId = $this->albumStoreId[$albumId];
                if (!isset($storeList[$storeId])) {
                    $storeList[$storeId] = new \stdClass;
                    $storeList[$storeId]->is_maxSort = false;
                    $storeList[$storeId]->albumCount = 0;
                    $storeList[$storeId]->distributedIdx = NULL;
                }

                if (!$storeList[$storeId]->is_maxSort) {
                    $storeList[$storeId]->albumCount++;
                    $storeList[$storeId]->ablum[] = $albumId;
                }

                if (!$storeList[$storeId]->is_maxSort && $storeList[$storeId]->albumCount >= $distributed['storeLocation'][$idx]) {
                    $storeList[$storeId]->is_maxSort = true; // 宣告該商家達到目標了
                    $storeList[$storeId]->distributedIdx = $idx; // 宣告該商家獲得的ID
                    $idx++;

                    // 有人達標的時候，看是不是有下一個人也達標了
                    foreach ($storeList as $storeId => $item) {
                        if ($item->albumCount >= $distributed['storeLocation'][$idx] && $item->is_maxSort == false) {
                            $storeList[$storeId]->is_maxSort     = true ; // 宣告該商家達到目標了
                            $storeList[$storeId]->distributedIdx = $idx; // 宣告該商家獲得的ID
                            $idx++;

                            // 滿了就能退出了
                            if ($idx == count($distributed['storeLocation'])) {
                                break;
                            }
                        }
                    }

                    // 滿了就能退出了
                    if ($idx == count($distributed['storeLocation'])) {
                        break;
                    }
                }
            }

            $this->score    = array_keys($this->score);
            $this->scoreNew = array_keys($this->scoreNew);
            $this->scoreTop = array_keys($this->scoreTop);

            // 將東西放進去我們預先規劃好的位置
            foreach ($distributed['rankSort'] as $key => $val) {
                $isAdd = false;
                if ($val == 'HOT') {
                    $this->pushRank(array_shift($this->score));
                    $isAdd = true;
                } elseif ($val == 'ACE') {
                    $this->pushRank(array_shift($this->scoreTop));
                    $isAdd = true;
                } elseif ($val == 'NEW') {
                    while ($isAdd == false) {
                        if (count($this->scoreNew) == 0) {
                            // 如果新品項的陣列沒產品了，那就跑熱門
                            $this->pushRank(array_shift($this->score));
                            $isAdd = true;
                        } elseif ($storeList[$this->albumStoreId[$this->scoreNew[0]]]->is_maxSort ?? false) {
                            // 排除已經上榜的廠商，給新人機會
                            $this->delRank(array_shift($this->scoreNew));
                        } else {
                            $this->pushRank(array_shift($this->scoreNew));
                            $isAdd = true;
                        }
                    }
                } else {
                    foreach ($storeList as $storeId => $item) {
                        if ($item->distributedIdx == $val) {
                            $this->pushRank(array_shift($storeList[$storeId]->ablum));
                            $isAdd = true;
                        }
                    }
                }
            }

            // 剩下的都塞進去
            foreach ($this->score as $val) {
                $this->pushRank(array_shift($this->score));
            }

            // 更新商家相本的熱門排行
            $count = count($this->rank);
            foreach ($this->rank as $key => $albumId) {
                $this->storeAlbum->where('id', $albumId)
                                    ->update(['rank' => $count - $key]);
            }

            // 計算總數
            $total += $count;
        }

        return $total;
    }

    /**
     * 取得收藏商家相本的積分
     *
     * @return int
     */
    private function getCollectScore($storeType, $storeAlbum)
    {
        // 收藏作品集(婚紗禮服)
        if ($storeType == 2) {
            $albumCollect = $storeAlbum->userCollects()
                                        ->wherePivotBetween('created_at', $this->dateRange);

        // 收藏單一作品(其他商家類型)
        } else {
            $albumCollect = $storeAlbum->imageCollects()
                                        ->whereBetween('user_collects.created_at', $this->dateRange);
        }

        $total        = $albumCollect->count();
        $uniqueMember = $albumCollect->groupBy('user_id')->count();

        //==================================================================
        //  重複收藏相本，平均每個人收藏個30個很厲害了，超過就讓他GG吧
        //  處置：超過就以不重複人數計算，並給折扣
        //==================================================================
        if ($total > $uniqueMember * 30) {
            return $uniqueMember * 25;
        } else {
            return $uniqueMember * 50 + $total * 3;
        }
    }

    /**
     * 取得相本的分佈架構
     * @param $googleAnalytics : 原始資料
     * @return
     */
    private function getDistributed()
    {
        $hotMaxCount = rand(4, 5);

        // 前三名只限hot
        for ( $i = 0; $i < $hotMaxCount; $i++ ) {
            $rankSort[] = 'HOT';
        }

        // 跑前36名
        $tempRankSort = array();
        $randFlag = rand(1, 3);
        switch( $randFlag ) {
            case 1:
                $runArray = [6, 5, 4, 4, 3, 3];
                break;
            case 2:
                $runArray = [5, 5, 4, 4, 4, 3];
                break;
            case 3:
                $runArray = [6, 5, 5, 4, 3, 2];
                break;
        }

        foreach( $runArray as $key => $val ) {
            for( $i = 0; $i < $val; $i++ ) {
                $tempRankSort[] = $key +1;
            }
        }
        // 參加上隨機的新進商家與大師
        $aceMaxCount = rand(3, 4);
        $newMaxCount = rand(5, 6);
        for( $i = 0; $i < $aceMaxCount; $i++ ) {
            $tempRankSort[] = 'ACE';
        }
        for( $i = 0; $i < $newMaxCount; $i++ ) {
            $tempRankSort[] = 'NEW';
        }
        shuffle( $tempRankSort );
        foreach( $tempRankSort as $key => $val ) {
            $rankSort[] = $val;
        }

        // 跑37名~108名
        $tempRankSort = array();
        $randFlag = rand(1, 4);
        switch( $randFlag ) {
            case 1:
                $runArray = [9, 8, 8, 7, 6, 6, 6, 6, 5, 4, 4, 4];
                break;
            case 2:
                $runArray = [9, 8, 7, 7, 6, 6, 6, 6, 5, 5, 4, 4];
                break;
            case 3:
                $runArray = [9, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4];
                break;
            case 4:
                $runArray = [9, 8, 7, 7, 7, 6, 6, 5, 5, 5, 4, 4];
                break;
        }
        foreach( $runArray as $key => $val ) {
            for( $i = 0; $i < $val; $i++ ) {
                $tempRankSort[] = $key +1;
            }
        }
        shuffle( $tempRankSort );
        foreach( $tempRankSort as $key => $val ) {
            $rankSort[] = $val;
        }

        // 跑109名~144名
        $tempRankSort = array();
        $randFlag = rand(1, 4 );
        switch( $randFlag ) {
            case 1:
                $runArray = [5, 4, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1];
                break;
            case 2:
                $runArray = [5, 4, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1];
                break;
            case 3:
                $runArray = [5, 4, 3, 3, 3, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1];
                break;
            case 4:
                $runArray = [5, 4, 3, 3, 3, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1];
                break;
        }
        foreach( $runArray as $key => $val ) {
            for( $i = 0; $i < $val; $i++ ) {
                $tempRankSort[] = $key +1;
            }
        }
        shuffle( $tempRankSort );
        foreach( $tempRankSort as $key => $val ) {
            $rankSort[] = $val;
        }

        for( $i = 1; $i <= 18; $i++ ) {
            $storeLocation[$i] = 0;
        }

        foreach( $rankSort as $val ) {
            if( intval( $val ) > 0 ) {
                $storeLocation[$val]++;
            }
        }

        $objData['storeLocation'] = $storeLocation;
        $objData['rankSort'] = $rankSort;

        return $objData;
    }

    /**
     * 將商家放進排名裡
     * @param $id：從array取出來的id
     * @return
     */
    private function pushRank($id)
    {
        if (!$id) {
            return null;
        }

        //放進陣列裡
        array_push($this->rank, $id);

        //移除陣列裡的東西
        if (count($this->score) > 0 && in_array($id, $this->score)) {
            unset($this->score[array_search($id, $this->score)]);
        }

        if (count($this->scoreNew) > 0 && in_array($id, $this->scoreNew)) {
            unset($this->scoreNew[array_search($id, $this->scoreNew)]);
        }

        if (count($this->scoreTop) > 0 && in_array($id, $this->scoreTop)) {
            unset($this->scoreTop[array_search($id, $this->scoreTop)]);
        }

        return true;
    }

    /**
     * 刪除陣列內的東西
     *
     * @param  mixed $id
     *
     * @return void
     */
    private function delRank($id)
    {
        if (!$id) {
            return null;
        }

        //移除陣列裡的東西
        if (count($this->score) > 0 && in_array($id, $this->score)) {
            unset($this->score[array_search($id, $this->score)]);
        }

        if (count($this->scoreNew) > 0 && in_array($id, $this->scoreNew)) {
            unset($this->scoreNew[array_search($id, $this->scoreNew)]);
        }

        if (count($this->scoreTop) > 0 && in_array($id, $this->scoreTop)) {
            unset($this->scoreTop[array_search($id, $this->scoreTop)]);
        }

        return true;
    }
}
