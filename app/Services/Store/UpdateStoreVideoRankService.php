<?php
/*
 |--------------------------------------
 |  更新商家影片排行 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\StoreVideo;
use App\Models\LogGaPageView;
use App\Models\StoreIntegral;
use Carbon\Carbon;

class UpdateStoreVideoRankService
{
    private $storeVideo;
    private $logGaPageView;
    private $storeIntegral;

    private $runDays   = 10; // 統計範圍的天數
    private $dateRange = [];

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Carbon $carbon,
        StoreVideo $storeVideo,
        LogGaPageView $logGaPageView,
        StoreIntegral $storeIntegral
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->carbon        = $carbon;
        $this->storeVideo    = $storeVideo;
        $this->logGaPageView = $logGaPageView;
        $this->storeIntegral = $storeIntegral;
    }

    /**
     * 計算商家影片的排名積分
     */
    public function run($date)
    {
        // 統計範圍的時間區間
        $this->dateRange = [
            $this->carbon->parse($date)->subDays($this->runDays - 1)->format('Y-m-d 00:00:00'),
            $this->carbon->parse($date)->format('Y-m-d 23:59:59'),
        ];

        // 先清空所有商家影片的熱門排行
        $this->storeVideo->whereNotNull('rank')
                            ->update(['rank' => NULL]);

        // 取得造假項目的商家ID
        $imitationIds = $this->storeIntegral->where('created_date', $date)
                                            ->whereNotNull('imitation')
                                            ->pluck('store_id');

        // 取得商家影片的流量積分
        $logs = $this->logGaPageView->selectRaw('
                                        target_id AS video_id,
                                        parent_id AS store_id,
                                        SUM(pageViews + totalUsers * 5) AS score
                                    ')
                                    ->where('type', 'store_video')
                                    ->whereBetween('created_date', $this->dateRange)
                                    ->groupBy('target_id')
                                    ->get();

        // 若沒有任何商家影片的流量積分就先跳出，避免下面壞掉
        if (!$logs->count()) {
            return 0;
        }

        foreach ($logs as $log) {

            // 判定是否為造假項目的商家
            if ($imitationIds->contains($log->store_id)) {
                $log->score *= 0.1;
            }

            // 紀錄總分
            $this->score[$log->video_id] = $log->score;
        }

        // 商家影片的總分排行榜，由高至低排序
        arsort($this->score);
        $this->score = array_keys($this->score);

        // 更新商家影片的熱門排行
        $total = count($this->score);
        foreach ($this->score as $key => $videoId) {
            $this->storeVideo->where('id', $videoId)
                                ->update(['rank' => $total - $key]);
        }

        return $total;
    }
}
