<?php
/*
 |--------------------------------------
 |  新增商家的外站點擊紀錄 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\Store;
use App\Models\RedirectUrl;
use App\Models\LogRedirectQuote;
use App\Models\LogGaEvent;
use App\Models\Wdv2\StoreAnalytics as StoreAnalyticsWdv2;
use App\Models\CompareExternalLink;
use App\Services\Google\BigQuery\BigQueryHandle;
use DB;

class LogExternalLinkService
{
    private $date;
    private $dateRange;

    private $store;
    private $redirectUrl;
    private $logRedirectQuote;
    private $logGaEvent;
    private $storeAnalyticsWdv2;
    private $compareExternalLink;
    private $bigQueryHandle;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Store $store,
        RedirectUrl $redirectUrl,
        LogRedirectQuote $logRedirectQuote,
        LogGaEvent $logGaEvent,
        StoreAnalyticsWdv2 $storeAnalyticsWdv2,
        CompareExternalLink $compareExternalLink,
        BigQueryHandle $bigQueryHandle
    ) {
        $this->store               = $store;
        $this->redirectUrl         = $redirectUrl;
        $this->logRedirectQuote    = $logRedirectQuote;
        $this->logGaEvent          = $logGaEvent;
        $this->storeAnalyticsWdv2  = $storeAnalyticsWdv2;
        $this->compareExternalLink = $compareExternalLink;
        $this->bigQueryHandle      = $bigQueryHandle;
    }

    /**
     * 新增商家的外站點擊紀錄
     *
     * @param $keyword
     * @param $source
     * @return void
     */
    public function run($date)
    {
        $this->date      = $date;
        $this->dateRange = [
            date('Y-m-d 00:00:00', strtotime($date)),
            date('Y-m-d 23:59:59', strtotime($date)),
        ];

        // 所有上架的商家
        $stores = $this->store->published()
                                ->get();
        foreach ($stores as $store) {

            // 轉址紀錄-商家資訊
            $redirectUrls = $this->redirectUrl->select(DB::raw('redirect_urls.column, COUNT(*) AS count'))
                                                ->join('log_redirect_urls', function($join) {
                                                    $join->on('log_redirect_urls.redirect_url_id', '=', 'redirect_urls.id')
                                                            ->whereBetween('log_redirect_urls.created_at', $this->dateRange);
                                                })
                                                ->where('target_type', 'store')
                                                ->where('target_id', $store->id)
                                                ->groupBy('column')
                                                ->get();
            $redirectProfile      = $redirectUrls->sum('count');
            $redirectProfileItems = $redirectUrls->pluck('count', 'column');

            // 轉址紀錄-主動報價
            $logRedirectQuotes = $this->logRedirectQuote->select(DB::raw('store_quote_id, COUNT(*) AS count'))
                                                        ->where('store_id', $store->id)
                                                        ->whereBetween('created_at', $this->dateRange)
                                                        ->groupBy('store_quote_id')
                                                        ->get();
            $redirectQuote      = $logRedirectQuotes->sum('count');
            $redirectQuoteItems = $logRedirectQuotes->pluck('count', 'store_quote_id');

            // GA3 init
            $ga3Profile      = 0;
            $ga3ProfileItems = [];
            $ga3Quote        = 0;
            $ga3QuoteItems   = [];
            $storeAnalyticsWdv2 = $this->storeAnalyticsWdv2->where('store_id', $store->id)
                                                            ->where('show_flag', 2)
                                                            ->where('created_at', $this->date)
                                                            ->first();
            if (isset($storeAnalyticsWdv2->statistic->analytics->track->event)) {
                foreach ($storeAnalyticsWdv2->statistic->analytics->track->event as $type => $event) {
                    // GA3-主動報價
                    if ($type == 'quote.shortlink') {
                        foreach ($event as $quoteId => $item) {
                            $ga3Quote        += $item->totalEvents;
                            $ga3QuoteItems[] = (int)$quoteId;
                        }
                    // GA3-商家資訊
                    } else {
                        $ga3Profile             += $event->totalEvents;
                        $ga3ProfileItems[$type] = (int)$event->totalEvents;
                    }
                }
            }

            // GA4 init
            $ga4Profile      = 0;
            $ga4ProfileItems = [];
            $ga4Quote        = 0;
            $ga4QuoteItems   = [];

            // GA4-商家資訊 (log_ga_events的商家資訊點擊數含有主動報價，所以只好直接從BigQuery拿)
            $analytics = $this->bigQueryHandle->handle('store_type_events', [
                'dateRange' => $this->date,
                'eventName' => 'external_link',
                'groupKeys' => [
                    'string' => 'click_type',
                ],
                'eventParams' => [
                    'INT'   => ['store_id' => $store->id],
                    'EXACT' => ['target_id' => 'not_quote'],
                ],
            ]);
            foreach ($analytics as $item) {
                $ga4Profile += $item['total_events'];
                $ga4ProfileItems[$item['click_type']] = $item['total_events'];
            }

            // GA4-主動報價
            $logGaEvents = $this->logGaEvent->where('type', 'quote')
                                            ->where('parent_id', $store->id)
                                            ->where('created_date', $this->date)
                                            ->get();
            $ga4Quote      = $logGaEvents->sum('totalEvents');
            $ga4QuoteItems = $logGaEvents->pluck('target_id');

            // 新增比較紀錄
            $this->compareExternalLink->updateOrCreate([
                'date'     => $this->date,
                'store_id' => $store->id,
            ], [
                'redirect_profile'       => $redirectProfile,
                'redirect_profile_items' => $redirectProfileItems,
                'redirect_quote'         => $redirectQuote,
                'redirect_quote_items'   => $redirectQuoteItems,
                'ga3_profile'            => $ga3Profile,
                'ga3_profile_items'      => collect($ga3ProfileItems),
                'ga3_quote'              => $ga3Quote,
                'ga3_quote_items'        => collect($ga3QuoteItems),
                'ga4_profile'            => $ga4Profile,
                'ga4_profile_items'      => collect($ga4ProfileItems),
                'ga4_quote'              => $ga4Quote,
                'ga4_quote_items'        => $ga4QuoteItems,
            ]);

            // echo $this->date.' ('.$store->id.')'.$store->name.": 已新增比較商家的外站點擊紀錄!\r\n";
        }

        return $stores->count();
    }
}
