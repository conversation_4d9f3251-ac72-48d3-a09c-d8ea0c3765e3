<?php
/*
 |--------------------------------------
 |  更新商家方案排行 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\StoreService;
use App\Models\LogGaPageView;
use App\Models\StoreIntegral;
use App\Models\UserCollect;
use Carbon\Carbon;

class UpdateStoreServiceRankService
{
    private $storeService;
    private $logGaPageView;
    private $storeIntegral;
    private $userCollect;

    private $runDays   = 10; // 統計範圍的天數
    private $dateRange = [];

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Carbon $carbon,
        StoreService $storeService,
        LogGaPageView $logGaPageView,
        StoreIntegral $storeIntegral,
        UserCollect $userCollect
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->carbon        = $carbon;
        $this->storeService  = $storeService;
        $this->logGaPageView = $logGaPageView;
        $this->storeIntegral = $storeIntegral;
        $this->userCollect   = $userCollect;
    }

    /**
     * 計算商家方案的排名積分
     */
    public function run($date)
    {
        // 統計範圍的時間區間
        $this->dateRange = [
            $this->carbon->parse($date)->subDays($this->runDays - 1)->format('Y-m-d 00:00:00'),
            $this->carbon->parse($date)->format('Y-m-d 23:59:59'),
        ];

        // 先清空所有商家方案的熱門排行
        $this->storeService->whereNotNull('rank')
                            ->update(['rank' => NULL]);

        // 取得造假項目的商家ID
        $imitationIds = $this->storeIntegral->where('created_date', $date)
                                            ->whereNotNull('imitation')
                                            ->pluck('store_id');

        // 取得商家方案的流量積分
        $logs = $this->logGaPageView->selectRaw('
                                        target_id AS service_id,
                                        parent_id AS store_id,
                                        activity_id,
                                        SUM(pageViews + totalUsers * 5) AS score
                                    ')
                                    ->join($this->storeService->getTable(), function($join)  {
                                         $join->on('store_services.id', '=', 'log_ga_page_views.target_id');
                                     })
                                    ->where('log_ga_page_views.type', 'store_service')
                                    ->whereBetween('log_ga_page_views.created_date', $this->dateRange)
                                    ->groupBy('target_id')
                                    ->get();

        // 若沒有任何商家方案的流量積分就先跳出，避免下面壞掉
        if (!$logs->count()) {
            return 0;
        }

        foreach ($logs as $log) {

            // 商家方案的流量積分 + 收藏積分
            $score = $log->score + $this->getCollectScore($log->service_id);

            // 判定是否為造假項目的商家
            if ($imitationIds->contains($log->store_id)) {
                $score *= 0.1;
            }

            //==================================================================
            //  檢查是否為活動方案
            //  處置：加分囉～
            //==================================================================
            if ($log->activity_id) {
                $score *= 10;
            }

            // 紀錄總分
            $this->score[$log->service_id] = $score;
        }

        // 商家方案的總分排行榜，由高至低排序
        arsort($this->score);
        $this->score = array_keys($this->score);

        // 更新商家方案的熱門排行
        $total = count($this->score);
        foreach ($this->score as $key => $serviceId) {
            $this->storeService->where('id', $serviceId)
                                ->update(['rank' => $total - $key]);
        }

        return $total;
    }

    /**
     * 取得收藏商家方案的積分
     *
     * @return int
     */
    private function getCollectScore($serviceId)
    {
        // 收藏方案
        $serviceCollect = $this->userCollect->where('type', 'service')
                                            ->where('target_id', $serviceId)
                                            ->whereBetween('created_at', $this->dateRange);

        $total        = $serviceCollect->count();
        $uniqueMember = $serviceCollect->groupBy('user_id')->count();

        //==================================================================
        //  重複收藏方案，平均每個人收藏個5個很厲害了，超過就讓他GG吧
        //  處置：超過就以不重複人數計算，並給折扣
        //==================================================================
        if ($total > $uniqueMember * 5) {
            return $uniqueMember * 25;
        } else {
            return $uniqueMember * 50 + $total * 3;
        }
    }
}
