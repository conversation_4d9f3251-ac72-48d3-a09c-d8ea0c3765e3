<?php

namespace App\Services\Store\Yzcube;

use App\Models\Store;
use App\Services\Store\Yzcube\ShowService;

class FilterDateService
{
    public $showService;

    public function __construct(
        ShowService $showService
    ) {
        $this->showService = $showService;
    }

    /**
     * 商家詳細內容頁-篩選日期
     *
     * @return array
     */
    public function run(Store $store, $request)
    {
        // 私訊狀況
        $messages = [];
        if ($request['message_date']) {
            $dateRange = [
                date('Y-m-d 00:00:00', strtotime($request['message_date'][0])),
                date('Y-m-d 23:59:59', strtotime($request['message_date'][1])),
            ];
            $messages = $this->showService->getMessagesByDateRange($store, $dateRange);
        }

        // 詢問單狀況
        $reserves = [];
        if ($request['reserve_date']) {
            $dateRange = [
                date('Y-m-d 00:00:00', strtotime($request['reserve_date'][0])),
                date('Y-m-d 23:59:59', strtotime($request['reserve_date'][1])),
            ];
            $reserves = $this->showService->getReservesByDateRange($store, $dateRange);
        }

        // 公開報價狀況
        $storeQuotes = [];
        if ($request['quote_date']) {
            $dateRange = [
                date('Y-m-d 00:00:00', strtotime($request['quote_date'][0])),
                date('Y-m-d 23:59:59', strtotime($request['quote_date'][1])),
            ];
            $storeQuotes = $this->showService->getStoreQuotesByDateRange($store, $dateRange);
        }

        // 流量狀況
        $analytics = [];
        if ($request['analytics_date']) {
            $dateRange = [
                date('Y-m-d 00:00:00', strtotime($request['analytics_date'][0])),
                date('Y-m-d 23:59:59', strtotime($request['analytics_date'][1])),
            ];
            $analytics = $this->showService->getAnalyticsByDateRange($store, $dateRange);
        }

        return [
            'messages'     => $messages,
            'reserves'     => $reserves,
            'store_quotes' => $storeQuotes,
            'analytics'    => $analytics,
        ];
    }
}
