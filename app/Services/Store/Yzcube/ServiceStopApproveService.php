<?php

namespace App\Services\Store\Yzcube;

use App\Models\Wdv2\ServiceStopApply;
use App\Services\Mail\Store\ServiceStopCompletedService;

class ServiceStopApproveService
{
    public $closingDate;
    public $serviceStopCompletedService;

    public function __construct(
        ServiceStopCompletedService $serviceStopCompletedService
    ) {
        $this->serviceStopCompletedService = $serviceStopCompletedService;
    }

    /**
     * 執行下架清算
     *
     * @return array
     */
    public function run(ServiceStopApply $apply)
    {
        // 下架時間
        $this->closingDate = $apply->closing_date->format('Y-m-d H:i:s');

        // 取得已經付款，但期限還沒過的合約(設定費)訂單
        $quotaOrders = $apply->store->contractOrders()
                                    ->whereHas('orderPackages', function ($q) {
                                        $q->where('order_package.end_date', '>', $this->closingDate);
                                    })
                                    ->get();
        foreach ($quotaOrders as $quotaOrder) {

            // 將合約訂單的付款狀態 改為取消付款，並記錄截止日為下架時間
            $_description = $quotaOrder->description;
            $_description->deadline_end = $this->closingDate;
            $quotaOrder->description    = $_description;
            $quotaOrder->payment_status = 2;
            $quotaOrder->save();

            // 將合約訂單的設定費品項，修改截止日為下架時間
            foreach ($quotaOrder->orderPackages as $orderPackage) {
                $orderPackage->end_date = $this->closingDate;
                $orderPackage->save();
            }

            // 訂單修改紀錄
            $quotaOrder->orderLogs()->create([
                'status'     => 1007,
                'detail'     => ['deadline' => [$quotaOrder->description->deadline_end, $this->closingDate]],
                'created_at' => now(),
            ]);
        }

        // 例外狀況：將所有未付款的訂單都取消
        $apply->store->unpaidOrders()->update([
            'payment_status' => 2,
        ]);

        // 重置發票資訊
        $apply->orderSet->update([
            'deadline'           => NULL,
            'closing_day'        => NULL,
            'contract_order_id'  => NULL,
            'free_setting_times' => NULL,
            'free_usage_times'   => NULL,
            'usage_limit'        => NULL,
            'usage_notice'       => NULL,
        ]);

        // 下架商家，可正常使用功能的狀態
        $apply->store->status = 'leave';
        $apply->store->save();

        // 執行下架清算，Wdv2的商家也要跟著變成下架商家(level=1)，可正常使用功能的狀態(show_flag=2)
        $apply->store->storeWdv2()->update([
            'level'     => 1,
            'show_flag' => 2,
        ]);

        // 將沒有更新過的方案都隱藏
        $apply->store->showServices()
                    ->whereNull('edited_at')
                    ->update(['status' => 'hide']);

        // 下架申請的處理狀態，修改為已結案
        $apply->yzcube_user_id = request('yzcube_user')->id;
        $apply->status         = 2;
        $apply->resolve_date   = now();
        $apply->save();

        // 寄出通知信
        $this->serviceStopCompletedService->sendMail($apply);
    }
}
