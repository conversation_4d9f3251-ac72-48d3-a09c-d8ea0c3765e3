<?php

namespace App\Services\Store\Yzcube;

use App\Models\Store;
use App\Models\StoreAlbum;
use App\Models\StoreService;
use App\Models\StoreAlbumImage;
use App\Models\VenueRoom;
use App\Models\LogGaPageView;
use App\Models\UserCollect;
use App\Traits\ParseKeywordTrait;
use Carbon\Carbon;

class DataAnalysisService
{
    use ParseKeywordTrait;

    private $store;
    private $storeAlbum;
    private $storeAlbumImage;
    private $storeService;
    private $venueRoom;
    private $logGaPageView;
    private $userCollect;
    private $model;
    private $carbon;

    public function __construct(
        Store $store,
        StoreAlbum $storeAlbum,
        StoreAlbumImage $storeAlbumImage,
        storeService $storeService,
        VenueRoom $venueRoom,
        LogGaPageView $logGaPageView,
        UserCollect $userCollect,
        Carbon $carbon
    ) {
        $this->store           = $store;
        $this->storeAlbum      = $storeAlbum;
        $this->storeAlbumImage = $storeAlbumImage;
        $this->storeService    = $storeService;
        $this->venueRoom       = $venueRoom;
        $this->logGaPageView   = $logGaPageView;
        $this->userCollect     = $userCollect;
        $this->carbon          = $carbon;
    }

    /**
     * 作品/方案數據-列表
     *
     * @return array
     */
    public function run($request)
    {
        // 資料類型 album:相本 service:方案 venue_room:場地廳房
        switch ($request['data_type']) {
            case 'album':
                $this->model = $this->storeAlbum;
                $logGaPageViewType = 'store_album';
                $userCollectType   = 'album';
                break;
            case 'service':
                $this->model = $this->storeService;
                $logGaPageViewType = 'store_service';
                $userCollectType   = 'service';
                break;
            case 'venue_room':
                $this->model = $this->venueRoom;
                $logGaPageViewType = 'store_venue_room';
                $userCollectType   = 'venue_room';
                break;

            default:
                return false;
        }

        // 統計範圍的時間區間
        $dateRange = [
            $this->carbon->parse($request['start_date'])->format('Y-m-d 00:00:00'),
            $this->carbon->parse($request['end_date'])->format('Y-m-d 23:59:59'),
        ];

        // 瀏覽數
        $pageViews = <<<EOT
                        SELECT CAST(COALESCE(SUM(pageViews), 0) AS UNSIGNED)
                        FROM {$this->logGaPageView->getTable()}
                        WHERE
                            {$this->model->getTable()}.id = log_ga_page_views.target_id AND
                            log_ga_page_views.type = '$logGaPageViewType' AND
                            created_date BETWEEN '$dateRange[0]' AND '$dateRange[1]'
                    EOT;

        // 收藏數
        $userCollects = <<<EOT
                            SELECT COUNT(user_collects.id)
                            FROM {$this->userCollect->getTable()}
                            WHERE
                                {$this->model->getTable()}.id = user_collects.target_id AND
                                user_collects.type = '$userCollectType' AND
                                created_at BETWEEN '$dateRange[0]' AND '$dateRange[1]'
                        EOT;

        // 除了婚紗禮服是收藏相本集，其餘皆商家類型是收藏照片
        if ($request['data_type'] == 'album' && $request['store_type'] != 2) {
            $userCollects = <<<EOT
                                SELECT COUNT(user_collects.id)
                                FROM {$this->userCollect->getTable()}
                                JOIN {$this->storeAlbumImage->getTable()}
                                WHERE
                                    store_albums.id = store_album_images.album_id AND
                                    store_album_images.id = user_collects.target_id AND
                                    user_collects.type = 'album_image' AND
                                    created_at BETWEEN '$dateRange[0]' AND '$dateRange[1]'
                            EOT;
        }

        // 取得欄位
        $model = $this->model->selectRaw(<<<EOT
                                            {$this->model->getTable()}.*,
                                            store_id,
                                            ($pageViews) AS page_views,
                                            ($userCollects) AS collect_count
                                        EOT);

        // 預載入
        $model = $model->with([
                            'allStore:id,name,status',
                            'cover:id,target_id,file_name',
                        ]);

        // 所屬商家
        $model = $model->join($this->store->getTable(), function($join) use ($request) {

                            $join->on('stores.id', '=', $this->model->getTable().'.store_id')
                                    ->whereNull('stores.deleted_at');

                            // 商家類型
                            if ($request['store_type']) {
                                $join->where('stores.type', $request['store_type']);
                            }

                            // 商家狀態
                            if ($request['store_status']) {
                                $join->where('stores.status', $request['store_status']);
                            }

                            // 關鍵字 keyword
                            $keyword = trim($request['keyword'] ?? '');
                            if ($keyword != '') {
                                $join->where(function ($q) use ($keyword) {
                                            $keywords = $this->splitToArray($keyword);
                                            foreach ($keywords as $val) {
                                                $q->where('stores.id', $val)
                                                    ->orWhere('stores.name', 'like', '%'.$val.'%');
                                            }
                                        });
                            }
                        });

        // 相本/方案/場地廳房 狀態
        if ($request['data_status']) {
            $model = $model->where($this->model->getTable().'.status', $request['data_status']);
        }

        // 排序 sort:id(default)|logGaPageViews|userCollects
        // 升降冪 direction:asc|desc(default)
        $sort = $request['sort'] ?: $this->model->getTable().'.id';
        $direction = $request['direction'] ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        // 每頁數量 per_page
        $model = $model->paginate($request['per_page']);

        return $model;
    }
}
