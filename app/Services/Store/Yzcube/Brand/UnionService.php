<?php
/*
 |--------------------------------------
 |  關聯商家 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Yzcube\Brand;

use App\Traits\ApiErrorTrait;
use App\Models\Brand;
use App\Models\Wdv2\ShareStoreLink;

class UnionService
{
    use ApiErrorTrait;

    private $brand;
    private $shareStoreLink;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Brand $brand,
        ShareStoreLink $shareStoreLink
    )
    {
        $this->brand          = $brand;
        $this->shareStoreLink = $shareStoreLink;
    }

    /**
     * 關聯商家
     *
     * @param [type] $request
     * @return void
     */
    public function run($request)
    {

        $brand = $this->brand
                        ->where('id', $request['union_id'])
                        ->withCount('stores')
                        ->first();

        //檢查是否有被關聯過
        if ($brand->stores_count > 0) {
            $this->setException('已經有關聯的商家囉');
        }

        $shareStoreLink = $this->shareStoreLink
                                ->where('store_id', $request['store_id'])
                                ->where('show_flag', 2)
                                ->first();

        //建立關聯
        $this->shareStoreLink->create([
            'share_store_id' => $request['union_id'],
            'store_id'       => $request['store_id'],
            'show_flag'      => ($shareStoreLink) ? 1 : 2,
        ]);

        return $request['union_id'];
    }
}
