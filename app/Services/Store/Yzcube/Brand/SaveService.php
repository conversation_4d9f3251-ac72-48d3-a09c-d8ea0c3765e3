<?php
/*
 |--------------------------------------
 |  儲存品牌資料 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Yzcube\Brand;

use App\Services\Tools\GetWebData\GetFacebookUrlInfoService;
use App\Services\Tools\GetWebData\GetParseUrlService;
use App\Traits\ApiErrorTrait;

class SaveService
{
    use ApiErrorTrait;

    private $getFacebookUrlInfoService;
    private $getParseUrlService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetFacebookUrlInfoService $getFacebookUrlInfoService,
        GetParseUrlService $getParseUrlService
    )
    {
        $this->getFacebookUrlInfoService    = $getFacebookUrlInfoService;
        $this->getParseUrlService           = $getParseUrlService;
    }

    /**
     * 儲存品牌資料
     */
    public function run($request)
    {
        $brand = request('brand');

        if (!$brand) {
            $this->setException('找不到商家，無法儲存資料');
        }

        $brand->name    = $request['name'];
        $brand->email   = $request['email'];
        $brand->phone   = $request['phone'];
        $brand->address = $request['address'];
        $brand->line    = $request['line'];

        //如果臉書資料有更新時，重新獲得連結與
        if ($request['fb_page'] && $brand->fb_page != $request['fb_page']) {

            $fbResult = $this->getFacebookUrlInfoService->get($request['fb_page']);
            if ($fbResult['id'] && $fbResult['url']) {
                $brand->fb_id   = $fbResult['id'];
                $brand->fb_page = $fbResult['url'];
            }
        }

        //處理一下網址
        if ($request['website'] && $brand->website != $request['website']) {
            $brand->website = $this->getParseUrlService->encode($request['website']);
        }
        if ($request['instagram'] && $brand->instagram != $request['instagram']) {
            $brand->instagram  = $this->getParseUrlService->encode($request['instagram']);
        }

        $brand->status  = $request['status'];
        $brand->updated_at = now();

        $brand->save();

        return $brand;
    }
}
