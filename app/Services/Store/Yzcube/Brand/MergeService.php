<?php
/*
 |--------------------------------------
 |  合併品牌的 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Yzcube\Brand;

use App\Traits\ApiErrorTrait;
use App\Models\Brand;
use DB;

class MergeService
{
    use ApiErrorTrait;

    private $brand;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Brand $brand
    ) {
        $this->brand = $brand;
    }

    /**
     * 合併
     *
     * @param [type] $request
     * @return void
     */
    public function run($request)
    {
        $request['merge_id'] = json_decode($request['merge_id'],true);

        $brand = $this->brand
                        ->whereIn('id', $request['merge_id'])
                        ->withCount('stores')
                        ->withCount('articles')
                        ->get();

        if ($brand && count($brand) < 1) {
            $this->setException('無法合併！，只找到一個商家');
        }

        //如果 merge_id 的array 裡 有兩個以上的 store 關聯，應該要產出錯誤
        $unionCount     = 0;
        foreach ($brand as $store) {
            if ($store->stores_count > 0) {
                //如果 main_id 是空的，那找到有關聯的商家 就成為 主要的
                if (!isset($request['main_id']) || !$request['main_id']) {
                    $request['main_id'] = $store->id;
                }

                $unionCount ++ ;
            }

        }
        if ($unionCount > 1) {
            $this->setException('無法合併！，有超過1個品牌與服務商家關聯');
        }

        //如果沒有main_id 以有store_id > brand.status = "published" > article_count
        if (!isset($request['main_id']) || !$request['main_id']) {
            //重新取得
            $request['main_id'] = $this->brand->select('id')
                                                ->whereIn('id', $request['merge_id'])
                                                ->withCount('articles')
                                                ->orderByRaw('FIELD(status , "published", "pending") ASC')
                                                ->orderBy('article_count', 'desc')
                                                ->first()
                                                ->id;
        }

        if (!isset($request['main_id']) || !$request['main_id']) {
            $this->setException('無法合併！，找不到需要留下來的ID');
        }

        //合併
        DB::table('forum_article_brand')->whereIn('brand_id', $request['merge_id'])
                                        ->where('brand_id', '!=', $request['main_id'])
                                        ->update([
                                            'brand_id' => $request['main_id']
                                        ]);

        $this->brand->whereIn('id', $request['merge_id'])
                    ->where('id', '!=', $request['main_id'])
                    ->delete();

        return $request['main_id'];
    }
}
