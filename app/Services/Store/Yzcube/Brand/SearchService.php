<?php
/*
 |--------------------------------------
 |  搜尋 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Yzcube\Brand;

use App\Traits\ApiErrorTrait;
use App\Models\Brand;

class SearchService
{
    use ApiErrorTrait;

    private $brand;

    private $result;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Brand $brand
    )
    {
        $this->brand = $brand;
    }

    /**
     * 搜尋
     *
     * @param [type] $request
     * @return void
     */
    public function search($request)
    {

        if ($request['keyword'] == '') {
            $this->result = $this->recommend($request);
        } else {
            $this->result = $this->keyword($request);
        }

        return $this->result;
    }

    /**
     * 用關鍵字搜尋
     *
     * @param [type] $request
     * @return void
     */
    private function keyword($request)
    {
        $this->result = $this->brand
                                ->withCount('stores')
                                ->withCount('articles')
                                ->where(function($query) use ($request) {
                                    $likeKeyWord = '%' . $request['keyword'] . '%';
                                    $query->Where('id', $request['keyword'])
                                        ->orWhere('name', 'like', $likeKeyWord)
                                        ->orWhere('email', 'like', $likeKeyWord)
                                        ->orWhere('phone', 'like', $likeKeyWord)
                                        ->orWhere('line', 'like', $likeKeyWord)
                                        ->orWhere('website', 'like', $likeKeyWord)
                                        ->orWhere('fb_page', 'like', $likeKeyWord);
                                });

        //如果是為了 合併，那找出來的都必須符合 stores_count = 0
        if ($request['type'] == 'merge') {
            $this->result = $this->result->having('stores_count', 0);
        }

        $this->result = $this->result->get();
        return $this->result;
    }

    /**
     * 如果沒有關鍵字就用媒合的方式
     *
     * @param [type] $request
     * @return void
     */
    private function recommend($request)
    {
        $brand = request('brand');

        $this->result = $this->brand
                                ->withCount('stores')
                                ->withCount('articles')
                                ->where(function($query) use ($brand) {
                                    $query->where('name', $brand->name);
                                    if ($brand->email) {
                                        $query = $query->orWhere('email', $brand->email);
                                    }
                                    if ($brand->phone) {
                                        $query = $query->orWhere('phone', $brand->phone);
                                    }
                                    if ($brand->website) {
                                        $query = $query->orWhere('website', $brand->website);
                                    }
                                    if ($brand->fb_page) {
                                        $query = $query->orWhere('fb_page', $brand->fb_page);
                                    }
                                });

        //如果是為了 合併，那找出來的都必須符合 stores_count = 0
        if ($request['type'] == 'merge') {
            $this->result = $this->result->having('stores_count', 0);
        }

        $this->result = $this->result->get();

        return $this->result;
    }
}
