<?php
/*
 |--------------------------------------
 |  儲存鑑定團文章 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store\Yzcube\Article;

class SaveService
{
    /**
     * 儲存鑑定團文章
     */
    public function run($request)
    {
        // 取得商家及鑑定團文章
        $store      = $request['store'];
        $article_id = $request['article_id'];

        // 關聯鑑定團文章
        $store->articles()->syncWithoutDetaching([
            $article_id => ['tag' => $request['tag']]
        ]);
        $article = $store->articles->find($article_id);

        // 預設分類名稱
        $_store = $article->stores->first();
        if (!$article->category && $_store) {
            $article->category = $_store->typeList[$_store->type];
        }

        // 更新鑑定團文章
        $article->title = $request['title'];
        $article->image = $request['image'];
        $article->save();
    }
}
