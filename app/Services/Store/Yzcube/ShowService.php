<?php

namespace App\Services\Store\Yzcube;

use App\Models\Store;
use App\Models\Event;
use App\Models\Wdv2\SetTag;
use App\Models\Wdv2\Order;
use App\Models\UserCollect;
use App\Models\LogGaPageView;
use App\Services\Google\BigQuery\BigQueryHandle;
use App\Transformers\Yzcube\AuthTransformer;
use App\Traits\Model\TransformerTrait;
use DB;

class ShowService
{
    public $event;
    public $setTag;
    public $order;
    public $userCollect;
    public $logGaPageView;
    public $bigQueryHandle;
    public $authTransformer;

    use TransformerTrait;

    public function __construct(
        Event $event,
        SetTag $setTag,
        Order $order,
        UserCollect $userCollect,
        LogGaPageView $logGaPageView,
        BigQueryHandle $bigQueryHandle,
        AuthTransformer $authTransformer
    ) {
        $this->event           = $event;
        $this->setTag          = $setTag;
        $this->order           = $order;
        $this->userCollect     = $userCollect;
        $this->logGaPageView   = $logGaPageView;
        $this->bigQueryHandle  = $bigQueryHandle;
        $this->authTransformer = $authTransformer;
    }

    /**
     * 商家詳細內容頁
     *
     * @param model $store
     * @return array
     */
    public function run(Store $store)
    {
        //==============================
        // 基本設定
        //==============================

        // 最後登入者
        $lastLoginUser = $store->accounts()->orderBy('last_login_at', 'DESC')->first();

        // 基本設定
        $profile = [
            'id'              => $store->id, // 商家ID
            'name'            => $store->name, // 商家名稱
            'logo'            => $store->logo->file_name ?? '', // 商家Logo
            'type'            => $store->typeList[$store->type], // 商家類別
            'status'          => $store->status, // 商家狀態
            'created_at'      => $store->created_at->format('Y-m-d H:i:s') ?: '', // 創立時間
            // 'deleted_at'      => '', // 目前沒有停用時間
            'last_login_user' => $lastLoginUser ? $lastLoginUser->only(['id', 'email', 'last_login_at']) : [], // 最後登入帳號
            'w_coins'         => $store->getCurrencyBalance('Wcoin'), // W幣
            'allowances'      => $store->getCurrencyBalance('allowance'), // 折讓金
            'mark_ids'        => $store->marks->pluck('id'), // 商家標記編號
            'contact_person'  => $store->orderSet->store_detail->addressee ?? '', // 主要聯絡人 (發票資訊)
            'phone'           => $store->orderSet->store_detail->cropTel ?? '', // 電話 (發票資訊)
            'email'           => $store->email, // Email (系統通知信箱)
            // 'email_legalize'  => ($store->show_flag == 2) ? 1 : 0, // Email 認證
        ];

        // 所有的商家標記
        $storeMarks = [];
        $setTags    = $this->setTag->storeMarks()->get();
        foreach ($setTags as $setTag) {
            $storeMarks[] = [
                'key'   => $setTag->id,
                'value' => $setTag->name,
            ];
        }

        //==============================
        // 活動表單關聯設定
        //==============================

        // 取得活動表單的關聯設定 & 列表
        $events = $store->allEvents->pluck('pivot.event_id', 'pivot.type');
        $events['list'] = $this->event->orderBy('created_at', 'DESC')
                                        ->get()
                                        ->map(function ($event) {
                                            return [
                                                'id'     => $event->id,
                                                'title'  => $event->title,
                                                'status' => $event->getStatusLabel(),
                                            ];
                                        });

        //==============================
        // 合約方案/付費方式
        //==============================

        // 合約方案名稱
        $contractProgram = '';

        // 取得最後一份合約(設定費)訂單
        $lastContractOrder = $store->contractOrders->last();
        if ($lastContractOrder) {

            // 取得合約訂單中，設定費的品項
            $lastContractOrderPackage = $lastContractOrder->getContractOrderPackage();

            // 取得合約訂單中，折抵設定費品項
            $contractProgram = $lastContractOrderPackage->package_name;
            foreach ($lastContractOrder->getSettingDiscountOrderPackages() as $orderPackage) {
                $contractProgram .= ' ('.$orderPackage->package_name.')';
            }
        }

        // 信用卡列表
        $creditCards = [];
        $cardRecords = $store->cardRecord->sortBy('sort');
        foreach ($cardRecords as $cardRecord) {
            $creditCards[] = [
                'issuer'      => $cardRecord->issuer, // 信用卡-發卡銀行
                'type'        => $cardRecord->typeList[$cardRecord->type], // 信用卡-類型
                'number'      => substr_replace($cardRecord->bin_code, ' ', 4, 0).'** **** '.$cardRecord->last_four, // 信用卡-卡號
                'expiry_date' => substr_replace($cardRecord->expiry_date, '/', 4, 0), // 信用卡-到期日
            ];
        }

        // 合約方案/付費方式
        $contract = [
            'status'       => $store->present()->contract_status_label, // 合約狀態
            'program'      => $contractProgram, // 合約方案
            'start'        => $lastContractOrder ? $lastContractOrderPackage->start_date : '', // 合約期限-開始
            'end'          => $lastContractOrder ? $lastContractOrderPackage->end_date : '', // 合約期限-結束
            'auto_pay'     => $store->orderSet->auto_pay ?? '', // 是否自動每月續費
            'usage_limit'  => $store->present()->usage_limit_label, // 使用費上限設定
            'credit_cards' => $creditCards, // 信用卡綁定
        ];

        //==============================
        // 訂單記錄
        //==============================

        // 消費總金額排行
        $rankOrders = $this->order->select(DB::raw('store_id, stores.type, SUM(`amount`) total, SUM(`setting_amount`) setting_total, SUM(`usage_amount`) usage_total'))
                                    ->join($store->getTable(), function($join) {
                                        $join->on('order.store_id', '=', 'stores.id');
                                    })
                                    ->paid()
                                    ->groupBy('store_id')
                                    ->orderBy('total', 'DESC')
                                    ->get();

        // 與商家同類型的消費總金額排行
        $typeRankOrders = $rankOrders->filter(function ($item) use ($store) {
                                        return $item->type == $store->type;
                                    });

        // 此商家的消費總金額
        $storeOrder = $rankOrders->first(function ($item) use ($store) {
                                    return $item->store_id == $store->id;
                                });

        // 訂單記錄
        $order = [];
        if ($storeOrder) {
            $order = [
                'total'           => (int)$storeOrder->total, // 消費總金額
                'setting_total'   => (int)$storeOrder->setting_total, // 設定費總計
                'usage_total'     => (int)$storeOrder->usage_total, // 使用費總計
                'all_rank'        => $rankOrders->pluck('total', 'store_id')->values()->search($storeOrder->total) + 1, // 消費總金額-排名
                'all_rank_total'  => $rankOrders->count(), // 消費總金額-排名總數
                'type_rank'       => $typeRankOrders->pluck('total', 'store_id')->values()->search($storeOrder->total) + 1, // 同類型消費總金額-排名
                'type_rank_total' => $typeRankOrders->count(), // 同類型消費總金額-排名總數
            ];
        }

        //==============================
        // 近30天的時間區間
        //==============================
        // $recentlyDate = [
        //     now()->subDays(30)->format('Y-m-d 00:00:00'),
        //     now()->subDay()->format('Y-m-d 23:59:59'),
        // ];

        //==============================
        // 私訊狀況
        //==============================
        $messages = [

            // 近30天的私訊狀況
            // 'recently' => $this->getMessagesByDateRange($store, $recentlyDate),

            // 歷史私訊狀況
            'history' => $store->firestoreMessages()
                                ->select(DB::raw('YEAR(`created_at`) year, MONTH(`created_at`) month, COUNT(`id`) count'))
                                ->groupBy('year', 'month')
                                ->get()
                                ->groupBy('year'),
        ];

        //==============================
        // 詢問單狀況
        //==============================
        $reserves = [

            // 近30天的私訊狀況
            // 'recently' => $this->getReservesByDateRange($store, $recentlyDate),

            // 歷史詢問單狀況
            'history' => $store->reserves()
                                ->select(DB::raw('YEAR(`created_at`) year, MONTH(`created_at`) month, COUNT(`id`) count'))
                                ->groupBy('year', 'month')
                                ->get()
                                ->groupBy('year'),
        ];

        //==============================
        // 公開報價狀況
        //==============================
        $storeQuotes = [

            // 近30天的公開報價狀況
            // 'recently' => $this->getStoreQuotesByDateRange($store, $recentlyDate),

            // 歷史公開報價狀況
            'history' => $store->storeQuotes()
                                ->select(DB::raw('YEAR(`created_at`) year, MONTH(`created_at`) month, COUNT(`id`) count'))
                                ->groupBy('year', 'month')
                                ->get()
                                ->groupBy('year'),
        ];

        //==============================
        // 買方發票資訊 (WD to Store)
        //==============================
        $buyerInvoice = [
            'type'         => $store->orderSet->invoice_contact->invoiceType ?? '', // 發票類型
            'crop_no'      => $store->orderSet->store_detail->cropNo ?? '', // 身分證/統一編號
            'crop_name'    => $store->orderSet->store_detail->cropName ?? '', // 公司名稱
            'crop_address' => $store->orderSet->store_detail->cropAddress ?? '', // 聯絡地址
            'addressee'    => $store->orderSet->store_detail->addressee ?? '', // 聯絡人
            'crop_tel'     => $store->orderSet->store_detail->cropTel ?? '', // 聯絡電話
            'email'        => $store->orderSet->store_detail->email ?? '', // 連絡信箱
        ];

        //==============================
        // 賣方發票設定 (Store to User)
        //==============================
        $sellerInvoice = [
            'seller_name'    => $store->invoiceSetting->seller_name ?? '', // 賣方營業名稱
            'seller_ubn'     => $store->invoiceSetting->seller_ubn ?? '', // 賣方統編
            'seller_brand'   => $store->invoiceSetting->seller_brand ?? '', // 賣方品牌名稱
            'merchant_id'    => $store->invoiceSetting->merchant_id ?? '', // ezPay 商店代號
            'hash_key'       => $store->invoiceSetting->hash_key ?? '', // ezPay HashKey
            'hash_iv'        => $store->invoiceSetting->hash_iv ?? '', // ezPay HashIV
            'spreadsheet_id' => $store->invoiceSetting->spreadsheet_id ?? '', // Google雲端試算表編號
            'sheet_id'       => $store->invoiceSetting->sheet_id ?? '', // Google雲端試算表的工作表編號
            'printer_ip'     => $store->invoiceSetting->printer_ip ?? '', // 列表機IP
        ];

        //==============================
        // 共同編輯帳號
        //==============================
        $accounts = [];
        foreach ($store->allAccounts as $account) {
            $accounts[] = $this->getAccountInfo($account);
        }

        //==============================
        // 共享W姐妹的夥伴
        //==============================
        $brandStores = [];
        foreach ($store->allBrands as $brand) {

            // 取得品牌的主要商家(排除自己)
            $primaryStore = $brand->primaryStores()->where('store_id', '!=', $store->id)->first();
            if (!$primaryStore) {
                continue;
            }

            $brandStores[] = $this->getBrandStoreInfo($primaryStore, $brand);
        }

        return [
            'profile'            => $profile, // 基本設定
            'store_marks'        => $storeMarks, // 所有的商家標記
            'events'             => $events, // 活動表單關聯設定
            'free_setting_times' => $this->getFreeSettingTimes($store), // 設定費設定
            'free_usage_times'   => $this->getFreeUsageTimes($store), // 使用費設定
            'contract'           => $contract, // 合約方案/付費方式
            'order'              => $order, // 訂單記錄
            'messages'           => $messages, // 私訊狀況
            'reserves'           => $reserves, // 詢問單狀況
            'store_quotes'       => $storeQuotes, // 公開報價狀況
            // 'analytics'          => $this->getAnalyticsByDateRange($store, $recentlyDate), // 最近 30 天流量狀況
            'buyer_invoice'      => $buyerInvoice, // 買方發票資訊 (WD to Store)
            'seller_invoice'     => $sellerInvoice, //  賣方發票設定 (Store to User)
            'accounts'           => $accounts, // 共同編輯帳號
            'brand_stores'       => $brandStores, // 共享 W 姐妹的夥伴
            'kol_articles'       => $this->getArticleList($store->kolArticles), // 鑑定團
            'blog_articles'      => $this->getArticleList($store->blogArticles), // 好婚專欄
            'status_list'        => $this->formatAttributeList($store->statusList), // 狀態列表
        ];
    }

    /**
     * 設定費設定
     *
     * @param model $store
     * @return array
     */
    public function getFreeSettingTimes(Store $store)
    {
        // 設定費設定紀錄
        $lastFreeSettingTimes  = NULL;
        $freeSettingTimesLogs  = [];
        $logFreeStoreOrderSets = $store->logFreeStoreOrderSets()->where('column', 'free_setting_times')->get();
        foreach ($logFreeStoreOrderSets as $log) {
            $lastFreeSettingTimes   = $log;
            $freeSettingTimesLogs[] = [
                'created_at'     => $log->created_at->format('Y-m-d H:i:s'), // 歷史紀錄-設定日
                'value'          => $log->value, // 歷史紀錄-設定值
                'is_prepaid'     => $log->is_prepaid, // 歷史紀錄-設定費啟動方式 (贈送/購買)
                'note'           => $log->note, // 歷史紀錄-備註原因
                'free_start_at'  => $log->free_start_at, // 歷史紀錄-預計免費開啟期間
                'free_end_at'    => $log->free_end_at, // 歷史紀錄-預計免費結束期間
                'first_order_id' => $log->first_order_id, // 歷史紀錄-首期訂單編號
                'yzcube_user'    => $log->yzcubeUser->only('id', 'name', 'email'), // 歷史紀錄-設定的管理者
                'deleted_at'     => $log->deleted_at, // 作廢時間
            ];
        }

        return [
            'is_prepaid'    => $store->orderSet->is_prepaid_setting ?? NULL, // 設定費啟動方式 (贈送/購買)
            'surplus_value' => $store->orderSet->free_setting_times ?? NULL, // 設定值
            'value'         => ($lastFreeSettingTimes && ($lastFreeSettingTimes->value == '永久' || $lastFreeSettingTimes->free_end_at > now())) ? $lastFreeSettingTimes->value : NULL, // 設定值-顯示
            'updated_at'    => ($lastFreeSettingTimes && ($lastFreeSettingTimes->value == '永久' || $lastFreeSettingTimes->free_end_at > now())) ? $lastFreeSettingTimes->created_at->format('Y-m-d H:i:s') : NULL, // 設定日-顯示
            'free_start_at' => $lastFreeSettingTimes ? $lastFreeSettingTimes->free_start_at : NULL, // 預計免費開啟期間
            'free_end_at'   => $lastFreeSettingTimes ? $lastFreeSettingTimes->free_end_at : NULL, // 預計免費結束期間
            'logs'          => $freeSettingTimesLogs, // 歷史紀錄
        ];
    }

    /**
     * 使用費設定
     *
     * @param model $store
     * @return array
     */
    public function getFreeUsageTimes(Store $store)
    {
        // 使用費設定紀錄
        $lastFreeUsageTimes    = NULL;
        $freeUsageTimesLogs    = [];
        $logFreeStoreOrderSets = $store->logFreeStoreOrderSets()->where('column', 'free_usage_times')->get();
        foreach ($logFreeStoreOrderSets as $log) {
            $lastFreeUsageTimes   = $log;
            $freeUsageTimesLogs[] = [
                'created_at'     => $log->created_at->format('Y-m-d H:i:s'), // 歷史紀錄-設定日
                'value'          => $log->value, // 歷史紀錄-設定值
                'note'           => $log->note, // 歷史紀錄-備註原因
                'free_start_at'  => $log->free_start_at, // 歷史紀錄-預計免費開啟期間
                'free_end_at'    => $log->free_end_at, // 歷史紀錄-預計免費結束期間
                'first_order_id' => $log->first_order_id, // 歷史紀錄-首期訂單編號
                'yzcube_user'    => $log->yzcubeUser->only('id', 'name', 'email'), // 歷史紀錄-設定的管理者
            ];
        }

        return [
            'surplus_value' => $store->orderSet->free_usage_times ?? NULL, // 設定值
            'value'         => ($lastFreeUsageTimes && ($lastFreeUsageTimes->value == '永久' || $lastFreeUsageTimes->free_end_at > now())) ? $lastFreeUsageTimes->value : NULL, // 設定值-顯示
            'updated_at'    => ($lastFreeUsageTimes && ($lastFreeUsageTimes->value == '永久' || $lastFreeUsageTimes->free_end_at > now())) ? $lastFreeUsageTimes->created_at->format('Y-m-d H:i:s') : NULL, // 設定日-顯示
            'free_start_at' => $lastFreeUsageTimes ? $lastFreeUsageTimes->free_start_at : NULL, // 預計免費開啟期間
            'free_end_at'   => $lastFreeUsageTimes ? $lastFreeUsageTimes->free_end_at : NULL, // 預計免費結束期間
            'logs'          => $freeUsageTimesLogs, // 歷史紀錄
        ];
    }

    /**
     * 篩選日期-私訊狀況
     *
     * @param model $store
     * @param array.date $dateRange
     * @return array
     */
    public function getMessagesByDateRange(Store $store, $dateRange)
    {
        return [
            // 點擊數
            'click_count' => $this->bigQueryHandle->handle('store_total_events', [
                                'dateRange'   => $dateRange,
                                'eventName'   => 'wd_message',
                                'eventParams' => [
                                    'EXACT' => ['action' => 'open'],
                                    'INT'   => ['store_id' => $store->id],
                                ],
                            ], false),
            // 填單數（轉換率）
            'achieve_count' => $store->firestoreMessages()
                                        ->whereBetween('created_at', $dateRange)
                                        ->count(),
        ];
    }

    /**
     * 篩選日期-詢問單狀況
     *
     * @param model $store
     * @param array.date $dateRange
     * @return array
     */
    public function getReservesByDateRange(Store $store, $dateRange)
    {
        return [
            // 點擊數
            'click_count' => $this->bigQueryHandle->handle('store_total_events', [
                                'dateRange'   => $dateRange,
                                'eventName'   => 'wd_reserve',
                                'eventParams' => [
                                    'EXACT' => ['action' => '1/2'],
                                    'INT'   => ['store_id' => $store->id],
                                ],
                            ], false),
            // 填單數（轉換率）
            'achieve_count' => $store->reserves()
                                        ->whereBetween('created_at', $dateRange)
                                        ->count(),
        ];
    }

    /**
     * 篩選日期-公開報價狀況
     *
     * @param model $store
     * @param array.date $dateRange
     * @return array
     */
    public function getStoreQuotesByDateRange(Store $store, $dateRange)
    {
        return [
            'page_views'    => $this->bigQueryHandle->handle('quote_landing_page_views', $dateRange, false), // 瀏覽數
            'achieve_count' => $store->storeQuotes()->whereBetween('created_at', $dateRange)->count(), // 填單數（轉換率）
        ];
    }

    /**
     * 篩選日期-流量狀況
     *
     * @param model $store
     * @param array.date $dateRange
     * @return array
     */
    public function getAnalyticsByDateRange(Store $store, $dateRange)
    {
        // 流量狀況-商家主頁
        $logGaPageViews = $store->logGaPageViews()
                                ->where('type', 'store')
                                ->whereBetween('created_date', $dateRange)
                                ->get();
        $result['index'] = [
            'page_views'  => $logGaPageViews->sum('pageViews'),
            'total_users' => $logGaPageViews->sum('totalUsers'),
        ];

        // 流量狀況-作品頁
        $logGaPageViews = $store->logGaPageViewsSubItems()
                                ->where('type', 'store_album')
                                ->whereBetween('created_date', $dateRange)
                                ->get();
        $result['albums'] = [
            'page_views'  => $logGaPageViews->sum('pageViews'),
            'total_users' => $logGaPageViews->sum('totalUsers'),
        ];

        // 流量狀況-方案頁
        $logGaPageViews = $store->logGaPageViewsSubItems()
                                ->where('type', 'store_service')
                                ->whereBetween('created_date', $dateRange)
                                ->get();
        $result['services'] = [
            'page_views'  => $logGaPageViews->sum('pageViews'),
            'total_users' => $logGaPageViews->sum('totalUsers'),
        ];

        // 流量狀況-Ｗ姐妹(XXX篇)
        $postIds        = $store->sharePostBrands->pluck('post_id');
        // $logGaPageViews = $this->logGaPageView->where('type', 'share_post')
        //                                         ->whereIn('target_id', $postIds)
        //                                         ->whereBetween('created_date', $dateRange)
        //                                         ->get();
        $result['shares'] = [
            'post_count'  => $postIds->count(),
            // 'page_views'  => $logGaPageViews->sum('pageViews'),
            // 'total_users' => $logGaPageViews->sum('totalUsers'),
        ];

        // 站外點擊總數-主動報價
        $result['store_quotes'] = [
            'achieve_count' => $store->storeQuotes()
                                        ->whereBetween('created_at', $dateRange)
                                        ->count(),
            'click_count' => $store->logGaEventsQuotes()
                                    ->whereBetween('created_date', $dateRange)
                                    ->get()
                                    ->sum('totalEvents'),
        ];

        // 站外點擊總數-商家主頁
        $result['store_index'] = $store->logGaEvents()
                                        ->whereBetween('created_date', $dateRange)
                                        ->get()
                                        ->groupBy('type')
                                        ->map(function ($logGaEvents) {
                                            return $logGaEvents->sum('totalEvents');
                                        });

        // 收藏總數
        // 婚紗禮服是收藏作品集，其他商家類型是收藏單一作品
        $collectType = (in_array($store->type, [2, 10])) ? 'album' : 'album_image';
        $albumIds    = (in_array($store->type, [2, 10])) ? $store->showAlbums->pluck('id') : $store->albumImages->pluck('id');
        $serviceIds  = $store->showServicesWithActivity->pluck('id');
        $result['collect_count'] = [
            // 商家收藏數
            'stores' => $this->userCollect->where('type', 'store')
                                            ->where('target_id', $store->id)
                                            ->whereBetween('created_at', $dateRange)
                                            ->count(),
            // 作品收藏數
            'albums' => $this->userCollect->where('type', $collectType)
                                            ->whereIn('target_id', $albumIds)
                                            ->whereBetween('created_at', $dateRange)
                                            ->count(),
            // 方案收藏數
            'services' => $this->userCollect->where('type', 'service')
                                            ->whereIn('target_id', $serviceIds)
                                            ->whereBetween('created_at', $dateRange)
                                            ->count(),
        ];

        return $result;
    }

    /**
     * 取得帳號資訊
     *
     * @param model $storeUser
     * @return array
     */
    public function getAccountInfo($storeUser)
    {
        return [
            'id'             => $storeUser->id, // 帳號ID
            'status'         => ($storeUser->status == 'delete') ? 'delete' : 'published', // 狀態
            'email_legalize' => ($storeUser->email_legalize) ? 1 : 0, // Email 認證
            'is_admin'       => $storeUser->is_admin, // 管理員
            'email'          => $storeUser->email, // Email
            'phone'          => $storeUser->phone ?: '', // 手機
            'created_at'     => $storeUser->created_at->format('Y-m-d H:i:s'), // 建立時間
            'last_login'     => $storeUser->last_login_at, // 最近登入時間
            'fb_binding'     => $storeUser->fb_id ? 1 : 0, // 綁定 Facebook
            'fb_avatar'      => $storeUser->present()->fb_avatar, // Facebook 頭像
            'line_binding'   => $storeUser->line_id ? 1 : 0, // Line 綁定
            'line_status'    => $storeUser->lineBotUser ? 1 : 0, // Line 狀態
            'is_link'        => is_null($storeUser->pivot->deleted_at) ? 1 : 0, // 是否關聯
            'auth_logs'      => $this->authTransformer->getLogAuthToken($storeUser), // 登入者的身份驗證紀錄
        ];
    }

    /**
     * 取得共享W姐妹的夥伴資訊
     *
     * @param model $store
     * @param model $brand
     * @return array
     */
    public function getBrandStoreInfo($store, $brand)
    {
        return [
            'id'         => $store->id, // 商家ID
            'name'       => $store->name, // 商家名稱
            'logo'       => $store->logo->file_name ?? '', // 商家 Logo
            'type'       => $store->typeList[$store->type], // 商家類別
            'status'     => $store->status, // 商家狀態
            'created_at' => $store->created_at->format('Y-m-d H:i:s') ?: '', // 創立時間
            'is_link'    => $brand->pivot->show_flag ? 1 : 0, // 是否關聯
        ];
    }

    /**
     * 取得部落格文章列表資訊
     *
     * @return array
     */
    private function getArticleList($articles)
    {
        return $articles->map(function($article) {
            return [
                'id'         => $article->pivot->id, // 部落格文章的關聯ID (用於排序)
                'article_id' => $article->id, // 部落格文章ID
                'url'        => config('params.'.$article->type.'_url').'/'.$article->blog_id, // 部落格文章連結
                'title'      => $article->title, // 部落格文章標題
                'image'      => $article->image, // 部落格文章封面照
                'tag'        => $article->pivot->tag ?: '', // 部落格文章標籤
            ];
        });
    }
}
