<?php
namespace App\Services\Store\Yzcube;

use GuzzleHttp\Client;
use App\Models\Store;
use App\Traits\ApiErrorTrait;

class SaveService
{
    use ApiErrorTrait;

    private $client;
    private $storeModel;

    private $store;
    private $yzcubeUser;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client,
        Store $storeModel
    ) {
        $this->client     = $client;
        $this->storeModel = $storeModel;
    }

    /**
     * 儲存商家
     */
    public function run($request)
    {
        $this->store      = $request['store'];
        $this->yzcubeUser = $request['yzcube_user'];

        // 更新免設定費的設定
        if (isset($request['is_prepaid_setting']) && isset($request['free_setting_times'])) {
            $this->updateFreeSettingTimes($request['free_setting_times'], $request['is_prepaid_setting'], $request['note']);
        }

        // 更新免使用費的設定
        if (isset($request['free_usage_times'])) {
            $this->updateFreeUsageTimes($request['free_usage_times'], $request['note']);
        }

        // 回傳商家資訊
        return $this->store;
    }

    /**
     * 更新免設定費的設定
     */
    private function updateFreeSettingTimes($freeSettingTimes, $isPrepaid, $note)
    {
        // 檢查商家發票設定
        $this->checkStoreOrderSet();

        // 有設定免設定費 & 目前沒有訂單合約
        $newContract = NULL;
        if ($freeSettingTimes && !$this->store->orderSet->deadline) {

            // 新增免設定費的商家合約
            $newContract = $this->createFreeStoreSetContract($freeSettingTimes, $isPrepaid);
        }

        // 若有新增合約，則直接減少一期
        $newValue = ($newContract && $freeSettingTimes != '永久') ? $freeSettingTimes - 1 : $freeSettingTimes;

        // 計算免設定費的免費期間
        $timeRange = $this->getFreeSettingTimeRange($freeSettingTimes, $newContract, $newValue);

        // 紀錄免設定費設定
        if ($this->store->orderSet->free_setting_times != $freeSettingTimes || $this->store->orderSet->is_prepaid_setting != $isPrepaid) {
            // 重複的設定要作廢
            $this->store->logFreeStoreOrderSets()
                        ->where('column', 'free_setting_times')
                        ->where(function($query) use ($timeRange) {
                            if ($timeRange['free_start_at']) {
                                $query->where('free_start_at', '>=', $timeRange['free_start_at'])
                                        ->orWhere('free_end_at', '>=', $timeRange['free_start_at']);
                            }
                        })
                        ->update(['deleted_at' => now()]);

            $this->store->logFreeStoreOrderSets()->create([
                'yzcube_user_id' => $this->yzcubeUser->id,
                'column'         => 'free_setting_times',
                'value'          => $freeSettingTimes,
                'is_prepaid'     => $isPrepaid,
                'note'           => $note,
                'free_start_at'  => $timeRange['free_start_at'],
                'free_end_at'    => $timeRange['free_end_at'],
                'first_order_id' => $newContract,
            ]);
        }

        // 儲存
        $this->store->orderSet->is_prepaid_setting = $isPrepaid;
        $this->store->orderSet->free_setting_times = $newValue;
        $this->store->orderSet->save();
    }

    /**
     * 更新免使用費的設定
     */
    private function updateFreeUsageTimes($freeUsageTimes, $note)
    {
        // 檢查商家發票設定
        $this->checkStoreOrderSet();

        // 目前沒有訂單合約
        if (!$this->store->orderSet->deadline OR !$this->store->orderSet->closing_day) {
            $this->setException('此商家目前沒有方案(設定費)唷！');
        }

        // 計算免使用費的免費期間
        $timeRange = $this->getFreeUsageTimeRange($freeUsageTimes);

        // 紀錄免使用費設定
        if ($this->store->orderSet->free_usage_times != $freeUsageTimes) {
            $this->store->logFreeStoreOrderSets()->create([
                'yzcube_user_id' => $this->yzcubeUser->id,
                'column'         => 'free_usage_times',
                'value'          => $freeUsageTimes,
                'note'           => $note,
                'free_start_at'  => $timeRange['free_start_at'],
                'free_end_at'    => $timeRange['free_end_at'],
            ]);
        }

        // 儲存
        $this->store->orderSet->free_usage_times = $freeUsageTimes;
        $this->store->orderSet->save();
    }

    /**
     * wdv2 api-檢查商家發票設定
     */
    private function checkStoreOrderSet()
    {
        // 有資料則省略
        if ($this->store->orderSet) {
            return;
        }

        // wdv2 api-新增商家發票設定
        $apiPath = config('params.wdv2.api_url').'/order/createStoreOrderSet/'.$this->store->id;
        $headers = ['Access-Token' => request()->header('Access-Token')];
        $resp = $this->client->request('POST', $apiPath, ['headers' => $headers]);
        if ($resp->getStatusCode() == 200) {
            $respObj = json_decode($resp->getBody()->getContents());

            // 成功執行，更新目前model的既有資料
            if ($respObj->status) {
                $this->store->orderSet = $respObj->data;
            } else {
                $this->setException('[wdv2]'.$respObj->errors);
            }
        }

        // 新增發票設定失敗
        if (!$this->store->orderSet) {
            $this->setException('新增商家發票設定的API失敗！');
        }
    }

    /**
     * wdv2 api-新增免設定費的商家合約
     */
    private function createFreeStoreSetContract($freeSettingTimes, $isPrepaid)
    {
        $newContract = NULL;

        // wdv2 api-新增免設定費的商家合約
        $apiPath = config('params.wdv2.api_url').'/order/createFreeStoreSetContract/'.$this->store->id;
        $headers = ['Access-Token' => request()->header('Access-Token')];
        $query   = ['free_setting_times' => $freeSettingTimes, 'is_prepaid' => $isPrepaid];
        $resp = $this->client->request('POST', $apiPath, ['headers' => $headers, 'query' => $query]);
        if ($resp->getStatusCode() == 200) {
            $respObj = json_decode($resp->getBody()->getContents());

            // 成功執行，更新目前model的既有資料，並記錄order_id
            if ($respObj->status && $respObj->order_id) {
                $this->store = $this->storeModel->find($this->store->id);
                $newContract = $respObj->order_id;
            } else {
                $this->setException('[wdv2]'.$respObj->errors);
            }
        }

        // 新增合約失敗
        if (!$newContract) {
            $this->setException('新增免設定費的商家合約API失敗！');
        }

        return $newContract;
    }

    /**
     * 計算免設定費的免費期間
     */
    private function getFreeSettingTimeRange($freeSettingTimes, $newContract, $newValue)
    {
        $freeStartAt = NULL;
        $freeEndAt   = NULL;
        if ($freeSettingTimes) {

            // 取出結算日
            $closingDay = str_pad($this->store->orderSet->closing_day, 2, '0', STR_PAD_LEFT);

            // (下一期)免設定費的起始日
            $deadline = $this->store->orderSet->deadline;
            $freeStartAt = $newContract ? now() : date('Y-m-d 00:00:00', strtotime($deadline.'+1 day'));

            // (未知期)免設定費的結束日
            if ($freeSettingTimes != '永久') {
                $freeEndAt = date('Y-m-d 23:59:59', strtotime($deadline.'+'.$newValue.' months'));
                // 驗證小月(28號~31號)的問題
                if (date('d', strtotime($freeEndAt)) != $closingDay) {
                    $freeEndAt = date('Y-m-d 23:59:59', strtotime(date('Y-m-01', strtotime($freeEndAt)).'-1 day'));
                }
            }
        }

        return [
            'free_start_at' => $freeStartAt,
            'free_end_at'   => $freeEndAt,
        ];
    }

    /**
     * 計算免使用費的免費期間
     */
    private function getFreeUsageTimeRange($freeUsageTimes)
    {
        $freeStartAt = NULL;
        $freeEndAt   = NULL;
        if ($freeUsageTimes) {

            // 若當期合約訂單有贈送優惠日(首期設定費送14天)
            $addStartBuff = '';
            $addEndBuff   = '';
            if (isset($this->store->orderSet->contractOrder->description->buffDeadline)) {
                $addStartBuff = '+1 month -'.($this->store->orderSet->contractOrder->description->buffDeadline + 1).' days';
                $addEndBuff   = '+1 month';
            }

            // 取出結算日
            $closingDay = str_pad($this->store->orderSet->closing_day, 2, '0', STR_PAD_LEFT);

            // 若目前日期小於等於結帳日，則取上一個月的結帳日
            $subMonth = (date('d') <= $closingDay) ? '-1 month' : '';
            $lastClosingDay = date('Y-m-'.$closingDay).$subMonth;

            // (當期)免使用費的起始日
            $freeStartAt = date('Y-m-d 00:00:00', strtotime($lastClosingDay.'+1 day'.$addStartBuff));
            // 驗證小月(28號~31號)的問題
            if (date('d', strtotime($lastClosingDay)) != $closingDay) {
                $freeStartAt = date('Y-m-01 00:00:00');
            }

            // (未知期)免使用費的結束日
            if ($freeUsageTimes != '永久') {
                $freeEndAt = date('Y-m-d 23:59:59', strtotime($lastClosingDay.'+'.$freeUsageTimes.' months'.$addEndBuff));
                // 驗證小月(28號~31號)的問題
                if (date('d', strtotime($freeEndAt)) != $closingDay) {
                    $freeEndAt = date('Y-m-d 23:59:59', strtotime(date('Y-m-01', strtotime($freeEndAt)).'-1 day'));
                }
            }
        }

        return [
            'free_start_at' => $freeStartAt,
            'free_end_at'   => $freeEndAt,
        ];
    }
}
