<?php
/*
 |--------------------------------------
 |  更新商家排行 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\Store;
use App\Models\Wdv2\SetTag;

class UpdateStoreRankService
{
    private $store;
    private $setTag;

    private $date; // 要執行的日期

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Store $store,
        SetTag $setTag
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->store  = $store;
        $this->setTag = $setTag;
    }

    /**
     * 計算商家的排名積分
     */
    public function run($date)
    {
        // 統計範圍的時間區間
        $this->date = $date;

        // 先清空所有商家的熱門排行
        $this->store->whereNotNull('rank')
                    ->orWhereNotNull('rank_discussion')
                    ->update([
                        'rank'            => NULL,
                        'rank_discussion' => NULL,
                    ]);

        // 新上架的所有付費商家，並排序
        $afterDate = date('Y-m-d', strtotime($this->date.'-10 days'));
        $newStores = $this->store->select('id', 'type')
                                    ->whereIn('type', array_keys($this->store->needRankList))
                                    ->published()
                                    ->where('released_at', '>', $afterDate)
                                    ->orderBy('released_at', 'DESC')
                                    ->get()
                                    ->groupBy('type')
                                    ->map(function ($stores) {
                                        return $stores->pluck('id')->toArray();
                                    });

        // 有大師標籤的所有付費商家
        $topStores = $this->store->select('id', 'type')
                                    ->whereIn('type', array_keys($this->store->needRankList))
                                    ->published()
                                    ->whereHas('marks', function ($q) {
                                        $q->where($this->setTag->getTable().'.id', $this->setTag->storeMasterId);
                                    })
                                    ->get()
                                    ->groupBy('type')
                                    ->map(function ($stores) {
                                        return $stores->pluck('id')->toArray();
                                    });

        // 需計算排名的所有付費商家
        $storeTypeGroups = $this->store->select('id', 'type')
                                        ->whereIn('type', array_keys($this->store->needRankList))
                                        ->published()
                                        ->with('integrals', function ($query) {
                                            $query->where('created_date', $this->date);
                                        })
                                        ->get()
                                        ->groupBy('type');

        // 依序商家分類
        $total = 0;
        foreach ($storeTypeGroups as $storeType => $stores) {

            // 統整商家積分
            $basic      = [];
            $income     = [];
            $conversion = [];
            $discussion = [];
            foreach ($stores as $store) {
                $storeIntegral = $store->integrals->first();
                if (!$storeIntegral) {
                    continue;
                }
                $basic[$store->id] = $storeIntegral->basic;
                if ($storeIntegral->income > 0) $income[$store->id] = $storeIntegral->income;
                if ($storeIntegral->conversion > 0) $conversion[$store->id] = $storeIntegral->conversion;
                $discussion[$store->id] = $storeIntegral->discussion;
            }

            // 商家的積分排行榜，由高至低排序
            arsort($basic);
            arsort($income);
            arsort($conversion);
            $basic      = array_keys($basic);
            $income     = array_keys($income);
            $conversion = array_keys($conversion);

            // 初始化總分數據
            $this->score    = [];
            $this->scoreNew = [];
            $this->scoreTop = [];
            $this->rank     = [];

            // 計算總分
            $this->addScore($basic, 1);
            $this->addScore($income, 0.3);
            $this->addScore($conversion, 0.01);

            // 若沒有任何商家的總分就先跳出，避免下面壞掉
            if (!$this->score) {
                continue;
            }

            // 沒資料的話會噴錯
            $_newStores = $newStores[$storeType] ?? [];
            $_topStores = $topStores[$storeType] ?? [];

            // 計算新商家的總分
            $this->addScoreNew($_newStores);

            // 計算大師標籤商家的總分
            $this->addScoreTop($_topStores);

            // 商家的總分排行榜，由高至低排序
            arsort($this->score);
            arsort($this->scoreNew);
            arsort($this->scoreTop);
            $this->score    = array_keys($this->score);
            $this->scoreNew = array_keys($this->scoreNew);
            $this->scoreTop = array_keys($this->scoreTop);

            // 取得實際排名的位置，並將商家放進去
            foreach ($this->getStoreRankLocation() as $value) {
                if ($value == 'new' && $this->scoreNew) {
                    if (!$this->pushRank(array_shift($this->scoreNew))) {
                        continue;
                    }
                }
                if ($value == 'top' && $this->scoreTop) {
                    if (!$this->pushRank(array_shift($this->scoreTop))) {
                        continue;
                    }
                }
                if (!$this->score) {
                    break;
                }
                $this->pushRank(array_shift($this->score));
            }

            // 剩下的就按造排序放進排名裡
            foreach ($this->score as $val) {
                $this->pushRank(array_shift($this->score));
            }

            // 更新商家的熱門排行
            $count = count($this->rank);
            foreach ($this->rank as $key => $storeId) {
                $rank  = $count - $key;
                $store = $this->store->find($storeId);
                $store->integrals()
                        ->where('created_date', $this->date)
                        ->update(['rank' => $rank]);

                $store->update([
                            'rank'            => $rank,
                            'rank_discussion' => $discussion[$storeId] ?: 0,
                        ]);
                $total++;
            }
        }

        return $total;
    }

    /**
     * 計算總分 (積分排行*權重分數)
     * @param array $integrals 商家的積分排行榜
     * @param int $weights 權重分數
     * @return
     */
    private function addScore($integrals, $weights)
    {
        $count = count($integrals);
        foreach ($integrals as $key => $storeId) {
            if (empty($this->score[$storeId])) {
                $this->score[$storeId] = 0;
            }
            $this->score[$storeId] += ($count - $key) * $weights;
        }
    }

    /**
     * 計算新商家的總分
     * @param array $integrals 新商家的積分排行榜
     * @return
     */
    private function addScoreNew($integrals)
    {
        // 依照總分排序增加權重
        $count      = count($integrals);
        $scoreArray = array_values(array_intersect(array_keys($this->score), $integrals));
        foreach ($scoreArray as $key => $storeId) {
            $this->scoreNew[$storeId] = ($count - $key) * 2;
        }

        // 越新上架的商家權重越高
        foreach ($integrals as $key => $storeId) {
            $this->scoreNew[$storeId] += $count - $key;
        }
    }

    /**
     * 計算大師標籤商家的總分
     * @param array $integrals 大師標籤商家的積分
     * @return
     */
    private function addScoreTop($integrals)
    {
        // 依照總分排序增加權重
        $count      = count($integrals);
        $scoreArray = array_values(array_intersect(array_keys($this->score), $integrals));
        foreach ($scoreArray as $key => $storeId) {
            $this->scoreTop[$storeId] = $count - $key;
        }
    }

    /**
     * 取得動態隨機的排名表
     * @return array
     */
    private function getStoreRankLocation()
    {
        $tagNew = rand(4, 10); // 決定標記『新進』在 11~40 名的名額
        $result = [];
        $set    = [
            '0-5' => [
                '-' => 5,
            ],
            '6-10' => [
                'top' => 2,// 大師
                '-'   => 3,
            ],
            '11-40' => [
                'top' => 2,
                'new' => $tagNew, // 新進店家
                '-'   => (28 - $tagNew),
            ],
            '31-120' => [
                'top' => 8,
                'new' => (12 - $tagNew),
                '-'   => (60 + $tagNew),
            ],
        ];

        foreach ($set as $range) {

             // 初始化
            $temp = [];
            foreach ($range as $k => $v) {

                // 創建各屬性的陣列
                if (count($range) == 1) {
                    $result = array_merge($result, array_fill(0, 5, $k));
                } else {
                    if ($temp) {
                        $temp = array_merge($temp, array_fill(count($temp), $v, $k));
                    } else {
                        $temp = array_fill(count($temp), $v, $k);
                    }
                }
            }

            // 隨機分配
            if ($temp) {
                shuffle($temp);
                foreach ($temp as $v) {
                    $result[] = $v;
                }
            }
        }

        return $result;
    }

    /**
     * 將商家放進排名裡
     * @param int $storeId 從排行榜取出來的商家ID
     * @return
     */
    private function pushRank($storeId)
    {
        if (!$storeId) {
            return false;
        }

        if (in_array($storeId, $this->rank)) {
            return false;
        }

        // 放進陣列裡
        $this->rank[] = $storeId;

        // 移除陣列裡的東西
        if (in_array($storeId, $this->score)) {
            unset($this->score[array_search($storeId, $this->score)]);
        }

        if (in_array($storeId, $this->scoreNew)) {
            unset($this->scoreNew[array_search($storeId, $this->scoreNew)]);
        }

        if (in_array($storeId, $this->scoreTop)) {
            unset($this->scoreTop[array_search($storeId, $this->scoreTop)]);
        }

        return true;
    }
}
