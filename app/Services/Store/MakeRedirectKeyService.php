<?php
/*
 |--------------------------------------
 |  建立轉址資訊 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Store;

use App\Models\RedirectUrl;
use App\Traits\RandStringTrait;

class MakeRedirectKeyService
{
    private $redirectUrl;

    use RandStringTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        RedirectUrl $redirectUrl
    ) {
        $this->redirectUrl = $redirectUrl;
    }

    /**
     * 建立轉址資訊
     */
    public function run($targetType, $model, $shopModel = Null, $callbackColumn = NULL)
    {
        $result = NULL;

        // 外站連結的欄位列表
        $columnList = $this->redirectUrl->{$targetType.'ColumnList'};
        foreach ($columnList as $column => $name) {

            // 驗證商家有沒有欄位資料
            if ($targetType == 'store' && !$model->{$column}) {
                continue;
            }

            // 驗證品牌有沒有欄位資料
            if ($targetType == 'brand' && (!isset($model->{$column}) || !$model->{$column})) {
                continue;
            }

            // 找出已建立的轉址資訊
            $redirectUrl = $this->redirectUrl->where('target_type', $targetType)
                                            ->where('target_id', $model->id)
                                            ->whereNull('shop_id')
                                            ->where('column', $column)
                                            ->first();

            // 修改或新增轉址資訊
            $redirectUrl = $this->redirectUrl->updateOrCreate([
                'key'         => $redirectUrl ? $redirectUrl->key : $this->getRedirectKey(),
                'target_type' => $targetType,
                'target_id'   => $model->id,
                'column'      => $column,
            ]);

            // 回傳需要的欄位類型
            if ($callbackColumn == $column) {
                $result = $redirectUrl;
            }
        }

        // For 門市的Model
        if ($shopModel) {
            $columnList = $this->redirectUrl->{'shopColumnList'};
            foreach ($columnList as $column => $name) {

                // 驗證門市有沒有欄位資料
                if (!$shopModel->{$column}) {
                    continue;
                }

                // 找出已建立的轉址資訊
                $redirectUrl = $this->redirectUrl->where('target_type', $targetType)
                                                ->where('target_id', $model->id)
                                                ->where('shop_id', $shopModel->id)
                                                ->where('column', $column)
                                                ->first();

                // 修改或新增轉址資訊
                $redirectUrl = $this->redirectUrl->updateOrCreate([
                    'key'         => $redirectUrl ? $redirectUrl->key : $this->getRedirectKey(),
                    'target_type' => $targetType,
                    'target_id'   => $model->id,
                    'shop_id'     => $shopModel->id,
                    'column'      => $column,
                ]);

                // 回傳需要的欄位類型
                if ($callbackColumn == $column) {
                    $result = $redirectUrl;
                }
            }
        }

        return $result;
    }

    /**
     * 產生唯一轉址索引值
     *
     * @return string $redirectUrl->key
     */
    protected function getRedirectKey()
    {
        $key = $this->getRandString(6);
        while ($this->redirectUrl->where('key', $key)->exists()) {
            $key = $this->getRandString(6);
        }

        return $key;
    }
}
