<?php
/*
 |--------------------------------------
 | 結算 Firestore 的訊息紀錄
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools;

use App\Models\FirestoreMessage;
use Carbon\Carbon;

class FirestoreMessageSettleService
{
    private $firestoreMessage;

    private $isExecute = false; // 是否執行

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        FirestoreMessage $firestoreMessage
    ) {
        $this->firestoreMessage = $firestoreMessage;
    }

    /**
     * 結算 Firestore 的訊息紀錄
     *
     * @return bool $isExecute 是否執行
     */
    public function run()
    {
        // 未結算的訊息紀錄
        $firestoreMessages = $this->firestoreMessage->whereNull('settle_status')->get();
        foreach ($firestoreMessages as $log) {

            // 找出聊天室資訊
            $firestore = app('firebase.firestore');
            $roomRef = $firestore->database()
                                    ->collection('messages')
                                    ->document($log->document_id);
            $roomObj = $roomRef->snapshot()
                                ->data();
            if (!$roomObj) {
                continue;
            }

            // 聊天室建立時間
            $roomCreatedAt = Carbon::parse($roomObj['created_at']);

            // 有檔期的商家類型
            if ($log->store->hasScheduleDateByType()) {

                // 新娘未提供婚期，此為「無效-新娘無婚期」
                if (empty($roomObj['user_first_wedding_date'])) {
                    $log->saveSettleResult('empty_date', $roomCreatedAt, $roomRef);
                    $this->isExecute = true;
                    continue;
                }

                // 新娘提供的婚期狀檔，此為「無效-商家滿檔」
                if (isset($roomObj['user_first_full_status']) && $roomObj['user_first_full_status'] == 3) {
                    $log->saveSettleResult('full_date', $roomCreatedAt, $roomRef);
                    $this->isExecute = true;
                    continue;
                }
            }

            // 商家有回覆
            if (isset($roomObj['store_first_reply_diff'])) {

                // 商家(於聊天室建立時間)超過48小時才回覆，此為「有效」
                if ($roomObj['store_first_reply_diff'] > 48 * 60 * 60) {
                    $log->saveSettleResult('effective', $roomCreatedAt->addHours(48), $roomRef);
                    $this->isExecute = true;
                    continue;

                // 商家(於聊天室建立時間)48小時內有回覆
                } else {

                    // 商家回覆時間
                    $storeReplyAt = $roomCreatedAt->addSeconds($roomObj['store_first_reply_diff']);

                    // 新娘已讀
                    if (isset($roomObj['user_first_read_diff'])) {

                        // 商家(於聊天室建立時間)48小時內有回覆、新娘超過48小時才已讀，此為「無效-新娘48小時未讀」
                        if ($roomObj['user_first_read_diff'] > 48 * 60 * 60) {
                            $log->saveSettleResult('user_unread_48h', $storeReplyAt->addHours(48), $roomRef);
                            $this->isExecute = true;

                        // 商家(於聊天室建立時間)48小時內有回覆、新娘48小時內已讀，此為「有效」
                        } else {

                            // 新娘已讀時間
                            $userReadAt = $storeReplyAt->addSeconds($roomObj['user_first_read_diff']);
                            $log->saveSettleResult('effective', $userReadAt, $roomRef);
                            $this->isExecute = true;
                        }
                        continue;

                    // 新娘未讀
                    } else {

                        // 商家48小時內有回覆、新娘未讀、商家的回覆時間超過48小時，此為「無效-新娘48小時未讀」
                        if (now()->diffInSeconds($storeReplyAt) > 48 * 60 * 60) {
                            $log->saveSettleResult('user_unread_48h', $storeReplyAt->addHours(48), $roomRef);
                            $this->isExecute = true;
                            continue;
                        }
                    }
                }

            // 商家未回覆
            } else {

                // 商家未回覆、聊天室建立時間超過48小時，此為「有效」
                if (now()->diffInSeconds($roomCreatedAt) > 48 * 60 * 60) {
                    $log->saveSettleResult('effective', $roomCreatedAt->addHours(48), $roomRef);
                    $this->isExecute = true;
                    continue;
                }
            }
        }

        return ($firestoreMessages->isEmpty() || $this->isExecute);
    }
}
