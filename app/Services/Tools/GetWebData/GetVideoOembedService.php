<?php
/*
 |--------------------------------------
 |  取得影片的嵌入資訊
 |--------------------------------------
 |
 |
 |
 */

namespace App\Services\Tools\GetWebData;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class GetVideoOembedService
{
    private $url;
    private $result;
    private $client;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client
    ) {
        $this->client = $client;
    }

    /**
     * 取得影片的嵌入資訊
     *
     * @param $url
     * @return object
     */
    public function getDataByUrl($url)
    {
        $this->url = $url;

        $pattern = '/(youtu|vimeo)/';
        if (!preg_match($pattern, $this->url, $matches)) {
            return false;
        }

        switch ($matches[0]) {
            case 'youtu':
                $this->getYoutubeOembed();
                break;

            case 'vimeo':
                $this->getVimeoOembed();
                break;

            default:
                return false;
        }

        return $this->result;
    }

    /**
     * 取得Youtube的嵌入資訊
     *
     * @param $url
     * @return void
     */
    private function getYoutubeOembed()
    {
        try {
            $apiPath = 'https://www.youtube.com/oembed?url='.$this->url.'&format=json';
            $resp    = $this->client->request('GET', $apiPath);
            if ($resp->getStatusCode() != 200) {
                return false;
            }
        } catch (RequestException $e) {
            return false;
        }

        $this->result = json_decode($resp->getBody());

        // 取得Youtube的影片ID
        $this->getYoutubeVideoId();

        // 取得Youtube的影片描述
        $this->getYoutubeDescription();

        // 2021-09-30: Youtube 封面照霧霧的，因為 hqdefault.jpg 尺寸太小了，又發現 sddefault.jpg 可能會給破圖，改 sd1.jpg 又會出現非封面照，最後決定維持 hqdefault.jpg 當做封面照.
        // 參考資料 https://stackoverflow.com/questions/2068344/how-do-i-get-a-youtube-video-thumbnail-from-the-youtube-api
        // $this->result->thumbnail_url = str_replace('hqdefault.jpg', 'sd1.jpg', $this->result->thumbnail_url);
    }

    /**
     * 取得Youtube的影片ID
     *
     * @param $url
     * @return void
     */
    private function getYoutubeVideoId()
    {
        $pattern = '%^# Match any youtube URL
            (?:https?://)?  # Optional scheme. Either http or https
            (?:www\.)?      # Optional www subdomain
            (?:             # Group host alternatives
              youtu\.be/    # Either youtu.be,
            | youtube\.com  # or youtube.com
              (?:           # Group path alternatives
                /embed/     # Either /embed/
              | /v/         # or /v/
              | /watch\?v=  # or /watch\?v=
              )             # End path alternatives.
            )               # End host alternatives.
            ([\w-]{10,12})  # Allow 10-12 for 11 char youtube id.
        $%x';

        preg_match($pattern, $this->url, $matches);

        $this->result->video_id = $matches[1] ?? '';
    }

    /**
     * 取得Youtube的影片描述
     *
     * @param $video_id
     * @return void
     */
    private function getYoutubeDescription()
    {
        $this->result->description = '';
        if (!$this->result->video_id) {
            return false;
        }

        $KEY_FILE_LOCATION = base_path(env('GOOGLE_SERVICE_ACCOUNT_JSON_LOCATION'));

        $client = new \Google_Client();
        $client->setAuthConfig($KEY_FILE_LOCATION);
        $client->setScopes(['https://www.googleapis.com/auth/youtube.readonly']);

        $service = new \Google_Service_YouTube($client);
        $response = $service->videos->listVideos('snippet', [
            'id' => $this->result->video_id,
        ]);

        // foreach ($response->getItems() as $item) {
        //     return $item->getSnippet()->getDescription();
        // }
        $this->result->description = $response['items'][0]['snippet']['description'] ?? '';
    }

    /**
     * 取得Vimeo的嵌入資訊
     *
     * @param $url
     * @return void
     */
    private function getVimeoOembed()
    {
        // 取出取乾淨的Vimeo影片網址
        $pattern = '/https?:\/\/[\w\.]*vimeo\.com\/[\d]+/';
        if (!preg_match($pattern, $this->url, $matches)) {
            return false;
        }

        try {
            // https://developer.vimeo.com/api/oembed/videos
            $apiPath = 'https://vimeo.com/api/oembed.json?url='.$matches[0];
            $resp    = $this->client->request('GET', $apiPath);
            if ($resp->getStatusCode() != 200) {
                return false;
            }
        } catch (RequestException $e) {
            return false;
        }

        $this->result = json_decode($resp->getBody());

        // 錯誤處理
        if (isset($this->result->domain_status_code)) {
            $this->getVimeoErrorMessage();
            return;
        }

        // 2021-09-30: Vimeo 封面照霧霧的，因為 _295x166 尺寸太小了，改成 _640 取得封面照.
        if (isset($this->result->thumbnail_url)) {
            $this->result->thumbnail_url = str_replace('_295x166', '_640', $this->result->thumbnail_url);
        }
    }

    /**
     * 取得Vimeo的錯誤訊息
     *
     * @param $domain_status_code
     * @return void
     */
    private function getVimeoErrorMessage()
    {
        switch ($this->result->domain_status_code) {
            case 304:
                $this->result->error_msg = '此影片無法使用，請確認影片連結是否正確';
                return;
            case 403:
                $this->result->error_msg = '影片因嵌入功能被禁止，無法使用';
                return;
            case 404:
                $this->result->error_msg = '影片因轉碼或隱私權限問題，無法使用';
                return;

            default:
                $this->result->error_msg = '此影片連結無法使用，請更換另一個連結';
                return;
        }
    }
}
