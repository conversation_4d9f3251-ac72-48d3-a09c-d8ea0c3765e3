<?php
/*
 |--------------------------------------
 |  取得部落格詳細資訊 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\GetWebData;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use App\Models\BlogArticle;
use App\Models\LogGaPageView;
use Log;

class GetBlogPostInfoService
{
    private $client;
    private $blogArticle;
    private $logGaPageView;
    private $type;
    private $post;
    private $result;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client,
        BlogArticle $blogArticle,
        LogGaPageView $logGaPageView
    ) {
        $this->client      = $client;
        $this->blogArticle = $blogArticle;
        $this->logGaPageView = $logGaPageView;
    }

    /**
     * 用部落格編號，取得部落格文章物件
     */
    public function getBlogPostById($type, $blogId)
    {
        try {
            $apiPath = config('params.'.$type.'_url').'/wp-json/wp/v2/posts/'.$blogId;
            $resp    = $this->client->request('GET', $apiPath);
            if ($resp->getStatusCode() != 200) {
                return false;
            }
        } catch (RequestException $e) {
            return false;
        }

        $this->post = json_decode($resp->getBody());
        $this->type = $type;
    }

    /**
     * 設定API回傳的部落格文章物件
     */
    public function setBlogPost($type, $post)
    {
        $this->type = $type;
        $this->post = $post;
    }

    /**
     * 儲存部落格文章
     */
    public function saveBlogArticle()
    {
        // 需先設定好參數
        if (!$this->type OR !$this->post) {
            return false;
        }

        // 若部落格文章已存在，則重新上架
        $this->result = $this->blogArticle->type($this->type)
                                            ->where('blog_id', $this->post->id)
                                            ->withTrashed()
                                            ->first();

        // 更新部落格文章
        if ($this->result) {

            // 標題
            if ($this->result->allow_import_title OR !$this->result->title) {
                $this->result->title = html_entity_decode($this->post->title->rendered);
            }

            // 封面照
            if ($this->result->allow_import_image OR !$this->result->image) {
                $this->result->image = $this->getBlogPostMediaImage();
            }

            // 預設分類名稱
            $store = $this->result->stores()
                                    ->whereIn('type', array_keys($this->blogArticle->categoryList))
                                    ->first();
            if (!$this->result->category && $store) {
                $this->result->category = $this->blogArticle->categoryList[$store->type];
            }
            $this->result->published_at = $this->post->date;
            $this->result->modified_at  = $this->post->modified;
            $this->result->page_view    = $this->logGaPageView->sumKolPageView($this->post->id);
            $this->result->restore();

        // 新增部落格文章
        } else {
            $this->result = $this->blogArticle->create([
                'type'         => $this->type,
                'blog_id'      => $this->post->id,
                'title'        => html_entity_decode($this->post->title->rendered),
                'image'        => $this->getBlogPostMediaImage(),
                'page_view'    => $this->logGaPageView->sumKolPageView($this->post->id),
                'published_at' => $this->post->date,
                'modified_at'  => $this->post->modified,
            ]);
        }

        return true;
    }

    /**
     * 取得部落格文章
     */
    public function getBlogArticle()
    {
        return $this->result;
    }

    /**
     * 取部落格文章預設封面
     */
    private function getBlogPostMediaImage()
    {
        $image = $this->post->featured_img ?? NULL;

        if (!$this->post->featured_media) {
            return $this->useResizeCloudFrontPath($image);
        }

        try {
            // wordpress api: List Media
            $mediaResult  = $this->client->request('GET', $this->post->_links->{'wp:featuredmedia'}[0]->href);
            $media        = json_decode($mediaResult->getBody());
            $mediaDetails = $media->media_details->sizes;
            $image        = isset($mediaDetails->medium_large) ? $mediaDetails->medium_large->source_url : $media->source_url;
        } catch (RequestException $e) {
            // Error Log
            $response = $e->getResponse()->getBody();
            Log::error($e->getMessage(), [
                'class'    => class_basename(get_class($this)),
                'api_path' => $this->post->_links->{'wp:featuredmedia'}[0]->href,
                'blog_id'  => $this->post->id,
                'response' => json_decode($response),
            ]);
        }

        return $this->useResizeCloudFrontPath($image);
    }

    /**
     * 封面照網址改用縮圖的CDN
     */
    private function useResizeCloudFrontPath($image)
    {
        // 先去除WP的縮圖參數，取cdn原圖
        $image = preg_replace_callback(
            '/https:\/\/cdn\.weddingday\.com\.tw\/.*(-\d+x\d+)\..*/',
            function ($matches) {
                return str_replace($matches[1], '', $matches[0]);
            },
            $image
        );

        // 改用rcdn
        return str_replace(config('params.file_url'), config('params.image_url'), $image);
    }
}
