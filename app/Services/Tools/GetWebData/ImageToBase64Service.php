<?php
/*
 |--------------------------------------
 |  圖片網址輸出Base64
 |--------------------------------------
 |
 |
 |
 */

namespace App\Services\Tools\GetWebData;

use App\Traits\CurlTrait;

class ImageToBase64Service
{
    use CurlTrait;

    /**
     * @name 圖片網址輸出Base64
     *
     * @param $url
     * @return base64
     */
    public function run($url)
    {
        if (!$url) {
            return false;
        }

        // 使用CURL 取得網址內容
        $content = $this->getCurlResponseContent($url);
        if (!$content) {
            return false;
        }

        // 驗證副檔名
        $mimeTypes = [
            'gif'  => 'image/gif',
            'jpg'  => 'image/jpg',
            'jpeg' => 'image/jpeg',
            'png'  => 'image/png',
            'bmp'  => 'image/bmp',
        ];
        $path = parse_url($url, PHP_URL_PATH);
        $ext  = pathinfo($path, PATHINFO_EXTENSION);
        $ext  = strtolower($ext);
        if (!array_key_exists($ext, $mimeTypes)) {
            return false;
        }

        return 'data:'.$mimeTypes[$ext].';base64,'.base64_encode($content);
    }
}
