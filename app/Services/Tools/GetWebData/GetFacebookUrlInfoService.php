<?php
/*
 |--------------------------------------
 |  從 粉絲頁連結 取得 粉絲頁ID  Service
 |--------------------------------------
 |
 |  因為臉書API的授權拿不到，所以用Curl方式來取得粉絲頁ID，以及統一的粉絲頁連結
 |
 */

namespace App\Services\Tools\GetWebData;

class GetFacebookUrlInfoService
{
    private $result = [
        'id'    => NULL,
        'url'   => NULL,
        'title' => NULL,
    ];

    /**
     * 透過臉書粉絲頁連結取得資訊
     *
     * @param [type] $url
     * @return void
     */
    public function get($url)
    {
        // 從粉絲頁連結取得粉絲頁的ID
        $this->encode($url);

        // 透過粉絲頁ID取得粉絲頁連結與名稱
        if ($this->result['id']) {
            $this->decode();
        }

        return $this->result;
    }

    /**
     * Url to Id
     *
     * @param [type] $url
     * @return void
     */
    public function encode($url)
    {
        // 整理出 FB 粉專主頁的連結
        // https://www.facebook.com/weddingdaytaiwan
        // https://www.facebook.com/pg/weddingdaytaiwan/photos/?ref=page_internal
        $url = str_replace('/pg/', '/', $url);
        preg_match('/https?:\/\/www\.facebook\.com\/[\w]+/', $url, $urlMatch);
        if (!$urlMatch) {
            return NULL;
        }
        $fburl = $urlMatch[0];

        // Curl 取得網頁
        $ch = curl_init();
        // curl_setopt($ch, CURLOPT_URL, 'https://findmyfbid.in/');
        // curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36');
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // curl_setopt($ch, CURLOPT_POST, true);
        // curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        // curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['fburl' => $url]));

        curl_setopt($ch, CURLOPT_URL, 'https://lookup-id.com/');
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, true); // 啟用POST
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['fburl' => $fburl, 'check' => 'Lookup']));
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

        $html = curl_exec($ch);
        curl_close($ch);

        // <code>168354659975964</code>
        // preg_match_all('/<code>([0-9]{1,})<\/code>/Usi', $html, $fb_id, PREG_SET_ORDER);

        // <span id="code">168354659975964</span>
        preg_match_all('/<span id=\"code\">(.*)<\/span>/Usi', $html, $fb_id, PREG_SET_ORDER);

        $this->result['id'] = isset($fb_id[0][1]) ? $fb_id[0][1] : NULL;
    }

    /**
     * Id to Url
     *
     * @return void
     */
    public function decode()
    {
        $fburl = 'https://www.facebook.com/'.$this->result['id'];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fburl);
        curl_setopt($ch, CURLOPT_HEADER, 0 );
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/76.0.3809.100 Safari/537.36');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $html = curl_exec($ch);
        curl_close($ch);

        //<link rel="alternate" media="handheld" href="https://m.facebook.com/weddingdaytaiwan/?locale2=zh_TW" />
        preg_match_all('/<link rel=\"alternate\" media=\"handheld\" href=\"(.*)\" \/>/Usi', $html, $fb_url, PREG_SET_ORDER);

        //<title id="pageTitle">WeddingDay好婚市集 - 首頁 | Facebook</title>
        // preg_match_all('/<title id=\"pageTitle\">(.*) - (.*)<\/title>/Usi', $html, $fb_title, PREG_SET_ORDER);

        //<title>WeddingDay好婚市集 - 首頁 | Facebook</title>
        preg_match_all('/<title>(.*) - (.*) | Facebook<\/title>/Usi', $html, $fb_title, PREG_SET_ORDER);

        if (isset($fb_url[0][1])) {
            // 分析網址
            $parseUrl     = parse_url($fb_url[0][1]);
            $this->result = [
                'id'    => $this->result['id'],
                'url'   => 'https://www.facebook.com'.$parseUrl['path'],
                'title' => $fb_title[0][1],
            ];
        }
    }
}
