<?php
/*
 |--------------------------------------
 |  匯入部落格所有文章 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\GetWebData;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use App\Models\BlogArticle;
use App\Services\Tools\GetWebData\GetBlogPostInfoService;
use Log;

class ImportBlogAllPostsService
{
    private $client;
    private $blogArticle;
    private $getBlogPostInfoService;
    private $publishedIDs;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client,
        BlogArticle $blogArticle,
        GetBlogPostInfoService $getBlogPostInfoService
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(1800);//時間設定在30分鐘

        $this->client                 = $client;
        $this->blogArticle            = $blogArticle;
        $this->getBlogPostInfoService = $getBlogPostInfoService;
    }

    /**
     * 匯入部落格所有文章
     * https://developer.wordpress.org/rest-api/reference/posts/
     */
    public function run()
    {
        // 參數設定
        $typeList = array_keys($this->blogArticle->typeList);
        $query    = [
            // 'context'  => 'embed',
            'context'  => 'view',
            '_fields'  => 'id,date,modified,excerpt,title,featured_media,_links',
            'order'    => 'asc',
            'orderby'  => 'id',
            'page'     => 1,
            'per_page' => 100,
        ];

        // 目前有兩個部落格
        foreach ($typeList as $type) {

            // 現有上架的部落格文章
            $this->publishedIDs = $this->blogArticle->type($type)
                                                    ->pluck('modified_at', 'blog_id');

            // API從第一頁開始call
            $query['page'] = 1;
            $flag          = true;
            $apiPath       = config('params.'.$type.'_url').'/wp-json/wp/v2/posts';
            while ($flag) {
                try {
                    // wordpress api: List Posts
                    $result = $this->client->request('GET', $apiPath, ['query' => $query]);
                    $posts  = json_decode($result->getBody());
                    foreach ($posts as $post) {

                        // 排除受保護(密碼)的文章
                        if ($post->excerpt->protected) {
                            continue;
                        }

                        // 檢視部落格文章
                        $this->checkBlogArticles($type, $post);
                    }

                    // 若此頁數量等於100筆則繼續
                    $flag = (count($posts) == $query['per_page']);

                } catch (RequestException $e) {
                    // Error Log
                    $response = $e->getResponse()->getBody();
                    Log::error($e->getMessage(), [
                        'class'    => class_basename(get_class($this)),
                        'api_path' => $apiPath.'?'.http_build_query($query),
                        'response' => json_decode($response),
                    ]);

                    // 中斷回圈
                    $flag = false;
                }

                // 下一頁
                $query['page']++;
            }

            // 更新需下架的文章
            $this->blogArticle->type($type)
                                ->whereIn('blog_id', $this->publishedIDs->keys())
                                ->delete();
        }
    }

    /**
     * 檢視部落格文章
     */
    private function checkBlogArticles($type, $post)
    {
        // 部落格文章已存在，且為上架中
        if ($this->publishedIDs->has($post->id)) {

            // 從現有上架陣列中移除，並取出最後更新時間
            $_modifiedAt = $this->publishedIDs->pull($post->id);

            // 若最後更新時間一樣，則不需要更新
            if (strtotime($post->modified) == strtotime($_modifiedAt)) {
                return;
            }
        }

        // 儲存部落格文章
        $this->getBlogPostInfoService->setBlogPost($type, $post);
        $this->getBlogPostInfoService->saveBlogArticle();
    }
}
