<?php
/*
 |--------------------------------------
 |  取得正確的網址，解決經常有人漏掉 http 的問題
 |--------------------------------------
 |
 |
 |
 */

namespace App\Services\Tools\GetWebData;

class GetParseUrlService
{
    private $result;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * 轉成正確的網址
     *
     * @param [type] $url
     * @return void
     */
    public function encode($url)
    {
        $parseUrl = parse_url($url);

        $this->result = null;

        //如果缺少 http or https
        if (!isset($parseUrl['scheme'])) {
            if ( preg_match('/(?:[a-zA-Z0-9.\-]+\.)+(?:[a-zA-Z0-9]{2,4})((?:[\w+=%&.~\-]*)*)/', $parseUrl['path']) ){
                $this->result = $this->result = 'http://' . $parseUrl['path'];
            }
        } elseif (isset($parseUrl['scheme'])) {
            $this->result = $parseUrl['scheme']. '://' . $parseUrl['host'] ;
            $this->result .= isset($parseUrl['path']) ? $parseUrl['path'] : '';
        }

        if ($this->result) {
            $this->result .= isset($parseUrl['query']) ? $parseUrl['query'] : '';
        }

        return $this->result;
    }

}
