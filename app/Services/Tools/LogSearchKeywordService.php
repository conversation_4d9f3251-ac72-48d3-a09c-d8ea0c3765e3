<?php
/*
 |--------------------------------------
 |  關鍵字搜尋紀錄 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools;

use App\Traits\ParseKeywordTrait;
use App\Models\LogSearchKeyword;

class LogSearchKeywordService
{
    private $keyword;
    private $logSearchKeyword;

    use ParseKeywordTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        LogSearchKeyword $logSearchKeyword
    ) {
        $this->logSearchKeyword = $logSearchKeyword;
    }

    /**
     * 新增記錄
     *
     * @param $keyword
     * @param $source
     * @return void
     */
    public function create($keyword, $source, $resultCount)
    {
        $user = request('user');

        // 新增
        $this->logSearchKeyword->user_id      = $user ? $user->id : NULL;
        $this->logSearchKeyword->source       = $source;
        $this->logSearchKeyword->keyword      = $this->simplifySpace($keyword);
        $this->logSearchKeyword->result_count = $resultCount;
        $this->logSearchKeyword->save();
    }

    /**
     * 取得記錄編號
     *
     * @return int $id
     */
    public function getId()
    {
        return $this->logSearchKeyword->id;
    }

    /**
     * 更新目標編號
     *
     * @param $log_id
     * @param $found_id
     * @return void
     */
    public function updateFoundById($log_id, $found_id)
    {
        // 找出記錄
        $this->logSearchKeyword = $this->logSearchKeyword->find($log_id);
        if (!$this->logSearchKeyword) {
            return;
        }

        // 更新所屬使用者
        if (!$this->logSearchKeyword->user_id) {
            $user = request('user');
            $this->logSearchKeyword->user_id = $user ? $user->id : NULL;
        }

        // 更新
        $this->logSearchKeyword->found_id = $this->logSearchKeyword->found_id ?: $found_id;
        $this->logSearchKeyword->found_at = $this->logSearchKeyword->found_at ?: now();
        $this->logSearchKeyword->save();
    }
}
