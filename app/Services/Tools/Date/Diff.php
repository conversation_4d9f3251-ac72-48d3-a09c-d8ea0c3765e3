<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 計算兩個日期間的差異
 |--------------------------------------
 |
 |
 */


namespace App\Services\Tools\Date;

class Diff extends AbstractDate
{
    /**
     * 計算差異天數
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return int
     */
    public function days($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->diffInDays($second);
    }

    /**
     * 計算差異時數
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return int
     */
    public function hours($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->diffInHours($second);
    }

}