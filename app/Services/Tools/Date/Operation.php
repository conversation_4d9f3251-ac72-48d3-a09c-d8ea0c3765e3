<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 時間運算
 |--------------------------------------
 | 計算兩個日期的大於小於
 | 時間格式以format屬性為主（AbstractDate）
 |
 */


namespace App\Services\Tools\Date;

class Operation extends AbstractDate
{
    /**
     * 大於等於
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return bool
     */
    public function gte($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->gte($second);
    }

    /**
     * 小於等於
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return bool
     */
    public function lte($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->lte($second);
    }

    /**
     * 等於
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return bool
     */
    public function eq($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->eq($second);
    }

    /**
     * 不等於
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return bool
     */
    public function ne($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->ne($second);
    }

    /**
     * 大於
     * @param $start : 起始時間
     * @param $end : 結束時間
     * @return bool
     */
    public function gt($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->gt($second);
    }

    /**
     * 小於
     * @param $start: 起始時間
     * @param $end: 結束時間
     * @return bool
     */
    public function lt($start, $end)
    {
        list($first, $second) = $this->carbonFormat($start, $end);
        return $first->lt($second);
    }
}