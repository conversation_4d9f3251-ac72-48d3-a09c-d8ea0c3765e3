<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 時間運算的 super class
 |--------------------------------------
 | 跟carbon相關的共用功能
 |
 */


namespace App\Services\Tools\Date;


use Carbon\Carbon;

class AbstractDate
{

    /**
     * @var string: 計算單位 date => Y-m-d;  datetime => Y-m-d H:i:s
     */
    public $format = 'date';

    /**
     * 格式化為carbon格式
     * @param $start : 開始時間
     * @param $end : 結束時間
     * @return array
     */
    protected function carbonFormat($start, $end): array
    {
        if($this->format == 'date'){
            $first = $this->formatToDate($start);
            $second = $this->formatToDate($end);
        } else {
            $first = $this->formatToDatetime($start);
            $second = $this->formatToDatetime($end);
        }

        $first = Carbon::createFromTimestamp(strtotime($first));
        $second = Carbon::createFromTimestamp(strtotime($second));

        return array($first, $second);
    }

    /**
     * 格式化為日期時間格式
     * @param $datetime
     * @return string
     */
    protected function formatToDatetime($datetime): string
    {
        return Carbon::createFromTimestamp(strtotime($datetime))->toDateTimeString();
    }

    /**
     * 格式化為日期格式
     * @param $datetime
     * @return string
     */
    protected function formatToDate($datetime): string
    {
        return Carbon::createFromTimestamp(strtotime($datetime))->toDateString();
    }

}