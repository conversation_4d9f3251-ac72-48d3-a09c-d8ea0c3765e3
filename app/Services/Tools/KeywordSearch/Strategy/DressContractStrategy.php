<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 商家禮服管理
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class DressContractStrategy extends AbsKeywordSearchStrategy
{
    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
            $q->where('id', $this->keyword)
                ->orWhere('contract_no', 'like', '%' . $this->keyword . '%')
                ->orWhere('bride_name', 'like', '%' . $this->keyword . '%')
                ->orWhere('bride_phone', 'like', '%' . $this->keyword . '%')
                ->orWhere('bridegroom_name', 'like', '%' . $this->keyword . '%')
                ->orWhere('bridegroom_phone', 'like', '%' . $this->keyword . '%');
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->where('id', $val)
                        ->orWhere('contract_no', 'like', '%' . $val . '%')
                        ->orWhere('bride_name', 'like', '%' . $val . '%')
                        ->orWhere('bride_phone', 'like', '%' . $val . '%')
                        ->orWhere('bridegroom_name', 'like', '%' . $val . '%')
                        ->orWhere('bridegroom_phone', 'like', '%' . $val . '%');
            }
        });
    }
}
