<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 廣告素材
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class AdCampaignStrategy extends AbsKeywordSearchStrategy
{
    /**
     * 精準搜尋
     */
    public function precise()
    {
        $type = $this->args[0];

        return $this->model->where(function ($q1) use ($type) {
            $relation = $this->getAdImageRelation($type);
            $q1->orWhere('name', 'like', '%' . $this->keyword . '%')
                ->orWhereHas($relation, function ($q2) {
                    $q2->where('name', 'like', '%' . $this->keyword . '%');
                });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $type = $this->args[0];
        $keywords = $this->splitToArray($this->keyword);

        return $this->model->where(function ($q1) use ($keywords, $type) {
            $relation = $this->getAdImageRelation($type);
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('name', 'like', '%' . $val . '%')
                    ->orWhereHas($relation, function ($q2) use ($val) {
                        $q2->where('name', 'like', '%' . $val . '%');
                    });
            }
        });
    }

    /**
     * 取得廣告素材的 Relation
     */
    private function getAdImageRelation($type)
    {
        switch ($type) {
            case 'banner':
                return 'bannerAdImages';

            case 'mobile_banner':
                return 'mobileBannerAdImage';

            case 'float':
                return 'floatAdImage';
        }
    }
}
