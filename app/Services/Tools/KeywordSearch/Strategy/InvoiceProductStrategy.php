<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 發票品項
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;

class InvoiceProductStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where('name', 'like', '%' . $this->keyword . '%');
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('name', 'like', '%' . $val . '%');
            }
        });
    }
}
