<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | Wordpress Blog 文章
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class BlogArticleStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q1) {
            $q1->orWhere('id', $this->keyword)
                ->orWhere('blog_id', $this->keyword)
                ->orWhere('title', 'like', '%' . $this->keyword . '%');
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('id', $val)
                    ->orWhere('blog_id', $val)
                    ->orWhere('title', 'like', '%' . $val . '%');
            }
        });
    }
}
