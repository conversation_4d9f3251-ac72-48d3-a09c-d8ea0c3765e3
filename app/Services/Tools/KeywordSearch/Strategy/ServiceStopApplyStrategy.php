<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 下架申請
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class ServiceStopApplyStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where('id', $this->keyword)
            ->orWhereHas('store', function ($q) {
                $q->where('id', $this->keyword)
                    ->orWhere('name', 'like', '%' . $this->keyword . '%')
                    ->orWhere('email', 'like', '%' . $this->keyword . '%');
            });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);

        return $this->model->where(function ($query) use ($keywords) {
            foreach ($keywords as $val) {
                $query = $query->orWhere('id', $val)
                    ->orWhereHas('store', function ($q) use ($val) {
                        $q->where('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%')
                            ->orWhere('email', 'like', '%' . $val . '%');
                    });

            }
        });
    }
}
