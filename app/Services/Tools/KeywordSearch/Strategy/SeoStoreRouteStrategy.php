<?php
/*
 |--------------------------------------
 | SEO商家路由客製
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;

class SeoStoreRouteStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($query) {
            $query->where('store_id', $this->keyword)
                    ->orWhereHas('store', function ($q) {
                        $q->where('name', 'like', '%' . $this->keyword . '%');
                    });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($query) use ($keywords) {
            foreach ($keywords as $val) {
                $query = $query->orWhere('store_id', $val)
                                ->orWhereHas('store', function ($q) use ($val) {
                                    $q->where('name', 'like', '%' . $val . '%');
                                });
            }
        });
    }
}
