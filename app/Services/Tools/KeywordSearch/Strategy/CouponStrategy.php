<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 優惠券
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class CouponStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q1) {
            $q1->orWhere('title', 'like', '%' . $this->keyword . '%')
                ->orWhere('code', $this->keyword)
                ->orWhere('note', 'like', '%' . $this->keyword . '%')
                // 多組獨立代碼
                ->orWhere(function($q2) {
                    $q2->whereRaw('BINARY `code` = ?', substr($this->keyword, 0, 3))
                        ->whereHas('multiple', function ($q3) {
                            $q3->whereRaw('BINARY `code` = ?', substr($this->keyword, 3));
                        });
                });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('title', 'like', '%' . $val . '%')
                            ->orWhere('code', $val)
                            ->orWhere('note', 'like', '%' . $val . '%')
                            // 多組獨立代碼
                            ->orWhere(function($q2) use ($val) {
                                $q2->whereRaw('BINARY `code` = ?', substr($val, 0, 3))
                                    ->whereHas('multiple', function ($q3) use ($val) {
                                        $q3->whereRaw('BINARY `code` = ?', substr($val, 3));
                                    });
                            });
            }
        });
    }
}
