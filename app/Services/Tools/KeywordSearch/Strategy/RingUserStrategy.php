<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 婚戒大賞-報名資訊
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class RingUserStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
            $q->orWhere('id', $this->keyword)
                ->orWhere('name', 'like', '%' . $this->keyword . '%')
                ->orWhere('mate_name', 'like', '%' . $this->keyword . '%')
                ->orWhere('email', 'like', '%' . $this->keyword . '%')
                ->orWhere('phone', 'like', '%' . $this->keyword . '%')
                ->orWhereHas('user', function ($qu) {
                    $qu->where('id', $this->keyword)
                        ->orWhere('name', 'like', '%' . $this->keyword . '%')
                        ->orWhere('real_name', 'like', '%' . $this->keyword . '%')
                        ->orWhere('anonymous_key', 'like', '%' . $this->keyword . '%')
                        ->orWhere('email', 'like', '%' . $this->keyword . '%')
                        ->orWhere('phone', 'like', '%' . $this->keyword . '%');
                });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('id', $val)
                        ->orWhere('name', 'like', '%' . $val . '%')
                        ->orWhere('mate_name', 'like', '%' . $val . '%')
                        ->orWhere('email', 'like', '%' . $val . '%')
                        ->orWhere('phone', 'like', '%' . $val . '%')
                        ->orWhereHas('user', function ($qu) use ($val) {
                            $qu->where('id', $val)
                                ->orWhere('name', 'like', '%' . $val . '%')
                                ->orWhere('real_name', 'like', '%' . $val . '%')
                                ->orWhere('anonymous_key', 'like', '%' . $val . '%')
                                ->orWhere('email', 'like', '%' . $val . '%')
                                ->orWhere('phone', 'like', '%' . $val . '%');
                        });
            }
        });
    }
}
