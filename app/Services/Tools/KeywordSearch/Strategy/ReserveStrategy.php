<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 詢問單 Reserve
 |--------------------------------------
 |
 |
 */
namespace App\Services\Tools\KeywordSearch\Strategy;

class ReserveStrategy extends AbsKeywordSearchStrategy
{
    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
            $jsonString = $this->jsonString($this->keyword); //at trait 轉換成可以like搜尋的json字串..

            $q->orWhere('id', $this->keyword)
                ->orWhere('contact', 'like', '%' . $jsonString . '%')
                ->orWhereHas('user', function ($qu) {
                    $qu->where('id', $this->keyword)
                        ->orWhere('name', 'like', '%' . $this->keyword . '%')
                        ->orWhere('anonymous_key', 'like', '%' . $this->keyword . '%')
                        ->orWhere('email', 'like', '%' . $this->keyword . '%')
                        ->orWhere('phone', 'like', '%' . $this->keyword . '%');
                })
                ->orWhereHas('rawStore', function ($qs) {
                    $qs->where('id', $this->keyword)
                        ->orWhere('name', 'like', '%' . $this->keyword . '%');
                });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);

        return $this->model->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $jsonString = $this->jsonString($val); //at trait 轉換成可以like搜尋的json字串..

                $q = $q->orWhere('id', $val)
                    ->orWhere('contact', 'like', '%' . $jsonString . '%')
                    ->orWhereHas('user', function ($qu) use ($val) {
                        $qu->where('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%')
                            ->orWhere('anonymous_key', 'like', '%' . $val . '%')
                            ->orWhere('email', 'like', '%' . $val . '%')
                            ->orWhere('phone', 'like', '%' . $val . '%');
                    })
                    ->orWhereHas('rawStore', function ($qs) use ($val) {
                        $qs->where('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%');
                    });

            }
        });
    }
}
