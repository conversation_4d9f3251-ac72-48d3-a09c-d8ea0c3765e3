<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 商家訂單
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;

use App\Models\Store;
use App\Models\Wdv2\PaymentLog;
use App\Models\Wdv2\PaymentSpgatewayLog;

class OrderStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
                                $q->orWhere('no_id', $this->keyword)
                                    ->orWhere('order.id', $this->keyword)
                                    ->orWhere('order.store_id', $this->keyword)
                                    ->orWhere('invoice', $this->keyword)
                                    ->orWhere('note', 'like', '%'.$this->keyword.'%')
                                    ->orWhereIn('order.store_id', function ($qs) {
                                        $qs->select('id')
                                            ->from((new Store)->getTable())
                                            ->where('name', 'like', '%'.$this->keyword.'%');
                                    })
                                    ->orWhereIn('order.id', function ($qp) {
                                        $qp->select('order_id')
                                            ->from((new PaymentLog)->getTable())
                                            ->where('bank_transaction_id', $this->keyword);
                                    })
                                    ->orWhereIn('no_id', function ($qps) {
                                        $qps->select('no_id')
                                            ->from((new PaymentSpgatewayLog)->getTable())
                                            ->where('trade_no', $this->keyword);
                                    });
                            });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q->orWhere('no_id', $val)
                    ->orWhere('order.id', $val)
                    ->orWhere('order.store_id', $val)
                    ->orWhere('invoice', $val)
                    ->orWhere('note', 'like', '%'.$val.'%')
                    ->orWhereIn('order.store_id', function ($qs) use ($val) {
                        $qs->select('id')
                            ->from((new Store)->getTable())
                            ->where('name', 'like', '%'.$val.'%');
                    })
                    ->orWhereIn('order.id', function ($qp) use ($val) {
                        $qp->select('order_id')
                            ->from((new PaymentLog)->getTable())
                            ->where('bank_transaction_id', $val);
                    })
                    ->orWhereIn('no_id', function ($qps) use ($val) {
                        $qps->select('no_id')
                            ->from((new PaymentSpgatewayLog)->getTable())
                            ->where('trade_no', $val);
                    });
            }
        });
    }
}
