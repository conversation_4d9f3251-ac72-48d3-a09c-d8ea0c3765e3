<?php
/*
 |--------------------------------------
 | LINE 群組
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;

class LineGroupStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($query) {
            $query->where('id', $this->keyword)
                    ->orWhere('name', 'like', '%' . $this->keyword . '%')
                    ->orWhereHas('store', function ($q) {
                        $q->where('id', $this->keyword)
                            ->orWhere('name', 'like', '%' . $this->keyword . '%');
                    });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($query) use ($keywords) {
            foreach ($keywords as $val) {
                $query = $query->orWhere('id', $val)
                                ->orWhere('name', 'like', '%' . $val . '%')
                                ->orWhereHas('store', function ($q) use ($val) {
                                    $q->where('id', $val)
                                        ->orWhere('name', 'like', '%' . $val . '%');
                                });

            }
        });
    }
}
