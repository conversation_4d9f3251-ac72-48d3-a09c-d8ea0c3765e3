<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 無效詢問申請 ReserveAppeal
 |--------------------------------------
 |
 |
 */
namespace App\Services\Tools\KeywordSearch\Strategy;

class ReserveAppealStrategy extends AbsKeywordSearchStrategy
{
    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
            $q = $q->orWhere('id', $this->keyword)
                    ->orWhereHas('reserve', function ($qr) {
                        $qr->whereHas('user', function ($qu) {
                            $qu->where('id', $this->keyword)
                                ->orWhere('name', 'like', '%'.$this->keyword.'%');
                        });
                    })
                    ->orWhereHas('store', function ($qs) {
                        $qs->where('id', $this->keyword)
                            ->orWhere('name', 'like', '%'.$this->keyword.'%');
                    });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q) use ($keywords) {
            foreach ($keywords as $val) {
                $q = $q->orWhere('id', $val)
                        ->orWhereHas('reserve', function ($qr) use ($val) {
                            $qr->whereHas('user', function ($qu) use ($val) {
                                $qu->where('id', $val)
                                    ->orWhere('name', 'like', '%'.$val.'%');
                            });
                        })
                        ->orWhereHas('store', function ($qs) use ($val) {
                            $qs->where('id', $val)
                                ->orWhere('name', 'like', '%'.$val.'%');
                        });
            }
        });
    }
}
