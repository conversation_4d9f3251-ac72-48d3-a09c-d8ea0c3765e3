<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 商家
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class StoreStrategy extends AbsKeywordSearchStrategy
{
    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
                $q->where('id', $this->keyword)
                    ->orWhere('name', 'like', '%' . $this->keyword . '%')
                    ->orWhere('email', 'like', '%' . $this->keyword . '%')
                    ->orWhere('address', 'like', '%' . $this->keyword . '%')
                    ->orWhere('tel', 'like', '%' . $this->keyword . '%')
                    ->orWhere('phone', 'like', '%' . $this->keyword . '%')
                    ->orWhere('contact_email', 'like', '%' . $this->keyword . '%')
                    ->orWhere('line', 'like', '%' . $this->keyword . '%')
                    ->orWhere('website', 'like', '%' . $this->keyword . '%')
                    ->orWhere('fb_page', 'like', '%' . $this->keyword . '%')
                    ->orWhere('instagram', 'like', '%' . $this->keyword . '%')
                    ->orWhereHas('accounts', function ($q) {
                        $q->where('email', 'like', '%' . $this->keyword . '%');
                    })
                    ->orWhereHas('orderSet', function ($q) {
                        $q->where('store_detail', 'like', '%' . json_encode($this->keyword) . '%');
                    })
                    ->orWhereHas('brands', function ($q) {
                        $q->where('name', 'like', '%' . $this->keyword . '%')
                            ->orWhere('email', 'like', '%' . $this->keyword . '%')
                            ->orWhere('phone', 'like', '%' . $this->keyword . '%')
                            ->orWhere('address', 'like', '%' . $this->keyword . '%')
                            ->orWhere('line', 'like', '%' . $this->keyword . '%')
                            ->orWhere('website', 'like', '%' . $this->keyword . '%')
                            ->orWhere('fb_page', 'like', '%' . $this->keyword . '%')
                            ->orWhere('instagram', 'like', '%' . $this->keyword . '%')
                            ->orWhere('description', 'like', '%' . json_encode($this->keyword) . '%');
                    });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->where('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%')
                            ->orWhere('email', 'like', '%' . $val . '%')
                            ->orWhere('address', 'like', '%' . $val . '%')
                            ->orWhere('tel', 'like', '%' . $val . '%')
                            ->orWhere('phone', 'like', '%' . $val . '%')
                            ->orWhere('contact_email', 'like', '%' . $val . '%')
                            ->orWhere('line', 'like', '%' . $val . '%')
                            ->orWhere('website', 'like', '%' . $val . '%')
                            ->orWhere('fb_page', 'like', '%' . $val . '%')
                            ->orWhere('instagram', 'like', '%' . $val . '%')
                            ->orWhereHas('accounts', function ($q2) use ($val) {
                                $q2->where('email', 'like', '%' . $val . '%');
                            })
                            ->orWhereHas('orderSet', function ($q2) use ($val) {
                                $q2->where('store_detail', 'like', '%' . json_encode($val) . '%');
                            })
                            ->orWhereHas('brands', function ($q2) use ($val) {
                                $q2->where('name', 'like', '%' . $val . '%')
                                    ->orWhere('email', 'like', '%' . $val . '%')
                                    ->orWhere('phone', 'like', '%' . $val . '%')
                                    ->orWhere('address', 'like', '%' . $val . '%')
                                    ->orWhere('line', 'like', '%' . $val . '%')
                                    ->orWhere('website', 'like', '%' . $val . '%')
                                    ->orWhere('fb_page', 'like', '%' . $val . '%')
                                    ->orWhere('instagram', 'like', '%' . $val . '%')
                                    ->orWhere('description', 'like', '%' . json_encode($val) . '%');
                            });
            }
        });
    }
}
