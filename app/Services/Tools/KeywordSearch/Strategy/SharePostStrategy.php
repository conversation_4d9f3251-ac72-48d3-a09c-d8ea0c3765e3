<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | W姐妹 分享文
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class SharePostStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q1) {
            $q1 = $q1->orWhere('share_posts.id', $this->keyword)
                ->orWhere('title', 'like', '%' . $this->keyword . '%')
                ->orWhere('content', 'like', '%' . $this->keyword . '%')
                ->orWhere('ip', 'like', '%' . $this->keyword . '%')
                ->orWhereHas('author', function ($q2) {
                    $q2->where('id', $this->keyword)
                        ->orWhere('name', 'like', '%' . $this->keyword . '%')
                        ->orWhere('anonymous_key', 'like', '%' . $this->keyword . '%')
                        ->orWhere('email', 'like', '%' . $this->keyword . '%')
                        ->orWhere('phone', 'like', '%' . $this->keyword . '%');
                })
                ->orWhereHas('tags', function ($q2) {
                    $q2->where('name', 'like', '%' . $this->keyword . '%');
                });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('share_posts.id', $val)
                    ->orWhere('title', 'like', '%' . $val . '%')
                    ->orWhere('content', 'like', '%' . $val . '%')
                    ->orWhere('ip', 'like', '%' . $val . '%')
                    ->orWhereHas('author', function ($q2) use ($val) {
                        $q2->where('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%')
                            ->orWhere('anonymous_key', 'like', '%' . $val . '%')
                            ->orWhere('email', 'like', '%' . $val . '%')
                            ->orWhere('phone', 'like', '%' . $val . '%');
                    })
                    ->orWhereHas('tags', function ($q2) use ($val) {
                        $q2->where('name', 'like', '%' . $val . '%');
                    });
            }
        });
    }
}
