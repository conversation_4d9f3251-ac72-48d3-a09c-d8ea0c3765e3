<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 活動訂單
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class EventOrderStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
            $q->orWhere('event_id', $this->keyword)
                ->orWhere('event_report_id', $this->keyword)
                ->orWhere('order_no', $this->keyword)

                ->orWhereHas('allEvent', function ($qe) {
                    $qe->where('path', 'like', '%' . $this->keyword . '%')
                        ->orWhere('title', 'like', '%' . $this->keyword . '%');
                })
                ->orWhereHas('allReport', function ($qr) {
                    $qr->where('name', 'like', '%' . $this->keyword . '%')
                        ->orWhere('email', 'like', '%' . $this->keyword . '%')
                        ->orWhere('phone', 'like', '%' . $this->keyword . '%');
                })
                ->orWhereHas('logPayment', function ($qp) {
                    $qp->where('bank_transaction_id', 'like', '%' . $this->keyword . '%');
                })
                ->orWhereHas('invoices', function ($qi) {
                    $qi->where('buyer_ubn', 'like', '%' . $this->keyword . '%')
                        ->orWhere('buyer_name', 'like', '%' . $this->keyword . '%')
                        ->orWhere('buyer_email', 'like', '%' . $this->keyword . '%')
                        ->orWhere('carrier_number', 'like', '%' . $this->keyword . '%')
                        ->orWhere('note', 'like', '%' . $this->keyword . '%')
                        ->orWhere('invoice_number', 'like', '%' . $this->keyword . '%');
                });
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('event_id', $val)
                            ->orWhere('event_report_id', $val)
                            ->orWhere('order_no', $val)

                            ->orWhereHas('allEvent', function ($qe) use ($val) {
                                $qe->where('path', 'like', '%' . $val . '%')
                                    ->orWhere('title', 'like', '%' . $val . '%');
                            })
                            ->orWhereHas('allReport', function ($qr) use ($val) {
                                $qr->where('name', 'like', '%' . $val . '%')
                                    ->orWhere('email', 'like', '%' . $val . '%')
                                    ->orWhere('phone', 'like', '%' . $val . '%');
                            })
                            ->orWhereHas('logPayment', function ($qp) use ($val) {
                                $qp->where('bank_transaction_id', 'like', '%' . $val . '%');
                            })
                            ->orWhereHas('invoices', function ($qi) use ($val) {
                                $qi->where('buyer_ubn', 'like', '%' . $val . '%')
                                    ->orWhere('buyer_name', 'like', '%' . $val . '%')
                                    ->orWhere('buyer_email', 'like', '%' . $val . '%')
                                    ->orWhere('carrier_number', 'like', '%' . $val . '%')
                                    ->orWhere('note', 'like', '%' . $val . '%')
                                    ->orWhere('invoice_number', 'like', '%' . $val . '%');
                            });
            }
        });
    }
}
