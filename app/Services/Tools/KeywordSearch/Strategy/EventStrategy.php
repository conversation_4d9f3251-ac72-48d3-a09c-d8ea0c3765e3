<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 活動
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class EventStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q1) {
            $q1->orWhere('id', $this->keyword)
                ->orWhere('path', 'like', '%' . $this->keyword . '%')
                ->orWhere('title', 'like', '%' . $this->keyword . '%');
                // ->orWhere('description', 'like', '%' . $this->keyword . '%')
                // ->orWhere('columns', 'like', '%' . $this->keyword . '%')
                // ->orWhere('pending_info', 'like', '%' . $this->keyword . '%')
                // ->orWhere('result_new_info', 'like', '%' . $this->keyword . '%')
                // ->orWhere('result_old_info', 'like', '%' . $this->keyword . '%')
                // ->orWhere('completed_info', 'like', '%' . $this->keyword . '%')
                // ->orWhere('calendar_summary', 'like', '%' . $this->keyword . '%')
                // ->orWhere('calendar_location', 'like', '%' . $this->keyword . '%')
                // ->orWhere('calendar_description', 'like', '%' . $this->keyword . '%');
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('id', $this->keyword)
                        ->orWhere('path', 'like', '%' . $val . '%')
                        ->orWhere('title', 'like', '%' . $val . '%');
                        // ->orWhere('description', 'like', '%' . $val . '%')
                        // ->orWhere('columns', 'like', '%' . $val . '%')
                        // ->orWhere('pending_info', 'like', '%' . $val . '%')
                        // ->orWhere('result_new_info', 'like', '%' . $val . '%')
                        // ->orWhere('result_old_info', 'like', '%' . $val . '%')
                        // ->orWhere('completed_info', 'like', '%' . $val . '%')
                        // ->orWhere('calendar_summary', 'like', '%' . $val . '%')
                        // ->orWhere('calendar_location', 'like', '%' . $val . '%')
                        // ->orWhere('calendar_description', 'like', '%' . $val . '%');
            }
        });
    }
}
