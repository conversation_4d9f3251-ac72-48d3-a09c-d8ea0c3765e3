<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 全站推播
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class PushMessageStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q1) {
            $q1->orWhere('title', 'like', '%' . $this->keyword . '%')
                ->orWhere('content', 'like', '%' . $this->keyword . '%');
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('title', 'like', '%' . $val . '%')
                        ->orWhere('content', 'like', '%' . $val . '%');
            }
        });
    }
}
