<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 商家user
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class StoreUserStrategy extends AbsKeywordSearchStrategy
{

    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
                $q->orWhere('id', $this->keyword)
                    ->orWhere('email', 'like', '%' . $this->keyword . '%')
                    ->orWhere('phone', 'like', '%' . $this->keyword . '%');
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->orWhere('id', $val)
                    ->orWhere('email', 'like', '%' . $val . '%')
                    ->orWhere('phone', 'like', '%' . $val . '%');
            }
        });
    }
}