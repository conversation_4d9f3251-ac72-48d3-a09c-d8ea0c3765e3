<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 商家影片
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools\KeywordSearch\Strategy;


class StoreVideoStrategy extends AbsKeywordSearchStrategy
{
    /**
     * 精準搜尋
     */
    public function precise()
    {
        return $this->model->where(function ($q) {
                $q->where('id', $this->keyword)
                    ->orWhere('name', 'like', '%' . $this->keyword . '%');
        });
    }

    /**
     * 模糊搜尋
     */
    public function fuzzy()
    {
        $keywords = $this->splitToArray($this->keyword);
        return $this->model->where(function ($q1) use ($keywords) {
            foreach ($keywords as $val) {
                $q1 = $q1->where('id', $val)
                            ->orWhere('name', 'like', '%' . $val . '%');
            }
        });
    }
}
