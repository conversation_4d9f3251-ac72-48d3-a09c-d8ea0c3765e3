<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 關鏈字搜尋的抽象類別..
 |--------------------------------------
 |
 |
 */
namespace App\Services\Tools\KeywordSearch\Strategy;
use App\Traits\ParseKeywordTrait;
use App\Traits\JsonStringTrait;

abstract class AbsKeywordSearchStrategy
{
    use ParseKeywordTrait;
    use JsonStringTrait;

    /** @var : query builder model */
    protected $model;

    /** @var : 搜尋關鏈字 */
    protected $keyword;

    /** @var : 其它參數 */
    protected $args;

    /**
     * 搜尋邏輯
     * @param $model : eloquent model
     * @param string $keyword : 搜尋關鏈字
     * @param array $args: 其它參數
     * @return mixed
     */
    public function search($model, string $keyword, $args)
    {
        $this->model = $model;
        $this->keyword = $keyword;
        $this->args = $args;

        $this->model = $this->precise();
        $this->model = $this->fuzzy();

        // ID搜尋優先
        if (is_numeric($keyword)) {
            $this->model = $this->model->orderByRaw('FIELD(id , '.$keyword.') DESC');
        }

        return $this->model;
    }

    /**
     * 精準搜尋
     * 把精準搜尋的邏輯寫在這..
     * @return mixed: query builder model
     */
    abstract public function precise();

    /**
     * 模糊搜尋
     * 把模糊搜尋的邏輯寫在這..
     * @return mixed: query builder model
     */
    abstract public function fuzzy();
}
