<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | keyword 關鏈字搜尋
 |--------------------------------------
 | 擴充方法:
 | 1.在 ../Strategy下建一個名為 {ModelName} . 'Strategy' 的 class 並繼承 AbsKeywordSearchStrategy
 | 2.實作 precise & fuzzy method
 */

namespace App\Services\Tools\KeywordSearch;

use App\Services\Tools\KeywordSearch\Strategy\AbsKeywordSearchStrategy;
use ReflectionClass;

class SearchService
{
    /**
     * 關鏈字搜尋
     * @param $model : eloquent model
     * @param string $keyword : 搜尋字串
     * @param mixed ...$args: 其它參數
     * @return mixed
     * @throws \ReflectionException
     */
    public function search($model, string $keyword, ...$args)
    {
        // 透過model name取得所屬的搜尋策略class..
        $reflect = new ReflectionClass($model->getModel());
        $strategyName = $reflect->getShortName() . 'Strategy';
        $classPath = __NAMESPACE__ . '\\Strategy\\' . $strategyName;

        if (!class_exists($classPath)) {
            return $model;
        }

        $strategy = resolve($classPath);

        //沒有實作abstract class就不理他....不然不能保證有precis() 跟 fuzzy() method
        if (!$strategy instanceof AbsKeywordSearchStrategy) {
            return $model;
        }

        $model = $strategy->search($model, $keyword, $args);
        return $model;
    }
}
