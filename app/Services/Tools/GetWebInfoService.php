<?php
/*
 |--------------------------------------
 |  取得部落格廣告詳細資訊 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools;

use DOMDocument;
use StdClass;

use App\Traits\ApiErrorTrait;

class GetWebInfoService
{
    private $document;
    private $result;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(

    ) {
        $this->document = new DOMDocument('1.0', 'UTF-8');
        $this->result   = new stdClass();
        $this->result->title = null;
        $this->result->description = null;
    }

    /**
     * 取得 Blog AD 廣告設定
     */
    public function get($url)
    {
        //Curl 取得網頁
        // $ch = curl_init();
        // curl_setopt($ch, CURLOPT_URL, $url);
        // curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36');
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        // $html = curl_exec($ch);
        // curl_close($ch);

        try {
            // https://stackoverflow.com/questions/26148701/file-get-contents-ssl-operation-failed-with-code-1-failed-to-enable-crypto
            $contextOptions = stream_context_create([
                'ssl' => [
                    'verify_peer'      => false,
                    'verify_peer_name' => false,
                ],
            ]);
            $html = file_get_contents($url, false, $contextOptions);
        } catch (\Throwable $th) {
            $this->setException('無法取得網頁資料！');
        }

        if (!$html) {
            return $this->result;
        }

        $internalErrors = libxml_use_internal_errors(true);
        $this->document->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        libxml_use_internal_errors($internalErrors);

        //取得標題
        $title = $this->document->getElementsByTagName("title");
        if ($title->length > 0) {
            $this->result->title = $title->item(0)->textContent;
        }

        //get description
        $metas = $this->document->getElementsByTagName('meta');

        foreach ($metas as $meta) {
            //取得描述
            if (strtolower($meta->getAttribute('name')) == 'description') {
                $this->result->description = $meta->getAttribute('content');
            }
            //取得圖片
            if (strtolower($meta->getAttribute('property')) == 'og:image') {
                $this->result->ogImage = $meta->getAttribute('content');
            }
        }

        return $this->result;
    }

}
