<?php
/*
 |--------------------------------------
 | sitemap 邏輯的service
 |--------------------------------------
 |
 |
 |
 |
 */

namespace App\Services\Tools\Sitemap;

use App\Services\Tools\Sitemap\SitemapInterface;
use App\Services\Tools\Sitemap\SitemapFactory;

class SitemapLogicService implements SitemapInterface
{
    /** @var : 邏輯實例 */
    private $logic;

    /**
     * SitemapLogicService constructor.
     * @param $methodName : class name
     * @throws \ReflectionException
     */
    public function __construct($methodName)
    {
        $this->logic = SitemapFactory::factory($methodName, SitemapInterface::class);
    }

    /**
     * 是否寫入檔案
     *
     * @param [type] ...$args
     * @return void
     */
    public function setPutFile(bool $flag)
    {
        return $this->logic->setPutFile($flag);
    }

    /**
     * 建立 sitemap
     * @param array $args
     * @return
     */
    public function create()
    {
        return $this->logic->create();
    }

}
