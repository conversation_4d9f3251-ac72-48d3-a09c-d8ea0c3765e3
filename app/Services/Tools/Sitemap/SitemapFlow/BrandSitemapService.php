<?php
/*
 |--------------------------------------
 |  品牌主頁的 的 SEO網站地圖 SitemapService
 |--------------------------------------
 |
 |  記得在robots.txt 登記
 |
 */

namespace App\Services\Tools\Sitemap\SitemapFlow;

use Illuminate\Support\Facades\Storage;
use App\Services\Tools\Sitemap\SitemapInterface;
use App\Models\Brand;

class BrandSitemapService implements SitemapInterface
{
    //====== sitemap 相關 ======
    // 網頁可能變更的頻率 always hourly daily weekly monthly yearly never
    private $changefreq = 'daily';
    // 優先順序 0.0 ~ 1.0
    private $priority   = '0.9';
    // 目標網址
    private $url;

    //====== sitemap Index 相關 ======
    // 索引sitemap的儲存位置
    private $indexUrl;
    // 檔案的最後修改日期
    private $indexLastmod;
    // 檔案名稱
    private $indexFileName  = 'brand';
    private $indexExtension = '.xml';
    // 初始化
    private $isPutFile    = true;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct()
    {
        $this->brand        = new Brand;
        $this->url          = config('params.wdv3.www_url');
        $this->indexUrl     = env('APP_URL').'/sitemap/';
        $this->indexLastmod = now()->tz('UTC')->toAtomString();
    }

    /**
     * 是否寫入檔案
     *
     * @param boolean $flag
     * @return void
     */
    public function setPutFile(bool $flag)
    {
        $this->isPutFile = $flag;
    }


    /**
     * 產生網站地圖 Sitemap
     *
     * @return void
     */
    public function create()
    {
        // 初始化
        $sitemap = [];

        // 取得所有品牌
        $brands = $this->brand->all();
        foreach ($brands as $brand) {

            // 品牌主頁
            $sitemap[] = [
                'loc'        => $this->url.'/brand/'.$brand->id,
                'lastmod'    => $brand->updated_at->tz('UTC')->toAtomString(),
                'changefreq' => $this->changefreq,
                'priority'   => $this->priority
            ];
        }

        // 儲存 sitemap
        $fileName = $this->indexFileName.$this->indexExtension;
        $this->putFile('sitemap.content', $fileName, $sitemap);
    }

    /**
     * 寫入檔案
     *
     * @param [type] $viewPath
     * @param [type] $fileName
     * @param [type] $sitemap
     * @return void
     */
    private function putFile($viewPath, $fileName, $sitemap)
    {
        if ($this->isPutFile !== true) {
            return;
        }

        Storage::disk('local')->put(
            'sitemap/'.$fileName,
            view($viewPath, ['sitemap' => $sitemap])
        );
    }
}
