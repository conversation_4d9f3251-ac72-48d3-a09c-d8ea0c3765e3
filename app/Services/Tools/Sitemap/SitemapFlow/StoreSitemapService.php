<?php
/*
 |--------------------------------------
 |  商家頁面 的 SEO網站地圖 SitemapService
 |--------------------------------------
 |
 |  記得在robots.txt 登記
 |
 */

namespace App\Services\Tools\Sitemap\SitemapFlow;

use Illuminate\Support\Facades\Storage;
use App\Services\Tools\Sitemap\SitemapInterface;
use App\Models\Store;

class StoreSitemapService implements SitemapInterface
{
    //====== sitemap 相關 ======
    // 網頁可能變更的頻率 always hourly daily weekly monthly yearly never
    private $changefreq = 'hourly';
    // 優先順序 0.0 ~ 1.0
    private $priority   = '1.0';
    // 目標網址
    private $url;

    //====== sitemap Index 相關 ======
    // 索引sitemap的儲存位置
    private $indexUrl;
    // 檔案的最後修改日期
    private $indexLastmod;
    // 檔案名稱
    private $indexFileName  = 'store';
    private $indexExtension = '.xml';
    // 初始化
    private $sitemapIndex = [];
    private $isPutFile    = true;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct()
    {
        $this->store        = new Store;
        $this->url          = config('params.wdv3.www_url');
        $this->indexUrl     = env('APP_URL').'/sitemap/';
        $this->indexLastmod = now()->tz('UTC')->toAtomString();
    }

    /**
     * 是否寫入檔案
     *
     * @param boolean $flag
     * @return void
     */
    public function setPutFile(bool $flag)
    {
        $this->isPutFile = $flag;
    }


    /**
     * 產生網站地圖 Sitemap
     *
     * @return void
     */
    public function create()
    {
        // 商家類型-索引值
        foreach ($this->store->typeKeyList as $type => $key) {

            // 商家內容初始化
            $data = [
                'main'    => [],
                'album'   => [],
                'video'   => [],
                'room'    => [],
                'service' => [],
                'member'  => [],
            ];

            // 取得商家
            $stores = $this->store->published($type)->get();
            foreach ($stores as $store) {

                // 商家頁面的url
                $url = $this->url.'/store-'.$key.'/'.$store->id;
                $lastmod = $store->edited_at ?: $store->updated_at;

                // 商家主頁
                $data['main'][] = [
                    'loc'        => $url,
                    'lastmod'    => $lastmod->tz('UTC')->toAtomString(),
                    'changefreq' => $this->changefreq,
                    'priority'   => $this->priority
                ];

                // 相本內容頁
                foreach ($store->showAlbums as $album) {
                    $lastmod = $album->edited_at ?: $album->updated_at;
                    $data['album'][] = [
                        'loc'        => $url.'/album/'.$album->id,
                        'lastmod'    => $lastmod->tz('UTC')->toAtomString(),
                        'changefreq' => $this->changefreq,
                        'priority'   => $this->priority
                    ];
                }

                // 影片內容頁
                foreach ($store->showVideos as $video) {
                    $lastmod = $video->edited_at ?: $video->updated_at;
                    $data['video'][] = [
                        'loc'        => $url.'/video/'.$video->id,
                        'lastmod'    => $lastmod->tz('UTC')->toAtomString(),
                        'changefreq' => $this->changefreq,
                        'priority'   => $this->priority
                    ];
                }

                // 婚宴場地廳房內容頁
                foreach ($store->showVenueRooms as $room) {
                    $lastmod = $room->edited_at ?: $room->updated_at;
                    $data['room'][] = [
                        'loc'        => $url.'/room/'.$room->id,
                        'lastmod'    => $lastmod->tz('UTC')->toAtomString(),
                        'changefreq' => $this->changefreq,
                        'priority'   => $this->priority
                    ];
                }

                // 方案內容頁
                foreach ($store->showServicesWithActivity as $service) {
                    $lastmod = $service->edited_at ?: $service->updated_at;
                    $data['service'][] = [
                        'loc'        => $url.'/service/'.$service->id,
                        'lastmod'    => $lastmod->tz('UTC')->toAtomString(),
                        'changefreq' => $this->changefreq,
                        'priority'   => $this->priority
                    ];
                }

                // 成員作品列表
                foreach ($store->members as $member) {
                    $data['member'][] = [
                        'loc'        => $url.'/member/'.$member->id,
                        'lastmod'    => $member->updated_at->tz('UTC')->toAtomString(),
                        'changefreq' => $this->changefreq,
                        'priority'   => $this->priority
                    ];
                }
            }

            // 儲存 sitemap
            foreach ($data as $page => $sitemap) {
                $fileName = $this->indexFileName.'_'.$key.'_'.$page.$this->indexExtension;
                $this->putFile('sitemap.content', $fileName, $sitemap);

                // 放入 sitemap index 中
                $this->sitemapIndex[] = [
                    'loc'     => $this->indexUrl.$fileName,
                    'lastmod' => $this->indexLastmod,
                ];
            }
        }

        // 將 Index 存成 sitemap
        $fileName = $this->indexFileName.$this->indexExtension;
        $this->putFile('sitemap.index', $fileName, $this->sitemapIndex);
    }

    /**
     * 寫入檔案
     *
     * @param [type] $viewPath
     * @param [type] $fileName
     * @param [type] $sitemap
     * @return void
     */
    private function putFile($viewPath, $fileName, $sitemap)
    {
        if ($this->isPutFile !== true) {
            return;
        }

        Storage::disk('local')->put(
            'sitemap/'.$fileName,
            view($viewPath, ['sitemap' => $sitemap])
        );
    }
}
