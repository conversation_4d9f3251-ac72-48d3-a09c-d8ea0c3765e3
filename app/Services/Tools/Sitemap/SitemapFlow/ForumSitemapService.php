<?php
/*
 |--------------------------------------
 |  好婚聊聊 的 SEO網站地圖 SitemapService
 |--------------------------------------
 |
 |  記得在robots.txt 登記
 |
 */

namespace App\Services\Tools\Sitemap\SitemapFlow;

use Illuminate\Support\Facades\Storage;
use App\Services\Tools\Sitemap\SitemapInterface;
use App\Models\ForumArticle as Article;

class ForumSitemapService implements SitemapInterface
{
    // 每個月份的季度
    private $dateRange  = [
        1 => [
            'start' => '01-01 00:00:00',
            'end'   => '03-31 23:59:59',
        ],
        2 => [
            'start' => '04-01 00:00:00',
            'end'   => '06-30 23:59:59',
        ],
        3 => [
            'start' => '07-01 00:00:00',
            'end'   => '09-30 23:59:59',
        ],
        4 => [
            'start' => '10-01 00:00:00',
            'end'   => '12-31 23:59:59',
        ]
    ];

    // while迴圈是否執行
    private $runFlag       = true;

    // 時間相關的設定
    private $year          = '2018'; // 起始的年份，通常會有
    private $quarter       = '3'; // 起始的季度，通常會有
    private $time; // 現在的時間，超過現在的時間就必須使迴圈中止

    //====== sitemap 相關 ======
    // 網頁可能變更的頻率 always hourly daily weekly monthly yearly never
    private $changefreq    = 'daily';
    // 優先順序 0.0 ~ 1.0
    private $priority      = '0.8';
    // 目標網址
    private $url;

    //====== sitemap Index 相關 ======
    // 索引sitemap的儲存位置
    private $indexUrl;
    // 檔案的最後修改日期
    private $indexLastmod;
    // 檔案名稱
    private $indexFileName  = 'forum_article';
    private $indexExtension = '.xml';
    // 初始化
    private $sitemapIndex  = [];
    private $isPutFile     = true;
    private $result;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct()
    {
        $this->article      = new Article;
        $this->url          = config('params.wdv3.forum_url').'/article/';
        $this->time         = time();
        $this->indexUrl     = env('APP_URL').'/sitemap/';
        $this->indexLastmod = now()->tz('UTC')->toAtomString();
    }

    /**
     * 是否寫入檔案
     *
     * @param boolean $flag
     * @return void
     */
    public function setPutFile(bool $flag)
    {
        $this->isPutFile = $flag;
    }


    /**
     * 產生網站地圖 Sitemap
     *
     * @return void
     */
    public function create()
    {
        while ($this->runFlag) {

            // 初始化
            $sitemap   = [];
            $startDate = $this->year.'-'.$this->dateRange[$this->quarter]['start'];
            $endDate   = $this->year.'-'.$this->dateRange[$this->quarter]['end'];

            // 取得好婚聊聊的文章
            $articles = $this->article->select('id', 'published_at')
                                    ->where('is_exclude_seo', 0)
                                    ->where('status', 'published')
                                    ->where('published_at', '>=', $startDate)
                                    ->where('published_at', '<=', $endDate)
                                    ->get();
            foreach ($articles as $article) {
                $sitemap[] = [
                    'loc'        => $this->url.$article->id,
                    'lastmod'    => $article->published_at->tz('UTC')->toAtomString(),
                    'changefreq' => $this->changefreq,
                    'priority'   => $this->priority
                ];
            }

            if ($sitemap) {

                // 儲存 sitemap
                $fileName = $this->indexFileName.'_'.$this->year.'_'.$this->quarter.$this->indexExtension;
                $this->putFile('sitemap.content', $fileName, $sitemap);

                // 放入 sitemap index 中
                $this->sitemapIndex[] = [
                    'loc'     => $this->indexUrl.$fileName,
                    'lastmod' => $this->indexLastmod,
                ];
            }

            $this->quarter++;
            if ($this->quarter > 4) {
                $this->quarter = 1;
                $this->year++;
            }

            if (strtotime($endDate) >= $this->time) {
                $this->runFlag = false;
                break;
            }
        }

        // 將 Index 存成 sitemap
        $fileName = $this->indexFileName.$this->indexExtension;
        $this->putFile('sitemap.index', $fileName, $this->sitemapIndex);

        return $this->sitemapIndex;
    }

    /**
     * 寫入檔案
     *
     * @param [type] $viewPath
     * @param [type] $fileName
     * @param [type] $sitemap
     * @return void
     */
    private function putFile($viewPath, $fileName, $sitemap)
    {
        if ($this->isPutFile === true) {
            Storage::disk('local')->put(
                'sitemap/'.$fileName,
                view($viewPath, ['sitemap' => $sitemap])
            );
        }
    }
}
