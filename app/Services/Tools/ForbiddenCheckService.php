<?php
/*
 |--------------------------------------
 |  禁言檢查 Service
 |--------------------------------------
 |
 |
 |
 */

namespace App\Services\Tools;

use App\Models\Forbidden;

class ForbiddenCheckService
{
    private $forbidden;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Forbidden $forbidden
    ) {
        $this->forbidden = $forbidden;
    }

    /**
     * 轉成正確的網址
     *
     * @param [type] $url
     * @return void
     */
    public function text($haystack, $isException = false)
    {
        $forbiddens = $this->forbidden->where('type', 'text');

        // 是否強制禁言
        if ($isException) {
            $forbiddens = $forbiddens->where('is_force', 1);
        }

        $forbiddens = $forbiddens->get();

        foreach ($forbiddens as $forbidden) {
            if (stripos($haystack, $forbidden->content) !== false) {
                return $forbidden->content;
            }
        }

        return false;
    }
}
