<?php
/*
 |--------------------------------------
 | 新增 Firestore 的訊息紀錄
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools;

use App\Models\FirestoreMessage;
use Google\Cloud\Core\Timestamp;
use DateTime;

class FirestoreMessageCreateService
{
    private $firestoreMessage;
    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        FirestoreMessage $firestoreMessage
    ) {
        $this->firestoreMessage = $firestoreMessage;
    }

    /**
     * 新增 Firestore 的訊息紀錄
     *
     * @param date $date Y-m-d
     * @return int $total 建立訊息數量
     */
    public function run($date)
    {
        // use Google\Cloud\Core\Timestamp
        $startDate = new Timestamp(new DateTime($date.' 00:00:00'));
        $endDate   = new Timestamp(new DateTime($date.' 23:59:59'));

        // 取得特定日期與新娘建立的訊息數量
        $firestore = app('firebase.firestore');
        $roomSanps = $firestore->database()
                                ->collection('messages')
                                ->where('created_at', '>=', $startDate)
                                ->where('created_at', '<=', $endDate)
                                ->orderBy('created_at')
                                ->orderBy('store_id')
                                ->orderBy('user_id')
                                ->documents();

        foreach ($roomSanps as $roomSanp) {
            $roomObj = $roomSanp->data();
            $this->firestoreMessage->updateOrCreate([
                'store_id' => $roomObj['store_id'],
                'user_id'  => $roomObj['user_id'],
            ], [
                'document_id'  => $roomSanp->id(),
                'wedding_date' => empty($roomObj['user_first_wedding_date']) ? NULL : $roomObj['user_first_wedding_date'], // 可能為空值
                'created_at'   => new DateTime($roomObj['created_at']),
            ]);
        }

        return $roomSanps->size();
    }
}
