<?php
/*
 |--------------------------------------
 |  取得查詢參數的索引值 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Tools;

use App\Models\LogQueryString;
use App\Traits\RandStringTrait;
use Illuminate\Support\Str;

class GetQueryKeyService
{
    private $logQueryString;

    use RandStringTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        LogQueryString $logQueryString
    ) {
        $this->logQueryString = $logQueryString;
    }

    /**
     * 取得查詢參數的索引值
     */
    public function run($request, $source, $calculate = true)
    {
        $method = 'filterFor'.Str::studly($source);
        $queryArray = $this->$method($request);

        $data = json_encode($queryArray, JSON_UNESCAPED_UNICODE);
        $md5  = md5($data);

        $logQueryString = $this->logQueryString->where('source', $source)
                                                ->where('md5', $md5)
                                                ->first();
        if (!$logQueryString) {
            $logQueryString = clone $this->logQueryString;

            $logQueryString->source = $source;
            $logQueryString->md5    = $md5;
            $logQueryString->data   = $data;

            // Key產生唯一值
            $logQueryString->key = $this->getRandString(8, ['small', 'number']);
            while (
                $this->logQueryString->where('source', $source)
                                        ->where('key', $logQueryString->key)
                                        ->count()
            ) {
                $logQueryString->key = $this->getRandString(8, ['small', 'number']);
            }
        }

        // 累計次數
        if ($calculate) {
            $logQueryString->count++;
        }

        $logQueryString->save();

        return $logQueryString->key;
    }

    /**
     * 論壇文章列表的篩選項目
     * @param int $category_id 分類ID
     * @param string $keyword 搜尋關鍵字
     * @param int $tag_id 標籤ID
     * @param string $type 類型 track:追蹤話題
     * @param bool $search 關鍵字首次搜尋紀錄
     * @param string $sort 排序 hot:熱門討論 update:最新討論
     * @param int $page 頁碼
     */
    private function filterForForum($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['category_id', 'keyword', 'tag_id', 'type', 'sort', 'page']);

        return collect($request)->filter();
    }

    /**
     * 商家一覽的篩選項目
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 地點(拍婚紗/婚紗禮服/婚攝婚錄/新娘秘書/婚宴場地/婚禮主持人/喜餅)
     * @param array.int $store_tags[] 商家標籤
     * @param array.int $free_fares[] 免車馬費地區
     * @param array.int $extra_services[] 服務類型 301:平面攝影 302:動態錄影 303:平面+動態錄影
     * @param bool $has_files 拍婚紗-檔案全贈
     * @param bool $has_trial_fee 婚紗禮服-免試穿費 | 新娘秘書-試妝服務
     * @param bool $has_free_shop_tasting 喜餅-免費門市試吃
     * @param array.int $activity_ids[] 顯示活動方案商家
     * @param bool $has_discount 顯示獨家優惠商家
     * @param string $sort 排序 hot:人氣排序 update:最新排序 evaluationRate:評價數排序 discussionRate:討論度排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     */
    private function filterForStore($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['store_type', 'keyword', 'wedding_date', 'locations', 'store_tags', 'free_fares', 'extra_services', 'has_files', 'has_trial_fee', 'has_free_shop_tasting', 'activity_ids', 'has_discount', 'sort', 'page']);

        return collect($request)->filter();
    }

    /**
     * 商家相本一覽的篩選項目
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $data_type 資料類型 album:相本 image:照片 tasting:宅配試吃
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 商家地點(拍婚紗/婚紗禮服/婚攝婚錄/新娘秘書/婚禮主持人/喜餅)
     * @param array.int $image_locations[] 拍攝地點(拍婚紗)
     * @param array.int $free_fares[] 商家免車馬費地區
     * @param array.int $price_range[] 價格區間(婚紗禮服/婚禮佈置)
     * @param array.int $types[] 類型(拍婚紗/婚禮佈置)
     * @param array.int $album_tags[] 作品集標籤(婚紗禮服/婚禮佈置)
     * @param array.int $image_tags[] 作品照標籤(拍婚紗/婚攝婚錄/新娘秘書)
     * @param bool $can_customized 可客製化(喜餅)
     * @param bool $is_meat 葷(喜餅)
     * @param bool $is_lacto_vegetarian 奶素(喜餅)
     * @param bool $is_ovo_lacto_vegetarian 蛋奶素(喜餅)
     * @param bool $is_vegetarian 全素(喜餅)
     * @param array.int $activity_ids[] 顯示活動方案商家
     * @param string $sort 排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     */
    private function filterForStoreAlbum($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['store_type', 'data_type', 'keyword', 'wedding_date', 'locations', 'image_locations', 'free_fares', 'price_range', 'types', 'album_tags', 'image_tags', 'can_customized', 'is_meat', 'is_lacto_vegetarian', 'is_ovo_lacto_vegetarian', 'is_vegetarian', 'activity_ids', 'sort', 'page']);

        return collect($request)->filter();
    }

    /**
     * 商家影片一覽的篩選項目
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 商家地點(婚攝婚錄/婚禮主持人)
     * @param array.int $free_fares[] 商家免車馬費地區
     * @param string $sort 排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     */
    private function filterForStoreVideo($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['store_type', 'keyword', 'wedding_date', 'locations', 'free_fares', 'sort', 'page']);

        return collect($request)->filter();
    }

    /**
     * 商家方案一覽的篩選項目
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 商家地點
     * @param array.int $free_fares[] 商家免車馬費地區
     * @param array.int $price_range[] 價格區間
     * @param array.string $type[] 方案類型
     * @param bool $studio_photo_file 檔案全贈(拍婚紗)
     * @param bool $studio_casual_wear 拍攝便服(拍婚紗)
     * @param array.int $studio_white_dress[] 拍攝白紗(拍婚紗/婚紗禮服)
     * @param array.int $studio_dress[] 拍攝禮服(拍婚紗/婚紗禮服)
     * @param array.int $wedding_white_dress[] 宴客白紗(拍婚紗/婚紗禮服)
     * @param array.int $wedding_dress[] 宴客禮服(拍婚紗/婚紗禮服)
     * @param array.int $studio_album_photo[] 精修照(拍婚紗)
     * @param bool $for_studio 拍攝用(婚紗禮服)
     * @param bool $for_wedding 宴客用(婚紗禮服)
     * @param bool $witness_ceremony 證婚需求(婚攝婚錄/新娘秘書)
     * @param array.int $ceremony_type[] 習俗儀式類型(婚攝婚錄/新娘秘書/婚禮主持人)
     * @param array.int $banquet_time[] 婚宴時段(婚攝婚錄/新娘秘書/婚禮主持人)
     * @param array.int $photographer_time[] 拍攝時數(婚攝婚錄)
     * @param array.int $photographer_count[] 攝影師人數(婚攝婚錄)
     * @param array.int $makeup_count[] 造型數(新娘秘書)
     * @param array.int $service_tags[] 方案包含的服務(婚宴場地)
     * @param array.int $store_tags[] 商家提供的服務(婚宴場地)
     * @param array.string $condition_type[] 優惠條件 full_quantity:滿盒優惠 full_amount:滿額優惠
     * @param array.int $activity_ids[] 顯示活動方案商家
     * @param bool $has_discount 顯示獨家優惠商家
     * @param string $sort 排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     */
    private function filterForStoreService($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['store_type', 'keyword', 'wedding_date', 'locations', 'free_fares', 'price_range', 'type', 'studio_photo_file', 'studio_casual_wear', 'studio_white_dress', 'studio_dress', 'wedding_white_dress', 'wedding_dress', 'studio_album_photo', 'for_studio', 'for_wedding', 'witness_ceremony', 'ceremony_type', 'banquet_time', 'photographer_time', 'photographer_count', 'makeup_count', 'service_tags', 'store_tags', 'condition_type', 'activity_ids', 'has_discount', 'sort', 'page']);

        return collect($request)->filter();
    }

    /**
     * 商家廳房一覽的篩選項目
     * @param string $keyword 搜尋關鍵字
     * @param array.int $locations[] 商家地點
     * @param array.string $types[] 廳房類型 banquet:宴客 witness:證婚 ceremony:文定/迎娶
     * @param array.int $numbers[] 容納人數
     * @param array.string $devices[] 廳房設備 bridal_room:新娘休息室 wifi:Wi-Fi projection:投影幕設備 led:LED螢幕設備 sound:音響設備 light:特殊燈光設備 stage:舞台 pillar:無樑柱 backplane:送客背板
     * @param string $sort 排序 hot:人氣排序 update:最新排序
     * @param int $page 頁碼
     */
    private function filterForStoreVenueRoom($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['keyword', 'locations', 'types', 'numbers', 'devices', 'sort', 'page']);

        return collect($request)->filter();
    }

    /**
     * 商家婚禮小物一覽的篩選項目
     * @param string $keyword 搜尋關鍵字
     * @param array.int $price_range[] 價格區間
     * @param string $sort 排序 hot:人氣排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     */
    private function filterForStoreMallItem($request)
    {
        $request['sort'] = $request['sort'] ?: 'hot';
        $request['page'] = (int)$request['page'] ?: 1;
        $request = $request->only(['keyword', 'price_range', 'sort', 'page']);

        return collect($request)->filter();
    }
}
