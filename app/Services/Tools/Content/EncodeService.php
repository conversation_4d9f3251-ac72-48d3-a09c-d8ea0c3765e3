<?php
/*
 |--------------------------------------
 |  文章內容的 encode 轉換成我們要的格式，並且取得摘要
 |--------------------------------------
 |
 |  @input $content
 |
    <p>
        文章內容<br />
        <a data-userid=87333>周胖</a><br />
        <a data-anonymous=true>@匿名鬼鬼5788</a><br />
    </p>
 |
 |
 |  @output $contentObj
 |
    return {
        "content": "<p>文章內容[at_user:87333]<br />[at_anonymous:5788]<br />",
        "atUsers": [
            {
                link: "",
                value: "",
                user_id: "",
                is_anonymous: ""
            }
        ],
        "images": [
            {
                link: "",
                imageID: "",
                value: "",
            }
        ],
        "summary": {
            "text": "",
            "images": [
                {
                    link: "",
                    isSelf: true
                }
            ]
        },
        "links": [
            {
                link: "",
                value: ""
            }
        ]
    }
 |
 */

namespace App\Services\Tools\Content;

use App\Services\Image\CreateImageService;
use App\Services\File\UploadImageFormService;
use App\Models\User;
use App\Traits\Model\SummaryTrait;
use DOMDocument;
use StdClass;

class EncodeService
{
    private $document;
    private $userModel;
    private $createImageService;
    private $uploadImageFormService;
    private $type;
    private $target_id;
    private $content;
    private $atUsers = []; // @User list
    private $images = []; // 圖片 list
    private $summary; // 文章的摘要
    private $links = []; // 所有的連結

    use SummaryTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        User $user,
        CreateImageService $createImageService,
        UploadImageFormService $uploadImageFormService
    ) {
        $this->userModel              = $user;
        $this->createImageService     = $createImageService;
        $this->uploadImageFormService = $uploadImageFormService;
        $this->document               = new DOMDocument();

        $this->summary = new StdClass();
        $this->summary->text   = '';
        $this->summary->images = [];
    }

    /**
     * 轉換
     */
    public function run($content, $type, $target_id)
    {
        $this->type      = $type;
        $this->target_id = $target_id;

        // 轉換輸入的 content資料，讓後面比較好取代
        $content = $content ?: '';
        $this->content = $this->getHtmlContent($content);
        if (!empty($this->content)) {
            @$this->document->loadHTML(mb_convert_encoding($this->content, 'HTML-ENTITIES', 'UTF-8'));
        }

        // 取得 @Users
        $this->getAtUsers();

        // 增加未加 a tag 的連結
        $this->addUndefinedLink();

        // 取得圖片清單
        $this->getImages();

        // 將文章轉換成我們的格式
        $this->formatContent();

        // 取得文章摘要
        $this->getSummaryText();

        $result = new stdClass();
        $result->content = $this->content;
        $result->atUsers = $this->atUsers;
        $result->images  = $this->images;
        $result->summary = $this->summary;
        $result->links   = $this->links;

        return $result;
    }

    /**
     * 整理HTML的資料，避免取代時出現問題
     *
     * @return void
     */
    private function getHtmlContent($content)
    {
        // 先轉成html
        if (!empty($content)) {
            @$this->document->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));
        }

        // 取得 body 的內容
        $body = $this->document->getElementsByTagName('body')->item(0);

        // 取代 <body> 與 </body>的標籤以及換行
        $body = $this->document->saveHTML($body);
        $body = preg_replace('/^<body>|<\/body>$/', '', $body);

        // 清除前後多於的空白
        $body = trim($body);

        return $body;
    }

    /**
     * 取得被 @的使用者
     *
     * @return void
     */
    private function getAtUsers()
    {
        $aTags = $this->document->getElementsByTagName('a');
        foreach ($aTags as $tag) {
            $data = [
                'link'         => $tag->getAttribute('href'),
                'value'        => $this->document->saveHTML($tag),
                'user_id'      => '',
                'is_anonymous' => '',
            ];

            $this->links[] = [
                'link'  => $data['link'],
                'value' => $data['value'],
            ];

            $user = NULL;
            if ($tag->getAttribute('data-userid')) {
                $user_id = $tag->getAttribute('data-userid');
                $user    = $this->userModel->find($user_id);
            }
            if ($tag->getAttribute('data-anonymous')) {
                $anonymousKey = str_replace('@匿名鬼鬼', '', $tag->textContent);
                $user         = $this->userModel->where('anonymous_key', $anonymousKey)->first();
                $data['is_anonymous'] = $anonymousKey;
            }
            if ($user) {
                $data['user_id'] = $user->id;
                $this->atUsers[] = $data;
            }
        }
    }

    /**
     * 取得文章內所有的圖片
     *
     * @return void
     */
    private function getImages()
    {
        $this->images = [];
        $this->summary->images = [];

        $imgTags = $this->document->getElementsByTagName('img');
        foreach ($imgTags as $tag) {

            // 處理圖片，檢查是否為我們的圖片，以及是否新增圖片
            $result = [
                'imageID' => NULL,
                'link'    => $tag->getAttribute('src'),
                'value'   => $this->document->saveHTML($tag),
            ];

            if ($tag->getAttribute('data-imgid')) {
                $parseUrl  = parse_url($result['link']);
                $file_name = str_replace('/original/', '', $parseUrl['path']);

                $result['imageID']   = $tag->getAttribute('data-imgid');
                $result['id']        = $result['imageID'];
                $result['file_name'] = $file_name;
                $this->images[] = $result;

            } else {

                // 正規比對是否為我們的圖片(https://rcdn.weddingday.com.tw)
                preg_match('/^https:\/\/rcdn.weddingday.com.tw/', $result['link'], $match, PREG_OFFSET_CAPTURE);
                if ($match) {
                    $parseUrl  = parse_url($result['link']);
                    $file_name = str_replace('/original/', '', $parseUrl['path']);

                    $result['type']      = $this->type;
                    $result['target_id'] = $this->target_id;
                    $result['file_name'] = $file_name;

                    if ($tag->getAttribute('data-width') && $tag->getAttribute('data-height')) {
                        $result['width']  = $tag->getAttribute('data-width');
                        $result['height'] = $tag->getAttribute('data-height');
                    }

                    $result            = $this->createImageService->add($result);
                    $result['imageID'] = $result['id'];

                    $this->images[] = $result;
                }

                // 處理Base64的圖片
                if (stripos($result['link'], 'data:image') !== false) {
                    $base64 = str_replace(urlencode('\"'), '', $result['link']);
                    $imageObj = $this->uploadImageFormService->uploadBase64($base64);
                    $file_name = $imageObj['file_name'];

                    $result['type']      = $this->type;
                    $result['target_id'] = $this->target_id;
                    $result['file_name'] = $file_name;

                    $result            = $this->createImageService->add($result);
                    $result['imageID'] = $result['id'];

                    $this->images[] = $result;
                }
            }

            //====================
            //  【摘要】 圖片
            //====================
            $this->summary->images[] = [
                'link'   => $result['imageID'] ? $file_name : $result['link'],
                'isSelf' => (bool)$result['imageID']
            ];
        }
    }

    /**
     * 為文章內未定義的Url加上Tag
     *
     * @return void
     */
    private function addUndefinedLink()
    {
        // 先將a標籤取代成暫時標記
        foreach ($this->links as $key => $link) {
            $this->content = str_replace($link['value'], '【超連結：'.$key.'】', $this->content);
        }

        // 去除所有html標籤，找出所有url
        $_content = strip_tags($this->content);
        preg_match_all('/(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$;?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$;?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$;?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$;])/imu', $_content, $match, PREG_SET_ORDER);

        // foreach url
        foreach ($match as $matchUrl) {

            // 檢查是不是url
            $url = $matchUrl[0];
            if (!parse_url($url)) {
                continue;
            }

            // 將url也取代成暫時標記
            $key           = count($this->links);
            $this->content = str_replace($url, '【超連結：'.$key.'】', $this->content);

            // 增加到Link中
            $this->links[$key] = [
                'link'  => $url,
                'value' => '<a href="'.$url.'" rel="noopener noreferrer" target="_blank">'.$url.'</a>',
            ];
        }

        // 將所有暫時標記取代回a標籤
        foreach ($this->links as $key => $link) {
            $this->content = str_replace('【超連結：'.$key.'】', $link['value'], $this->content);
        }
    }

    private function formatContent()
    {
        // 將圖片轉成 特定的格式 [image:{image_id}]
        if (count($this->images) > 0) {
            foreach ($this->images as $image) {
                $this->content = str_replace($image['value'], '[image:'.$image['imageID'].']', $this->content);
            }
        }

        // 將@User轉成特定的格式 [at_user:{user_id}] 或 [at_anonymous:{anonymous_key}]
        if (count($this->atUsers) > 0) {
            foreach ($this->atUsers as $atUser) {
                $tagName = $atUser['is_anonymous'] ? '[at_anonymous:'.$atUser['is_anonymous'].']' : '[at_user:'.$atUser['user_id'].']';
                $this->content = str_replace($atUser['value'], $tagName, $this->content);
            }
        }
    }

    /**
     * 取得文章摘要
     *
     * @return void
     */
    private function getSummaryText()
    {
        // 取代image標籤 [image:{image_id}]
        $this->summary->text = preg_replace('/\[image:[0-9]{1,}\]/', '', $this->content);

        // 取代at_user標籤為姓名 [at_user:{user_id}] 或 [at_anonymous:{anonymous_key}]
        preg_match_all('/\[(at_user|at_anonymous):([A-Za-z0-9]{1,})\]/Usi', $this->summary->text, $match, PREG_SET_ORDER);
        if ($match) {
            foreach ($match as $tag) {
                $user = NULL;
                if ($tag[1] == 'at_user') {
                    $user = $this->userModel->find($tag[2]);
                } else {
                    $user = $this->userModel->where('anonymous_key', $tag[2])->first();
                }

                $userName = '';
                if ($user) {
                    $userName = ($tag[1] == 'at_user') ? '@'.$user->name : '@匿名鬼鬼'.$user->anonymous_key;
                }
                $this->summary->text = str_replace($tag[0], $userName, $this->summary->text);
            }
        }

        // 去除HTML標籤
        $this->summary->text = $this->getSummaryStripTags($this->summary->text, 200);
    }
}
