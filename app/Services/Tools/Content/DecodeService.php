<?php
/*
 |--------------------------------------
 |  文章內容的 encode 轉換成我們要的格式，並且取得摘要
 |--------------------------------------
 |
 |  @input $content
 |
    文章詳細內容...[image:9453]...[at_user:87333]...[at_anonymous:5788]...
 |
 |
 |  @output $contentObj
 |
 |  return
    {
      "html": "文章詳細內容...[image:9453]...[at_user:87333]...[at_anonymous:5788]...",
      "images": [
        {
          "id": 9453,
          "replace_tag": "[image:9453]",
          "file_url": "https://rcdn.weddingday.comn.tw/original/b7bf50fd836f3a1e9996441f892afe2f.jpg",
          "file_name": "b7bf50fd836f3a1e9996441f892afe2f.jpg", // 可用於刪除圖片
          "width": "800", // 目前參考用
          "height": "600" // 目前參考用
        }
      ],
      "at_users": [
        {
          "id": 87333,
          "replace_tag": "[at_user:87333]",
          "name": "周胖",
          "link": "https://www.weddingday.com.tw/user/main/87333",
          "avatar": "https://graph.facebook.com/3939889/picture?width=200&height=200" // 目前參考用
        }
      ]
      "at_anonymous": [
        {
          "replace_tag": "[at_anonymous:5788]",
          "name": "匿名鬼鬼5788",
        }
      ]
    }
 |
 */

namespace App\Services\Tools\Content;

use App\Services\Image\GetImageUrlService;
use App\Models\Image;
use App\Models\User;

class DecodeService
{
    private $getImageUrlService;
    private $imageModel;
    private $userModel;
    private $content;
    private $atUsers = []; // @User list
    private $atAnonymous = []; // @Anonymou list
    private $images = []; // 圖片 list

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetImageUrlService $getImageUrlService,
        Image $image,
        User $user
    ) {
        $this->getImageUrlService = $getImageUrlService;
        $this->imageModel         = $image;
        $this->userModel          = $user;
    }

    /**
     * decode
     *
     * @param  mixed $content
     *
     * @return void
     */
    public function decode($content)
    {
        // 去除分行符號
        $this->content     = preg_replace('/[\n\r\t]/', '', $content) ?: '';
        $this->atUsers     = [];
        $this->atAnonymous = [];
        $this->images      = [];

        $this->getAtUsers();
        $this->getImages();

        $result = new \stdClass();
        $result->html         = $this->content;
        $result->at_users     = $this->atUsers;
        $result->at_anonymous = $this->atAnonymous;
        $result->images       = $this->images;

        return $result;
    }

    public function decodeV2($content, $allAtUsers)
    {
        // 去除分行符號
        $this->content     = preg_replace('/[\n\r\t]/', '', $content) ?: '';
        $this->atUsers     = [];
        $this->atAnonymous = [];
        $this->images      = [];

        $this->getAtUsersById($allAtUsers);
        $this->getImages();

        $result = new \stdClass();
        $result->html         = $this->content;
        $result->at_users     = $this->atUsers;
        $result->at_anonymous = $this->atAnonymous;
        $result->images       = $this->images;

        return $result;
    }

    /**
     * 取得文章內關於我們自己的圖片清單
     *
     * @return void
     */
    private function getImages()
    {
        preg_match_all('/\[image:([0-9]{1,})\]/Usi', $this->content, $match, PREG_SET_ORDER);
        if ($match) {
            foreach ($match as $tag) {
                $img = $this->imageModel->where('id', $tag[1])->first();
                if ($img) {
                    $this->images[] = [
                        'id'          => $tag[1],
                        'replace_tag' => $tag[0],
                        'file_name'   => $img->file_name,
                        'width'       => $img->width ?: '',
                        'height'      => $img->height ?: '',
                        'file_url'    => $this->getImageUrlService->get($img->file_name),
                    ];
                }
            }
        }
    }

    /**
     * 取得文章內關於被 @的使用者清單
     *
     * @return void
     */
    private function getAtUsers()
    {
        // 取代at_user標籤為姓名 [at_user:{user_id}] 或 [at_anonymous:{anonymous_key}]
        preg_match_all('/\[(at_user|at_anonymous):([A-Za-z0-9]{1,})\]/Usi', $this->content, $match, PREG_SET_ORDER);
        if ($match) {
            foreach ($match as $tag) {
                if ($tag[1] == 'at_user') {
                    $user = $this->userModel->find($tag[2]);
                    if ($user) {
                        $this->atUsers[] = [
                            'id'          => $tag[2],
                            'replace_tag' => $tag[0],
                            'name'        => $user->name,
                            'link'        => $this->getUserUrl($user->id)
                        ];
                    }
                } else {
                    $user = $this->userModel->where('anonymous_key', $tag[2])->first();
                    if ($user) {
                        $this->atAnonymous[] = [
                            'key'         => $user->anonymous_key,
                            'replace_tag' => $tag[0],
                            'name'        => '匿名鬼鬼'.$user->anonymous_key,
                        ];
                    }
                }
            }
        }
    }
    private function getAtUsersById($allAtUsers)
    {
        // 取代at_user標籤為姓名 [at_user:{user_id}] 或 [at_anonymous:{anonymous_key}]
        preg_match_all('/\[(at_user|at_anonymous):([A-Za-z0-9]{1,})\]/Usi', $this->content, $match, PREG_SET_ORDER);
        if ($match) {
            foreach ($match as $tag) {
                if ($tag[1] == 'at_user') {
                    $user = $allAtUsers[$tag[2]];
                    if ($user) {
                        $this->atUsers[] = [
                            'id'          => $tag[2],
                            'replace_tag' => $tag[0],
                            'name'        => $user->name,
                            'link'        => $this->getUserUrl($user->id)
                        ];
                    }
                } else {
                    $this->atAnonymous[] = [
                        'key'         => $tag[2],
                        'replace_tag' => $tag[0],
                        'name'        => '匿名鬼鬼'.$tag[2],
                    ];
                }
            }
        }
    }
    public function getAtUsersList($contents)
    {
        $atUserList = [];
        foreach($contents as $content) {
            $content = preg_replace('/[\n\r\t]/', '', $content->content) ?: '';
            // 取代at_user標籤為姓名 [at_user:{user_id}] 或 [at_anonymous:{anonymous_key}]
            preg_match_all('/\[(at_user|at_anonymous):([A-Za-z0-9]{1,})\]/Usi', $content, $match, PREG_SET_ORDER);
            if ($match) {
                foreach ($match as $tag) {
                    if ($tag[1] == 'at_user') {
                        $atUserList[] = $tag[2];
                    }
                }
            }
        }

        return $atUserList;
    }

    /**
     * 取得使用者連結
     *
     * @param  mixed $id
     *
     * @return void
     */
    private function getUserUrl($id)
    {
        return config('params.wdv3.user_url').'/main/'.$id;
    }
}
