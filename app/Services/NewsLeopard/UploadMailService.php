<?php
declare(strict_types=1);
/*------------------------------------
 | 上傳email到群組
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard;

use App\Services\NewsLeopard\UploadMail\GroupData;
use App\Services\NewsLeopard\ApiClient\ClearGroupData;
use App\Services\NewsLeopard\UploadMail\MakeFiles;
use App\Services\NewsLeopard\UploadMail\GetUsersData;
use App\Services\NewsLeopard\ApiClient\GetUploadUrl;
use App\Services\NewsLeopard\ApiClient\UploadFile;

class UploadMailService
{
    /** @var GroupData */
    private $groupData;

    /** @var ClearGroupData */
    private $clearGroupData;

    /** @var MakeFiles  */
    private $makeFiles;

    /** @var GetUsersData  */
    private $getUsersData;

    /** @var GetUploadUrl */
    private $getUploadUrl;

    /** @var UploadFile */
    private $uploadFile;

    /** @var array : 群組資料 */
    private $group = [];

    /**
     * UploadMailListService constructor.
     * @param GroupData $groupData
     * @param ClearGroupData $clearGroupData
     * @param MakeFiles $makeFiles
     * @param GetUsersData $getUsersData
     * @param GetUploadUrl $getUploadUrl
     * @param UploadFile $uploadFile
     */
    public function __construct(
        GroupData $groupData,
        ClearGroupData $clearGroupData,
        MakeFiles $makeFiles,
        GetUsersData $getUsersData,
        GetUploadUrl $getUploadUrl,
        UploadFile $uploadFile
    )
    {
        $this->groupData = $groupData;
        $this->clearGroupData = $clearGroupData;
        $this->makeFiles = $makeFiles;
        $this->getUsersData = $getUsersData;
        $this->getUploadUrl = $getUploadUrl;
        $this->uploadFile = $uploadFile;
    }

    /**
     * 上傳邏輯
     */
    public function upload()
    {
        $this->group = $this->groupData->handle(); //群組驗證處理
        $this->clearGroup(); //清空電子豹的群組資料
        $this->makeFile(); //建立mail list檔案
        $this->getUrl(); //取得上傳檔案的url
        $this->putFile(); //上傳檔案
    }

    /**
     * 清空群組資料
     * 非同步射後不理..有錯會記在Log裡
     */
    private function clearGroup()
    {
        foreach ($this->group as $group) {
            $this->clearGroupData->send($group['sn']);
        }
    }

    /**
     * 建立mail list檔案
     */
    private function makeFile()
    {
        //0~6個月加入
        $firstModel = $this->getUsersData->firstHalfYears();
        $this->makeFiles->date($firstModel, env('LEOPARD_FIRST_HALF_GROUP_NAME').'.txt');
        //6～12個月加入
        $secondModel = $this->getUsersData->secondHalfYears();
        $this->makeFiles->date($secondModel, env('LEOPARD_SECOND_HALF_GROUP_NAME').'.txt');
        //失效信箱
        $invalidModel = $this->getUsersData->invalidUsers();
        $this->makeFiles->date($invalidModel, env('LEOPARD_INVALID_GROUP_NAME').'.txt');
    }

    /**
     * 取得上傳檔案的url
     */
    private function getUrl()
    {
        foreach ($this->group as $key => $group) {
            $resp = $this->getUploadUrl->send($group['sn']);
            $this->group[$key]['url'] = $resp->url;
            $this->group[$key]['import_sn'] = $resp->importSn;
        }
    }

    /**
     * 上傳檔案
     */
    private function putFile()
    {
        foreach ($this->group as $group) {
            $this->uploadFile->send($group);
        }
    }

}
