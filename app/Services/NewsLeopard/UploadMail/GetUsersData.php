<?php
declare(strict_types=1);
/*------------------------------------
 | 取得要加入名單的使用者
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\UploadMail;

use App\Repositories\UserRepository;
use App\Repositories\MailBaseRepository;

class GetUsersData
{
    /** @var UserRepository */
    private $userRepository;

    /** @var MailBaseRepository */
    private $mailBaseRepository;

    /**
     * GetUserService constructor.
     * @param UserRepository $userRepository
     * @param MailBaseRepository $mailBaseRepository
     */
    public function __construct(
        UserRepository $userRepository,
        MailBaseRepository $mailBaseRepository
    )
    {
        $this->userRepository = $userRepository;
        $this->mailBaseRepository = $mailBaseRepository;
    }

    /**
     * 取得加入0~6個月的使用者
     * @return mixed
     */
    public function firstHalfYears()
    {
        $start = now()->subMonths(6)->startOfDay();
        $end   = now()->subDay()->endOfDay();
        return $this->getBetweenData($start, $end);
    }

    /**
     * 取得入6~12個月的使用者
     * @return mixed
     */
    public function secondHalfYears()
    {
        $start = now()->subYear()->startOfDay();
        $end   = now()->subMonths(6)->subDay()->endOfDay();
        return $this->getBetweenData($start, $end);
    }

    /**
     * 取得使用者區間資料
     * @param $start : 開始時間
     * @param $end : 結束時間
     * @return mixed
     */
    private function getBetweenData($start, $end)
    {
        return $this->userRepository->getData(function ($query) use ($start, $end) {
            $query->whereBetween('created_at', [$start, $end])
                ->where('is_admin', 0)
                ->where('is_fake', 0)
                ->live();
        });
    }

    /**
     * 取得失效的email資料
     */
    public function invalidUsers()
    {
        return $this->mailBaseRepository->getData(['valid' => 0]);
    }
}
