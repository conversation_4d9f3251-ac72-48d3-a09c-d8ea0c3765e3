<?php
declare(strict_types=1);
/*------------------------------------
 | 處理群組資料
 |------------------------------------
 | 如果DB是空的..先建立群組..並寫進DB
 | 如果DB有資料.就判斷群組存不存在電子豹上..不在的話要重建群組
 |
 */

namespace App\Services\NewsLeopard\UploadMail;

use App\Repositories\NewsLeopardRepository;
use App\Services\NewsLeopard\ApiClient\CreateGroup;
use App\Services\NewsLeopard\ApiClient\GetGroupList;
use Illuminate\Support\Facades\Log;

class GroupData
{
    /** @var NewsLeopardRepository  */
    private $newsLeopardRepository;

    /** @var CreateGroup  */
    private $createGroup;

    /** @var GetGroupList  */
    private $getGroupList;

    /** @var array : 從電子豹上拉回來的群組列表 */
    private $groupList = [];

    /**
     * GroupData constructor.
     * @param NewsLeopardRepository $newsLeopardRepository
     * @param CreateGroup $createGroup
     * @param GetGroupList $getGroupList
     */
    public function __construct(
        NewsLeopardRepository $newsLeopardRepository,
        CreateGroup $createGroup,
        GetGroupList $getGroupList
    )
    {
        $this->newsLeopardRepository = $newsLeopardRepository;
        $this->createGroup = $createGroup;
        $this->getGroupList = $getGroupList;
    }

    /**
     * 群組處理邏輯
     * @return array|array[]
     */
    public function handle()
    {
        $groupData = $this->newsLeopardRepository->getAll();
        if ($groupData->count() <= 0) { //DB沒有資料..建立新群組
            $group = $this->createNewGroup();
        } else { //DB有資料..確認電子豹上有沒有群組在.有可能被某腦殘刪了..
            $this->groupList = $this->getGroupList->send();
            $group = $this->checkGroup($groupData);
        }
        return $group;
    }

    /**
     * 建立新群組
     * @return array[]
     */
    private function createNewGroup()
    {
        $group = [
            ['name' => env('LEOPARD_FIRST_HALF_GROUP_NAME'), 'sn' => ''],
            ['name' => env('LEOPARD_SECOND_HALF_GROUP_NAME'), 'sn' => ''],
            ['name' => env('LEOPARD_INVALID_GROUP_NAME'), 'sn' => ''],
        ];

        foreach ($group as $key => $value) {
            $resp = $this->sendCreate($value['name']);
            if (!$resp) {
                Log::debug('GroupData@createNewGroup error ', ['data' => $value['name']]);
                continue;
            }
            $this->newsLeopardRepository->addData([
                'group_name' => $value['name'],
                'group_sn'   => $resp->sn
            ]);
            $group[$key]['sn'] = $resp->sn;
        }
        return $group;
    }

    /**
     * 判斷群組是否存在
     * @param array $groupData : 群組資料
     * @return array
     */
    private function checkGroup($groupData)
    {
        $group = [];
        $list = collect($this->groupList);

        foreach ($groupData as $data) {
            //判斷sn是否在list裡
            $sn = $data->group_sn;
            $key = $list->search(function ($item) use ($sn) {
                return $item->sn == $sn;
            });

            if ($key !== false) { //群組存在..把資料加到group裡
                $group[] = ['name' => $data->group_name, 'sn' => $data->group_sn];
            } else { //群組不存在..重建一個.
                $resp = $this->sendCreate($data->group_name); //call api
                if (!$resp) {
                    Log::debug('GroupData@checkGroup error ', ['data' => $data->group_name]);
                    continue;
                }
                $this->newsLeopardRepository->updateOrCreate(
                    ['group_name' => $data->group_name],
                    [
                        'group_name' => $data->group_name,
                        'group_sn'   => $resp->sn
                    ]
                );
                $group[] = ['name' => $data->group_name, 'sn' => $resp->sn];
            }
        }

        return $group;
    }

    /**
     * call電子豹新增群組的api
     * @param $name : 群組名
     * @return mixed|\Psr\Http\Message\ResponseInterface|null
     */
    private function sendCreate($name)
    {
        return $this->createGroup->send($name);
    }
}
