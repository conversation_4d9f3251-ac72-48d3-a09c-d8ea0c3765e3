<?php
declare(strict_types=1);
/*------------------------------------
 | 建立email列表
 |------------------------------------
 |
 */

namespace App\Services\NewsLeopard\UploadMail;

use Illuminate\Support\Facades\Storage;

class MakeFiles
{
    /**
     * 建立時間檔案
     * ref: https://newsleopard.com/newsleopard/api/v1/#header-%E6%A9%9F%E5%88%B6%E8%AA%AA%E6%98%8E
     * @param $model : user資料model
     * @param $fileName : 檔案名
     */
    public function date($model, $fileName)
    {
        //用yield節省資源
        $generate = function ($m)
        {
            foreach ($m as $v) {
                yield $v;
            }
        };

        //串接檔案內容..電子豹要求的格式
        $contents = "EMAIL,NAME,CREATED_AT" . PHP_EOL;
        foreach ($generate($model) as $k => $value) {
            $contents .= "{$value->email},{$value->name},{$value->created_at->format('Y-m-d')}" . PHP_EOL;
        }

        //寫入檔案
        Storage::put("newsleopard/" . $fileName, $contents);
    }

    /**
     * 建立失效使用者檔案
     * ref: https://newsleopard.com/newsleopard/api/v1/#header-%E6%A9%9F%E5%88%B6%E8%AA%AA%E6%98%8E
     * @param $model : user資料model
     * @param $fileName :
     */
    public function invalid($model, $fileName)
    {
        //用yield節省資源
        $generate = function ($m)
        {
            foreach ($m as $v) {
                yield $v;
            }
        };

        //串接檔案內容..電子豹要求的格式
        $contents = "EMAIL,NAME,CREATED_AT" . PHP_EOL;
        foreach ($generate($model) as $k => $value) {
            $userName = $value->userByMail->name ?? "null";
            $contents .= "{$value->email},{$userName},{$value->created_at->format('Y-m-d')}" . PHP_EOL;
        }

        //寫入檔案
        Storage::put("newsleopard/" . $fileName, $contents);
    }

}
