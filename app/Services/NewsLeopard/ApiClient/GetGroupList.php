<?php
declare(strict_types=1);
/*------------------------------------
 | 取得所有群組列表
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\ApiClient;


class GetGroupList extends AbsClient
{
    /**
     * 取得所有群組列表
     * @param $name: 群組名稱
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function send($name = '') {
        $dataArr = [];
        $page = 0;

        do {
            $resp = $this->client->request('GET', "/v1/contacts/lists?size=50&page={$page}");
            if($resp->getStatusCode() != 200){
                return null;
            }
            $list = json_decode($resp->getBody()->getContents());
            if(count($list) <= 0){
                break;
            }
            $dataArr = array_merge($dataArr, $list);
            $page++;
            usleep(20000); //電子豹每秒只能有10個request..所以睡個2毫秒
        } while (true);

        return $dataArr;
    }
}
