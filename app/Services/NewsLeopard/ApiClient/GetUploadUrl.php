<?php
declare(strict_types=1);
/*------------------------------------
 | 取得上傳群組名單的url
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\ApiClient;


class GetUploadUrl extends AbsClient
{
    /**
     * 取得上傳群組名單的url
     * @param $groupSN: 群組編號
     * @return mixed|null
     */
    public function send($groupSN)
    {
        $resp = $this->client->request('POST', "/v1/contacts/imports/{$groupSN}/file");
        if($resp->getStatusCode() != 200){
            return null;
        }
        return json_decode($resp->getBody()->getContents());
    }
}
