<?php
declare(strict_types=1);
/*------------------------------------
 | 清除群組內的所有資料
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\ApiClient;

use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Exception\RequestException;
use Log;

class ClearGroupData extends AbsClient
{
    /**
     * 清除群組內的所有資料
     * @param $groupSN : 群組編號
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function send($groupSN)
    {
        //白爛死了的電子豹格式...
        $data = [
            'filters' => [
                [
                    'columnName' => 'MAIL_ADDRESS',
                    'operator'   => 'NOT_EQ',
                    'value'      => ''
                ]
            ]
        ];

        try {
            $resp = $this->client->request('DELETE', "/v1/contacts/{$groupSN}", [
                'json' => $data
            ]);
            if ($resp->getStatusCode() != 200) {
                return null;
            }
            return json_decode($resp->getBody()->getContents());
        } catch (\Exception $e) { //對方會噴504 Gateway Timeout
            //不理他..
            return null;
        }

// 同步會噴504..用非同步怕舊資料還沒刪完就又上傳新資料..
//        $promise = $this->client->requestAsync('DELETE', "/v1/contacts/{$groupSN}", [
//            'json' => $data
//        ]);
//
//        $promise->then(
//            function (ResponseInterface $resp) {
//                if ($resp->getStatusCode() != 200) {
//                    return null;
//                }
//                return json_decode($resp->getBody()->getContents());
//            },
//            function (RequestException $e) {
//                Log::debug('ClearGroupData@send error', ['data' => $e->getMessage()]);
//                return null;
//            }
//        );
    }
}
