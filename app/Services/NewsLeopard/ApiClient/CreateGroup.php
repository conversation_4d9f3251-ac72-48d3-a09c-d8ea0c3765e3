<?php
declare(strict_types=1);
/*------------------------------------
 | 建立群組
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\ApiClient;


class CreateGroup extends AbsClient
{
    /**
     * 建立群組
     * @param $name: 群組名稱
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function send($name) {
        $resp = $this->client->request('POST', '/v1/contacts/lists/insert', [
            'json' => ['name' => $name]
        ]);
        if($resp->getStatusCode() != 200){
            return null;
        }
        return json_decode($resp->getBody()->getContents());
    }
}
