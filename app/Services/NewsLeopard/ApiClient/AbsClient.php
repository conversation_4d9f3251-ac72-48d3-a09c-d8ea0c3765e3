<?php
declare(strict_types=1);
/*------------------------------------
 | client 電子豹api的抽象類
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\ApiClient;

use GuzzleHttp\Client;

abstract class AbsClient
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => env('LEOPARD_END_POINT'),
            'headers' => ['x-api-key' => env('LEOPARD_API_KEY')],
        ]);
    }

    /**
     * @param mixed data: call api需要的參數
     * @return mixed
     */
    abstract public function send($data);
}
