<?php
declare(strict_types=1);
/*------------------------------------
 | 上傳mail list檔案
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\NewsLeopard\ApiClient;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Storage;

class UploadFile extends AbsClient
{
    protected $client;

    /**
     * @override
     * constructor.
     */
    public function __construct()
    {
        parent::__construct();

        //上傳的uri每次都不一樣..所以new個自己的client
        $this->client = new Client([
            'headers' => ['x-api-key' => env('LEOPARD_API_KEY')],
        ]);
    }

    /**
     * 上傳mail list檔案
     * @param array $data: ['name' => '要上傳的檔名', 'url' => '上傳的路徑...可由GetUploadUrl取得']
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function send($data) {
        $body = Storage::get('newsleopard/' . $data['name'].'.txt');  //檔案真實路徑在 storage/app/newsleopard

        $resp = $this->client->request(
            'PUT',
            $data['url'],
            ['body' => $body]
        );
        if($resp->getStatusCode() != 200){
            return null;
        }
        return json_decode($resp->getBody()->getContents());
    }
}
