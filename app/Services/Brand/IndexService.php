<?php
/*
 |--------------------------------------
 |  品牌主頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Brand;

use App\Models\ForumArticle;

class IndexService
{
    private $forumArticle;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(ForumArticle $forumArticle)
    {
        $this->forumArticle = $forumArticle;
    }

    /**
     * 品牌主頁
     */
    public function run($brand, $request)
    {
        // 取得主要商家
        $primaryStore = $brand->primaryStores->first();
        if ($primaryStore) {

            // 所有品牌的分享文列表
            $articleBrandIds = $primaryStore->sharePostBrands->pluck('brand_id', 'article_id');
            $forumArticles   = $this->forumArticle->whereIn('id', $articleBrandIds->keys());
            $forumArticles   = $this->queryForumArticles($forumArticles, $request);

            // 取得分享文的評價
            foreach ($forumArticles as $key => $forumArticle) {
                $forumArticles[$key]->pivot = $forumArticle->brands()->wherePivot('brand_id', $articleBrandIds[$forumArticle->id])->first()->pivot;
            }

        } else {

            // 此品牌的分享文列表
            $forumArticles = $this->queryForumArticles($brand->articles(), $request);
        }

        return [
            'brand'        => $brand,
            'primaryStore' => $primaryStore,
            'sharePosts'   => $forumArticles,
        ];
    }

    // 取得分享文列表
    private function queryForumArticles($model, $request)
    {
        // 熱門排序 sort:hot(default)
        if (!$request['sort'] || $request['sort'] == 'hot') {
            $model = $model->sortHotScore();
        }

        // 最新排序 sort:update
        $model = $model->sortPublishedAt();

        // 分頁 page
        $model = $model->paginate(20);

        return $model;
    }
}
