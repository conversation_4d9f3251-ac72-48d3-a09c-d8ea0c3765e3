<?php
/*
 |--------------------------------------
 |  刪除s3上檔案的 Service
 |--------------------------------------
 |
 |
 */


namespace App\Services\File;

use Illuminate\Support\Facades\Storage;
use Log;

class DeleteFileService
{
    /**
     * delete
     *
     * @param  mixed $fileName
     *
     * @return void
     */
    public function delete($data)
    {
        // 若資料與環境參數不相同，則無法刪除
        if ($data['app_env'] != env('APP_ENV')) {
            return;
        }

        try {
            Storage::disk('s3')->delete([
                'original/'.$data['file_name'],
                'resize/'.$data['file_name']
            ]);
        } catch (\Exception $e) {
            // Error Log
            Log::error('刪除檔案失敗: '.$e->getMessage(), [
                'class' => class_basename(get_class($this)),
                'data'  => $data,
            ]);
        }
    }
}
