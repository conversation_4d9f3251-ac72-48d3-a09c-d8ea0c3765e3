<?php
/*
 |--------------------------------------
 |  上傳檔案 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\File;

use App\Traits\ApiErrorTrait;
use Illuminate\Support\Facades\Storage;

class UploadFileFormService
{
    use ApiErrorTrait;

    /**
     * 上傳檔案 By Form表單
     *
     * @return $result
     */
    public function run($file, $path)
    {
        if (!$file) {
            $this->setException('上傳檔案不存在！');
        }

        if (!$file->isValid()) {
            $this->setException('上傳檔案無效！');
        }

        // 設定上傳至 s3 的目錄
        $filePath = env('APP_DEBUG') ? '/wdv3-dev/'.$path : '/wdv3/'.$path;

        // 產生檔案名稱
        $extension = $file->getClientOriginalExtension();
        $fileName  = md5(microtime()).'_'.sha1(uniqid()).'.'.$extension;

        // 設定上傳至 s3 的 options
        $contentType = $file->getClientMimeType();
        $options     = [
            'ContentType'  => $contentType,
            'ACL'          => 'public-read',
            'CacheControl' => 'max-age=31536000, public'
        ];

        // 先存一份原檔到 S3上面
        try {
            Storage::disk('s3')->putFileAs($filePath, $file, $fileName, $options);
        } catch (\Exception $e) {
            $this->setException('上傳檔案失敗，無法存放至目的地！');
        }

        return [
            'file_name'    => $fileName,
            'extension'    => $extension,
            'content_type' => $contentType,
            'file_url'     => config('params.file_url').$filePath.'/'.$fileName,
        ];
    }
}
