<?php
/*
 |--------------------------------------
 |  取得S3上圖片的寬高
 |--------------------------------------
 |
 |
 */

namespace App\Services\File;

use Illuminate\Support\Facades\Storage;
use Log;
use Imagick;
use ImagickPixel;
// use Image;

class GetImageInfoService
{
    /**
     * delete
     *
     * @param  mixed $fileName
     * @return void
     */
    public function run($fileName)
    {
        // S3 原圖路徑
        $fileName = 'original/'.$fileName;

        // 驗證 S3 原圖是否存在
        $exists = Storage::disk('s3')->exists($fileName);
        if (!$exists) {
            Log::error('S3 原圖不存在: '.$fileName, [
                'class' => class_basename(get_class($this)),
            ]);

            return false;
        }

        // 驗證 S3 原圖是否取得到檔案
        $file = Storage::disk('s3')->get($fileName);
        if (!$file) {
            // Storage::disk('s3')->delete($fileName);
            Log::error('S3 原圖無法取得檔案: '.$fileName, [
                'class' => class_basename(get_class($this)),
            ]);

            return false;
        }

        $imagick = new Imagick();
        $imagick->readImageBlob($file);
        unset($file);

        // 圖片轉正的旋轉度數 (for iphone & safari)
        $degrees = 0;
        switch ($imagick->getImageOrientation()) {
            case Imagick::ORIENTATION_BOTTOMRIGHT: // value = 3, 翻轉180度
                $degrees = 180;
                break;
            case Imagick::ORIENTATION_RIGHTTOP: // value = 6, 順時鐘翻轉270度
                $degrees = 90;
                break;
            case Imagick::ORIENTATION_LEFTBOTTOM: // value = 8, 順時鐘翻轉90度
                $degrees = -90;
                break;
        }
        if ($degrees) {
            $imagick->rotateImage(new ImagickPixel(), $degrees);
            $imagick->setImageOrientation(0); // 重置圖片方向 (一樣 for iphone & safari)
            $imagickBlob = $imagick->getImageBlob();

            // 儲存 S3 原圖
            $params = [
                'ContentType'  => $imagick->getImageMimeType(),
                'ACL'          => 'public-read',
                'CacheControl' => 'max-age=31536000, public'
            ];
            Storage::disk('s3')->put($fileName, $imagickBlob, $params);
        }

        // 取得圖片寬高
        $geometry = $imagick->getImageGeometry();
        unset($imagick);

        return $geometry;

        // 取得圖片寬高
        // $image = Image::make($file);

        // return [
        //     // 'degrees' => $degrees,
        //     'width'  => $image->width(),
        //     'height' => $image->height(),
        // ];
    }
}
