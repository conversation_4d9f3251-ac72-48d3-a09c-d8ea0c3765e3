<?php
/*
 |--------------------------------------
 |  上傳圖片 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\File;

use Illuminate\Support\Facades\Storage;
use App\Services\Image\GetImageUrlService;
use App\Services\File\GetImageInfoService;
use App\Traits\ApiErrorTrait;
use App\Models\Image;
use Illuminate\Support\Facades\Http;
use Imagick;
use ImagickPixel;

class UploadImageFormService
{
    private $imagick;
    private $imageModel;
    private $getImageUrlService;
    private $getImageInfoService;

    // 允許上傳的圖片格式
    private $limitMimeTypes;
    private $fileName;
    private $options;

    use ApiErrorTrait;

    public function __construct(
        Image $image,
        GetImageUrlService $getImageUrlService,
        GetImageInfoService $getImageInfoService
    ) {
        $this->imagick             = new Imagick;
        $this->imageModel          = $image;
        $this->getImageUrlService  = $getImageUrlService;
        $this->getImageInfoService = $getImageInfoService;
        $this->limitMimeTypes      = config('params.image.mime_types');
    }

    /**
     * 上傳圖片 By Base64
     *
     * @return $result
     */
    public function uploadBase64($data)
    {
        if (!$data) {
            $this->setException('上傳檔案不存在！');
        }

        // use Imagick
        try {
            list($type, $data) = explode(';', $data);
            list(, $data)      = explode(',', $data);
            $imageBlob = base64_decode($data);
            $this->imagick->readImageBlob($imageBlob);
        } catch (\Exception $e) {
            $this->setException('上傳圖片無效！');
        }

        // 產生檔案名稱
        $contentType = $this->imagick->getImageMimeType();
        switch ($contentType) {
            case 'image/png':
                $extension = 'png';

            case 'image/gif':
                $extension = 'gif';

            case 'image/svg+xml':
                $extension = 'svg';

            case 'image/jpeg':
            default:
                $extension = 'jpg';
        }
        $this->makeFileName($extension);

        // 檢查檔案格式
        $this->setOptions($contentType);

        // 先存一份原檔到 S3上面
        try {
            Storage::disk('s3')->put('original/'.$this->fileName, $this->imagick->__toString(), $this->options);
        } catch (\Exception $e) {
            $this->setException('上傳圖片失敗，無法存放至目的地');
        }

        // 取得S3上圖片的資訊，並縮圖
        $data = $this->getImageInfoService->run($this->fileName);
        if (!$data) {
            $this->setException('上傳圖片失敗，請重新上傳');
        }

        // 將圖片寫進資料庫
        return $this->createImageModel($data);
    }

    /**
     * 上傳圖片 By Form表單
     *
     * @return $result
     */
    public function uploadFile($file)
    {
        if (!$file) {
            $this->setException('上傳檔案不存在！');
        }

        if (!$file->isValid()) {
            $this->setException('上傳圖片無效！');
        }

        // 產生檔案名稱
        $extension = $file->getClientOriginalExtension();
        $this->makeFileName($extension);

        // 檢查檔案格式
        $contentType = $file->getClientMimeType();
        $this->setOptions($contentType);

        // 先存一份原檔到 S3上面
        try {
            Storage::disk('s3')->putFileAs('original/', $file, $this->fileName, $this->options);
        } catch (\Exception $e) {
            $this->setException('上傳圖片失敗，無法存放至目的地！');
        }

        // 取得S3上圖片的資訊，並縮圖
        $data = $this->getImageInfoService->run($this->fileName);
        if (!$data) {
            $this->setException('上傳圖片失敗，請重新上傳');
        }

        // 將圖片寫進資料庫
        return $this->createImageModel($data);
    }

    /**
     * 下載 url 照片 轉成 base64
     *
     * @return $result
     */
    public function downloadImageAsBase64($url)
    {
        if (!$url) {
            $this->setException('上傳檔案不存在！');
        }

        $response = Http::get($url);

        if ($response->successful()) {
            $imageContent = $response->body();
            $mimeType = $response->header('Content-Type');

            // 確保是圖片類型
            if (str_starts_with($mimeType, 'image/')) {
                return 'data:' . $mimeType . ';base64,' . base64_encode($imageContent);
            }
        }
        return $this->setException('上傳圖片失敗，請重新上傳');
    }

    /**
     * 產生檔案名稱
     *
     */
    private function makeFileName($extension)
    {
        $this->fileName = md5(microtime()).'_'.sha1(uniqid()).'.'.$extension;
    }

    /**
     * 設定 S3 的上傳參數
     *
     */
    private function setOptions($contentType)
    {
        if (!in_array($contentType, $this->limitMimeTypes)) {
            $this->setException('圖片格式有誤，僅接受圖片類型的檔案上傳！');
        }

        //設定上傳至 s3 的 options
        $this->options = [
            'ContentType'  => $contentType,
            'ACL'          => 'public-read',
            'CacheControl' => 'max-age=31536000, public'
        ];
    }

    /**
     * 將圖片寫進資料庫
     *
     */
    private function createImageModel($data)
    {
        $image = $this->imageModel->create([
            'file_name' => $this->fileName,
            'width'     => $data['width'],
            'height'    => $data['height'],
            'app_env'   => env('APP_ENV'),
        ]);

        return [
            'id'        => $image->id,
            'file_name' => $image->file_name,
            'width'     => $image->width,
            'height'    => $image->height,
            'file_url'  => $this->getImageUrlService->get($image->file_name),
        ];
    }
}
