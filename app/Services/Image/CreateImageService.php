<?php
/*
 |--------------------------------------
 |  新增圖片至資料庫，會先檢查資料庫內是否有相同的圖片
 |--------------------------------------
 |  input Array
    [
        {
            'file_name'     => '檔案名稱',
            'type'          => '圖片的類型 article:論壇文章 comment:回文留言文章  profile:會員頭像 share:分享文',
            'target_id'     => '目標 ID',
            'width'         => '圖片的寬度，允許 NULL or !isset',
            'height'        => '圖片的高度，允許 NULL or !isset'
            'only'          => true 如果設定true的話，另一張就會設定成刪除狀態，可用於頭像之類的服務，預設：false
        }
    ]
 |
 */
namespace App\Services\Image;

use App\Models\Image;
use App\Jobs\File\ReacquisitionImageSize;
use App\Traits\ApiErrorTrait;

class CreateImageService
{
    private $image;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Image $image
    ) {
        $this->image = $image;
    }

    /**
     * 新增圖片
     *
     * @param [type] $data
     * @return void
     */
    public function add($data)
    {
        // 檢查陣列內是否都有 file_name type target_id 這三者
        if (
            !is_array($data) ||
            !isset($data['file_name']) || !$data['file_name'] ||
            !isset($data['type'])      || !$data['type'] ||
            !isset($data['target_id']) || !$data['target_id']
        ) {
            $this->setException('圖片資訊有誤，必須要有相關值');
        }

        // 找出 Image Model
        $image = $this->image->where('file_name', $data['file_name'])
                                    ->where('type', $data['type'])
                                    ->where('target_id', $data['target_id'])
                                    ->first();

        // 找出未設定的 Image Model
        if (!$image) {
            $image = $this->image->where('file_name', $data['file_name'])
                                        ->whereNull('type')
                                        ->whereNull('target_id')
                                        ->first();
        }

        // 新增 Image Model
        if (!$image) {
            $image = clone $this->image;
            $image->app_env = $data['app_env'] ?? env('APP_ENV');
        }

        // 更新資料
        $image->file_name = $data['file_name'];
        $image->type      = $data['type'];
        $image->target_id = $data['target_id'];
        $image->width     = (!$image->width && isset($data['width'])) ? $data['width'] : $image->width;
        $image->height    = (!$image->height && isset($data['height'])) ? $data['height'] : $image->height;
        $image->save();

        $data['id'] = $image->id;

        // 如果有設定only的話，就應該是唯一，將其他的都更新為NULL，並且設定為待刪除狀態
        if (isset($data['only']) && $data['only'] === true) {
            $this->image->where('id', '!=', $image->id)
                        ->where('type', $image->type)
                        ->where('target_id', $image->target_id)
                        ->update([
                            'type'       => NULL,
                            'target_id'  => NULL,
                            'deleted_at' => now()
                        ]);
        }

        // 用 Queue 重新處理圖片的寬高資訊
        if (!$image->width || !$image->height) {
            ReacquisitionImageSize::dispatch($image->id);
        }

        return $data;
    }
}
