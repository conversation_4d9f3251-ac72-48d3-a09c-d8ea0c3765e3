<?php
/*
 |--------------------------------------
 |  檢查圖片及縮圖
 |--------------------------------------
 |
 |
 */

namespace App\Services\Image;

use App\Services\File\GetImageInfoService;

class CheckImageService
{
    private $getImageInfoService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        GetImageInfoService $getImageInfoService
    ) {
        $this->getImageInfoService = $getImageInfoService;
    }

    /**
     * 取得圖片路徑
     * @param Model $image
     * @return $data
     */
    public function run($image)
    {
        // 確認是否有圖檔名
        if (!$image) {
            if (!$image->file_name) {
                $image->delete();
            }
            return false;
        }

        // 取得S3上圖片的資訊
        $data = $this->getImageInfoService->run($image->file_name);
        if (!$data) {
            // $image->delete();
            return false;
        }

        // 更新 Image Model
        $image->width  = $data['width'];
        $image->height = $data['height'];
        $image->save();

        return $data;
    }
}
