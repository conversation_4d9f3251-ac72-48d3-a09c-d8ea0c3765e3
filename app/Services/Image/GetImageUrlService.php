<?php
/*
 |--------------------------------------
 |  產生圖片網址
 |--------------------------------------
 |
 |
 */

namespace App\Services\Image;

class GetImageUrlService
{
    /**
     * 取得圖片路徑
     * @param $fileName 圖片的檔案名稱
     * @param $width 希望取得圖片的寬度
     * @param $height 希望取得圖片的高度
     *
     * @return $string url
     */
    public function get($fileName, $width=null, $height=null)
    {
        $url = config('params.image_url').'/original/'.$fileName;

        if ($width && intval($width) > 0 && $height && intval($height) > 0 ) {
            $url .= '?width='.intval($width).'&height='.intval($height);
        } elseif ($width && intval($width) > 0 ) {
            $url .= '?width='.intval($width);
        } elseif ($height && intval($height) > 0) {
            $url .= '?width=0&height='.intval($height);
        }

        return $url;
    }
}
