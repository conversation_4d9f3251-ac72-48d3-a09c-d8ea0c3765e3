<?php
/*
 |--------------------------------------
 |  刪除圖片，會連同 S3上面的一起刪除掉
 |--------------------------------------
 |
 |  共提供兩種方式刪除照片
 |  1. id
 |  2. file_name
 |
    input Array
    [
        {
            'id'        => '如果有ID 其他就可以不用了',
            'file_name' => '如果有 file_name 其他就可以不用了',
        }
    ]
 |
 */


namespace App\Services\Image;

use App\Models\Image;
use App\Services\File\DeleteFileService;
use App\Traits\ApiErrorTrait;

class DeleteImageService
{
    use ApiErrorTrait;

    private $image;
    private $deleteFileService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Image $image,
        DeleteFileService $deleteFileService
    )
    {
        $this->image             = $image;
        $this->deleteFileService = $deleteFileService;
    }


    /**
     * 刪除圖片
     *
     * @param [type] $imageData
     * @return void
     */
    public function delete($imageData)
    {
        if (isset($imageData['id'])) {
            $image = $this->image->find($imageData['id']);
        } elseif (isset($imageData['file_name'])) {
            $image = $this->image->where('file_name', $imageData['file_name'])->first();
        }

        if (!$image) {
            $this->setException('查不到圖片資訊，無法刪除圖片');
        }

        // 若沒有其他同檔名的圖片，則刪除S3圖檔
        $otherImage = $this->image->withTrashed()
                                    ->where('file_name', $image->file_name)
                                    ->where('id', '!=', $image->id)
                                    ->exists();
        if (!$otherImage) {
            $this->deleteFileService->delete([
                'file_name' => $image->file_name,
                'app_env'   => $image->app_env,
            ]);
        }

        $id = $image->id;
        $image->delete();

        return $id;
    }

}

