<?php
/*
 |--------------------------------------
 |  串接婚展報到系統 Service
 |--------------------------------------
 |
 */

namespace App\Services\Event;

use App\Models\EventReport;
use App\Models\EventExhibition;
use GuzzleHttp\Client;
use Log;

class ImportEventToolService
{
    private $method = 'POST';
    private $report;
    private $eventTool;
    private $exhibition;
    private $result;
    private $client;

    // use CurlTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client
    ) {
        $this->client = $client;
    }

    /**
     * 執行活動表單串接
     *
     * @param EventReport $report
     * @param EventExhibition $exhibition
     * @return content
     */
    public function import(EventReport $report, EventExhibition $exhibition = NULL)
    {
        $this->report     = $report;
        $this->exhibition = $exhibition;
        $this->eventTool  = $exhibition ? $exhibition->eventTool : $report->event->eventTool;

        // 轉換表單欄位
        $data = $this->convertFields();

        // 使用CURL 取得網址內容
        // $apiPath = 'https://event-tools.yzcube.com/api/huashan211218/import-user';
        $apiPath = config('params.event_tools.api_url').'/'.$this->eventTool->path.'/import-user';

        try {
            $resp = $this->client->request($this->method, $apiPath, ['query' => $data]);
        } catch (\Exception $e) {
            $this->logErrorMessage($e->getMessage());
            return false;
        }

        // API Error Log
        if ($resp->getStatusCode() != 200) {
            $this->logErrorMessage('EventTools API Error');
            return false;
        }

        // 儲存結果
        $content = $resp->getBody()->getContents();
        $this->report->import_result = $content;
        $this->report->save();

        // API Respond Status Error Log
        $this->result = json_decode($content);
        if ($this->result->status != 'success') {
            $this->logErrorMessage('EventTools API Respond Status Error');
            return false;
        }
    }

    /**
     * 轉換欄位
     *
     * @return array $data
     */
    private function convertFields()
    {
        $onsite_signup_dates = $this->exhibition ? $this->exhibition->onsite_signup_dates : $this->report->event->onsite_signup_dates;

        return [
            'debug'         => env('APP_DEBUG'),
            'id'            => $this->report->id,
            'name'          => $this->report->name,
            'phone'         => $this->report->phone,
            'email'         => $this->report->email,
            'is_online'     => (strpos($onsite_signup_dates, date('Y-m-d')) === false), // 是否網路報名 1:網路 0:現場
            'checkin_token' => $this->report->qrcode_token,
            'created_at'    => $this->report->created_at,
        ];
    }

    /**
     * 紀錄錯誤訊息
     *
     * @return void
     */
    private function logErrorMessage($message)
    {
        Log::error('串接「'.$this->eventTool->name.'」報到系統錯誤: '.$message, [
            'class'         => class_basename(get_class($this)),
            'event_id'      => $this->report->event_id,
            'report_id'     => $this->report->id,
            'exhibition_id' => $this->exhibition->id ?? '',
            'result'        => $this->result,
        ]);
    }
}
