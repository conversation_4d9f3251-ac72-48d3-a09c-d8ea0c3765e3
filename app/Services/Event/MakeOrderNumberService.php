<?php
/*
 |--------------------------------------
 |  製作訂單編號
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Models\Event;
use App\Models\EventOrder;
use Illuminate\Support\Str;

class MakeOrderNumberService
{
    private $eventOrder;

    public function __construct(
        EventOrder $eventOrder
    ) {
        $this->eventOrder = $eventOrder;
    }

    public function getUniqueNo(Event $event)
    {
        // 產生訂單編號
        $orderNo = 'E'.$event->id.'_'.date('ymdHi').Str::random(2);

        // 驗證唯一值
        while ($this->eventOrder->where('event_id', $event->id)->where('order_no', $orderNo)->exists()) {
            $orderNo = 'E'.$event->id.'_'.date('ymdHi').Str::random(2);
        }

        return $orderNo;
    }
}
