<?php
/*
 |--------------------------------------
 |  匯入婚展多選項目
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Models\EventReport;
use App\Services\Event\ImportEventToolService;
use App\Services\Event\GoogleSheetsService;
use App\Services\Event\PostFormData\ImportLogicService;
use App\Jobs\Event\AddGoogleCalendar;
use Log;

class ImportExhibitionsService
{
    private $importEventToolService;
    private $googleSheetsService;

    public function __construct(
        ImportEventToolService $importEventToolService,
        GoogleSheetsService $googleSheetsService
    ) {
        $this->importEventToolService = $importEventToolService;
        $this->googleSheetsService    = $googleSheetsService;
    }

    public function run(EventReport $report, $exhibitionIds)
    {
        foreach ($exhibitionIds as $exhibitionId) {

            // 找出婚展多選項目
            $exhibition = $report->event->exhibitions()->find($exhibitionId);
            if (!$exhibition) {
                continue;
            }

            try {
                // 串接婚展報到系統
                $this->importEventToolService->import($report, $exhibition);

                // Google試算表-新增一筆紀錄
                $this->googleSheetsService->addRecord($report, $exhibition);

                // Google行事曆-新增與會者邀請事件
                if ($exhibition->use_calendar && strpos($exhibition->onsite_signup_dates, date('Y-m-d')) === false) {
                    AddGoogleCalendar::dispatch($exhibition->calendar->calendar_key, $report->email);
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage(), [
                    'class'         => class_basename(get_class($this)),
                    'event_id'      => $report->event->id,
                    'report_id'     => $report->id,
                    'exhibition_id' => $exhibition->id,
                ]);
            }
        }
    }
}
