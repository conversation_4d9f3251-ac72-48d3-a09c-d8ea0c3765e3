<?php
/*
 |--------------------------------------
 |  串接外站表單-李亭香 Service
 |--------------------------------------
 |  李亭香Li <PERSON> https://lee-cake.com
 |
 */

namespace App\Services\Event\PostFormData\Vendors;

use App\Services\Event\PostFormData\ImportInterface;
use App\Models\EventReport;
use App\Traits\CurlTrait;
use Log;

class LiTingXiangService implements ImportInterface
{
    private $url    = 'https://www.holkee.com/api/anon/saveAmpFormSubmissions?__amp_source_origin=https%3A%2F%2Flee-cake.com';
    private $method = 'POST';

    private $eventReport;
    private $result;

    use CurlTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * 執行活動表單串接
     *
     * @param EventReport $report
     * @return content
     */
    public function import(EventReport $eventReport)
    {
        $this->eventReport = $eventReport;

        // 轉換表單欄位
        $data = $this->convertFields();

        // 使用CURL 取得網址內容
        $this->result = $this->getCurlResponseContent($this->url, $data, $this->method);

        // 紀錄執行匯入表單的回應結果
        $this->logResponseContent();
    }

    /**
     * 轉換欄位
     *
     * @return array $data
     */
    private function convertFields()
    {
        return [
            // <input type="hidden" name="form_id" value="4604">
            'form_id' => 4604,

            // <input type="hidden" name="time_offset" value="TIMEZONE" data-amp-replace="TIMEZONE">
            'time_offset' => -480,

            // <input type="hidden" name="notification" value="0">
            'notification' => 0,

            // 您的姓名
            // <input type="text" name="field10001" placeholder="請輸入內容" required="">
            'field10001' => $this->eventReport->name,

            // 聯絡電話
            // <input type="text" name="field10002" placeholder="請輸入內容" required="">
            'field10002' => $this->eventReport->phone,

            // 請選擇您的需求
            // <select name="field10003" required="" class="user-invalid valueMissing">
            //     <option value="">請選擇</option>
            //     <option value="想要洽詢喜餅試吃 / 優惠 等相關訊息">想要洽詢喜餅試吃 / 優惠 等相關訊息</option>
            //     <option value="想要洽詢彌月試吃 / 優惠 等相關訊息">想要洽詢彌月試吃 / 優惠 等相關訊息</option>
            //     <option value="企業 / 個人客製化或大量訂購事宜">企業 / 個人客製化或大量訂購事宜</option>
            //     <option value="一般詢問或客服問題反映">一般詢問或客服問題反映</option>
            // </select>
            'field10003' => '想要洽詢喜餅試吃 / 優惠 等相關訊息',

            // 請說明您的需求或問題
            // <textarea name="field1583052693891" placeholder="請輸入內容"></textarea>
            'field1583052693891' => '【WeddingDay 好婚市集】申請喜餅試吃名單',

            // 輸入Line ID 或 Email溝通更便捷
            // <input type="email" name="field1583052809057" placeholder="請輸入內容" required="">
            'field1583052809057' => $this->eventReport->email,
        ];
    }

    /**
     * 紀錄執行匯入表單的回應結果
     *
     * @return void
     */
    private function logResponseContent()
    {
        $this->eventReport->import_result = $this->result;
        $this->eventReport->save();

        $resultObj = json_decode($this->result);
        if ($resultObj->status != 'success') {
            // Error Log
            Log::error('串接李亭香表單錯誤: '.$resultObj->message, [
                'class'     => class_basename(get_class($this)),
                'event_id'  => $eventReport->event_id,
                'report_id' => $eventReport->id,
                'result'    => $this->result,
            ]);
        }
    }
}
