<?php
/*
 |--------------------------------------
 |  串接報到系統-台中臻愛婚展 Service
 |--------------------------------------
 |  1. 確認報到系統已有 ImportUsers Command & import-user Api
 |  2. 將已報名的資料，匯入報到系統的DB：529truelove.import_users
 |  3. 至報到系統確執行command：php artisan 529truelove:import-users
 |  4. 加入活動表單串接：wdv3.events.import_service
 |
 */

namespace App\Services\Event\PostFormData\Vendors;

use App\Services\Event\PostFormData\ImportInterface;
use App\Models\EventReport;
use App\Traits\CurlTrait;
use Log;

class Truelove529 implements ImportInterface
{
    private $url = 'https://event-tools.yzcube.com/api/529truelove/import-user';
    private $method = 'POST';
    private $result;

    use CurlTrait;

    /**
     * 執行活動表單串接
     *
     * @param EventReport $report
     * @return content
     */
    public function import(EventReport $eventReport)
    {
        $this->eventReport = $eventReport;

        // 轉換表單欄位
        $data = $this->convertFields();

        // 使用CURL 取得網址內容
        $this->result = $this->getCurlResponseContent($this->url, $data, $this->method);

        // 紀錄執行匯入表單的回應結果
        $this->logResponseContent();
    }

    /**
     * 轉換欄位
     *
     * @return array $data
     */
    private function convertFields()
    {
        return [
            'debug'         => env('APP_DEBUG'),
            'id'            => $this->eventReport->id,
            'name'          => $this->eventReport->name,
            'phone'         => $this->eventReport->phone,
            'email'         => $this->eventReport->email,
            'is_online'     => (strpos($this->eventReport->event->onsite_signup_dates, date('Y-m-d')) === false), // 是否網路報名 1:網路 0:現場
            'checkin_token' => $this->eventReport->qrcode_token,
            'created_at'    => $this->eventReport->created_at,
        ];
    }

    /**
     * 紀錄執行匯入表單的回應結果
     *
     * @return void
     */
    private function logResponseContent()
    {
        $this->eventReport->import_result = $this->result;
        $this->eventReport->save();

        $resultObj = json_decode($this->result);
        if ($resultObj->status != 'success') {
            // Error Log
            Log::error('串接台中臻愛婚展報到系統錯誤: '.$resultObj->message, [
                'class'     => class_basename(get_class($this)),
                'event_id'  => $eventReport->event_id,
                'report_id' => $eventReport->id,
                'result'    => $this->result,
            ]);
        }
    }
}
