<?php
/*
 |--------------------------------------
 | 活動表單串接 邏輯的service
 |--------------------------------------
 |
 */

namespace App\Services\Event\PostFormData;

use App\Services\Event\PostFormData\ImportInterface;
use App\Services\Event\PostFormData\ImportFactory;
use App\Models\EventReport;

class ImportLogicService implements ImportInterface
{
    /** @var : 邏輯實例 */
    private $logic;

    /**
     * ImportLogicService constructor.
     * @param $methodName : class name
     * @throws \ReflectionException
     */
    public function __construct($methodName)
    {
        $this->logic = ImportFactory::factory($methodName, ImportInterface::class);
    }

    /**
     * 執行活動表單串接
     *
     * @param Event $event
     * @param EventReport $eventReport
     * @return content
     */
    public function import(EventReport $eventReport)
    {
        return $this->logic->import($eventReport);
    }
}
