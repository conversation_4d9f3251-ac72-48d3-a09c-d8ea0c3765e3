<?php
/*
 |--------------------------------------
 |  活動表單串接 的工廠模式
 |--------------------------------------
 |
 */

namespace App\Services\Event\PostFormData;

use ReflectionClass;

class ImportFactory
{
    /**
     * import 工廠
     * @param $className : 要實例化的方法
     * @param $args : __construct的參數
     * @param $interface : 規範的interface
     * @return object
     * @throws \ReflectionException
     * @throws \Exception
     */
    public static function factory($className, $interface)
    {
        try {
            $namespace = __NAMESPACE__.'\\Vendors\\' ;
            $reflection = new ReflectionClass($namespace.$className); //反射new class
            $reflection = $reflection->newInstanceArgs();

            if ($reflection instanceof $interface) return $reflection; //必須符合規定的Interface
            throw new \Exception('not instance of Interface');
        } catch (\ReflectionException $e) {
            throw $e;
        }
    }
}
