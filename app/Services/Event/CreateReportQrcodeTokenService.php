<?php
/*
 |--------------------------------------
 |  建立活動報表的QRcode權杖
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Traits\Auth\CreateTokenTrait;
use App\Models\Event;

class CreateReportQrcodeTokenService
{
    use CreateTokenTrait;

    public function getUniqueToken(Event $event)
    {
        // 建立QRcode權杖
        $token = $this->getNewToken();

        // 驗證唯一值
        while ($event->reports()->where('qrcode_token', $token)->exists()) {
            $token = $this->getNewToken();
        }

        return $token;
    }
}
