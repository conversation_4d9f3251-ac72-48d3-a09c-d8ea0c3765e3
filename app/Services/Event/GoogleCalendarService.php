<?php
/*
 |--------------------------------------
 |  google試算表Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Models\EventCalendar;
use Carbon\Carbon;
use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Event;

class GoogleCalendarService
{
    private $event;
    private $carbon;
    private $eventCalendar;
    private $calendarId;
    private $service;

    public function __construct(
        Google_Service_Calendar_Event $event,
        Carbon $carbon,
        EventCalendar $eventCalendar
    ) {
        $this->event         = $event;
        $this->carbon        = $carbon;
        $this->eventCalendar = $eventCalendar;
        $this->calendarId    = env('GOOGLE_SERVICE_ACCOUNT_EMAIL');

        $KEY_FILE_LOCATION = base_path(env('GOOGLE_SERVICE_ACCOUNT_JSON_LOCATION'));

        $client = new Google_Client();
        $client->setAuthConfig($KEY_FILE_LOCATION);
        $client->setScopes([Google_Service_Calendar::CALENDAR]);

        $this->service = new Google_Service_Calendar($client);
    }

    /**
     * 儲存google行事曆
     *
     */
    public function save($data)
    {
        // 更新活動表單的行事曆
        $model = $this->eventCalendar->updateOrCreate([
            'target_type' => $data['target_type'],
            'target_id'   => $data['target_id'],
        ], [
            'summary'     => $data['summary'],
            'location'    => $data['location'],
            'description' => $data['description'],
            'start_at'    => $data['start_at'],
            'end_at'      => $data['end_at'],
        ]);

        // 取得google行事曆
        if ($model->calendar_key) {
            $event = $this->service->events->get($this->calendarId, $model->calendar_key);

        // 初始化google行事曆
        } else {
            $event = clone $this->event;
        }

        $event->summary                 = $model->summary;
        $event->location                = $model->location;
        $event->description             = $model->description;
        $event->visibility              = 'private';
        $event->guestsCanSeeOtherGuests = false;
        $event->attendees               = $event->getAttendees();
        $event->start = [
            'dateTime' => $this->carbon->createFromFormat('Y-m-d H:i:s', $model->start_at)->toRfc3339String(),
            'timeZone' => config('app.timezone'),
        ];
        $event->end = [
            'dateTime' => $this->carbon->createFromFormat('Y-m-d H:i:s', $model->end_at)->toRfc3339String(),
            'timeZone' => config('app.timezone'),
        ];
        // $event->reminders = [
        //     'useDefault' => false,
        //     'overrides'  => [
        //         ['method' => 'email', 'minutes' => 60 * 24],
        //         ['method' => 'popup', 'minutes' => 30],
        //     ],
        // ];

        // 更新google行事曆
        if ($model->calendar_key) {
            $event = $this->service->events->update($this->calendarId, $model->calendar_key, $event);

        // 新增google行事曆
        } else {
            $event = $this->service->events->insert($this->calendarId, $event);

            $model->calendar_key = $event->getId();
            $model->save();
        }
    }

    /**
     * 新增與會者邀請事件
     *
     * @param $calendarEventId Google行事曆編號
     * @param $email 與會者Email
     * @return void
     */
    public function addAttendee($calendarEventId, $email)
    {
        $event = $this->service->events->get($this->calendarId, $calendarEventId);

        $attendees   = $event->getAttendees();
        $attendees[] = ['email' => $email];

        $event->attendees        = $attendees;
        $event->attendeesOmitted = false;

        $this->service->events->update($this->calendarId, $calendarEventId, $event);
    }

    /**
     * 刪除google行事曆
     *
     * @param EventCalendar $model 活動表單Google行事曆
     * @return void
     */
    public function delete(EventCalendar $model)
    {
        $this->service->events->delete($this->calendarId, $model->calendar_key);

        $model->delete();
    }
}
