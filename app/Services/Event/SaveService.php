<?php
/*
 |--------------------------------------
 |  活動報名儲存表單Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Repositories\UserRepository;
use App\Repositories\EventStoreRepository;
use App\Services\Event\CreateReportQrcodeTokenService;
use App\Services\Event\CreateOrderService;
use App\Services\Event\ImportExhibitionsService;
use App\Services\Event\ImportEventToolService;
use App\Services\Event\GoogleSheetsService;
use App\Services\Event\PostFormData\ImportLogicService;
use App\Services\Mail\Auth\EditPasswordService;
use App\Services\Mail\Event\CompletedService;
use App\Jobs\Event\AddGoogleCalendar;
use App\Models\EventEmailVerified;
use App\Models\EventStore;
use App\Models\Store;
use App\Models\Event;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\CreateTokenTrait;
use Log;
use stdClass;

class SaveService
{
    private $userRepository;
    private $createReportQrcodeTokenService;
    private $createOrderService;
    private $importExhibitionsService;
    private $importEventToolService;
    private $googleSheetsService;
    private $completedService;
    private $eventEmailVerified;
    private $editPasswordService;

    use ApiErrorTrait;
    use CreateTokenTrait;

    public function __construct(
        UserRepository $userRepository,
        CreateReportQrcodeTokenService $createReportQrcodeTokenService,
        CreateOrderService $createOrderService,
        ImportExhibitionsService $importExhibitionsService,
        ImportEventToolService $importEventToolService,
        GoogleSheetsService $googleSheetsService,
        CompletedService $completedService,
        EventEmailVerified $eventEmailVerified,
        EditPasswordService $editPasswordService
    ) {
        $this->userRepository                 = $userRepository;
        $this->createReportQrcodeTokenService = $createReportQrcodeTokenService;
        $this->createOrderService             = $createOrderService;
        $this->importExhibitionsService       = $importExhibitionsService;
        $this->importEventToolService         = $importEventToolService;
        $this->googleSheetsService            = $googleSheetsService;
        $this->completedService               = $completedService;
        $this->eventEmailVerified             = $eventEmailVerified;
        $this->editPasswordService            = $editPasswordService;
    }

    /**
     * 儲存表單
     *
     */
    public function run($request, $event)
    {
        // 驗證表單基本欄位
        $content = json_decode($request['content']);
        if (!$content OR empty($content->event_name) OR empty($content->event_email) OR empty($content->event_phone)) {
            $this->setException('活動報名表單的基本欄位有誤！');
        }

        // 更新既有會員資訊
        $user = $this->userRepository->getFirst(['email' => $content->event_email]);
        if (!$user) {
            $user = $this->userRepository->getModel();
        }
        $user->name      = $user->name ?: $content->event_name;
        $user->real_name = $user->real_name ?: $content->event_name;
        if (!$user->phone) {
            $user->phone          = $content->event_phone;
            $user->phone_legalize = 0;
        }
        if ($user->id) {
            $user->save();
        }

        // 儲存表單
        $report = $event->reports()->create([
            'user_id'      => $user->id,
            'is_new_user'  => $user->wasRecentlyCreated,
            'need_invite'  => (!$user->id),
            'qrcode_token' => $this->createReportQrcodeTokenService->getUniqueToken($event),
            'name'         => $content->event_name,
            'email'        => $content->event_email,
            'phone'        => $content->event_phone,
            'wedding_date' => !empty($content->wedding_date) ? $content->wedding_date : NULL,
            'content'      => $request['content'],
            'created_at'   => date('Y-m-d H:i:s'),
        ]);

        // 新增活動訂單
        if ($event->use_payment) {
            $this->createOrderService->run($report, $request);

            // 若付款失敗 ErrorCode 401：前台需重新取得TapPay的付款憑證，用於下一筆新的訂購單
            if ($report->order->payment_status == 'fail') {
                $this->setException('訂單線上付款失敗: '.$report->order->logPayment->msg, 4010);
            }
        }

        // 匯入婚展多選項目
        if ($request['exhibitions']) {
            $this->importExhibitionsService->run($report, $request['exhibitions']);
        }

        // WeddingDay 活動報名完成信
        $this->completedService->sendMail($event, $report);

        try {
            // 串接婚展報到系統
            if ($event->event_tool_id) {
                $this->importEventToolService->import($report);
            }

            // 活動表單串接
            if ($event->import_service) {
                $importLogicService = new ImportLogicService($event->import_service);
                $importLogicService->import($report);
            }

            // Google試算表-新增一筆紀錄 (因訂購表單寫入行數不固定，所以改由排成新增紀錄)
            if (!$event->use_payment) {
                $this->googleSheetsService->addRecord($report);

                // 標記已新增
                $report->add_google_sheet = 1;
                $report->save();
            }

            // Google行事曆-新增與會者邀請事件
            if ($event->use_calendar && strpos($event->onsite_signup_dates, date('Y-m-d')) === false) {
                AddGoogleCalendar::dispatch($event->calendar->calendar_key, $report->email);
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage(), [
                'class'     => class_basename(get_class($this)),
                'event_id'  => $event->id,
                'report_id' => $report->id,
            ]);
        }

        // 活動表單被填寫商店通知信
        $this->processEventNotify($event, json_decode($request['content'], true));

        // 若有信箱登入驗證碼，則寄出修改密碼信
        $emailVerified = $this->eventEmailVerified->where('event_id', $event->id)
                                                    ->where('email', $content->event_email)
                                                    ->whereNotNull('code')
                                                    ->count();
        if ($emailVerified) {
            // 產生 pwd_token
            $pwd_token = $this->getNewToken();
            $user->pwd_tokens()->create([
                'target_id'   => $user->id,
                'type'        => 'user_password',
                'token'       => $pwd_token,
                'deadline_at' => now()->addDay(),
            ]);

            // 好婚市集修改密碼信
            $this->editPasswordService->sendMail($user, $pwd_token);
        }

        // 將信箱登入驗證碼失效
        $this->eventEmailVerified->where('event_id', $event->id)
                                    ->where('email', $content->event_email)
                                    ->delete();

        $result = new stdClass;
        $result->user   = $user;
        $result->report = $report;

        return $result;
    }

    /**
     * 處理活動通知寄信給商家
     *
     * @param Event $event
     * @param array $customData
     * @return viod
     */
    private function processEventNotify(Event $event, array $customData)
    {
        $getAllStore = $event->store;

        foreach ($getAllStore as $store) {

            $data = [
                'storeName'     => $store->name, // 商店名稱
                'eventName'     => $customData['event_name'], // 訂購者名稱
                'eventDate'     => date("Y-m-d H:i:s"), // 訂購時間
                'spreadsheetId' => $event->spreadsheet_id,
                'sheetId'       => $event->sheet_id,
                'storeId'       => $store->id // 商店ID
            ];

            // 判斷是否有開啟 Email 通知
            if ((!$store->subscription OR $store->subscription->email_event) && $store->email) {
                // 寄出 Email 通知
                $this->eventNotify($event->id, $store->id, 'email', [$store->email], $data);
            }

            // 判斷是否有開啟 Line 通知
            if ($store->accounts) {

                $setLineId = [];
                $getLineArray = $store->accounts->toArray();
                $lineArray = array_column($getLineArray, 'line_id', 'id');
                array_map(function ($val) use (&$setLineId, $lineArray) {
                    if ($val['line_id'] && $val['pivot']['line_event']) {
                        $setLineId[] = $lineArray[$val['pivot']['store_user_id']];
                    }
                }, $getLineArray);

                if ($setLineId) {
                    // 寄出 line 通知
                    $this->eventNotify($event->id, $store->id, 'line', $setLineId, $data);
                }
            }
        }
    }

    /**
     * 活動通知信 - 門市試吃/宅配試吃
     *
     * @param int $eventId
     * @param int $storeId
     * @param string $type
     * @param array $sendArray
     * @param array $data
     * [
     *  storeName 商店名稱
     *  eventName 訂購名字
     *  eventDate 訂購日期
     *  spreadsheetId 表單 sheet ID
     *  sheetId 表單 ID
     *  storeId 商店ID
     * ]
     * @return viod
     */
    private function eventNotify(
        int $eventId,
        int $storeId,
        string $type,
        array $sendArray,
        array $data
    ) {
        // 判斷是否有活動表單關聯
        $getType = (new EventStoreRepository)->getType($storeId, $eventId);

        switch ($getType) {
            case EventStoreRepository::SHOP_TASTING:
                switch ($type) {
                    case 'email':
                        $this->completedService->sendEventMail(EventStoreRepository::SHOP_TASTING, $sendArray[0], $data);

                        break;
                    case 'line':

                        $this->completedService->sendEventLine(EventStoreRepository::SHOP_TASTING, $sendArray, $data);
                        break;
                    default:
                        break;
                }
                break;

            case EventStoreRepository::DELIVERY_TASTING:
                switch ($type) {
                    case 'email':
                        $this->completedService->sendEventMail(EventStoreRepository::DELIVERY_TASTING, $sendArray[0], $data);

                        break;
                    case 'line':
                        $this->completedService->sendEventLine(EventStoreRepository::DELIVERY_TASTING, $sendArray, $data);

                        break;
                    default:
                        break;
                }
                break;
        }
    }
}
