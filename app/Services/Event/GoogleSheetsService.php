<?php
/*
 |--------------------------------------
 |  Google試算表Service
 |--------------------------------------
 | https://github.com/kawax/laravel-google-sheets
 |
 */

namespace App\Services\Event;

use App\Models\Event;
use App\Models\EventReport;
use App\Models\EventExhibition;
use App\Traits\GoogleSheetTrait;
use Sheets;

class GoogleSheetsService
{
    private $spreadsheet;
    private $sheet;
    private $columnArray = [];
    private $fileArray   = [];

    use GoogleSheetTrait;

    /**
     * 初始化Google試算表
     *
     */
    public function initGoogleSheets($spreadsheetId, $sheetId, $sheetTitle)
    {
        $this->spreadsheet = Sheets::spreadsheet($spreadsheetId);
        $sheetTitle = '【勿動】'.$sheetTitle;

        // 有找到特定名稱的工作表
        $sheetList = $this->spreadsheet->sheetList();
        if (in_array($sheetTitle, $sheetList)) {
            $sheetId = array_search($sheetTitle, $sheetList);

        // 沒有sheetId 或 找不到sheetId的工作表，則新增工作表
        } elseif (is_null($sheetId) OR !array_key_exists($sheetId, $sheetList)) {
            $this->spreadsheet->addSheet($sheetTitle);
            $sheetId = array_key_last($this->spreadsheet->sheetList());

        // 更新sheetId的工作表標題
        } else {
            $this->updateSheetTitle($spreadsheetId, $sheetId, $sheetTitle);
        }

        // vendor/revolution/laravel-google-sheets/src/Sheets.php
        $this->sheet = $this->spreadsheet->sheetById($sheetId);

        return $sheetId;
    }

    /**
     * 工作表更名
     *
     */
    private function updateSheetTitle($spreadsheetId, $sheetId, $sheetTitle)
    {
        $body = new \Google_Service_Sheets_BatchUpdateSpreadsheetRequest(
            [
                'requests' => [
                    'updateSheetProperties' => [
                        'properties' => [
                            'sheetId' => $sheetId,
                            'title'   => $sheetTitle,
                        ],
                        'fields' => 'title',
                    ],
                ],
            ]
        );

        $this->spreadsheet->getService()->spreadsheets->batchUpdate($spreadsheetId, $body);
    }

    /**
     * 更新試算表的欄位標題
     *
     */
    public function updateHead(Event $event)
    {
        // 初始化Google試算表，並記錄工作表編號
        $sheetId = $this->initGoogleSheets($event->spreadsheet_id, $event->sheet_id, $event->path);
        if ($sheetId != $event->sheet_id) {
            $event->sheet_id = $sheetId;
            $event->save();
        }

        // 實作更新試算表的欄位標題
        $this->doUpdateSheetHeadByEvent($event);
    }

    /**
     * 更新試算表的欄位標題 For 婚展多選項目
     *
     */
    public function updateExhibitionHead(EventExhibition $exhibition)
    {
        // 初始化Google試算表，並記錄工作表編號
        $sheetId = $this->initGoogleSheets($exhibition->event->spreadsheet_id, $exhibition->sheet_id, $exhibition->name);
        if ($sheetId != $exhibition->sheet_id) {
            $exhibition->sheet_id = $sheetId;
            $exhibition->save();
        }

        // 實作更新試算表的欄位標題
        $this->doUpdateSheetHeadByEvent($exhibition->event);
    }

    /**
     * 實作更新試算表的欄位標題
     *
     * @return array $data
     */
    private function doUpdateSheetHeadByEvent(Event $event)
    {
        // 表單欄位標題
        if ($event->use_payment) {
            $headers = ['報名編號', '付款狀態', '訂購商品', '品項', '訂購數量', '品項小計(售價)', '訂購品項的上傳照', '建立時間', '訂單編號', '銀行訂單編號', '商品小計(售價)', '優惠代碼', '優惠代碼折扣金額', '額外費用', '訂單總額'];
        } else {
            $headers = ['報名編號', '建立時間'];
        }

        $columns = json_decode($event->columns);
        $columns = collect($columns)->sortBy('serial_number');
        foreach ($columns as $column) {

            // 排除非表單欄位
            if (in_array($column->format, ['heading', 'subheading', 'info', 'image', 'product', 'payment'])) {
                continue;
            }

            // 若為上傳檔案
            if ($column->format == 'file') {
                $this->fileArray[] = $column->key;
            }

            // 欄位標題
            $this->columnArray[$column->key] = $column->name;
            $headers[] = $column->name;
        }

        // 若有使用QRcode，則顯示Token
        if ($event->use_qrcode) {
            $headers[] = 'QRcode Token';
        }

        // 更新試算表
        $this->sheet->range('1:1')->clear();
        $this->sheet->range('A1')->update([$headers]);
    }

    /**
     * 新增一筆紀錄
     *
     */
    public function addRecord(EventReport $report, EventExhibition $exhibition = NULL)
    {
        // 更新試算表的欄位標題
        if ($exhibition) {
            $this->updateExhibitionHead($exhibition);
        } else {
            $this->updateHead($report->event);
        }

        // 訂單資訊
        if ($report->order) {

            // 訂購品項
            $items = $report->order->orderItems()->get();
            foreach ($items as $key => $item) {
                $data = [
                    $report->id, // 報名編號
                    $report->order->present()->paymentStatusLabel, // 付款狀態
                    $item->product_name, // 訂購商品
                    $item->item_name, // 品項
                    $item->quantity, // 訂購數量
                    $item->amount, // 品項小計(售價)
                    $item->images->map(function ($image) {
                        return config('params.file_url').'/original/'.$image['file_name'];
                    })->implode('、'), // 訂購品項的上傳照
                ];

                // 如果訂購兩件品項以上，則後面欄位忽略
                if ($key) {
                    $this->appendRecordWithGoogleSheet($this->sheet, [$data]);
                    continue;
                }

                $sellingSubtotal = 0; // 商品小計(售價)
                $couponDiscount  = ''; // 優惠代碼折扣金額
                $extraFeeArray   = []; // 額外費用
                $detailList = $report->order->present()->detailList;
                foreach ($detailList as $detail) {
                    if ($detail['key'] == 'subtotal') {
                        $sellingSubtotal += $detail['price'];
                    } elseif ($detail['key'] == 'order') {
                        $sellingSubtotal -= $detail['price'];
                    } elseif ($detail['key'] == 'coupon') {
                        $couponDiscount = $detail['price'];
                    } else {
                        $extraFeeArray[] = $detail['name'].'：'.$detail['price'];
                    }
                }

                $temp = [
                    $report->created_at, // 建立時間
                    $report->order->order_no, // 訂單編號
                    $report->order->logPayment->bank_transaction_id ?? '', // 銀行訂單編號
                    $sellingSubtotal, // 商品小計(售價)
                    $report->getCouponCode(), // 優惠代碼
                    $couponDiscount, // 優惠代碼折扣金額
                    implode('、', $extraFeeArray), // 額外費用
                    $report->order->amount, // 訂單總額
                ];
                $data = array_merge($data, $temp, $this->getReportColumnDataArray($report)); // 表單欄位資料陣列
                $this->appendRecordWithGoogleSheet($this->sheet, [$data]);
            }

        // 一般表單資料
        } else {
            $data = [
                $report->id, // 報名編號
                $report->created_at, // 建立時間
            ];
            $data = array_merge($data, $this->getReportColumnDataArray($report)); // 表單欄位資料陣列
            $this->appendRecordWithGoogleSheet($this->sheet, [$data]);
        }
    }

    /**
     * 取得表單欄位資料陣列
     *
     * @return array $data
     */
    private function getReportColumnDataArray($report)
    {
        // 取得欄位架構
        $result = [];
        foreach ($this->columnArray as $key => $value) {
            $result[$key] = '';
        }

        // 寫入欄位資料
        $content = json_decode($report->content);
        foreach ($content as $key => $value) {

            // 若為上傳檔案
            if (in_array($key, $this->fileArray)) {
                $filePath = env('APP_DEBUG') ? '/wdv3-dev/event/'.$report->event->path.'/' : '/wdv3/event/'.$report->event->path.'/';
                $result[$key] = config('params.file_url').$filePath.$value;
                continue;
            }

            // 排除非表單欄位
            if (array_key_exists($key, $this->columnArray)) {
                $result[$key] = is_array($value) ? implode('、', $value) : $value;
            }
        }

        // 移除索引值
        $result = array_values($result);

        // 若有使用QRcode，則顯示Token
        if ($report->event->use_qrcode) {
            $result[] = $report->qrcode_token;
        }

        return $result;
    }
}
