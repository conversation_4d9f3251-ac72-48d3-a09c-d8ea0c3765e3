<?php
/*
 |--------------------------------------
 |  新增活動訂單
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Models\EventReport;
use App\Models\EventOrderItem;
use App\Services\Event\MakeOrderNumberService;
use App\Services\Image\CreateImageService;
use App\Services\Payment\TapPay\PaymentHandle;
use App\Services\Event\CreateInvoiceService;

class CreateOrderService
{
    private $eventOrderItem;
    private $makeOrderNumberService;
    private $createImageService;
    private $paymentHandle;
    private $createInvoiceService;

    public function __construct(
        EventOrderItem $eventOrderItem,
        MakeOrderNumberService $makeOrderNumberService,
        CreateImageService $createImageService,
        PaymentHandle $paymentHandle,
        CreateInvoiceService $createInvoiceService
    ) {
        $this->eventOrderItem         = $eventOrderItem;
        $this->makeOrderNumberService = $makeOrderNumberService;
        $this->createImageService     = $createImageService;
        $this->paymentHandle          = $paymentHandle;
        $this->createInvoiceService   = $createInvoiceService;
    }

    public function run(EventReport $eventReport, $request)
    {
        // 活動表單
        $event = $eventReport->event;

        // 製作訂單編號
        $orderNo = $this->makeOrderNumberService->getUniqueNo($event);

        // 新增活動訂單
        $orderList  = $request['orderList'];
        $eventOrder = $eventReport->order()->create([
            'event_id'           => $event->id,
            'order_no'           => $orderNo,
            'amount'             => $orderList['total'],
            'invoice_setting_id' => $event->invoice_setting_id,
            'seller_name'        => $event->seller_name,
            'seller_ubn'         => $event->seller_ubn,
        ]);

        // 紀錄活動訂單品項
        foreach ($orderList['items'] as $key => $item) {
            $item['order_no'] = $orderNo;
            $eventOrderItem = $this->eventOrderItem->create($item);

            // 更新上傳的圖檔的來源
            foreach ($orderList['images'][$key] as $image) {
                $this->createImageService->add([
                    'file_name' => $image,
                    'type'      => 'event_order_item',
                    'target_id' => $eventOrderItem->id,
                ]);
            }
        }

        // 新增活動訂單明細-商品小計
        $eventOrder->details()->create([
            'key'         => 'subtotal',
            'is_positive' => 1,
            'price'       => $orderList['subtotal'],
        ]);

        // 新增活動訂單明細-訂單折扣
        if ($orderList['discount']['order']) {
            $eventOrder->details()->create([
                'key'         => 'order',
                'is_positive' => 0,
                'price'       => $orderList['discount']['order'],
            ]);
        }

        // 新增活動訂單明細-優惠代碼折扣
        if ($orderList['discount']['coupon']) {
            $eventOrder->details()->create([
                'key'         => 'coupon',
                'name'        => '優惠代碼折扣 ('.$request['coupon_code'].')',
                'is_positive' => 0,
                'price'       => $orderList['discount']['coupon'],
            ]);
        }

        // 新增活動訂單明細-額外費用
        foreach ($orderList['extra_fees'] as $extraFee) {
            $eventOrder->details()->create([
                'key'         => 'extra_fee',
                'name'        => $extraFee['name'],
                'is_positive' => $extraFee['is_positive'],
                'price'       => $extraFee['price'],
            ]);
        }

        // TapPay 線上支付
        $this->paymentHandle->handle('event_order', [
            'prime'       => $request['prime'],
            'eventReport' => $eventReport,
        ]);

        // 付款失敗直接退出
        $eventOrder = $eventReport->order;
        if (!in_array($eventOrder->payment_status, ['success', 'unnecessary'])) {
            return;
        }

        // 新增優惠卷使用紀錄
        $coupon = $request['coupon'];
        if ($coupon) {
            $data = [
                'event_id'        => $event->id,
                'event_report_id' => $eventReport->id,
                'used_at'         => now(),
            ];

            // 單組通用代碼
            if ($coupon->type == 'single') {
                $coupon->single()->create($data);

            // 多組獨立代碼
            } else {
                $coupon->multiple()->whereRaw('BINARY `code` = ?', substr($request['coupon_code'], 3))
                                    ->unused()
                                    ->update($data);
            }

            // 更新使用數量
            $coupon->used_count = $coupon->{$coupon->type}()->used()->count();
            $coupon->save();
        }

        // 開立發票
        $this->createInvoiceService->run($eventReport, $request['invoice']);
    }
}
