<?php
/*
 |--------------------------------------
 |  活動訂單開立發票
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use App\Models\EventReport;
use App\Services\Payment\EzPay\InvoiceHandle;
use App\Services\Admin\Invoice\SaveService as AdminInvoiceSaveService;
use App\Jobs\Invoice\AddGoogleSheets;
use Log;

class CreateInvoiceService
{
    private $invoiceHandle;
    private $adminInvoiceSaveService;

    public function __construct(
        InvoiceHandle $invoiceHandle,
        AdminInvoiceSaveService $adminInvoiceSaveService
    ) {
        $this->invoiceHandle           = $invoiceHandle;
        $this->adminInvoiceSaveService = $adminInvoiceSaveService;
    }

    public function run(EventReport $eventReport, $invoiceRequest)
    {
        // 活動表單 & 活動訂單
        $event          = $eventReport->event;
        $eventOrder     = $eventReport->order;
        $invoiceSetting = $eventOrder->invoiceSetting;
        $total          = $eventOrder->amount;

        // 無需開立發票 (免費訂單)
        if (!$total) {
            return;
        }

        // 活動訂單品項
        $items = [];
        foreach ($eventOrder->orderItems as $orderItem) {
            $price   = $orderItem->original_price ?: $orderItem->selling_price;
            $items[] = [
                'name'     => $orderItem->product_name.'：'.$orderItem->item_name,
                'price'    => $price,
                'quantity' => $orderItem->quantity,
                'amount'   => $price * $orderItem->quantity,
            ];
        }
        // 活動訂單明細
        foreach ($eventOrder->present()->detailList as $detail) {
            if ($detail['key'] == 'subtotal') {
                continue;
            }
            $price   = $detail['price'] * ($detail['is_positive'] ? 1 : -1);
            $items[] = [
                'name'     => $detail['name'],
                'price'    => $price,
                'quantity' => 1,
                'amount'   => $price,
            ];
        }

        // 銷售額(未稅)
        $sales = round($total / 1.05);

        // 開立發票內容欄位
        $invoiceData = array_merge(
            json_decode($invoiceRequest, true), [
                'order_no' => $eventOrder->order_no, // 訂單編號
                'items'    => json_encode($items), // 品項明細
                'note'     => $eventOrder->logPayment->creditCard->last_four ?? '', // 開立統一發票時，於發票備註欄載明簽帳卡號末四碼
                'sales'    => $sales, // 銷售額(未稅)
                'tax'      => $total - $sales, // 稅額
                'total'    => $total, // 發票金額
        ]);

        // 先儲存發票資訊
        $eventOrder->invoice_data = $invoiceData;

        // 無需開立發票
        if (!$event->need_invoice) {
            $eventOrder->save();
            return;
        }

        // 需要開立發票，則發票狀態預設為尚未開立
        $eventOrder->invoice_status = 'pending';
        $eventOrder->save();

        // 需要開立發票，但不立即開立發票
        if (!$event->invoice_now) {
            return;
        }

        // 商家發票設定錯誤
        if (!$invoiceSetting) {
            Log::error('找不到商家的發票設定！', [
                'class'     => class_basename(get_class($this)),
                'event_id'  => $event->id,
                'report_id' => $eventReport->id,
                'order_id'  => $eventOrder->id,
            ]);
            return;
        }

        // ezPay 開立電子發票
        $invoiceResult  = $this->invoiceHandle->handle('invoice_issue', [
            'merchantOrderNo' => $eventOrder->order_no,
            'invoiceSetting'  => $invoiceSetting,
            'data'            => $invoiceData,
        ]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            Log::error('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message, [
                'class'              => class_basename(get_class($this)),
                'event_id'           => $event->id,
                'report_id'          => $eventReport->id,
                'order_id'           => $eventOrder->id,
                'invoice_setting_id' => $invoiceSetting->id,
            ]);
            return;
        }

        // 更新活動訂單的發票狀態
        $eventOrder->invoice_status = 'success';
        $eventOrder->save();

        // 儲存發票記錄
        $invoice = $this->adminInvoiceSaveService->createInvoice($invoiceSetting, $invoiceData, $invoiceResult->Result);

        // 發票管理-Google試算表-新增紀錄 use Job
        if ($invoiceSetting->spreadsheet_id) {
            AddGoogleSheets::dispatch($invoice);
        }
    }
}
