<?php
/*
 |--------------------------------------
 |  建立活動報表的QRcode
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event;

use SimpleSoftwareIO\QrCode\Facades\QrCode;

class CreateReportQrcodeService
{
    public function generate($content, $useBsae64 = false)
    {
        $qrCode = QrCode::size(250)
                    // ->gradient(251, 98, 84, 255, 50, 90, 'diagonal')
                    // ->eyeColor(0, 251, 98, 84, 251, 98, 84)
                    // ->eyeColor(1, 251, 98, 84, 251, 98, 84)
                    // ->eyeColor(2, 251, 98, 84, 251, 98, 84)
                    ->margin(2)
                    ->backgroundColor(255, 255, 255)
                    ->errorCorrection('H')
                    ->format('png')
                    ->merge(config('params.file_url').'/wedding-icon/email/WDB.png', .25, true)
                    ->generate($content);

        return $useBsae64 ? base64_encode($qrCode) : $qrCode;
    }
}
