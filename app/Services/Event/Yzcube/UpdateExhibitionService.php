<?php
/*
 |--------------------------------------
 |  更新婚展多選項目 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event\Yzcube;

use App\Models\Event;
use App\Services\Event\GoogleSheetsService;
use App\Services\Event\GoogleCalendarService;

class UpdateExhibitionService
{
    private $googleSheetsService;
    private $googleCalendarService;

    public function __construct(
        GoogleSheetsService $googleSheetsService,
        GoogleCalendarService $googleCalendarService
    ) {
        $this->googleSheetsService   = $googleSheetsService;
        $this->googleCalendarService = $googleCalendarService;
    }

    /**
     * 更新婚展多選項目
     *
     */
    public function run(Event $event, $data)
    {
        // 先刪除既有婚展多選項目
        $event->exhibitions()->delete();

        // 儲存婚展多選項目
        foreach ($data as $key => $item) {
            $exhibition = $event->exhibitions()
                                ->withTrashed()
                                ->updateOrCreate([
                                    'id' => $item->exhibition_id ?: NULL,
                                ],[
                                    'event_tool_id'       => $item->tool_id,
                                    'name'                => $item->name,
                                    'expired_at'          => $item->expired_at,
                                    'onsite_signup_dates' => $item->onsite_signup_dates,
                                    'use_calendar'        => $item->use_calendar,
                                    'sequence'            => $key + 1,
                                    'deleted_at'          => NULL,
                                ]);

            try {
                // google試算表-更新試算表的欄位標題
                $this->googleSheetsService->updateExhibitionHead($exhibition);
            } catch (\Exception $e) {
                $this->setException('Google試算表編輯錯誤，請正確輸入試算表ID，以及試算表的編輯權限！'.$e->getMessage());
            }

            try {
                // google行事曆-儲存
                if ($exhibition->use_calendar) {
                    $this->googleCalendarService->save([
                        'target_type' => 'event_exhibition',
                        'target_id'   => $exhibition->id,
                        'summary'     => $item->calendar_summary,
                        'location'    => $item->calendar_location,
                        'description' => $item->calendar_description,
                        'start_at'    => $item->calendar_start,
                        'end_at'      => $item->calendar_end,
                    ]);

                // google行事曆-刪除
                } elseif ($exhibition->calendar) {
                    $this->googleCalendarService->delete($exhibition->calendar);
                }
            } catch (\Exception $e) {
                $this->setException('Google行事曆儲存錯誤，請確認行事曆欄位是否輸入正確！'.$e->getMessage());
            }
        }
    }
}
