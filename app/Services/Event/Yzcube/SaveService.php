<?php
/*
 |--------------------------------------
 |  活動報名儲存表單Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event\Yzcube;

use App\Services\Event\GoogleSheetsService;
use App\Services\Event\GoogleCalendarService;
use App\Services\Event\Yzcube\UpdateProductService;
use App\Services\Event\Yzcube\UpdateExhibitionService;
use App\Services\Image\CreateImageService;
use App\Services\Tools\Content\EncodeService;
use App\Traits\ApiErrorTrait;

class SaveService
{
    private $googleSheetsService;
    private $googleCalendarService;
    private $updateProductService;
    private $updateExhibitionService;
    private $createImageService;
    private $encodeService;

    use ApiErrorTrait;

    public function __construct(
        GoogleSheetsService $googleSheetsService,
        GoogleCalendarService $googleCalendarService,
        UpdateProductService $updateProductService,
        UpdateExhibitionService $updateExhibitionService,
        CreateImageService $createImageService,
        EncodeService $encodeService
    ) {
        $this->googleSheetsService     = $googleSheetsService;
        $this->googleCalendarService   = $googleCalendarService;
        $this->updateProductService    = $updateProductService;
        $this->updateExhibitionService = $updateExhibitionService;
        $this->createImageService      = $createImageService;
        $this->encodeService           = $encodeService;
    }

    /**
     * 儲存表單
     *
     */
    public function run($request, $event)
    {
        // 若有人報名，則儲存移除的欄位
        $columns = [];
        $data    = json_decode($request['columns']);
        foreach ($data as $_column) {
            if (!$event->reports->count() && !$_column->status) {
                continue;
            }
            $columns[] = $_column;
        }

        // 寫入活動報名資訊
        $event->use_payment          = $request['use_payment'];
        $event->start_date           = $request['start_date'];
        $event->end_date             = $request['end_date'];
        $event->path                 = $request['path'];
        $event->title                = $request['title'];
        $event->description          = $request['description'];
        $event->columns              = json_encode($columns, JSON_UNESCAPED_UNICODE);
        $event->share_url            = $request['share_url'];
        $event->float_btn            = $request['float_btn'];
        $event->pending_info         = $request['pending_info'];
        $event->result_new_info      = $request['result_new_info'];
        $event->result_new_btn_show  = $request['result_new_btn_show'];
        $event->result_new_btn       = $request['result_new_btn'];
        $event->result_new_url       = $request['result_new_url'];
        $event->result_old_info      = $request['result_old_info'];
        $event->result_old_btn       = $request['result_old_btn'];
        $event->result_old_url       = $request['result_old_url'];
        $event->completed_info       = $request['completed_info'];
        $event->email_title          = $request['email_title'];
        $event->email_info           = $request['email_info'];
        $event->email_info_more      = $request['email_info_more'];
        $event->spreadsheet_id       = $request['spreadsheet_id'];
        $event->use_report_no        = $request['use_report_no'];
        $event->show_report_no_page  = $request['show_report_no_page'];
        $event->use_qrcode           = $request['use_qrcode'];
        $event->show_qrcode_page     = $request['show_qrcode_page'];
        $event->event_tool_id        = $request['event_tool_id'];
        $event->onsite_signup_dates  = $request['onsite_signup_dates'];
        $event->use_calendar         = $request['use_calendar'] ?: 0;
        $event->purchase_item_unique = $request['purchase_item_unique'];
        $event->need_invoice         = $request['need_invoice'];
        $event->invoice_now          = $request['invoice_now'];
        $event->invoice_setting_id   = $request['invoice_setting_id'];
        $event->seller_name          = $request['seller_name'];
        $event->seller_ubn           = $request['seller_ubn'];

        // 儲存活動報名資訊
        $event->save();

        // 更新活動商品
        $products  = json_decode($request['products']);
        $extraFees = json_decode($request['extra_fees'], true);
        $this->updateProductService->run($event, $products, $extraFees);

        // 更新婚展模組
        $data = json_decode($request['exhibitions']);
        $this->updateExhibitionService->run($event, $data);

        try {
            // google試算表-更新試算表的欄位標題
            $this->googleSheetsService->updateHead($event);
        } catch (\Exception $e) {
            $this->setException('Google試算表編輯錯誤，請正確輸入試算表ID，以及試算表的編輯權限！'.$e->getMessage());
        }

        try {
            // google行事曆-儲存
            if ($event->use_calendar) {
                $this->googleCalendarService->save([
                    'target_type' => 'event',
                    'target_id'   => $event->id,
                    'summary'     => $request['calendar_summary'],
                    'location'    => $request['calendar_location'],
                    'description' => $request['calendar_description'],
                    'start_at'    => $request['calendar_start'],
                    'end_at'      => $request['calendar_end'],
                ]);

            // google行事曆-刪除
            } elseif ($event->calendar) {
                $this->googleCalendarService->delete($event->calendar);
            }
        } catch (\Exception $e) {
            $this->setException('Google行事曆儲存錯誤，請確認行事曆欄位是否輸入正確！'.$e->getMessage());
        }

        // 更新上傳的圖檔的來源
        $this->createImageService->add([
            'file_name' => $request['image'],
            'type'      => 'event_image',
            'target_id' => $event->id,
            'only'      => true,
        ]);
        $this->createImageService->add([
            'file_name' => $request['image_xs'],
            'type'      => 'event_image_xs',
            'target_id' => $event->id,
            'only'      => true,
        ]);
        $this->createImageService->add([
            'file_name' => $request['image_meta'],
            'type'      => 'event_image_meta',
            'target_id' => $event->id,
            'only'      => true,
        ]);
        $this->encodeService->run($event->description, 'event_description', $event->id);

        return $event;
    }
}
