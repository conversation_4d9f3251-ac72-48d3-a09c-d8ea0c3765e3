<?php
/*
 |--------------------------------------
 |  更新活動商品 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Event\Yzcube;

use App\Models\Event;
use App\Services\Image\CreateImageService;

class UpdateProductService
{
    private $createImageService;

    public function __construct(
        CreateImageService $createImageService
    ) {
        $this->createImageService = $createImageService;
    }

    /**
     * 更新活動商品
     *
     */
    public function run(Event $event, $data, $extraFees)
    {
        // 先刪除既有商品品項
        foreach ($event->products as $product) {
            $product->items()->delete();
        }

        // 再刪除既有商品
        $event->products()->delete();

        // 儲存商品
        foreach ($data as $_product) {
            $product = $event->products()
                                ->withTrashed()
                                ->updateOrCreate([
                                    'id' => $_product->product_id ?: NULL,
                                ],[
                                    'name'       => $_product->name,
                                    'deleted_at' => NULL,
                                ]);

            // 儲存商品品項
            foreach ($_product->items as $_item) {
                $product->items()
                        ->withTrashed()
                        ->updateOrCreate([
                            'id' => $_item->item_id ?: NULL,
                        ],[
                            'name'              => $_item->name,
                            'original_price'    => $_item->original_price ?: NULL,
                            'selling_price'     => $_item->selling_price,
                            'inventory'         => $_item->inventory,
                            'purchase_limit'    => $_item->purchase_limit ?: NULL,
                            'use_upload_images' => $_item->use_upload_images,
                            'use_crop_images'   => $_item->use_crop_images,
                            'crop_width'        => $_item->crop_width ?: NULL,
                            'crop_height'       => $_item->crop_height ?: NULL,
                            'limit_min_side'    => $_item->limit_min_side ?: NULL,
                            'deleted_at'        => NULL,
                        ]);
            }

            // 更新上傳的圖檔的來源
            $this->createImageService->add([
                'file_name' => $_product->image,
                'type'      => 'event_product_image',
                'target_id' => $product->id,
                'only'      => true,
            ]);
        }

        // 先刪除既有活動訂單的額外費用
        $event->extraFees()->delete();

        // 再更新活動訂單的額外費用
        foreach ($extraFees as $extraFee) {
            $event->extraFees()
                    ->withTrashed()
                    ->updateOrCreate($extraFee, [
                        'deleted_at' => NULL,
                    ]);
        }
    }
}
