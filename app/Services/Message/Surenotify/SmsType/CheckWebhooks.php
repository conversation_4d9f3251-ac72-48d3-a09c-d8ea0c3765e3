<?php
/*------------------------------------
 | 簡訊 API - 查詢 Webhook
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Message\Surenotify\SmsType;

use App\Services\Message\Surenotify\Contract\AbsSmsTypeHandle;

class CheckWebhooks extends AbsSmsTypeHandle
{
    /**
     * 簡訊 API - 查詢 Webhook
     *
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function run()
    {
        // 執行 Surenotify API
        $this->executeAPI('GET', '/v1/sms/webhooks');

        // 回傳回應內容
        return $this->respObj;
    }
}
