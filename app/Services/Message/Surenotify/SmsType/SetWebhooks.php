<?php
/*------------------------------------
 | 簡訊 API - 新增/修改 Webhook
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Message\Surenotify\SmsType;

use App\Services\Message\Surenotify\Contract\AbsSmsTypeHandle;

class SetWebhooks extends AbsSmsTypeHandle
{
    /**
     * 簡訊 API - 新增/修改 Webhook
     *
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function run()
    {
        $list = [

            // 事件類型代碼 delivery（到達）：3
            [
                'type' => 3,
                'url'  => route('surenotify.webhook.sms-status'),
            ],

            // 事件類型代碼 bounce（退信）：6
            [
                'type' => 6,
                'url'  => route('surenotify.webhook.sms-status'),
            ],
        ];

        foreach ($list as $row) {
            $data = [
                'json' => [
                    'type' => $row['type'],
                    'url'  => $row['url'],
                ]
            ];

            // 執行 Surenotify API
            $this->executeAPI('POST', '/v1/sms/webhooks', $data);
        }
    }
}
