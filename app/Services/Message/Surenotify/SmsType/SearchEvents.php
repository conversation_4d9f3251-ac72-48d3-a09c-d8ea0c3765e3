<?php
/*------------------------------------
 | 簡訊 API - 事件查詢
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Message\Surenotify\SmsType;

use App\Services\Message\Surenotify\Contract\AbsSmsTypeHandle;

class SearchEvents extends AbsSmsTypeHandle
{
    /**
     * 簡訊 API - 事件查詢
     *
     * @param string $messageId * 簡訊ID (與 recipient 擇一)
     * @param string $recipient * 收件人電話 (與 messageId 擇一)。範例：0912345678 或 912345678
     * @param string $countryCode * 收件人國碼 (與 messageId 擇一)
     * @param string $from 查詢區間起始時間，格式範例：2019-12-15T08:38:32Z 請確認日期時間為 UTC+0
     * @param string $to 查詢區間起始時間，格式範例：2019-12-15T08:38:32Z 請確認日期時間為 UTC+0
     * @param string $status 事件類型：accept（已接收請求）,delivery（到達）,bounce（退信），可多選，需使用半形逗號 , 作分隔。
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function run()
    {
        $query = [
            'id'           => $this->data['messageId'] ?? '',
            'recipient'    => $this->data['recipient'] ?? '',
            'country_code' => $this->data['countryCode'] ?? '',
            'from'         => $this->data['from'] ?? '',
            'to'           => $this->data['to'] ?? '',
            'status'       => $this->data['status'] ?? '',
        ];

        // 移除陣列空值
        $query = array_filter($query);

        // 執行 Surenotify API
        $this->executeAPI('GET', '/v1/sms/events', ['query' => $query]);

        // 回傳回應內容
        return $this->respObj;
    }
}
