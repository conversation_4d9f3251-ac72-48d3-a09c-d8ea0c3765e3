<?php
/*------------------------------------
 | 簡訊 API - 單筆/多筆寄送
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Message\Surenotify\SmsType;

use App\Services\Message\Surenotify\Contract\AbsSmsTypeHandle;
use Illuminate\Support\Str;
use stdClass;

class SendMessages extends AbsSmsTypeHandle
{
    /**
     * 簡訊 API - 單筆/多筆寄送
     *
     * @param string $content * 簡訊內容
     * @param string $phones * 電話號碼 (逗號分隔)
     * @param string $from 寄件人專屬門號，若無專屬門號則不用填
     * @param int $aliveMins 嘗試 Retry 的期限，單位為「分鐘」，設定值範圍：5 ~ 480，預設為最小值
     * @param model $targetModel 目標Model
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function run()
    {
        $content     = $this->data['content'];
        $phones      = $this->data['phones'];
        $from        = $this->data['from'] ?? '';
        $aliveMins   = $this->data['aliveMins'] ?? 5;
        $targetModel = $this->data['targetModel'] ?? NULL;

        // 測試環境：強制變更測試電話！
        if (env('APP_DEBUG')) {
            $content = '【'.env('APP_NAME').'】'.$content;
            $phones  = env('SMS_TEST');

            // 測試環境：不寄送簡訊，直接回傳回應內容
            if (!env('SMS_TEST_SEND')) {
                $result = new stdClass();
                $result->id = (string)Str::uuid();
                $result->success = [];
                $result->failure = [];
                foreach (explode(',', $phones) as $address) {
                    $success = new stdClass();
                    $success->id       = (string)Str::uuid();
                    $success->address  = $address;
                    $result->success[] = $success;
                }
                return $result;
            }
        }

        // 收件人，最多放置 100 recipient / request
        $recipients = [];
        foreach (explode(',', $phones) as $address) {
            $recipients[] = [
                'address'      => $address, // 收件人電話，支援格式為純數字。
                'country_code' => '886', // 收件人國碼，台灣國碼：886
                // 'variables' => [] // 收件人的變數，自定義，每個 variables 內容長度最長 100，不得用數學運算式且開頭不得為數字。
            ];
        }

        $data = [
            'json' => [
                'content'     => $content,
                'recipients'  => $recipients,
                'from'        => $from,
                'alive_mins'  => $aliveMins,
            ]
        ];

        // 執行 Surenotify API
        $this->executeAPI('POST', '/v1/sms/messages', $data, $targetModel);

        // 回傳回應內容
        return $this->respObj;
    }
}
