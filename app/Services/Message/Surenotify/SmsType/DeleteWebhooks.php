<?php
/*------------------------------------
 | 簡訊 API - 刪除 Webhook
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Message\Surenotify\SmsType;

use App\Services\Message\Surenotify\Contract\AbsSmsTypeHandle;

class DeleteWebhooks extends AbsSmsTypeHandle
{
    /**
     * 簡訊 API - 刪除 Webhook
     *
     * @return mixed|\Psr\Http\Message\ResponseInterface
     */
    public function run()
    {
        $list = [

            // 事件類型代碼 delivery（到達）：3
            ['type' => 3],

            // 事件類型代碼 bounce（退信）：6
            ['type' => 6],
        ];

        foreach ($list as $row) {
            $data = [
                'json' => [
                    'type' => $row['type'],
                    'url'  => $row['url'],
                ]
            ];

            // 執行 Surenotify API
            $this->executeAPI('DELETE', '/v1/sms/webhooks', $data);
        }
    }
}
