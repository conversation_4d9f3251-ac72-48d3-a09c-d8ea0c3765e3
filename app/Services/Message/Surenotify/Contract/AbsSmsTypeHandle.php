<?php
/*
 |--------------------------------------
 | 電子豹 Sms Type 處理的 abstract class
 |--------------------------------------
 |
 | 電子豹 Surenotify API 文件
 | https://newsleopard.com/surenotify/api/v1
 |
 */

namespace App\Services\Message\Surenotify\Contract;

use App\Models\LogNewsleopardResponse;
use GuzzleHttp\Client;
use Log;

abstract class AbsSmsTypeHandle
{
    /** sms 參數 */
    protected $type;
    protected $data;
    protected $respObj;

    private $logNewsleopardResponse;
    private $surenotify_client;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        LogNewsleopardResponse $logNewsleopardResponse
    ) {
        $this->logNewsleopardResponse = $logNewsleopardResponse;

        $this->surenotify_client = new Client([
            'base_uri' => env('LEOPARD_SURENOTIFY'),
            'headers' => ['x-api-key' => env('LEOPARD_SURENOTIFY_API_KEY')],
        ]);
    }

    /**
     * Handler logic
     * @param $type
     * @param $data
     * @return void
     */
    public function handle($type, $data)
    {
        $this->type = $type;
        $this->data = $data;

        return $this->run();
    }

    /**
     * 執行 Message
     * @return void
     */
    abstract public function run();

    /**
     *  執行 Surenotify API
     *
     * @param string $method GET|POST|DELETE
     * @param string $url
     * @param array $data
     * @param object $targetModel
     * @return object
    **/
    protected function executeAPI(string $method, string $url, array $data = [], object $targetModel = NULL)
    {
        try {
            $resp = $this->surenotify_client->request($method, $url, $data);
        } catch (\Exception $e) {
            Log::error('Surenotify API Error!', [
                'message' => $e->getMessage(),
                'class'   => class_basename(get_class($this)),
                'method'  => $method,
                'url'     => $url,
                'data'    => $data,
            ]);

            return false;
        }

        // 新增電子豹回覆記錄
        $statusCode    = $resp->getStatusCode();
        $this->respObj = json_decode($resp->getBody()->getContents());
        $this->logNewsleopardResponse->create([
            'target_type' => $targetModel ? get_class($targetModel) : NULL,
            'target_id'   => $targetModel ? $targetModel->id : NULL,
            'api_type'    => 'surenotify',
            'method'      => $method,
            'url'         => $url,
            'data'        => $data,
            'status_code' => $statusCode,
            'content'     => $this->respObj,
            'message_id'  => $this->respObj->success[0]->id ?? NULL,
        ]);

        if ($statusCode != 200) {
            return false;
        }
    }
}
