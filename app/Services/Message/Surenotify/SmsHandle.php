<?php
/*
 |------------------------------------
 | 電子豹 Surenotify API
 |------------------------------------
 |
 | 電子豹 Surenotify API 文件
 | https://newsleopard.com/surenotify/api/v1
 |
 */

namespace App\Services\Message\Surenotify;

use Illuminate\Support\Str;
use App\Services\Message\Surenotify\Contract\AbsSmsTypeHandle;

class SmsHandle
{
    /**
     * 簡訊處理邏輯
     * @param $type
     * @param $data
     * @return array
     */
    public function handle($type, $data = [])
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Message\Surenotify\SmsType\SendMessages
         |  App\Services\Message\Surenotify\SmsType\SetWebhooks
         |  App\Services\Message\Surenotify\SmsType\CheckWebhooks
         |  App\Services\Message\Surenotify\SmsType\DeleteWebhooks
         |  App\Services\Message\Surenotify\SmsType\SearchEvents
         |  App\Services\Message\Surenotify\SmsType\ExclusiveNumber
         |
         */
        $classPath = 'App\Services\Message\Surenotify\SmsType\\'.Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsSmsTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data);
    }
}
