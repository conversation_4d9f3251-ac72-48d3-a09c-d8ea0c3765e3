<?php
/*
 |--------------------------------------
 |  儲存全站推播 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Message\Yzcube\Push;

use App\Repositories\PushMessageRepository;
use App\Services\Image\CreateImageService;
use App\Jobs\Firebase\Message;
use App\Traits\ApiErrorTrait;

class SaveService
{
    private $pushMessageRepository;
    private $createImageService;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        PushMessageRepository $pushMessageRepository,
        CreateImageService $createImageService
    ) {
        $this->pushMessageRepository = $pushMessageRepository;
        $this->createImageService    = $createImageService;
    }

    /**
     * 儲存全站推播
     */
    public function run($request)
    {
        // 新增
        $pushMessage = $this->pushMessageRepository->getFirst(['id' => $request['push_id']]);
        if (!$pushMessage) {
            $pushMessage = $this->pushMessageRepository->getModel();

        // 僅能編輯排程中的推播訊息
        } elseif ($pushMessage->status != 'scheduling') {
            $this->setException('僅能編輯排程中的推播訊息！');
        }

        // 儲存
        $pushMessage->admin_id       = $request['admin_id'];
        $pushMessage->receiver_type  = $request['receiver_type'];
        $pushMessage->store_type     = $request['store_type'];
        $pushMessage->title          = $request['title'];
        $pushMessage->content        = $request['content'];
        $pushMessage->images         = $request['images'];
        $pushMessage->status         = $request['status'];
        $pushMessage->push_count     = $request['push_count'];
        $pushMessage->set_publish_at = ($request['status'] == 'scheduling') ? $request['set_publish_at'] : NULL;
        $pushMessage->published_at   = ($request['status'] == 'scheduling') ? $request['set_publish_at'] : now();
        $pushMessage->save();

        // 立即推播
        if ($pushMessage->status == 'immediately') {

            // 即時通訊處理 use Job
            $messageType = 'push_'.$pushMessage->receiver_type;
            Message::dispatch($messageType, $pushMessage);
        }
    }
}
