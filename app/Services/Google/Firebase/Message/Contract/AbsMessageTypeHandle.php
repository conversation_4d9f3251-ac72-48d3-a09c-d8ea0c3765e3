<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | Firebase Message Type 處理的 abstract class
 |--------------------------------------
 | template pattern
 |
 */

namespace App\Services\Google\Firebase\Message\Contract;

abstract class AbsMessageTypeHandle
{
    /** Firebase 參數 */
    protected $collectionKey = 'messages';
    protected $database;

    /** 傳入參數 */
    protected $type;
    protected $data;

    /**
     * Handler logic
     * @param $data:
     * @return void
     */
    public function handle($type, $data)
    {
        $firestore      = app('firebase.firestore');
        $this->database = $firestore->database();
        $this->type     = $type;
        $this->data     = $data;

        return $this->run();
    }

    /**
     * 執行 Firestore
     * @return void
     */
    abstract public function run();


    /**
     * Update a Firestore document using field paths and values
     * @return array
     */
    protected function formatUpdateParameters($array)
    {
        $result = [];
        foreach ($array as $key => $value) {
            $result[] = [
                'path'  => $key,
                'value' => $value,
            ];
        }

        return $result;
    }
}
