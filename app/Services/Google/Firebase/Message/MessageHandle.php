<?php
declare(strict_types=1);
/*
 |------------------------------------
 | Firebase 即時通訊處理
 |------------------------------------
 |
 |
 */

namespace App\Services\Google\Firebase\Message;

use Illuminate\Support\Str;
use App\Services\Google\Firebase\Message\Contract\AbsMessageTypeHandle;

class MessageHandle
{
    /**
     * 即時通訊處理邏輯
     * @param $type
     * @param $data
     * @return array
     */
    public function handle($type, $data)
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Google\Firebase\Message\Type\Push
         |
         */
        $classPath = 'App\Services\Google\Firebase\Message\Type\\' . Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsMessageTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data);
    }
}
