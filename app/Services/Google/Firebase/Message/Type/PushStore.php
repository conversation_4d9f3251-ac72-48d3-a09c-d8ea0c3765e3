<?php
declare(strict_types=1);
/*------------------------------------
 | 即時通訊-商家全站推播
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Message\Type;

use App\Services\Google\Firebase\Message\Contract\AbsMessageTypeHandle;
use App\Models\Store;
use App\Repositories\StoreUserRepository;
use App\Services\Google\Firebase\CloudMessaging\MessagingHandle;
use App\Services\Line\LineService;
use App\Services\Mail\Firebase\NewMessageAdminToStoreService;
use App\Traits\Model\SummaryTrait;

class PushStore extends AbsMessageTypeHandle
{
    private $store;
    private $storeUserRepository;
    private $messagingHandle;
    private $lineService;
    private $newMessageAdminToStoreService;
    private $pushMessage;
    private $admin;

    use SummaryTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Store $store,
        StoreUserRepository $storeUserRepository,
        MessagingHandle $messagingHandle,
        LineService $lineService,
        NewMessageAdminToStoreService $newMessageAdminToStoreService
    ) {
        $this->store                         = $store;
        $this->storeUserRepository           = $storeUserRepository;
        $this->messagingHandle               = $messagingHandle;
        $this->lineService                   = $lineService;
        $this->newMessageAdminToStoreService = $newMessageAdminToStoreService;
    }

    /**
     * 即時通訊-商家全站推播
     * @return void
     */
    public function run()
    {
        // 全站推播Model
        $this->pushMessage = $this->data;
        $this->admin       = config('params.firebase.admins.'.$this->pushMessage->admin_id);

        // 取得付費商家
        $stores = $this->getHasPaidStores();

        // 先更新全站推播Model，避免排程重複執行
        $this->pushMessage->push_count = $stores->count();
        $this->pushMessage->status     = 'published';
        $this->pushMessage->save();

        foreach ($stores as $store) {

            // 取代特定字串
            $content = str_replace(
                ['{store_id}', '{store_name}', '{url_store_name}'],
                [$store->id, $store->name, urlencode($store->name)],
                $this->pushMessage->content
            );

            // 取得聊天室(文件)
            $roomSanps = $this->database
                                ->collection($this->collectionKey)
                                ->where('admin_id', '=', $this->pushMessage->admin_id)
                                ->where('store_id', '=', $store->id)
                                ->documents();

            // 新增聊天室
            if ($roomSanps->isEmpty()) {
                $roomRef = $this->addRoomDocument($store, $content);

            // 更新聊天室
            } else {
                $roomSanp = $roomSanps->rows()[0];
                $roomRef = $this->updateRoomDocument($roomSanp, $store, $content);

            }

            // 新增訊息
            $roomRef->collection('list')
                    ->add([
                        'target_type' => 'admin',
                        'push_id'     => $this->pushMessage->id,
                        'target_id'   => $this->pushMessage->admin_id,
                        'text'        => $content,
                        'images'      => $this->pushMessage->images,
                        'created_at'  => now(),
                    ]);

            // 瀏覽器推播通知
            $summary = $this->getSummaryStripTags($content, 60);
            foreach ($store->accounts as $account) {
                if (!empty($account->liveWebNotification->tokens)) {
                    $this->messagingHandle->handle('push_web_notification', [
                        'webNotification' => $account->liveWebNotification,
                        'title'           => $this->admin['name'].' 傳訊息給您',
                        'message'         => '說「'.$summary.'」',
                        'link'            => config('params.wdv2.admin_url').'/redirect/'.$store->id.'/message?admin_id='.$this->admin['id'],
                    ]);
                }
            }

            // Line通知商家
            $this->newMessageLineNotice($store->id, $summary);

            // Email通知
            $this->newMessageAdminToStoreService->sendMail($this->admin, $store, $content, now());

            // 新增全站推播紀錄
            $this->pushMessage->logs()->create(['receiver_id' => $store->id]);
        }
    }

    // 取得付費商家
    private function getHasPaidStores()
    {
        $stores = $this->store->select('id', 'type', 'name', 'email')
                                ->emailNotEmpty()
                                ->published($this->pushMessage->store_type);

        // 測試環境只送通知給管理員意思意思就好拉，好嗎？
        if (env('APP_DEBUG')) {
            $stores = $stores->whereHas('accounts', function ($q) {
                $q->where('is_admin', 1);
            });
        }

        return $stores->get();
    }

    // 新增聊天室
    private function addRoomDocument($store, $content)
    {
        return $this->database
                    ->collection($this->collectionKey)
                    ->add([
                        'admin_id'                  => $this->pushMessage->admin_id,
                        'admin_unread'              => 0,
                        'admin_last_msg_updated_at' => now(),
                        'is_push_unread'            => true,
                        'store_id'                  => $store->id,
                        'store_unread'              => 1,
                        'store_last_msg_updated_at' => NULL,
                        'store_type'                => $store->type,
                        'last_msg_text'             => $content,
                        'last_msg_user'             => 'admin',
                        'last_msg_updated_at'       => now(),
                        'updated_at'                => now(),
                        'created_at'                => now(),
                    ]);
    }

    // 更新聊天室
    private function updateRoomDocument($roomSanp, $store, $content)
    {
        $roomRef = $this->database
                        ->collection($this->collectionKey)
                        ->document($roomSanp->id());

        $data = $this->formatUpdateParameters([
            'admin_id'                  => $this->pushMessage->admin_id,
            'admin_last_msg_updated_at' => now(),
            'is_push_unread'            => true,
            'store_id'                  => $store->id,
            'store_unread'              => $roomSanp->get('store_unread') + 1,
            'last_msg_text'             => $content,
            'last_msg_user'             => 'admin',
            'last_msg_updated_at'       => now(),
            'updated_at'                => now(),
        ]);
        $roomRef->update($data);

        return $roomRef;
    }

    // Line通知商家
    private function newMessageLineNotice($storeId, $summary)
    {
        // 取得須通知的商家Line IDs
        $devices = $this->storeUserRepository->getLineIdsByStoreId($storeId);

        // 通知訊息
        $message = '【💗官方來訊】'."\n";
        $message .= $this->admin['name'].' 傳訊息給您：「'.$summary.'」'."\n";
        $message .= "\n";
        $message .= '👇點連結看完整訊息👇'."\n";
        $message .= config('params.wdv2.admin_url').'/redirect/'.$storeId.'/message?admin_id='.$this->admin['id'].'&openExternalBrowser=1';

        // 送出訊息
        $this->lineService->send($message, $devices);
    }
}
