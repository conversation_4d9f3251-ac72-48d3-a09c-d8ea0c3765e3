<?php
declare(strict_types=1);
/*------------------------------------
 | 即時通訊-婚戒大賞-集點通知
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Message\Type;

use App\Services\Google\Firebase\Message\Contract\AbsMessageTypeHandle;
use App\Services\Google\Firebase\Notification\NotificationHandle;
use App\Services\Google\Firebase\CloudMessaging\MessagingHandle;
use App\Services\Mail\RingEvent\CollectPointService;
use App\Traits\Model\SummaryTrait;

class RingEventCollectPoint extends AbsMessageTypeHandle
{
    // 好婚市集-待嫁小編
    private $admin_id = 3;

    private $notificationHandle;
    private $messagingHandle;
    private $collectPointService;
    private $user;
    private $admin;
    private $content;

    use SummaryTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        NotificationHandle $notificationHandle,
        MessagingHandle $messagingHandle,
        CollectPointService $collectPointService
    ) {
        $this->notificationHandle  = $notificationHandle;
        $this->messagingHandle     = $messagingHandle;
        $this->collectPointService = $collectPointService;

        $this->admin = config('params.firebase.admins.'.$this->admin_id);
    }

    /**
     * 即時通訊-新娘全站推播
     * @return void
     */
    public function run()
    {
        // 婚戒大賞-報名資訊 Model
        $ringUser   = $this->data;
        $this->user = $ringUser->user;

        // 累積點數
        if ($ringUser->collect_count == 1) {
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event' : 'https://lihi.weddingday.com.tw/3UzeT/massage_1';
            $this->content = "《2022婚戒大賞》活動通知：恭喜成功達成1點，請至信箱領取超實用婚禮表格💌\r\n\r\n查看集點卡\r\n>> ".$link;
        } elseif ($ringUser->collect_count == 3) {
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event' : 'https://lihi.weddingday.com.tw/3UzeT/massage_3';
            $this->content = "《2022婚戒大賞》活動通知：恭喜成功達成3點，請至信箱領取專屬的SPA美體/美顏券💆‍♀\r\n\r\n查看集點卡\r\n>> ".$link;
        } elseif ($ringUser->collect_count == 5) {
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event' : 'https://lihi.weddingday.com.tw/3UzeT/massage_5';
            $this->content = "《2022婚戒大賞》活動通知：恭喜成功解鎖2022賞鑽集點任務，請至信箱領取專屬VIP好禮🎁\r\n\r\n查看集點卡\r\n>> ".$link;
        } else {
            return false;
        }

        // 取得聊天室(文件)
        $roomSanps = $this->database
                            ->collection($this->collectionKey)
                            ->where('admin_id', '=', $this->admin_id)
                            ->where('user_id', '=', $this->user->id)
                            ->documents();

        // 新增聊天室
        if ($roomSanps->isEmpty()) {
            $roomRef = $this->addRoomDocument();

        // 更新聊天室
        } else {
            $roomSanp = $roomSanps->rows()[0];
            $roomRef = $this->updateRoomDocument($roomSanp);

        }

        // 新增訊息
        $roomRef->collection('list')
                ->add([
                    'target_type' => 'admin',
                    'target_id'   => $this->admin_id,
                    'text'        => $this->content,
                    'created_at'  => now(),
                ]);

        // 小鈴鐺通知使用者
        $this->notificationHandle->handle('new_message', [
            'roomObj' => $roomRef->snapshot()->data(),
        ]);

        // 瀏覽器推播通知
        if (!empty($this->user->liveWebNotification->tokens)) {
            $this->messagingHandle->handle('push_web_notification', [
                'webNotification' => $this->user->liveWebNotification,
                'title'           => $this->admin['name'].' 傳訊息給你',
                'message'         => '說「'.$this->getSummaryStripTags($this->content, 60).'」',
                'link'            => config('params.wdv3.user_url').'/message?admin_id='.$this->admin_id,
            ]);
        }

        // Email通知
        $this->collectPointService->sendMail($ringUser);
    }

    // 新增聊天室
    private function addRoomDocument()
    {
        return $this->database
                    ->collection($this->collectionKey)
                    ->add([
                        'admin_id'                  => $this->admin_id,
                        'admin_unread'              => 0,
                        'admin_last_msg_updated_at' => now(),
                        'is_push_unread'            => true,
                        'user_id'                   => $this->user->id,
                        'user_unread'               => 1,
                        'user_last_msg_updated_at'  => NULL,
                        'store_type'                => NULL,
                        'last_msg_text'             => $this->content,
                        'last_msg_user'             => 'admin',
                        'last_msg_updated_at'       => now(),
                        'updated_at'                => now(),
                        'created_at'                => now(),
                    ]);
    }

    // 更新聊天室
    private function updateRoomDocument($roomSanp)
    {
        $roomRef = $this->database
                        ->collection($this->collectionKey)
                        ->document($roomSanp->id());

        $data = $this->formatUpdateParameters([
            'admin_id'                  => $this->admin_id,
            'admin_last_msg_updated_at' => now(),
            'is_push_unread'            => true,
            'user_id'                   => $this->user->id,
            'user_unread'               => $roomSanp->get('user_unread') + 1,
            'last_msg_text'             => $this->content,
            'last_msg_user'             => 'admin',
            'last_msg_updated_at'       => now(),
            'updated_at'                => now(),
        ]);
        $roomRef->update($data);

        return $roomRef;
    }
}
