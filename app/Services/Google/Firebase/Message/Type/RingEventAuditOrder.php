<?php
declare(strict_types=1);
/*------------------------------------
 | 即時通訊-婚戒大賞-訂單審核通知
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Message\Type;

use App\Services\Google\Firebase\Message\Contract\AbsMessageTypeHandle;
use App\Services\Google\Firebase\Notification\NotificationHandle;
use App\Services\Google\Firebase\CloudMessaging\MessagingHandle;
use App\Services\Mail\RingEvent\AuditOrderService;
use App\Traits\Model\SummaryTrait;

class RingEventAuditOrder extends AbsMessageTypeHandle
{
    // 好婚市集-待嫁小編
    private $admin_id = 3;

    private $notificationHandle;
    private $messagingHandle;
    private $auditOrderService;
    private $user;
    private $admin;
    private $content;

    use SummaryTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        NotificationHandle $notificationHandle,
        MessagingHandle $messagingHandle,
        AuditOrderService $auditOrderService
    ) {
        $this->notificationHandle = $notificationHandle;
        $this->messagingHandle    = $messagingHandle;
        $this->auditOrderService  = $auditOrderService;

        $this->admin = config('params.firebase.admins.'.$this->admin_id);
    }

    /**
     * 即時通訊-新娘全站推播
     * @return void
     */
    public function run()
    {
        // 婚戒大賞-報名資訊 Model
        $ringOrder  = $this->data;
        $this->user = $ringOrder->ringUser->user;

        // 累積點數
        if ($ringOrder->status == 'approved') {
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event' : 'https://lihi.weddingday.com.tw/3UzeT/massage_orderpass';
            $this->content = "《2022婚戒大賞》活動通知：你上傳的訂單資料已通過審核，恭喜你的點數直接升級集滿 5 點囉 🎉  可前往信箱查看好禮內容唷！\r\n\r\n查看集點卡\r\n>> ".$link;
        } else {
            $link = env('APP_DEBUG') ? config('params.wdv3.user_url').'/event#order' : 'https://lihi.weddingday.com.tw/ZLU3I/massage_orderfail';
            $this->content = "《2022婚戒大賞》活動通知：您上傳的訂單資料有點狀況導致審核失敗，請立即前往確認👇\r\n>> ".$link;
        }

        // 取得聊天室(文件)
        $roomSanps = $this->database
                            ->collection($this->collectionKey)
                            ->where('admin_id', '=', $this->admin_id)
                            ->where('user_id', '=', $this->user->id)
                            ->documents();

        // 新增聊天室
        if ($roomSanps->isEmpty()) {
            $roomRef = $this->addRoomDocument();

        // 更新聊天室
        } else {
            $roomSanp = $roomSanps->rows()[0];
            $roomRef = $this->updateRoomDocument($roomSanp);

        }

        // 新增訊息
        $roomRef->collection('list')
                ->add([
                    'target_type' => 'admin',
                    'target_id'   => $this->admin_id,
                    'text'        => $this->content,
                    'created_at'  => now(),
                ]);

        // 小鈴鐺通知使用者
        $this->notificationHandle->handle('new_message', [
            'roomObj' => $roomRef->snapshot()->data(),
        ]);

        // 瀏覽器推播通知
        if (!empty($this->user->liveWebNotification->tokens)) {
            $this->messagingHandle->handle('push_web_notification', [
                'webNotification' => $this->user->liveWebNotification,
                'title'           => $this->admin['name'].' 傳訊息給你',
                'message'         => '說「'.$this->getSummaryStripTags($this->content, 60).'」',
                'link'            => config('params.wdv3.user_url').'/message?admin_id='.$this->admin_id,
            ]);
        }

        // Email通知
        $this->auditOrderService->sendMail($ringOrder);
    }

    // 新增聊天室
    private function addRoomDocument()
    {
        return $this->database
                    ->collection($this->collectionKey)
                    ->add([
                        'admin_id'                  => $this->admin_id,
                        'admin_unread'              => 0,
                        'admin_last_msg_updated_at' => now(),
                        'is_push_unread'            => true,
                        'user_id'                   => $this->user->id,
                        'user_unread'               => 1,
                        'user_last_msg_updated_at'  => NULL,
                        'store_type'                => NULL,
                        'last_msg_text'             => $this->content,
                        'last_msg_user'             => 'admin',
                        'last_msg_updated_at'       => now(),
                        'updated_at'                => now(),
                        'created_at'                => now(),
                    ]);
    }

    // 更新聊天室
    private function updateRoomDocument($roomSanp)
    {
        $roomRef = $this->database
                        ->collection($this->collectionKey)
                        ->document($roomSanp->id());

        $data = $this->formatUpdateParameters([
            'admin_id'                  => $this->admin_id,
            'admin_last_msg_updated_at' => now(),
            'is_push_unread'            => true,
            'user_id'                   => $this->user->id,
            'user_unread'               => $roomSanp->get('user_unread') + 1,
            'last_msg_text'             => $this->content,
            'last_msg_user'             => 'admin',
            'last_msg_updated_at'       => now(),
            'updated_at'                => now(),
        ]);
        $roomRef->update($data);

        return $roomRef;
    }
}
