<?php
declare(strict_types=1);
/*------------------------------------
 | 即時通訊-新娘全站推播
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Message\Type;

use App\Services\Google\Firebase\Message\Contract\AbsMessageTypeHandle;
use App\Models\User;
use App\Services\Google\Firebase\Notification\NotificationHandle;
use App\Services\Google\Firebase\CloudMessaging\MessagingHandle;
use App\Services\Mail\Firebase\NewMessageAdminToUserService;
use App\Traits\Model\SummaryTrait;

class PushUser extends AbsMessageTypeHandle
{
    private $user;
    private $notificationHandle;
    private $messagingHandle;
    private $newMessageAdminToUserService;
    private $pushMessage;
    private $admin;

    use SummaryTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        User $user,
        NotificationHandle $notificationHandle,
        MessagingHandle $messagingHandle,
        NewMessageAdminToUserService $newMessageAdminToUserService
    ) {
        $this->user                         = $user;
        $this->notificationHandle           = $notificationHandle;
        $this->messagingHandle              = $messagingHandle;
        $this->newMessageAdminToUserService = $newMessageAdminToUserService;
    }

    /**
     * 即時通訊-新娘全站推播
     * @return void
     */
    public function run()
    {
        // 全站推播Model
        $this->pushMessage = $this->data;
        $this->admin       = config('params.firebase.admins.'.$this->pushMessage->admin_id);

        // 一年內未發佈分享文，但有互動的新娘
        $users = $this->getActiveUsers();

        // 先更新全站推播Model，避免排程重複執行
        $this->pushMessage->push_count = $users->count();
        $this->pushMessage->status     = 'published';
        $this->pushMessage->save();

        foreach ($users as $user) {

            // 取得聊天室(文件)
            $roomSanps = $this->database
                                ->collection($this->collectionKey)
                                ->where('admin_id', '=', $this->pushMessage->admin_id)
                                ->where('user_id', '=', $user->id)
                                ->documents();

            // 新增聊天室
            if ($roomSanps->isEmpty()) {
                $roomRef = $this->addRoomDocument($user);

            // 更新聊天室
            } else {
                $roomSanp = $roomSanps->rows()[0];
                $roomRef = $this->updateRoomDocument($roomSanp, $user);

            }

            // 新增訊息
            $roomRef->collection('list')
                    ->add([
                        'target_type' => 'admin',
                        'push_id'     => $this->pushMessage->id,
                        'target_id'   => $this->pushMessage->admin_id,
                        'text'        => $this->pushMessage->content,
                        'images'      => $this->pushMessage->images,
                        'created_at'  => now(),
                    ]);

            // 小鈴鐺通知使用者
            $this->notificationHandle->handle('push_message', [
                'push_id' => $this->pushMessage->id,
                'roomObj' => $roomRef->snapshot()->data(),
            ]);

            // 瀏覽器推播通知
            if (!empty($user->liveWebNotification->tokens)) {
                $this->messagingHandle->handle('push_web_notification', [
                    'webNotification' => $user->liveWebNotification,
                    'title'           => $this->admin['name'].' 傳訊息給你',
                    'message'         => '說「'.$this->getSummaryStripTags($this->pushMessage->content, 60).'」',
                    'link'            => config('params.wdv3.user_url').'/message?admin_id='.$this->admin['id'],
                ]);
            }

            // Email通知
            $this->newMessageAdminToUserService->sendMail($this->admin, $user, $this->pushMessage->content, now());

            // 新增全站推播紀錄
            $this->pushMessage->logs()->create(['receiver_id' => $user->id]);
        }
    }

    // 一年內未發佈分享文，但有互動的新娘
    private function getActiveUsers()
    {
        $users = $this->user->select('id', 'name', 'email')
                            ->doesntPostsHasInteractive();

        // 測試環境只送通知給管理員意思意思就好拉，好嗎？
        if (env('APP_DEBUG')) {
            $users = $users->where('is_admin', 1);
        }

        return $users->get();
    }

    // 新增聊天室
    private function addRoomDocument($user)
    {
        return $this->database
                    ->collection($this->collectionKey)
                    ->add([
                        'admin_id'                  => $this->pushMessage->admin_id,
                        'admin_unread'              => 0,
                        'admin_last_msg_updated_at' => now(),
                        'is_push_unread'            => true,
                        'user_id'                   => $user->id,
                        'user_unread'               => 1,
                        'user_last_msg_updated_at'  => NULL,
                        'store_type'                => NULL,
                        'last_msg_text'             => $this->pushMessage->content,
                        'last_msg_user'             => 'admin',
                        'last_msg_updated_at'       => now(),
                        'updated_at'                => now(),
                        'created_at'                => now(),
                    ]);
    }

    // 更新聊天室
    private function updateRoomDocument($roomSanp, $user)
    {
        $roomRef = $this->database
                        ->collection($this->collectionKey)
                        ->document($roomSanp->id());

        $data = $this->formatUpdateParameters([
            'admin_id'                  => $this->pushMessage->admin_id,
            'admin_last_msg_updated_at' => now(),
            'is_push_unread'            => true,
            'user_id'                   => $user->id,
            'user_unread'               => $roomSanp->get('user_unread') + 1,
            'last_msg_text'             => $this->pushMessage->content,
            'last_msg_user'             => 'admin',
            'last_msg_updated_at'       => now(),
            'updated_at'                => now(),
        ]);
        $roomRef->update($data);

        return $roomRef;
    }
}
