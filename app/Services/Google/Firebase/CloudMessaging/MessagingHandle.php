<?php
declare(strict_types=1);
/*
 |------------------------------------
 | Firebase Cloud Messaging(FCM) 推播服務
 |------------------------------------
 |
 |
 */

namespace App\Services\Google\Firebase\CloudMessaging;

use Illuminate\Support\Str;
use App\Services\Google\Firebase\CloudMessaging\Contract\AbsMessagingTypeHandle;

class MessagingHandle
{
    /**
     * 推播處理邏輯
     * @param $type
     * @param $data
     * @return array
     */
    public function handle($type, $data)
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Google\Firebase\CloudMessaging\Type\Push
         |
         */
        $classPath = 'App\Services\Google\Firebase\CloudMessaging\Type\\' . Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsMessagingTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data);
    }
}
