<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | Firebase Cloud Messaging Type 處理的 abstract class
 |--------------------------------------
 | template pattern
 |
 */

namespace App\Services\Google\Firebase\CloudMessaging\Contract;

abstract class AbsMessagingTypeHandle
{
    /** Firebase 參數 */
    protected $messaging;

    /** 傳入參數 */
    protected $type;
    protected $data;

    /**
     * Handler logic
     * @param $data:
     * @return void
     */
    public function handle($type, $data)
    {
        $this->messaging = app('firebase.messaging');
        $this->type      = $type;
        $this->data      = $data;

        return $this->run();
    }

    /**
     * 執行 Firestore
     * @return void
     */
    abstract public function run();
}
