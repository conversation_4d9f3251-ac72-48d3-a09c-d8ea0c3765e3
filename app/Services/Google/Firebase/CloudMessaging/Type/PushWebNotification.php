<?php
declare(strict_types=1);
/*------------------------------------
 | 即時通訊-商家全站推播
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\CloudMessaging\Type;

use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\WebPushConfig;
use App\Services\Google\Firebase\CloudMessaging\Contract\AbsMessagingTypeHandle;

class PushWebNotification extends AbsMessagingTypeHandle
{
    /**
     * 即時通訊-商家全站推播
     * @return void
     */
    public function run()
    {
        $webNotification = $this->data['webNotification'];

        // Example from https://firebase.google.com/docs/cloud-messaging/admin/send-messages#webpush_specific_fields
        $config = WebPushConfig::fromArray([
            'notification' => [
                'title' => $this->data['title'],
                'body'  => $this->data['message'],
                'icon'  => config('params.file_url').'/wedding-icon/browser_push.png',
            ],
            'data' => [
                'link' => $this->data['link'],
            ],
        ]);
        $message = CloudMessage::new()->withWebPushConfig($config);

        // 分割多個陣列, You can send send one message to up to 500 devices.
        $validTokens = [];
        $tokenChunks = array_chunk($webNotification->tokens, 500);
        foreach ($tokenChunks as $tokens) {
            $report = $this->messaging->sendMulticast($message, $tokens);
            $validTokens = array_merge($validTokens, $report->validTokens());
        }

        // 更新有效tokens
        $webNotification->tokens = $validTokens;
        $webNotification->save();
    }
}
