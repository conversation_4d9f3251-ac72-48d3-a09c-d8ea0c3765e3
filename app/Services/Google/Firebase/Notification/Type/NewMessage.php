<?php
declare(strict_types=1);
/*------------------------------------
 | 小鈴鐺通知-新訊息通知
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Notification\Type;

use App\Services\Google\Firebase\Notification\Contract\AbsNotificationTypeHandle;

class NewMessage extends AbsNotificationTypeHandle
{
    /**
     * 小鈴鐺通知-推播訊息通知
     * @return void
     */
    public function run()
    {
        // 聊天室資訊
        $roomObj = $this->data['roomObj'];

        // 新增使用者通知(文件), user_{user_id}_message_admin_{admin_id}
        $this->database
                ->collection($this->collectionKey)
                ->document('user_'.$roomObj['user_id'].'_message_admin_'.$roomObj['admin_id'])
                ->set([
                    'target_type' => 'user',
                    'target_id'   => $roomObj['user_id'],
                    'type'        => $this->type,
                    'admin_id'    => $roomObj['admin_id'],
                    'text'        => $roomObj['last_msg_text'],
                    'created_at'  => now(),
                    'is_read'     => false,
                    'is_click'    => false,
                ]);
    }
}
