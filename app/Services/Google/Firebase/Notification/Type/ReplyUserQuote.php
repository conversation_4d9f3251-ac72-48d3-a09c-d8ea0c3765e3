<?php
declare(strict_types=1);
/*------------------------------------
 | 小鈴鐺通知-商家回應主動報價
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Notification\Type;

use App\Services\Google\Firebase\Notification\Contract\AbsNotificationTypeHandle;

class ReplyUserQuote extends AbsNotificationTypeHandle
{
    /**
     * 小鈴鐺通知-商家回應主動報價
     * @return void
     */
    public function run()
    {
        // 取得商家的主動報價
        $storeQuote = $this->data;

        // 取得使用者通知(文件), user_{user_id}_reply_user_quote_{user_quote_id}
        $userQuote = $storeQuote->userQuote;
        $user_id   = $userQuote->user_id;
        $userRef = $this->database
                        ->collection($this->collectionKey)
                        ->document('user_'.$user_id.'_'.$this->type.'_'.$userQuote->id);

        // 欄位資訊
        $data = [
            'target_type'     => 'user',
            'target_id'       => $user_id,
            'type'            => $this->type,
            'user_quote_id'   => $userQuote->id,
            'user_quote_type' => $userQuote->present()->store_type_name,
            'store_ids'       => [$storeQuote->store_id],
            'text'            => $storeQuote->present()->detail_text(40),
            'created_at'      => now(),
            'is_read'         => false,
            'is_click'        => false,
        ];

        // 儲存通知
        $userSanp = $userRef->snapshot();
        if ($userSanp->exists()) {
            $storeIds = $userSanp->get('store_ids');
            $data['store_ids'] = array_values(array_unique(array_merge($data['store_ids'], $storeIds)));
            $data = $this->formatUpdateParameters($data);
            $userRef->update($data);
        } else {
            $userRef->create($data);
        }
    }
}
