<?php
declare(strict_types=1);
/*------------------------------------
 | 小鈴鐺通知-新上架商家
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Notification\Type;

use App\Services\Google\Firebase\Notification\Contract\AbsNotificationTypeHandle;
use App\Models\User;

class ReleaseStore extends AbsNotificationTypeHandle
{
    /**
     * 小鈴鐺通知-新上架商家
     *
     * @return void
     */
    public function run()
    {
        // 取得商家
        $store = $this->data;

        // 一年半內的近期活躍新娘 (最近有登入)
        $users = $this->getActiveUsers();

        // 分割多個陣列, maximum 500 writes allowed per request
        $useChunks = $users->chunk(500);
        foreach ($useChunks as $users) {

            // 批次處理
            $batch = $this->database->batch();
            foreach ($users as $user) {

                // 取得使用者通知(文件), user_{user_id}_release_store_{store_id}
                $userRef = $this->database
                                ->collection($this->collectionKey)
                                ->document('user_'.$user->id.'_'.$this->type.'_'.$store->id);

                // 儲存通知
                $batch->set($userRef, [
                    'target_type' => 'user',
                    'target_id'   => $user->id,
                    'type'        => $this->type,
                    'store_id'    => $store->id,
                    'created_at'  => now(),
                    'is_read'     => false,
                    'is_click'    => false,
                ]);
            }

            // 執行批次
            if (!$batch->isEmpty()) {
                $batch->commit();
            }
        }
    }

    /**
     * 一年半內的近期活躍新娘 (最近有登入)
     *
     * @return model $users
     */
    private function getActiveUsers()
    {
        $afterAt = now()->subMonths(18);

        $users = User::select('id')
                    ->recentlyActive($afterAt);

        // 測試環境只送通知給管理員意思意思就好拉，好嗎？
        if (env('APP_DEBUG')) {
            $users = $users->where('is_admin', 1);
        }

        return $users->get();
    }
}
