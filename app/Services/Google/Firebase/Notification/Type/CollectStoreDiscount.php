<?php
declare(strict_types=1);
/*------------------------------------
 | 小鈴鐺通知-收藏的商家獨家優惠即將到期
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Notification\Type;

use App\Services\Google\Firebase\Notification\Contract\AbsNotificationTypeHandle;

class CollectStoreDiscount extends AbsNotificationTypeHandle
{
    /**
     * 小鈴鐺通知-收藏的商家獨家優惠即將到期
     * @return void
     */
    public function run()
    {
        // 通知列表
        foreach ($this->data as $countdown => $stores) {
            foreach ($stores as $store_id => $users) {

                // 分割多個陣列, maximum 500 writes allowed per request
                $useChunks = array_chunk($users, 500);
                foreach ($useChunks as $users) {

                    // 批次處理
                    $batch = $this->database->batch();
                    foreach ($users as $user_id) {

                        // 取得使用者通知(文件), user_{user_id}_collect_store_discount_{store_id}
                        $userRef = $this->database
                                        ->collection($this->collectionKey)
                                        ->document('user_'.$user_id.'_'.$this->type.'_'.$store_id);

                        // 儲存通知
                        $batch->set($userRef, [
                            'target_type' => 'user',
                            'target_id'   => $user_id,
                            'type'        => $this->type,
                            'store_id'    => $store_id,
                            'countdown'   => $countdown,
                            'created_at'  => now(),
                            'is_read'     => false,
                            'is_click'    => false,
                        ]);
                    }

                    // 執行批次
                    if (!$batch->isEmpty()) {
                        $batch->commit();
                    }
                }
            }
        }
    }
}
