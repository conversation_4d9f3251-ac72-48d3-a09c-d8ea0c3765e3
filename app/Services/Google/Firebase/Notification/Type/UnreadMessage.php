<?php
declare(strict_types=1);
/*------------------------------------
 | 小鈴鐺通知-未讀訊息通知
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Google\Firebase\Notification\Type;

use App\Services\Google\Firebase\Notification\Contract\AbsNotificationTypeHandle;

class UnreadMessage extends AbsNotificationTypeHandle
{
    /**
     * 小鈴鐺通知-未讀訊息通知
     * @return void
     */
    public function run()
    {
        // 聊天室資訊
        $roomObj = $this->data;

        // 儲存使用者通知(文件), user_{user_id}_message_store_{store_id}
        $this->database
                ->collection($this->collectionKey)
                ->document('user_'.$roomObj['user_id'].'_message_store_'.$roomObj['store_id'])
                ->set([
                    'target_type' => 'user',
                    'target_id'   => $roomObj['user_id'],
                    'type'        => 'unread_message',
                    'store_id'    => $roomObj['store_id'],
                    'text'        => $roomObj['last_msg_text'],
                    'created_at'  => now(),
                    'is_read'     => false,
                    'is_click'    => false,
                ]);
    }
}
