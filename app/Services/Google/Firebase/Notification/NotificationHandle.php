<?php
declare(strict_types=1);
/*
 |------------------------------------
 | Firebase 小鈴鐺通知處理
 |------------------------------------
 |
 |
 */

namespace App\Services\Google\Firebase\Notification;

use Illuminate\Support\Str;
use App\Services\Google\Firebase\Notification\Contract\AbsNotificationTypeHandle;

class NotificationHandle
{
    /**
     * 小鈴鐺通知處理邏輯
     * @param $type
     * @param $data
     * @return array
     */
    public function handle($type, $data)
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Google\Firebase\Notification\Type\ForumComment
         |  App\Services\Google\Firebase\Notification\Type\ForumAtUser
         |  App\Services\Google\Firebase\Notification\Type\ForumLike
         |  App\Services\Google\Firebase\Notification\Type\ForumTrack
         |  App\Services\Google\Firebase\Notification\Type\ShareComment
         |  App\Services\Google\Firebase\Notification\Type\ShareAtUser
         |  App\Services\Google\Firebase\Notification\Type\ShareLike
         |  App\Services\Google\Firebase\Notification\Type\ShareCollect
         |  App\Services\Google\Firebase\Notification\Type\StoreQuote
         |  App\Services\Google\Firebase\Notification\Type\CollectStoreWork
         |  App\Services\Google\Firebase\Notification\Type\CollectStoreService
         |  App\Services\Google\Firebase\Notification\Type\CollectStoreDiscount
         |  App\Services\Google\Firebase\Notification\Type\NewStore
         |  App\Services\Google\Firebase\Notification\Type\NewMessage
         |
         */
        $classPath = 'App\Services\Google\Firebase\Notification\Type\\' . Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsNotificationTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data);
    }
}
