<?php
/*
 |------------------------------------
 | 紀錄當天的GA事件
 |------------------------------------
 |
 |
 */

namespace App\Services\Google\BigQuery;

use App\Services\Google\BigQuery\BigQueryHandle;
use App\Models\Wdv2\StoreQuote;
use App\Models\LogGaEvent;

class LogEventService
{
    protected $bigQueryHandle;
    protected $storeQuote;
    protected $logGaEvent;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        BigQueryHandle $bigQueryHandle,
        StoreQuote $storeQuote,
        LogGaEvent $logGaEvent
    ) {
        $this->bigQueryHandle = $bigQueryHandle;
        $this->storeQuote      = $storeQuote;
        $this->logGaEvent      = $logGaEvent;
    }

    /**
     * 紀錄當天的GA事件
     * @param date|Y-m-d $data
     * @param bool $intraday 是否使用BigQuery當日的臨時表
     *
     * @return int $total
     */
    public function run($date, $intraday = true)
    {
        $total = 0;

        // 商家的外站點擊
        $analytics = $this->bigQueryHandle->handle('store_type_events', [
            'dateRange' => $date,
            'eventName' => 'external_link',
            'groupKeys' => [
                'int'    => 'store_id',
                'string' => 'click_type',
            ],
        ], $intraday);
        foreach ($analytics as $item) {
            $this->logGaEvent->updateOrCreate([
                'type'         => $item['click_type'],
                'target_id'    => $item['store_id'],
                'created_date' => $date,
            ], [
                'totalEvents' => $item['total_events'],
                'totalUsers'  => $item['total_users'],
            ]);
            $total++;
        }

        // 主動報價的外站點擊
        $analytics = $this->bigQueryHandle->handle('store_type_events', [
            'dateRange' => $date,
            'eventName' => 'external_link',
            'groupKeys' => [
                'string' => 'target_id',
            ],
            'eventParams' => [
                'BEGINS_WITH' => ['target_id' => 'quote_'],
            ],
        ], $intraday);
        foreach ($analytics as $item) {
            $storeQuoteId = explode('_', $item['target_id'])[1] ?? NULL; // target_id = "quote_{storeQuoteId}"
            if (!$storeQuoteId) {
                continue;
            }
            $this->logGaEvent->updateOrCreate([
                'type'         => 'quote',
                'target_id'    => $storeQuoteId,
                'parent_id'    => $this->storeQuote->find($storeQuoteId)->store_id ?? NULL, // 所屬商家ID
                'created_date' => $date,
            ], [
                'totalEvents' => $item['total_events'],
                'totalUsers'  => $item['total_users'],
            ]);
            $total++;
        }

        return $total;
    }
}
