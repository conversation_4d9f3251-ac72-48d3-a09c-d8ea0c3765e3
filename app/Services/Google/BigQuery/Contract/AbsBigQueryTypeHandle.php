<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | Google BigQuery Type 處理的 abstract class
 |--------------------------------------
 | template pattern
 |
 */

namespace App\Services\Google\BigQuery\Contract;

use BigQuery;

abstract class AbsBigQueryTypeHandle
{
    /** BigQuery 參數 */
    const DATASET              = 'analytics_251282431';
    const TABLEPREFIX          = 'events_';
    const TABLEPREFIX_INTRADAY = 'events_intraday_';

    /** 傳入參數 */
    protected $type;
    protected $data;
    protected $intraday;
    protected $table;

    protected $result = []; // 回傳值

    /**
     * Handler logic
     * @param string $type 資料類型
     * @param object $data 參數設定
     * @param bool $intraday 是否使用BigQuery當日的臨時表
     * @return object $this->result
     */
    public function handle($type, $data, $intraday = true)
    {
        $this->type     = $type;
        $this->data     = $data;
        $this->intraday = $intraday;

        // 預設使用BigQuery當日的臨時表
        if ($intraday) {
            $this->table = self::DATASET.'.'.self::TABLEPREFIX_INTRADAY.'*';
        } else {
            $this->table = self::DATASET.'.'.self::TABLEPREFIX.'*';
        }

        return $this->run();
    }

    /**
     * 執行 Google BigQuery
     * @return void
     */
    abstract public function run();

    /**
     * 取得 BigQuery 上的資料
     * @return object $this->result
     */
    protected function getBigQuery($query)
    {
        $queryJobConfig = BigQuery::query($query);
        $queryResults   = BigQuery::runQuery($queryJobConfig);

        if ($queryResults->isComplete()) {
            $rows = $queryResults->rows();

            // 若BigQuery當日的臨時表沒有資料，則改取BigQuery的歷史總表
            if ($this->intraday && !$rows->valid()) {
                return $this->handle($this->type, $this->data, false);
            }

            // 轉換資料
            $this->transformData($rows);

            return $this->result;
        } else {
            throw new Exception('The query failed to complete');
        }
    }

    /**
     * 取得 BigQuery 事件條件的 SQL語法
     * @return string $sql
     */
    protected function getEventConditionSql()
    {
        $sql = '';

        $eventParams = $this->data['eventParams'] ?? [];
        foreach ($eventParams as $operator => $params) {
            foreach ($params as $key => $value) {
                switch ($operator) {
                    // 數值
                    case 'INT':
                        $sql .= " AND (SELECT value.int_value FROM UNNEST(event_params) WHERE KEY = '$key') = $value";
                        break;

                    // 完全比對字串
                    case 'EXACT':
                       $sql .= " AND (SELECT value.string_value FROM UNNEST(event_params) WHERE KEY = '$key') = '$value'";
                        break;

                    // 字串包含
                    case 'PARTIAL':
                       $sql .= " AND (SELECT value.string_value FROM UNNEST(event_params) WHERE KEY = '$key') LIKE '%$value%'";
                        break;

                    // 開頭字串
                    case 'BEGINS_WITH':
                       $sql .= " AND (SELECT value.string_value FROM UNNEST(event_params) WHERE KEY = '$key') LIKE '$value%'";
                        break;

                    // 結尾字串
                    case 'ENDS_WITH':
                       $sql .= " AND (SELECT value.string_value FROM UNNEST(event_params) WHERE KEY = '$key') LIKE '%$value'";
                        break;

                    // 規則運算式
                    case 'REGEXP':
                       $sql .= " AND REGEXP_CONTAINS((SELECT value.string_value FROM UNNEST(event_params) WHERE KEY = '$key'), '$value')";
                        break;

                    default:
                        break;
                }
            }
        }

        return $sql;
    }

    /**
     * 取得 BigQuery 事件集合的 SQL語法
     * @return string $sql
     */
    protected function getEventGroupSql()
    {
        $selectSql = '';
        $groupSql  = '';

        $groupKeys = $this->data['groupKeys'] ?? [];
        foreach ($groupKeys as $type => $key) {
            $selectSql .= "(SELECT value.{$type}_value FROM UNNEST(event_params) WHERE KEY = '$key') AS $key,";
            $groupSql  .= ','.$key;
        }

        return [$selectSql, substr($groupSql, 1)];
    }
}
