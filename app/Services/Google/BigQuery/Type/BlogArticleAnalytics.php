<?php
/**
 *--------------------------------------
 *  好婚專欄文章的Analytics資料
 *--------------------------------------
 *
 * @param date|array.date $data
 *  [
 *      'Y-m-d', // 時間區間-起始日
 *      'Y-m-d', // 時間區間-結束日
 *  ]
 *
 * @return array.object $analytics 分析資料
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class BlogArticleAnalytics extends AbsBigQueryTypeHandle
{
    /**
     * 好婚專欄文章的Analytics資料
     *
     */
    public function run()
    {
        if (is_array($this->data)) {
            $startDate = date('Ymd', strtotime($this->data[0]));
            $endDate   = date('Ymd', strtotime($this->data[1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data));
        }

        $query = <<<ENDSQL
SELECT
  SPLIT(SPLIT((
      SELECT
        value.string_value
      FROM
        UNNEST(event_params)
      WHERE
        KEY = 'page_location'), 'https://www.weddingday.com.tw')[SAFE_OFFSET(1)], '?')[SAFE_OFFSET(0)] AS page,
  COUNT(*) AS page_views,
  COUNT(DISTINCT user_pseudo_id) AS total_users
FROM
  `$this->table`
WHERE
  _table_suffix BETWEEN '$startDate' AND '$endDate'
  AND event_name = 'page_view'
  AND (
  SELECT
    value.string_value
  FROM
    UNNEST(event_params)
  WHERE
    KEY = 'page_location') LIKE 'https://www.weddingday.com.tw/blog/archives/%'
GROUP BY
  page
ORDER BY
  page ASC;
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $pattern = '/\/blog\/archives\/([0-9]+)/';

        foreach($rows as $item) {
            preg_match($pattern, $item['page'], $urlMatch);
            if (!$urlMatch) {
                continue;
            }

            $articleID = $urlMatch[1];
            if (!isset($this->result[$articleID])) {
                $this->result[$articleID] = (object)[
                    'pageViews'  => 0,
                    'totalUsers' => 0,
                ];
            }

            $this->result[$articleID]->pageViews  += $item['page_views'];
            $this->result[$articleID]->totalUsers += $item['total_users'];
        }
    }
}
