<?php
/**
 *--------------------------------------
 *  撈出近90天有page view的文章id
 *--------------------------------------
 *
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class ActiveArticles extends AbsBigQueryTypeHandle
{
    public function run()
    {
        if (is_array($this->data['dateRange'])) {
            $startDate = date('Ymd', strtotime($this->data['dateRange'][0]));
            $endDate   = date('Ymd', strtotime($this->data['dateRange'][1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data['dateRange']));
        }
        $eventName = $this->data['eventName'];
        $location = $this->data['location'];

        $locationCondition = '';
        if ($location) {
            $locationCondition = "AND (SELECT ep.value.string_value
                                      FROM UNNEST(event_params) ep
                                      WHERE ep.key = 'location') = '$location'";
        }

        $query = <<<ENDSQL
SELECT
  DISTINCT (SELECT ep.value.int_value
            FROM UNNEST(event_params) ep
            WHERE ep.key = 'target_id') AS target_id,
  count(*) AS total_page_view, -- 總的 page view 數（有重複）
FROM
  `$this->table`
WHERE
  event_name = '$eventName'
  AND _table_suffix BETWEEN '$startDate' AND '$endDate'
  $locationCondition
group by target_id
order by total_page_view desc;
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $rowsArray = iterator_to_array($rows);
        $this->result = array_map(fn($item) => $item['target_id'], $rowsArray);
    }
}
