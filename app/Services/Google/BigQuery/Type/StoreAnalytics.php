<?php
/**
 *--------------------------------------
 *  商家的Analytics資料
 *--------------------------------------
 *
 * @param date|array.date $data
 *  [
 *      'Y-m-d', // 時間區間-起始日
 *      'Y-m-d', // 時間區間-結束日
 *  ]
 *
 * @return array.object $analytics 分析資料
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class StoreAnalytics extends AbsBigQueryTypeHandle
{
    /**
     * 商家的Analytics資料
     *
     */
    public function run()
    {
        if (is_array($this->data)) {
            $startDate = date('Ymd', strtotime($this->data[0]));
            $endDate   = date('Ymd', strtotime($this->data[1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data));
        }

        $query = <<<ENDSQL
SELECT
  SPLIT(SPLIT((
      SELECT
        value.string_value
      FROM
        UNNEST(event_params)
      WHERE
        KEY = 'page_location'), 'https://www.weddingday.com.tw')[SAFE_OFFSET(1)], '?')[SAFE_OFFSET(0)] AS page,
  COUNTIF(event_name = 'page_view') AS page_views,
  COUNT(DISTINCT
  IF
    (event_name = 'page_view', user_pseudo_id, NULL)) AS total_users,
  SUM((
    SELECT
      value.int_value
    FROM
      UNNEST(event_params)
    WHERE
      KEY = 'engagement_time_msec')) / NULLIF(COUNT(DISTINCT
    IF
      (event_name = 'page_view', user_pseudo_id, NULL)), 0) / 1000 AS avg_engagement_time,
  COUNTIF(traffic_source.medium = 'organic' AND event_name = 'page_view') AS organic_page_views,
  COUNT(DISTINCT
  IF
    (traffic_source.medium = 'organic' AND event_name = 'page_view', user_pseudo_id, NULL)) AS organic_total_users
FROM
  `$this->table`
WHERE
  _table_suffix BETWEEN '$startDate' AND '$endDate'
  AND (
  SELECT
    value.string_value
  FROM
    UNNEST(event_params)
  WHERE
    KEY = 'page_location') LIKE 'https://www.weddingday.com.tw/store-%'
GROUP BY
  page
ORDER BY
  page ASC;
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $pattern     = '/\/store-(?:studio|dress|photographer|makeup|venue|decoration|host|weddingcake)\/([0-9]+)/';
        $typePattern = '/\/store-(?:studio|dress|photographer|makeup|venue|decoration|host|weddingcake)\/([0-9]+)\/?(?:.*)?\/(album|video|service|room|member)\/([0-9]+)/';

        foreach($rows as $item) {
            preg_match($pattern, $item['page'], $urlMatch);
            if (!$urlMatch) {
                continue;
            }

            // 商家ID
            $storeId = $urlMatch[1];

            // 累計商家的所有頁面數據
            $this->pushResult($storeId, $item);

            // 商家的所有功能頁面
            preg_match($typePattern, $item['page'], $typeMatch);
            if (empty($typeMatch[2]) OR empty($typeMatch[3])) {
                continue;
            }

            // 功能類型 & ID
            $functionType = $typeMatch[2];
            $functionId   = $typeMatch[3];

            // 相本
            if ($functionType == 'album') {
                $this->pushResultForFunction($storeId, 'album', $functionId, $item);
            }

            // 影片
            if ($functionType == 'video') {
                $this->pushResultForFunction($storeId, 'video', $functionId, $item);
            }

            // 服務
            if ($functionType == 'service') {
                $this->pushResultForFunction($storeId, 'service', $functionId, $item);
            }

            // 廳房
            if ($functionType == 'room') {
                $this->pushResultForFunction($storeId, 'venueRoom', $functionId, $item);
            }

            // 成員
            if ($functionType == 'member') {
                $this->pushResultForFunction($storeId, 'member', $functionId, $item);
            }
        }

        // 統計平均停留時間
        foreach ($this->result as $storeId => $row) {
            if (!$row->pageViews) {
                continue;
            }
            $this->result[$storeId]->avgEngagementTime = $row->avgEngagementTime / $row->pageViews;
        }

        return $this->result;
    }

    // 累計商家的所有頁面數據
    protected function pushResult($storeId, $item)
    {
        // 所有商家頁面 (所有流量&自然流量) 初始化
        if (empty($this->result[$storeId])) {
            $this->result[$storeId] = (object)[
                'pageViews'         => 0,
                'totalUsers'        => 0,
                'avgEngagementTime' => 0,
            ];
            $this->result[$storeId]->organic = (object)[
                'pageViews'  => 0,
                'totalUsers' => 0,
            ];
        }

        // 所有商家頁面 (所有流量)
        $this->result[$storeId]->pageViews           += $item['page_views'];
        $this->result[$storeId]->totalUsers          += $item['total_users'];
        $this->result[$storeId]->avgEngagementTime   += ($item['avg_engagement_time'] * $item['page_views']);
        $this->result[$storeId]->organic->pageViews  += $item['organic_page_views'];
        $this->result[$storeId]->organic->totalUsers += $item['organic_total_users'];
    }

    // 累計商家的所有功能頁面
    protected function pushResultForFunction($storeId, $functionType, $functionId, $item)
    {
        // 商家的所有功能頁面 初始化
        if (empty($this->result[$storeId]->{$functionType}[$functionId])) {
            $this->result[$storeId]->{$functionType}[$functionId] = (object)[
                'pageViews'  => 0,
                'totalUsers' => 0,
            ];
        }

        // 累計
        $this->result[$storeId]->{$functionType}[$functionId]->pageViews  += $item['page_views'];
        $this->result[$storeId]->{$functionType}[$functionId]->totalUsers += $item['total_users'];
    }
}
