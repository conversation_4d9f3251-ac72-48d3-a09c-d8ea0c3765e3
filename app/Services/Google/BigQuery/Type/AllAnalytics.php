<?php
/**
 *--------------------------------------
 *  所有頁面的Analytics資料
 *--------------------------------------
 *
 * @param date|array.date $data
 *  [
 *      'Y-m-d', // 時間區間-起始日
 *      'Y-m-d', // 時間區間-結束日
 *  ]
 *
 * @return array.object $analytics 分析資料
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;
use stdClass;

class AllAnalytics extends AbsBigQueryTypeHandle
{
    /**
     * 所有頁面的Analytics資料
     *
     */
    public function run()
    {
        if (is_array($this->data)) {
            $startDate = date('Ymd', strtotime($this->data[0]));
            $endDate   = date('Ymd', strtotime($this->data[1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data));
        }

        $query = <<<ENDSQL
SELECT
  COUNT(*) AS page_views,
  COUNT(DISTINCT user_pseudo_id) AS total_users
FROM
  `$this->table`
WHERE
  _table_suffix BETWEEN '$startDate' AND '$endDate'
  AND event_name = 'page_view';
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $item = $rows->current();

        // 若BigQuery當日的臨時表沒有資料，則改取BigQuery的歷史總表
        if ($this->intraday && !$item['page_views']) {
            return $this->handle($this->type, $this->data, false);
        }

        $this->result[] = (object)[
          'pageViews'  => $item['page_views'],
          'totalUsers' => $item['total_users'],
        ];
    }
}
