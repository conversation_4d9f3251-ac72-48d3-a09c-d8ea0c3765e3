<?php
/**
 *--------------------------------------
 *  商家事件總數
 *--------------------------------------
 *
 * @param date|array.date $data
 *  [
 *      'Y-m-d', // 時間區間-起始日
 *      'Y-m-d', // 時間區間-結束日
 *  ]
 *
 * @param array $data
 *  [
 *      'dateRange' => [
 *          'Y-m-d', // 時間區間-起始日
 *          'Y-m-d', // 時間區間-結束日
 *      ],
 *      'eventName' => '', // 事件名稱
 *      'eventParams' => [ // 維度篩選
 *          $operator => [ // 運算符 INT:數值 EXACT:完全比對字串 PARTIAL:字串包含 BEGINS_WITH:開頭字串 ENDS_WITH:結尾字串 REGEXP:規則運算式
 *              $key => $value, // 維度篩選 event_params.key = event_params.value
 *          ],
 *      ],
 *  ]
 *
 * @return int $totalEvents 事件總數
 *
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class StoreTotalEvents extends AbsBigQueryTypeHandle
{
    /**
     * 商家事件總數
     *
     */
    public function run()
    {
        if (is_array($this->data['dateRange'])) {
            $startDate = date('Ymd', strtotime($this->data['dateRange'][0]));
            $endDate   = date('Ymd', strtotime($this->data['dateRange'][1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data['dateRange']));
        }

        $eventName = $this->data['eventName'];
        $condition = $this->getEventConditionSql();

        $query = <<<ENDSQL
SELECT
  COUNT(*) AS total_events
FROM
  `$this->table`
WHERE
  _table_suffix BETWEEN '$startDate' AND '$endDate'
  AND event_name = '$eventName'
  $condition;
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $item = $rows->current();
        $this->result = $item['total_events'];
    }
}
