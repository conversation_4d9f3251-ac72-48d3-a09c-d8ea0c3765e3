<?php
/**
 *--------------------------------------
 *  新娘搜尋婚期的瀏覽量
 *--------------------------------------
 *
 * @param date|array.date $data
 *  [
 *      'Y-m-d', // 時間區間-起始日
 *      'Y-m-d', // 時間區間-結束日
 *  ]
 *
 * @return array.int $pageViews 瀏覽量
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class WeddingDayPageViews extends AbsBigQueryTypeHandle
{
    /**
     * 新娘搜尋婚期的瀏覽量
     *
     */
    public function run()
    {
        if (is_array($this->data)) {
            $startDate = date('Ymd', strtotime($this->data[0]));
            $endDate   = date('Ymd', strtotime($this->data[1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data));
        }

        $query = <<<ENDSQL
SELECT
  SPLIT((
    SELECT
      value.string_value
    FROM
      UNNEST(event_params)
    WHERE
      KEY = 'page_location'), 'https://www.weddingday.com.tw')[SAFE_OFFSET(1)] AS page,
  COUNT(*) AS page_views
FROM
  `$this->table`
WHERE
  _table_suffix BETWEEN '$startDate' AND '$endDate'
  AND event_name = 'page_view'
  AND (
  SELECT
    value.string_value
  FROM
    UNNEST(event_params)
  WHERE
    KEY = 'page_location') LIKE '%date=%'
GROUP BY
  page
ORDER BY
  page ASC;
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $pattern = '/date=([0-9]{4}-[0-9]{2}-[0-9]{2})/';

        foreach($rows as $item) {
            preg_match($pattern, $item['page'], $urlMatch);
            if (!$urlMatch) {
                continue;
            }

            $date = $urlMatch[1];
            if (!isset($this->result[$date])) {
                $this->result[$date] = 0;
            }

            $this->result[$date] += $item['page_views'];
        }
    }
}
