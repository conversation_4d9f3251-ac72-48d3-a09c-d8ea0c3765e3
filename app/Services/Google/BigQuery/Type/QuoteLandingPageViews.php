<?php
/**
 *--------------------------------------
 *  公開報價登陸頁的瀏覽量
 *--------------------------------------
 *
 * @param date|array.date $data
 *  [
 *      'Y-m-d', // 時間區間-起始日
 *      'Y-m-d', // 時間區間-結束日
 *  ]
 *
 * @return int $pageViews 瀏覽量
 *
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class QuoteLandingPageViews extends AbsBigQueryTypeHandle
{
    /**
     * 公開報價登陸頁的瀏覽量
     *
     */
    public function run()
    {
        if (is_array($this->data)) {
            $startDate = date('Ymd', strtotime($this->data[0]));
            $endDate   = date('Ymd', strtotime($this->data[1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data));
        }

        $query = <<<ENDSQL
SELECT
  SUM(page_views) AS total
FROM (
  SELECT
    SPLIT(SPLIT((
        SELECT
          value.string_value
        FROM
          UNNEST(event_params)
        WHERE
          KEY = 'page_location'), 'https://www.weddingday.com.tw')[SAFE_OFFSET(1)], '?')[SAFE_OFFSET(0)] AS page,
    COUNT(*) AS page_views,
    COUNT(DISTINCT user_pseudo_id) AS total_users
  FROM
    `$this->table`
  WHERE
    _table_suffix BETWEEN '$startDate' AND '$endDate'
    AND event_name = 'page_view'
    AND (REGEXP_CONTAINS((
        SELECT
          value.string_value
        FROM
          UNNEST(event_params)
        WHERE
          KEY = 'page_location'), 'www.weddingday.com.tw/(quote|user/quote/add)'))
  GROUP BY
    page
  ORDER BY
    page ASC);
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        foreach($rows as $item) {
            $this->result = $item['total'];
        }
    }
}
