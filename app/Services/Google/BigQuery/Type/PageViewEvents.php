<?php
/**
 *--------------------------------------
 *  撈出近90天有page view的文章id
 *--------------------------------------
 *
 */

namespace App\Services\Google\BigQuery\Type;

use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class PageViewEvents extends AbsBigQueryTypeHandle
{
    public function run()
    {
        if (is_array($this->data['dateRange'])) {
            $startDate = date('Ymd', strtotime($this->data['dateRange'][0]));
            $endDate   = date('Ymd', strtotime($this->data['dateRange'][1]));
        } else {
            $startDate = $endDate = date('Ymd', strtotime($this->data['dateRange']));
        }
        $eventName = $this->data['eventName'];
        $location = $this->data['location'];
        $targetId = $this->data['targetId']? $this->data['targetId'] : null;
        $storeId = $this->data['storeId']? $this->data['storeId'] : null;

        $locationCondition = '';
        if ($location) {
            $locationCondition = "AND location = '$location'";
        }
        $storeIdCondition = '';
        if ($storeId) {
            $storeIdCondition = "AND store_id = '$storeId'";
        }

        $query = <<<ENDSQL
SELECT
  target_id,
  COUNT(*) AS total_page_view, -- 總的 page view 數（有重複）
  COUNT(DISTINCT CONCAT(user_pseudo_id, CAST(session_id AS STRING))) AS unique_page_view -- 不重複的 page view 數
FROM (
  SELECT
    user_pseudo_id,
    (SELECT ep.value.int_value FROM UNNEST(event_params) ep WHERE ep.key = 'target_id') AS target_id,
    (SELECT ep.value.string_value FROM UNNEST(event_params) ep WHERE ep.key = 'location') AS location,
    (SELECT ep.value.int_value FROM UNNEST(event_params) ep WHERE ep.key = 'ga_session_id') AS session_id,
    (SELECT ep.value.string_value FROM UNNEST(event_params) ep WHERE ep.key = 'store_id') AS store_id
  FROM
    `$this->table`
  WHERE
    event_name = '$eventName'
    AND _table_suffix BETWEEN '$startDate' AND '$endDate'
)
WHERE
  target_id IN $targetId -- << 你要查的 target_id 列表
  $locationCondition -- << 你要查的 location
  $storeIdCondition
GROUP BY
  target_id
ORDER BY
  total_page_view DESC;
ENDSQL;

        return $this->getBigQuery($query);
    }

    /**
     * 轉換資料
     *
     */
    protected function transformData($rows)
    {
        $this->result = [];
        foreach ($rows as $item) {
            $this->result[$item['target_id']] = [
                'total_page_view' => $item['total_page_view'],
                'unique_page_view' => $item['unique_page_view'],
            ];
        }
    }
}