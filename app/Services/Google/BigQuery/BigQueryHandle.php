<?php
declare(strict_types=1);
/*
 |------------------------------------
 | Google BigQuery
 |------------------------------------
 |
 |
 */

namespace App\Services\Google\BigQuery;

use Illuminate\Support\Str;
use App\Services\Google\BigQuery\Contract\AbsBigQueryTypeHandle;

class BigQueryHandle
{
    /**
     * BigQuery處理邏輯
     * @param string $type 資料類型
     * @param object $data 參數設定
     * @param bool $intraday 是否使用BigQuery當日的臨時表
     * @return array
     */
    public function handle($type, $data, $intraday = true)
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Google\BigQuery\Type\AllAnalytics
         |  App\Services\Google\BigQuery\Type\BlogArticleAnalytics
         |  App\Services\Google\BigQuery\Type\ForumArticleAnalytics
         |  App\Services\Google\BigQuery\Type\KolArticleAnalytics
         |  App\Services\Google\BigQuery\Type\QuoteLandingPageViews
         |  App\Services\Google\BigQuery\Type\SharePostAnalytics
         |  App\Services\Google\BigQuery\Type\StoreAnalytics
         |  App\Services\Google\BigQuery\Type\StoreTotalEvents
         |  App\Services\Google\BigQuery\Type\StoreTypeEvents
         |  App\Services\Google\BigQuery\Type\WeddingDayPageViews
         |  App\Services\Google\BigQuery\Type\ActiveArticles
         |  App\Services\Google\BigQuery\Type\PageViewEvents
         |
         */
        $classPath = 'App\Services\Google\BigQuery\Type\\' . Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsBigQueryTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data, $intraday);
    }
}
