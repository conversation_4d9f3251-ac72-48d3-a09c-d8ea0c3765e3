<?php
/*
 |------------------------------------
 | 紀錄當天的GA網頁瀏覽量
 |------------------------------------
 |
 |
 */

namespace App\Services\Google\BigQuery;

use App\Services\Google\BigQuery\BigQueryHandle;
use App\Models\LogGaPageView;

class LogPageViewService
{
    protected $bigQueryHandle;
    protected $logGaPageView;
    protected $date;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        BigQueryHandle $bigQueryHandle,
        LogGaPageView $logGaPageView
    ) {
        $this->bigQueryHandle = $bigQueryHandle;
        $this->logGaPageView  = $logGaPageView;
    }

    /**
     * 紀錄當天的GA網頁瀏覽量
     * @param string $type 資料類型
     * @param date|Y-m-d $data
     * @param bool $intraday 是否使用BigQuery當日的臨時表
     *
     * @return int $total
     */
    public function run($type, $date, $intraday = true)
    {
        $this->date = $date;

        // 取得當天的GA網頁瀏覽量
        $analytics = $this->bigQueryHandle->handle($type.'_analytics', $date, $intraday);
        foreach ($analytics as $id => $row) {

            // 紀錄GA網頁瀏覽量
            $this->updateOrCreateLog($type, $id, $row);

            // 紀錄商家頁面的GA網頁瀏覽量
            if ($type == 'store') {
                $this->logStorePageAnalytics($id, $row);
            }
        }

        return count($analytics);
    }

    /**
     * 實作紀錄GA網頁瀏覽量
     */
    private function updateOrCreateLog($type, $target_id, $analytics, $parent_id = NULL)
    {
        $this->logGaPageView->updateOrCreate([
            'type'         => $type,
            'target_id'    => $target_id ?: NULL, // 官網所有頁面(type=all)沒有target_id
            'parent_id'    => $parent_id,
            'created_date' => $this->date,
        ], [
            'pageViews'         => $analytics->pageViews,
            'totalUsers'        => $analytics->totalUsers,
            'avgEngagementTime' => $analytics->avgEngagementTime ?? NULL, // 目前僅商家使用
        ]);
    }

    /**
     * 紀錄商家頁面的GA網頁瀏覽量
     */
    private function logStorePageAnalytics($store_id, $analytics)
    {
        // 自然流量
        $this->updateOrCreateLog('store_organic', $store_id, $analytics->organic);

        // 相本
        if (isset($analytics->album)) {
            foreach ($analytics->album as $id => $row) {
                $this->updateOrCreateLog('store_album', $id, $row, $store_id);
            }
        }

        // 影片
        if (isset($analytics->video)) {
            foreach ($analytics->video as $id => $row) {
                $this->updateOrCreateLog('store_video', $id, $row, $store_id);
            }
        }

        // 服務
        if (isset($analytics->service)) {
            foreach ($analytics->service as $id => $row) {
                $this->updateOrCreateLog('store_service', $id, $row, $store_id);
            }
        }

        // 廳房
        if (isset($analytics->venueRoom)) {
            foreach ($analytics->venueRoom as $id => $row) {
                $this->updateOrCreateLog('store_venue_room', $id, $row, $store_id);
            }
        }

        // 成員
        if (isset($analytics->member)) {
            foreach ($analytics->member as $id => $row) {
                $this->updateOrCreateLog('store_member', $id, $row, $store_id);
            }
        }
    }
}
