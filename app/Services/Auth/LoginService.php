<?php
/*
 |--------------------------------------
 |  一般登入Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth;

use App\Repositories\UserRepository;
use App\Models\User;
use App\Models\RingUser;
use App\Services\Image\CreateImageService;
use App\Services\File\UploadImageFormService;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\ConvertPasswordTrait;

class LoginService
{
    private $createImageService;
    private $uploadImageFormService;
    private $userRepository;
    private $ringUser;
    private $user;

    use ApiErrorTrait;
    use CreateTokenTrait;
    use ConvertPasswordTrait;

    public function __construct(
        CreateImageService $createImageService,
        UploadImageFormService $uploadImageFormService,
        UserRepository $userRepository,
        RingUser $ringUser,
        User $user
    ) {
        $this->createImageService     = $createImageService;
        $this->uploadImageFormService = $uploadImageFormService;
        $this->userRepository = $userRepository;
        $this->ringUser       = $ringUser;
        $this->user           = $user;
    }

    /**
     * 一般登入
     *
     * @return user model
     */
    public function run($request)
    {
        $login = 0;
        if ($request['type'] == 'email') {
            // 驗證是否有email
            $user = $this->user->where('email', $request['email'])->first();
            if (!$user) {
                $this->setException('找不到此會員帳號，請至[link_user_register]免費註冊！ ', 4003);
            }

            // 驗證密碼是否正確
            if (!$this->isCorrectPassword($request['password'], $user->password)) {
                $this->setException('登入失敗，帳號或密碼錯誤 ');
            }

            // 驗證會員是否停用
            if ($user->status == 'delete') {
                $this->setException('此會員帳號已停用，無法使用 ', 4000, ['email' => '此會員帳號已停用，無法使用']);
            }

            $login = 1;
        } else {
            $third_column = $request['type'].'_id';

            // 第三方id是否存在 存在就登入
            if ($user = $this->user->where($third_column, $request['third_id'])->first()) {
                // 驗證會員是否停用
                if ($user->status == 'delete') {
                    $this->setException('此會員帳號已停用，無法使用 ', 4000, ['email' => '此會員帳號已停用，無法使用']);
                }
                if ($user->email_legalize == 1) {
                    $login = 1;
                    // 紀錄第三方登入資訊
                    $this->recordThirdInfo($request, $user);
                }
            // 第三方email存在 已驗證 尚未綁定此第三方: 就更新第三方id
            } else if (($request['email'] ?? false) && $user = $this->user->where('email', $request['email'])->first()) {
                // 驗證會員是否停用
                if ($user->status == 'delete') {
                    $this->setException('此會員帳號已停用，無法使用 ', 4000, ['email' => '此會員帳號已停用，無法使用']);
                }
                if ($user->{$third_column} == null && $user->email_legalize == 1) {
                    // 更新第三方id
                    $user->{$third_column} = $request['third_id'];
                    $login = 1;
                    $user->save();
                    // 紀錄第三方登入資訊
                    $this->recordThirdInfo($request, $user);
                } else {
                    // 第三方email存在 已驗證 已綁定第三方: 重新走驗證 請他去驗證其他的信箱
                    $user->email_legalize = 0;  // 是否跳驗證畫面的判斷 0:未驗證 跳 1:已驗證 不跳
                }
            // 其他情況就跳驗證畫面
            }
        }

        return [
            'user' => $login ? $this->doLoginByUser($user) : $user??null,
        ];
    }

    /**
     * 實作登入
     *
     * @param $user
     * @param bool $updateLastLogin: 是否更新最後登入時間
     * @return user model
     */
    public function doLoginByUser($user, $updateLastLogin = true)
    {
        // 綁定婚戒大賞的預約記錄
        $this->bindRingUserReserveRecord($user->id);

        // 更新最後登入時間
        if ($updateLastLogin) {
            $user->last_login_at = now();
            $user->save(); //測試時save()真的會寫入DB..無法mock..如果寫資料這件事委派給Repository addData時,就可以mock了
        }

        // 產生 user_token
        $user->token = $this->getNewToken();
        $user->tokens()->create([
            'target_id'   => $user->id,
            'type'        => 'user',
            'token'       => $user->token,
            'deadline_at' => now()->addWeek(),
        ]);

        return $user;
    }

    /**
     * 綁定婚戒大賞的預約記錄
     *
     * @param $user_id 會員ID
     * @return void
     */
    private function bindRingUserReserveRecord($userId)
    {
        // 檢查參數
        if (!request('ring_auth_token')) {
            return;
        }

        // 取得報名資訊
        $ringUser = $this->ringUser->where('auth_token', request('ring_auth_token'))
                                    ->first();
        if (!$ringUser) {
            return;
        }

        // 若已經被綁定過，則清空身份驗證碼
        if ($ringUser->user_id) {
            $ringUser->auth_token = NULL;
            $ringUser->save();
            return;
        }

        // 執行身份綁定
        $ringUser->user_id    = $userId;
        $ringUser->auth_token = NULL;
        $ringUser->save();

        // 是否有已存在的報名資訊
        $existRingUser = $this->ringUser->where('id', '!=', $ringUser->id)
                                        ->where('user_id', $userId)
                                        ->first();
        if (!$existRingUser) {
            return;
        }

        // 找出已上傳訂單的品牌ID
        $orderBrandIds = $existRingUser->orders->pluck('ring_brand_id');
        foreach ($ringUser->reserves as $reserve) {

            // 排除已上傳訂單
            if ($orderBrandIds->contains($reserve->brand_id)) {
                continue;
            }

            // 更新預約紀錄
            $item = $reserve->toArray();
            unset($item['id']);
            unset($item['ring_user_id']);
            $existRingUser->allReserves()->updateOrCreate([
                'ring_brand_id' => $reserve->ring_brand_id
            ], $item);
        }

        // 刪除重複的報名資訊
        $ringUser->delete();
    }

    /**
     * 會員第三方登入/綁定紀錄
     */
    public function recordThirdInfo($request, $user)
    {
        // 下載第三方avatar然後上傳
        if($request['third_avatar']) {
            $base64 = $this->uploadImageFormService->downloadImageAsBase64($request['third_avatar']);
            $avatarResult = $this->uploadImageFormService->uploadBase64($base64);
            $imageType = 'user_'.$request['type'].'_avatar';

            // 紀錄第三方登入資訊
            $user->recordThirdInfo($request['third_id'], $request['third_name'], $avatarResult['file_name'], $request['type']);
            // 若無avatar 則更新
            if (!$user->avatar) {
                $user->avatar = $avatarResult['file_name'];
                $imageType = 'user_profile';
            }
            $user->save();

            // 更新上傳的圖檔的來源
            $this->createImageService->add([
                'file_name' => $avatarResult['file_name'],
                'type'      => $imageType,
                'target_id' => $user->id,
                'only'      => true,
            ]);
        } else {
            // 紀錄第三方登入資訊
            $user->recordThirdInfo($request['third_id'], $request['third_name'], null, $request['type']);
        }
    }
}
