<?php
/*
 |--------------------------------------
 |  一般登入Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth\Yzcube;

use App\Models\YzcubeUser;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\ConvertPasswordTrait;

class LoginService
{
    private $yzcubeUser;

    use ApiErrorTrait;
    use CreateTokenTrait;
    use ConvertPasswordTrait;

    public function __construct(YzcubeUser $yzcubeUser)
    {
        $this->yzcubeUser = $yzcubeUser;
    }

    /**
     * 一般登入
     *
     * @return user model
     */
    public function run($request)
    {
        // 驗證是否有email
        $yzcubeUser = $this->yzcubeUser->where('email', $request['email'])->first();
        if (!$yzcubeUser) {
            $this->setException('找不到此管理者帳號！');
        }

        // 驗證密碼是否正確
        if (!$this->isCorrectPassword($request['password'], $yzcubeUser->password)) {
            $this->setException('管理者帳號/密碼驗證錯誤！');
        }

        // 驗證管理者是否停用
        if ($yzcubeUser->status != 'published') {
            $this->setException('您的管理者帳號已停用！');
        }

        // 產生 yzcube_token
        $yzcubeUser->token = $this->getNewToken();
        $yzcubeUser->tokens()->create([
            'target_id'   => $yzcubeUser->id,
            'type'        => 'yzcube',
            'token'       => $yzcubeUser->token,
            'deadline_at' => now()->addMonth(),
        ]);

        return $yzcubeUser;
    }
}
