<?php
/*
 |--------------------------------------
 |  發出手機驗證的簡訊 Service
 |--------------------------------------
 |
 |
 * 電子豹 Webhook 管理
 * @references https://newsleopard.com/surenotify/api/v1
 */

namespace App\Services\Auth;

use App\Repositories\UserPhoneLegalizeRepository;
use App\Models\StoreUser;
use App\Models\User;
use App\Models\PhoneLegalize;
use App\Services\Notification\ShortMessageService;
use App\Services\Message\Surenotify\SmsHandle;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;
use App\Traits\Regex\RegexPhoneTrait;

class SendPhoneTokenService
{
    //token 有效期限 5分鐘 (用於告知前端)
    private $liveTime = 300;

    /** @var userPhoneLegalizeRepository  */
    private $userPhoneLegalizeRepository;

    private $storeUser;
    private $user;
    private $phoneLegalize;

    /** @var shortMessageService  */
    private $shortMessageService;
    private $smsHandle;

    use ApiErrorTrait;
    use RandStringTrait;
    use RegexPhoneTrait;

    public function __construct(
        UserPhoneLegalizeRepository $userPhoneLegalizeRepository,
        ShortMessageService $shortMessageService,
        SmsHandle $smsHandle,
        PhoneLegalize $phoneLegalize,
        StoreUser $storeUser,
        User $user
    ){
        $this->userPhoneLegalizeRepository = $userPhoneLegalizeRepository;

        $this->storeUser = $storeUser;
        $this->user = $user;
        $this->phoneLegalize = $phoneLegalize;

        $this->shortMessageService = $shortMessageService;
        $this->smsHandle = $smsHandle;
    }

    /**
     * @param $userID : 用戶ID
     * @param $phone : 十碼手機號碼
     * @return
     */
    public function send($userID, $phone, $type, $source = 'normal')
    {
        $phone = $this->regexPhone($phone);
        if ($phone == false) {
            $this->setException('請輸入有效的手機號碼 ', 4000, ['phone' => '請輸入有效的手機號碼']);
        }

        // 手機號碼是否已經被使用（不是自己）
        $model = ($type == 'store_user') ? $this->storeUser : $this->user;
        $existingUser = $model->where('phone', $phone)->where('phone_legalize', 1)->first();
        if ($existingUser && $existingUser->id != $userID) {
            $this->setException('此手機已註冊過，請更換手機號碼 ', 4000, ['phone' => '此手機已註冊過，請更換手機號碼']);
        }

        // 驗證同帳號不可重複三次發送
        $userPhoneLegalizeCount = $this->phoneLegalize->where('target_id', $userID)
                            ->where('created_at', '>=', now()->subSeconds($this->liveTime))
                            ->count();
        if ($userPhoneLegalizeCount >= 3) {
            $this->setException("驗證碼請求過於頻繁，暫時無法發送，請等待一段時間重新嘗試或[link_contacts] ", 4005);
        }

        //回傳有效期限
        if ($source == 'normal') {
            $result['live_time'] = $this->liveTime;
        } else {
            $result['live_time'] = 24 * 60 * 60; // 24小時
        }

        //產生新的 token
        $userPhoneLegalize = $this->getTokenV2($userID, $phone, $type, $source);

        //如果是開發模式，多增加個token回傳
        if (env('MESSAGE_ENV') == 'DEV') {
            $result['phone_token'] = $userPhoneLegalize->key;
        }

        // 電子豹送出簡訊
        $this->smsHandle->handle('send_messages', [
            'content'     => $this->getContent($userPhoneLegalize->key, $source),
            'phones'      => $phone,
            'targetModel' => $model->find($userID),
        ]);

        return $result;
    }

    public function validateUniquePhone($userID, $phone, $type)
    {
        // 手機號碼是否已經被使用（不是自己）
        $model = $type == 'store_user' ? $this->storeUser : $this->user;
        $existingUser = $model->where('phone', $phone)->first();
        if ($existingUser && $existingUser->id != $userID) {
            $this->setException('此手機已註冊過，請更換手機號碼 ', 4000, ['phone' => '此手機已註冊過，請更換手機號碼']);
        }
    }

    /**
     * @param $userID : 用戶ID
     * @param $phone : 十碼手機號碼
     * @return token
     */
    private function getToken($userID, $phone)
    {
        //產生Token
        $token = (env('MESSAGE_ENV') == 'DEV') ? '1234' : $this->getRandString(4, ['number']);

        //驗證碼寫進資料庫
        return $this->userPhoneLegalizeRepository->addData([
            'user_id'   => $userID,
            'phone'     => $phone,
            'token'     => $token,
            'show_flag' => 1,
        ]);
    }

    private function getTokenV2($userId, $phone, $type, $source = 'normal')
    {
        //產生Token
        $key = (env('MESSAGE_ENV') == 'DEV') ? '1234' : $this->getRandString(4, ['number']);

        //驗證碼寫進資料庫
        return $this->phoneLegalize->create([
            'phone'       => $phone,
            'key'         => $key,
            'source'      => $source,
            'target_type' => $type,
            'target_id'   => $userId,
        ]);
    }

    /**
     * @param $token
     * @return token
     */
    private function getContent($token, $source)
    {
        $timeText = ($source == 'yzcube')? '24小時' : '5分鐘';
        return '好婚市集感謝您的支持，您的手機驗證碼為 ' . $token . '，請於 ' . $timeText . '內回填完成驗證，請勿將此驗證碼提供他人，謝謝！';
    }
}
