
<?php
/*
 |--------------------------------------
 |  一般登入Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth\Blog;

use App\Models\BlogUser;
use App\Models\YzcubeUser;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\ConvertPasswordTrait;

class LoginService
{
    private $blogUser;
    private $yzcubeUser;

    use ApiErrorTrait;
    use CreateTokenTrait;
    use ConvertPasswordTrait;

    public function __construct(
        BlogUser $blogUser,
        YzcubeUser $yzcubeUser
    ) {
        $this->blogUser   = $blogUser;
        $this->yzcubeUser = $yzcubeUser;
    }

    /**
     * 一般登入
     *
     * @return user model
     */
    public function run($request)
    {
        // 驗證是否有email
        $blogUser = $this->blogUser->where('email', $request['email'])->first();
        if ($blogUser) {
            $type = 'blog';
        } else {
            $blogUser = $this->yzcubeUser->where('email', $request['email'])->first();
            if ($blogUser) {
                $type = 'blog_yzcube';
            } else {
                $this->setException('找不到此管理者帳號！');
            }
        }

        // 驗證密碼是否正確
        if (!$this->isCorrectPassword($request['password'], $blogUser->password)) {
            $this->setException('管理者帳號/密碼驗證錯誤！');
        }

        // 驗證管理者是否停用
        if ($blogUser->status != 'published') {
            $this->setException('您的管理者帳號已停用！');
        }

        // 產生 blog_token
        $blogUser->token = $this->getNewToken();
        $blogUser->tokens()->create([
            'target_id'   => $blogUser->id,
            'type'        => $type,
            'token'       => $blogUser->token,
            'deadline_at' => now()->addMonth(),
        ]);

        return $blogUser;
    }

    /**
     * 實作登入
     *
     * @param $blogUser
     * @return blogUser model
     */
    public function doLoginByUser($blogUser)
    {
        // 產生 blog_token
        $blogUser->token = $this->getNewToken();
        $type = $blogUser instanceof YzcubeUser ? 'blog_yzcube' : 'blog';
        
        $blogUser->tokens()->create([
            'target_id'   => $blogUser->id,
            'type'        => $type,
            'token'       => $blogUser->token,
            'deadline_at' => now()->addMonth(),
        ]);

        return $blogUser;
    }
}
