<?php
/*
 |--------------------------------------
 |  商家後台-登入註冊填寫商家資料 建立新商家
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth\Admin;

use App\Repositories\StoreRepository;
use App\Models\StoreUser;
use App\Traits\ApiErrorTrait;

class StoreInfoService
{
    private $storeUser;
    private $store;
    private $storeUserModel;

    use ApiErrorTrait;

    public function __construct(
        StoreRepository $store,
        StoreUser $storeUserModel
    )
    {
        $this->store = $store;
        $this->storeUserModel = $storeUserModel;
    }

    public function run($user, $type, $name)
    {
        $user_id = $user->id;
        $this->storeUser = $this->storeUserModel->find($user_id);
        if (!$this->storeUser) {
            $this->setException('找不到此管理者帳號！');
        }

        $email = $this->storeUser->email;
        $phone = $this->storeUser->phone;
        $store = $this->store->addData([
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'type' => $type,
        ]);
        $this->storeUser->addStore($store->id);

        return $store->id;
    }
}
