<?php
/*
 |--------------------------------------
 |  一般註冊Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth\Admin;

use App\Models\StoreUser;
use App\Models\EmailLegalize;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\ConvertPasswordTrait;

class RegisterService
{
    private $storeUser;
    private $authKey;

    use ApiErrorTrait;
    use CreateTokenTrait;
    use ConvertPasswordTrait;

    public function __construct(
        StoreUser $storeUser,
        EmailLegalize $authKey
    ) {
        $this->storeUser = $storeUser;
        $this->authKey = $authKey;
    }

    /**
     * 一般註冊
     *
     * @return storeUser model
     */
    public function run($request)
    {
        // 驗證是否有email
        $storeUser = $this->storeUser->where('email', $request['email'])->first();
        // 新註冊 非FB登入跳轉 判斷是否有此信箱
        if ($storeUser && !$request['fb_id']) {
            $this->setException('此信箱已註冊過，請更換信箱 ', 4000, ['email' => '此信箱已註冊過，請更換信箱']);
        }

        // 檢查驗證碼
        $authKey = $this->authKey->where('email', request('email'))
                                ->whereRaw('BINARY `key` = ?', [request('key')])
                                ->where('deadline_at', '>', now())
                                ->first();
        if (!$authKey || $authKey->key != request('key')) {
            $this->setException('請輸入正確的 6 位數驗證碼 ', 4000, ['key' => '請輸入正確的 6 位數驗證碼']);
        }
        // 驗證成功 刪掉此mail的所有驗證碼
        $authKey->where('email', request('email'))->delete();

        if ($request['fb_id']) {
            // fb登入後走註冊流程
            $this->storeUser->where('fb_id', $request['fb_id'])->update([
                'email' => $request['email'],
                'email_legalize' => 1,
                'password' => $this->getInsertHashPassword($request['password']),
            ]);

            $storeUser = $this->storeUser->where('fb_id', $request['fb_id'])->first();
            // 找store_user的store store若信箱未驗證 改成這個email然後已驗證
            $stores = $storeUser->stores;
            if ($stores) {
                foreach ($stores as $store) {
                    if ($store->email_legalize == 0) {
                        $store->email = request('email');
                        $store->email_legalize = 1;
                        $store->save();
                    }
                }
            }
        } else {
             // 新增商家
            $this->storeUser->create([
                'email' => $request['email'],
                'email_legalize' => 1,
                'password' => $this->getInsertHashPassword($request['password']),
            ]);
            $storeUser = $this->storeUser->where('email', $request['email'])->first();
        }

        return $storeUser;
    }
}