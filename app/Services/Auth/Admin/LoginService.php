<?php
/*
 |--------------------------------------
 |  一般登入Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth\Admin;

use App\Models\StoreUser;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\ConvertPasswordTrait;

class LoginService
{
    private $storeUser;

    use ApiErrorTrait;
    use CreateTokenTrait;
    use ConvertPasswordTrait;

    public function __construct(StoreUser $storeUser)
    {
        $this->storeUser = $storeUser;
    }

    /**
     * 一般登入
     *
     * @return storeUser model
     */
    public function run($request)
    {
        $login_success = 0;

        if ($request['type'] == 'email' || $request['type'] == 'bind_line') {
            // 驗證是否有email
            $storeUser = $this->storeUser->where('email', $request['email'])->first();
            if (!$storeUser) {
                if ($request['type'] == 'bind_line') {
                    $this->setException('找不到此商家帳號 ', 4003);
                }
                $this->setException('找不到此商家帳號，請至[link_store_user_register]註冊新帳號 ', 4003);
            }

            // 驗證密碼是否正確
            if (!$this->isCorrectPassword($request['password'], $storeUser->password)) {
                $this->setException('帳號或密碼錯誤 ');
            }

            // 驗證管理者是否停用
            if ($storeUser->status == 'delete') {
                $this->setException('此商家帳號已停用，無法使用 ', 4000, ['email' => '此商家帳號已停用，無法使用']);
            }

            if ($storeUser->email_legalize == 1) {
                if ($storeUser->phone_legalize == 1) {
                    $login_success = 1;
                }
            }
        }

        if ($request['type'] == 'fb') {
            $third_column = $request['type'].'_id';
            $storeUser = $this->storeUser->where($third_column, $request['third_id'])->first();
            if ($storeUser) {
                // 驗證管理者是否停用
                if ($storeUser->status == 'delete') {
                    $this->setException('此商家帳號已停用，無法使用 ', 4000, ['email' => '此商家帳號已停用，無法使用']);
                }
								$testUserArr = ['122095472312857813','122096362136855249', '122102032460843974', '122102108480843197'];
								if ($storeUser->email_legalize == 1 && !in_array($request['third_id'], $testUserArr)) {
                //if ($storeUser->email_legalize == 1) {
                    $this->setException('我們即將於年底移除 Facebook 登入功能，後續請統一使用信箱登入，感謝您的配合。 ');
                }
            } else {
                $this->setException('Facebook 登入僅限已註冊的商家使用，請改用信箱登入，或註冊新帳號。 ');
            }
        }

        if ($login_success) {
            // 產生 store_token
            $storeUser->token = $this->getNewToken();
            $storeUser->tokens()->create([
                'target_id'   => $storeUser->id,
                'type'        => 'store',
                'token'       => $storeUser->token,
                'deadline_at' => now()->addMonth(),
            ]);
        } else {
            if ($storeUser) {
                $storeUser->token = null;
            };
        }

        return [
            'storeUser' => $storeUser? $storeUser : null,
        ];
    }
}
