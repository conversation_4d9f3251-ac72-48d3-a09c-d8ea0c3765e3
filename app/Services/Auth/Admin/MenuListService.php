<?php
/*
 |--------------------------------------
 |  商家後台-菜單列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth\Admin;

use App\Models\AdminPermission;
use App\Models\Activity;
use App\Services\Tools\KeywordSearch\SearchService;

class MenuListService
{
    private $store;
    private $adminPermission;
    private $activeMenuIds  = [];
    private $menuList       = [];
    private $breadcrumbList = [];

    /**
     * 菜單列表
     *
     * @param $request
     * @return array
     */
    public function run($request)
    {
        $this->store           = $request['store'];
        $this->adminPermission = $request['admin_permission'];

        // 菜單的目前所在層
        $last = $this->adminPermission->parentBreadcrumb ?: $this->adminPermission;
        $this->activeMenuIds[] = $last->id;

        // 一直往上一層找菜單
        while ($last->parentMenu) {
            $last = $last->parentMenu;
            $this->activeMenuIds[] = $last->id;
        }

        // 菜單列表
        AdminPermission::where('parent_menu_id', 0)
                        ->sort()
                        ->get()
                        ->map(function($parent) {
                            $_temp = $this->getMenuItem($parent);
                            if ($_temp) {
                                $this->menuList[] = $_temp;
                            }
                        });

        return $this->menuList;
    }

    /**
     * 取得菜單內容
     *
     * @param model $adminPermission 商家的權限設定
     * @return array
     */
    private function getMenuItem(AdminPermission $adminPermission)
    {
        // 商家類型不符
        if ($adminPermission->store_types && !in_array($this->store->type, $adminPermission->store_types)) {
            return false;
        }

        // 是否有發票管理
        if ($adminPermission->id == 25 && !$this->store->invoiceSetting) {
            return false;
        }

        // 是否有活動方案管理 (驗證商家類型是否有活動方案)
        if ($adminPermission->id == 48) {
            if (Activity::storeType($this->store->type)->doesntExist()) {
                return false;
            }
        }

        
        // 是否有開啟禮服組借
        if ($adminPermission->id == 49) {
            // Vicky TODO
            // 目前先寫死 商家ID(880 念念|4171 洋洋|5832 念念板橋店)，之後再看狀況改
            if (!in_array($this->store->id, [880, 4171, 5832])) {
                return false;
            }
        }

        // 找出下一層菜單
        $children = [];
        foreach ($adminPermission->childrenMenu as $item) {
            $_temp = $this->getMenuItem($item);
            if ($_temp) {
                $children[] = $_temp;
            }
        }

        return [
            'id'             => $adminPermission->id,
            'title'          => $adminPermission->title,
            'icon'           => $adminPermission->icon ?: '',
            'link_subdomain' => $adminPermission->link_subdomain,
            'link_path'      => str_replace('{store_id}', $this->store->id, $adminPermission->link_path),
            'has_lock'       => ($adminPermission->store_status && !in_array($this->store->status, $adminPermission->store_status)),
            'has_new'        => $adminPermission->has_new,
            'is_active'      => in_array($adminPermission->id, $this->activeMenuIds),
            'children'       => $children,
        ];
    }

    /**
     * 取得麵包屑列表
     *
     * @return array
     */
    public function getBreadcrumbList()
    {
        // 最後一層麵包屑
        $last = $this->adminPermission;
        if (!$last->parent_breadcrumb_id) {
            return [];
        }

        // 一直往上一層找麵包屑
        $last->link_path = '';
        $this->breadcrumbList[] = $this->getBreadcrumbItem($last);
        while ($last->parentBreadcrumb) {
            $last = $last->parentBreadcrumb;
            $this->breadcrumbList[] = $this->getBreadcrumbItem($last);
        }

        return array_reverse($this->breadcrumbList);
    }

    /**
     * 取得麵包屑內容
     *
     * @param model $adminPermission 商家的權限設定
     * @return array
     */
    private function getBreadcrumbItem(AdminPermission $adminPermission)
    {
        return [
            'title'          => $adminPermission->title,
            'icon'           => $adminPermission->icon ?: '',
            'link_subdomain' => $adminPermission->link_subdomain,
            'link_path'      => str_replace('{store_id}', $this->store->id, $adminPermission->link_path),
        ];
    }
}
