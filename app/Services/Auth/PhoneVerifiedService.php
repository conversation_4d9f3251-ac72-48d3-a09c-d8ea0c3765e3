<?php
/*
 |--------------------------------------
 |  驗證手機號碼 Service
 |--------------------------------------
 |
 |
 */


namespace App\Services\Auth;

use App\Repositories\UserPhoneLegalizeRepository;
use App\Models\StoreUser;
use App\Models\User;
use App\Models\PhoneLegalize;
use App\Traits\ApiErrorTrait;
use App\Traits\Regex\RegexPhoneTrait;

class PhoneVerifiedService
{
    //token 有效期限 10分鐘 (用於後端驗證)
    private $liveTime = 600;

    //結果
    private $result;

    private $storeUser;
    private $user;

    /** @var userPhoneLegalizeRepository  */
    private $userPhoneLegalizeRepository;

    private $phoneLegalize;

    use ApiErrorTrait;
    use RegexPhoneTrait;

    public function __construct(
        StoreUser $storeUser,
        User $user,
        UserPhoneLegalizeRepository $userPhoneLegalizeRepository,
        PhoneLegalize $phoneLegalize
    ){
        $this->storeUser = $storeUser;
        $this->user = $user;

        $this->userPhoneLegalizeRepository = $userPhoneLegalizeRepository;

        $this->phoneLegalize = $phoneLegalize;
    }

    /**
     * @param $phone : 十碼手機號碼
     * @return
     */
    public function verified($phone, $key, $type)
    {
        $phone = $this->regexPhone($phone);
        if ($phone == false) {
            $this->setException('請輸入有效的手機號碼 ', 4000, ['phone' => '手機號碼有誤，請重新輸入。']);
        }

        // 檢查有效期限內是否有驗證碼
        // 是否過期判斷：source是 normal 是五分鐘, source是 yzcube 是24小時
        $phoneLegalize = $this->phoneLegalize->where('phone', $phone)
            ->where('target_type', $type)
            ->where('key', $key)
            ->orderby('id', 'desc')
            ->first();
        if ($phoneLegalize) {
            $expiryTime = $phoneLegalize->source == 'normal' ? $this->liveTime : 86400;
            if ($phoneLegalize->created_at < date('Y-m-d H:i:s', time() - $expiryTime)) {
                $this->setException('請輸入正確的 4 位數字認證碼 ', 4000, ['key' => '請輸入正確的 4 位數字認證碼']);
            }
        } else {
            $this->setException('請輸入正確的 4 位數字認證碼 ', 4000, ['key' => '請輸入正確的 4 位數字認證碼']);
        }

        // 更新驗證資料
        $phoneLegalize->where('phone', $phone)
                        ->where('target_type', $type)
                        ->delete();

        // 更新使用者手機驗證結果
        if ($type == 'store_user') {
            $this->storeUser->where('id', $phoneLegalize->target_id)->update([
                'phone' => $phone,
                'phone_legalize' => 1,
            ]);
            if ($store_user = $this->storeUser->where('id', $phoneLegalize->target_id)->first()) {
                if ($store_user->email_legalize == 1) {
                    $store_user->status = 'published';
                    $store_user->save();
                }
            }

            return $store_user;
        } elseif($type == 'user') {
            $this->user->where('id', $phoneLegalize->target_id)->update([
                'phone' => $phone,
                'phone_legalize' => 1,
            ]);

            return;
        }
    }
}
