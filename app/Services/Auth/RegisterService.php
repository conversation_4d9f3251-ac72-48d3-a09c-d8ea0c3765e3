<?php
/*
 |--------------------------------------
 |  一般註冊Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth;

use App\Repositories\UserRepository;
use App\Repositories\EventReportRepository;
use App\Models\User;
use App\Services\Mail\Auth\FounderLetterService;
use App\Services\Mail\Auth\EmailVerificationService;
use App\Services\Mail\Auth\EditPasswordService;
use App\Services\Auth\LoginService;
use App\Services\Mail\Auth\EmailAuthKeyService;
use App\Traits\Auth\MakeAnonymousKeyTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Traits\ApiErrorTrait;

class RegisterService
{
    private $userRepository;
    private $eventReportRepository;
    private $founderLetterService;
    private $emailVerificationService;
    private $emailAuthKeyService;
    private $editPasswordService;
    private $loginService;
    private $user;

    use MakeAnonymousKeyTrait;
    use CreateTokenTrait;
    use ConvertPasswordTrait;
    use ApiErrorTrait;

    public function __construct(
        UserRepository $userRepository,
        EventReportRepository $eventReportRepository,
        FounderLetterService $founderLetterService,
        EmailVerificationService $emailVerificationService,
        EmailAuthKeyService $emailAuthKeyService,
        EditPasswordService $editPasswordService,
        LoginService $loginService,
        User $user
    ) {
        $this->userRepository           = $userRepository;
        $this->eventReportRepository    = $eventReportRepository;
        $this->founderLetterService     = $founderLetterService;
        $this->emailVerificationService = $emailVerificationService;
        $this->emailAuthKeyService      = $emailAuthKeyService;
        $this->editPasswordService      = $editPasswordService;
        $this->loginService             = $loginService;
        $this->user                     = $user;
    }

    /**
     * 信箱註冊
     */
    public function run($request)
    {
        // 驗證帳號
        if ($this->user->where(['email' => $request['email']])->first()) {
            $this->setException('此信箱已註冊過，請更換信箱 ', 4000, ['email' => '此信箱已註冊過，請更換信箱']);
        }

        return $this->user->create([
            'email'               => $request['email'],
            'password'            => $this->getInsertHashPassword($request['password']),
            'name'                => $request['name'],
            'real_name'           => $request['name'],
            'avatar'              => $this->user->present()->rand_avatar_image,
            'anonymous_key'       => $this->getAnonymousKey(),
            'email_legalize'      => 0,
        ]);
    }

    /**
     * 建立user
     */
    public function createThirdUser($email, $type, $third_info)
    {
        $third_column = $type.'_id';

        return $this->user->create([
            $third_column => $third_info ['id'],
            'email' => $email,
            'notify_email' => $email,
            'name' => $third_info ['name'],
            'real_name' => $third_info ['name'],
            'anonymous_key' => $this->getAnonymousKey(),
            'email_legalize' => 0,
            'status' => 'published',
        ]);
    }
}
