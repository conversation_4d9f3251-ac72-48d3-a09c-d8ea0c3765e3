<?php
/*
 |--------------------------------------
 |  從Token取得Admin資料 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Auth;

use App\Repositories\AuthTokenRepository;

class TokenToAdminService
{
    private $authTokenRepository;

    public function __construct(AuthTokenRepository $authTokenRepository)
    {
        $this->authTokenRepository = $authTokenRepository;
    }

    /**
     * @param $token
     * @return
     */
    public function get($token)
    {
        $authToken = $this->authTokenRepository->getAuthTokenByValidToken('store', $token);
        if (!$authToken) {
            return NULL;
        }

        // 若預計的有效期限，超過原有的有效期限24小時則更新
        $newDeadline = now()->addMonth();
        if ($authToken->deadline_at && $authToken->deadline_at->diffInHours($newDeadline) >= 24) {
            $authToken->deadline_at = $newDeadline;
            $authToken->save();
        }

        // 新增身份驗證紀錄
        $userIp    = request()->header('User-Ip');
        $userAgent = request()->header('User-Device');
        if ($userIp && $userAgent) {
            $authToken->logs()->updateOrCreate([
                'auth_token_id' => $authToken->id,
                'ip'            => $userIp,
                'user_agent'    => $userAgent,
            ], [
                'has_yzcube_token' => (bool)request()->header('Yzcube-Token'),
                'updated_at'       => now(),
            ]);
        }

        return $authToken->admin;
    }

}
