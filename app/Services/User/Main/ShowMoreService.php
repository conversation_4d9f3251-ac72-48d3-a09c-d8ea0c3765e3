<?php
/*
 |--------------------------------------
 |  婚禮中心-個人主頁-載入更多 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Main;

use App\Repositories\UserRepository;
use App\Traits\ApiErrorTrait;

class ShowMoreService
{
    private $userRepository;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserRepository $userRepository
    ) {
        $this->userRepository = $userRepository;
    }

    /**
     * 個人主頁-載入更多
     */
    public function run($user_id, $request)
    {
        // 取得 User
        $user = $this->userRepository->getFirst(['id' => $user_id]);
        if (!$user) {
            $this->setException('此新娘尚未有公開資訊！', 3004);
        }

        // 取得使用者的討論
        $list = [];
        switch ($request['type']) {
            case 'articles':
                $list = $user->articlesAnonymous(0)->status('published')->paginate($request['per_page']);
                break;

            case 'article_comments':
                $list = $user->commentsAnonymous(0)->status('published')->autoReply(0)->sortCreatedAt()->paginate($request['per_page']);
                break;

            default:
                $this->setException('傳入參數有誤！');
                break;
        }

        return [
            'type' => $request['type'],
            'list' => $list,
        ];
    }
}
