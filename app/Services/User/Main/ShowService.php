<?php
/*
 |--------------------------------------
 |  婚禮中心-個人主頁 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Main;

use App\Models\User;
use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Traits\ApiErrorTrait;

class ShowService
{
    private $user;
    private $categoryRepository;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        User $user,
        CategoryRepository $categoryRepository
    ) {
        $this->user               = $user;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * 個人主頁
     */
    public function run($user_id, $request)
    {
        // 取得 User
        $user = $this->user->live()->find($user_id);
        if (!$user) {
            $this->setException('此新娘尚未有公開資訊！', 3004);
        }

        // 取得使用者的分享&討論 (若更動記得改App\Services\User\Main\ShowMoreService，以及婚禮中心中我的分享＆我的討論)
        $articles        = $user->articlesAnonymous(0)->status('published')->paginate($request['per_page']);
        $articleComments = $user->commentsAnonymous(0)->status('published')->autoReply(0)->sortCreatedAt()->paginate($request['per_page']);

        // 若使用者沒有分享文或實名討論、不公開黃金團隊、不是登入者身份，則不顯示該會員任何資訊
        $login_id = $request['user'] ? $request['user']->id : '';
        if (
            !$articles->count() &&
            !$articleComments->count() &&
            $user->wedding && !$user->wedding->show_gold_team &&
            $user_id != $login_id
        ) {
            $this->setException('此新娘尚未有公開資訊！', 3004);
        }

        // 取得使用者的主要婚期及論壇分類
        $wedding_type = $user->getFirstMainDateWeddingType();
        $categories   = $this->categoryRepository->getSimpleList();

        // 分享&討論 數據
        $article_page_view     = $articles->sum('page_view');
        $article_comment_count = $articles->sum('comment_count');
        $article_share_count   = $articles->sum(function ($article) {
                                    return $article->logShares->count();
                                });
        $article_track_count   = $articles->sum('track_count');

        // 分享文/論壇瀏覽數*1 + 論壇按讚次數*3 + 分享文/論壇留言/回覆/分享次數*5 + 分享文收藏/論壇追蹤*10
        $popular_score = $article_page_view;
        $popular_score += $user->forum_get_like_count * 3;
        $popular_score += ($article_comment_count + $article_share_count) * 5;
        $popular_score += ($article_track_count) * 10;

        // 幫助新娘數（該位新娘所有文章＆留言被按讚＋該位新娘所有文章被收藏數量）
        $help_count = $user->forum_get_like_count + $articles->sum('track_count');

        return [
            'user'         => $user,
            'wedding_type' => $wedding_type,
            'categories'   => $categories,
            'forum'        => [
                'page_view' => $article_page_view,
                'articles'  => $articles,
                'comments'  => $articleComments,
            ],
            'popular_score' => $popular_score,
            'help_count'    => $help_count,
        ];
    }
}
