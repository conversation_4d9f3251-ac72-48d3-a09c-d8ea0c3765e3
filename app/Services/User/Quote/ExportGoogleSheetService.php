<?php
/*
 |--------------------------------------
 |  匯出主動報價至Google試算表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Quote;

use App\Models\Wdv2\UserQuote;
use App\Models\User;
use App\Models\Store;
use App\Models\Wdv2\StoreQuote;
use App\Models\LogRedirectQuote;
use App\Traits\GoogleSheetTrait;
use Carbon\Carbon;
use Sheets;
use DB;

class ExportGoogleSheetService
{
    private $sheet;
    private $structures;
    private $firstDate = '2023-08-16';
    private $userQuote;
    private $user;
    private $store;
    private $storeQuote;
    private $logRedirectQuote;

    use GoogleSheetTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserQuote $userQuote,
        User $user,
        Store $store,
        StoreQuote $storeQuote,
        LogRedirectQuote $logRedirectQuote
    ) {
        // 主動報價原始數據 Google Sheet
        // 測試環境表單: https://docs.google.com/spreadsheets/d/1ulzbmXTWDlvZyiGEQqiEtP6oy1Q-U9zggemgUQWJcRk
        // 正式環境表單: https://docs.google.com/spreadsheets/d/1Vab6UPlO0y_FIMLhxIQpS4fo7tocg302DxkeJV62oLQ
        $spreadsheetId = env('APP_DEBUG') ? '1ulzbmXTWDlvZyiGEQqiEtP6oy1Q-U9zggemgUQWJcRk' : '1Vab6UPlO0y_FIMLhxIQpS4fo7tocg302DxkeJV62oLQ';
        $this->sheet = Sheets::spreadsheet($spreadsheetId);

        // Google Sheet 架構
        $this->structures = [
            [
                'sheet' => '新娘報價',
                'header' => ['報價單產生時間', '報價ID', '會員姓名', '報價類型', '需求日期', '地區'],
            ],
            [
                'sheet' => '商家報價',
                'header' => ['商家回覆報價時間', '報價類型', '報價ID', '商家報價時間差', '商家類型', '商家名稱'],
            ],
            [
                'sheet' => '【營收】新娘點擊',
                'header' => ['事件發生日期', '事件數', '不重複事件數', '營收價值', '商家回覆報價ID', '報價ID', '報價類型', '使用者名稱', '商家類型', '商家名稱'],
            ],
        ];

        $this->userQuote        = $userQuote;
        $this->user             = $user;
        $this->store            = $store;
        $this->storeQuote       = $storeQuote;
        $this->logRedirectQuote = $logRedirectQuote;
    }

    /**
     * 重新匯出至特定日期
     */
    public function runRefresh($date)
    {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        // 清空並設定標題
        foreach ($this->structures as $structure) {
            $this->sheet->sheet($structure['sheet'])->range('')->clear();
            $this->sheet->sheet($structure['sheet'])->range('A1')->update([$structure['header']]);
        }

        // 從最早的主動報價日期依序匯出
        $currentDate = Carbon::createFromFormat('Y-m-d', $this->firstDate)->startOfDay();
        $totalDays   = $currentDate->diffInDays($date) + 1;
        while ($currentDate < $date) {
            echo date('Y-m-d.', strtotime($currentDate));

            // 添加特定日期的數據
            $this->runDaily($currentDate);
            $currentDate->addDay();

            // Quota exceeded for quota group 'WriteGroup' and limit 'Write requests per user per 100 seconds' of service 'sheets.googleapis.com' for consumer 'project_number:74135766788'.
            sleep(2);
        }

        return $totalDays;
    }

    /**
     * 添加特定日期的數據
     */
    public function runDaily($date)
    {
        // 當天的時間範圍
        $dateRange = [
            date('Y-m-d 00:00:00', strtotime($date)),
            date('Y-m-d 23:59:59', strtotime($date)),
        ];

        // 新娘主動報價
        $userQuotes = $this->userQuote->select([
                                            'user_quote.created_at',
                                            'user_quote.id',
                                            'user_quote.user_id',
                                            'users.name',
                                            'user_quote.type',
                                            'user_quote.date',
                                            'user_quote.city_code',
                                        ])
                                        ->join($this->user->getTable(), 'users.id', 'user_quote.user_id')
                                        ->whereBetween('user_quote.created_at', $dateRange)
                                        ->get();

        $data = [];
        foreach ($userQuotes as $userQuote) {
            $data[] = [
                $userQuote->created_at->format('Y-m-d H:i:s'),
                $userQuote->id,
                '('.$userQuote->user_id.')'.$userQuote->name,
                $this->store->quoteTypeList[$userQuote->type],
                $userQuote->date,
                $userQuote->present()->cityArea,
            ];
        }
        $this->sheet->sheet($this->structures[0]['sheet']);
        $this->appendRecordWithGoogleSheet($this->sheet, $data);

        // 商家回覆報價
        $storeQuotes = $this->storeQuote->select([
                                            'store_quote.created_at',
                                            'user_quote.type',
                                            'store_quote.user_quote_id',
                                            'user_quote.created_at AS user_quote_created_at',
                                            'stores.status',
                                            'store_quote.store_id',
                                            'stores.name',
                                        ])
                                        ->join($this->userQuote->getTable(), 'user_quote.id', 'store_quote.user_quote_id')
                                        ->join($this->store->getTable(), 'stores.id', 'store_quote.store_id')
                                        ->whereBetween('store_quote.created_at', $dateRange)
                                        ->get();
        $data = [];
        foreach ($storeQuotes as $storeQuote) {
            $data[] = [
                $storeQuote->created_at->format('Y-m-d H:i:s'),
                $this->store->quoteTypeList[$storeQuote->type],
                $storeQuote->user_quote_id,
                $storeQuote->created_at->diffInHours($storeQuote->user_quote_created_at),
                in_array($storeQuote->status, ['published', 'pending', 'checkout']) ? '付費' : '免費',
                '('.$storeQuote->store_id.')'.$storeQuote->name,
            ];
        }
        $this->sheet->sheet($this->structures[1]['sheet']);
        $this->appendRecordWithGoogleSheet($this->sheet, $data);

        // 新娘點擊紀錄
        $storeQuoteClickLogTable = $this->logRedirectQuote->getTable();
        $clickLogs = DB::table($storeQuoteClickLogTable)
                        ->select([
                            DB::raw("DATE_FORMAT(log_redirect_quotes.created_at,'%Y-%m-%d') AS created_at"),
                            DB::raw('COUNT(*) AS event_count'),
                            // 'log_redirect_quotes.unique_events',
                            DB::raw("(
                                SELECT
                                    COUNT(*) AS old_event_count
                                FROM {$storeQuoteClickLogTable} AS old_logs
                                WHERE
                                    old_logs.store_quote_id = log_redirect_quotes.store_quote_id AND
                                    old_logs.created_at < log_redirect_quotes.created_at
                                GROUP BY old_logs.store_quote_id
                            ) AS old_event_count"),
                            'log_redirect_quotes.store_quote_id',
                            'store_quote.user_quote_id',
                            'user_quote.type',
                            'user_quote.user_id',
                            'users.name AS user_name',
                            'stores.status',
                            'store_quote.store_id',
                            'stores.name AS store_name',
                        ])
                        ->join($this->storeQuote->getTable(), 'store_quote.id', 'log_redirect_quotes.store_quote_id')
                        ->join($this->userQuote->getTable(), 'user_quote.id', 'store_quote.user_quote_id')
                        ->join($this->store->getTable(), 'stores.id', 'store_quote.store_id')
                        ->join($this->user->getTable(), 'users.id', 'user_quote.user_id')
                        ->where(DB::raw("DATE_FORMAT(log_redirect_quotes.created_at,'%Y-%m-%d')"), date('Y-m-d', strtotime($date)))
                        ->groupBy('store_quote_id')
                        ->get();

        $data = [];
        foreach ($clickLogs as $clickLog) {
            $data[] = [
                $clickLog->created_at,
                $clickLog->event_count,
                $clickLog->event_count,
                $this->getRevenueValue($clickLog->event_count, $clickLog->old_event_count),
                $clickLog->store_quote_id,
                $clickLog->user_quote_id,
                $this->store->quoteTypeList[$clickLog->type],
                '('.$clickLog->user_id.')'.$clickLog->user_name,
                in_array($clickLog->status, ['published', 'pending', 'checkout']) ? '付費' : '免費',
                '('.$clickLog->store_id.')'.$clickLog->store_name,
            ];
        }
        $this->sheet->sheet($this->structures[2]['sheet']);
        $this->appendRecordWithGoogleSheet($this->sheet, $data);
    }

    // 計算營收價值
    private function getRevenueValue($unique_events, $uniqued_events)
    {
        if ($uniqued_events >= 2) {
            return 0;
        }

        if (($unique_events - $uniqued_events) > 1) {
            return ($uniqued_events >= 1) ? 20 : 40;
        } else {
            return 20;
        }
    }
}
