<?php
/*
 |--------------------------------------
 |  婚禮中心-新增我要詢價 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Quote;

use App\Jobs\User\SendNotificationNewQuote;
use App\Services\Coupon\UserQuoteTastingService;

class StoreService
{
    private $userQuoteTastingService;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserQuoteTastingService $userQuoteTastingService
    ) {
        $this->userQuoteTastingService = $userQuoteTastingService;
    }

    /**
     * 新增我要詢價
     */
    public function run($request)
    {
        // 取得使用者
        $user = $request['user'];

        // 儲存主動報價
        $userQuote = $user->quotes()->create([
            'type'               => $request['store_type'],
            'wedding_type'       => $request['wedding_type'],
            'date'               => $request['wedding_date'],
            'city_code'          => $request['city_code'],
            'wedding_venue_type' => $request['wedding_venue_type'],
            'wedding_venue'      => $request['wedding_venue'],
            'detail'             => json_decode($request['detail']),
        ]);

        // WeddingDay 主動報價新增通知
        SendNotificationNewQuote::dispatch($userQuote);

        // 公開詢價的試吃優惠卷
        $this->userQuoteTastingService->run($user);

        return $userQuote;
    }
}
