<?php
/*
 |--------------------------------------
 |  計算即時通訊婚期比例 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Date;

use App\Models\FirestoreMessage;
use App\Models\WeddingDate;
use Carbon\Carbon;
use DB;

class GetMessageRankService
{
    private $firestoreMessage;
    private $weddingDate;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        FirestoreMessage $firestoreMessage,
        WeddingDate $weddingDate
    ) {
        $this->firestoreMessage = $firestoreMessage;
        $this->weddingDate      = $weddingDate;
    }

    /**
     * 計算即時通訊婚期比例
     */
    public function run($startMonth, $endMonth)
    {
        $startDate = date('Y-m-d', strtotime('first day of '.$startMonth.' -1 Month'));
        $endDate   = date('Y-m-d', strtotime('last day of '.$endMonth.' +1 Month'));

        // 即時通訊婚期紀錄
        $messageDates = $this->firestoreMessage->select(DB::raw('wedding_date, COUNT(`id`) count'))
                                                ->whereBetween('wedding_date', [$startDate, $endDate])
                                                ->groupBy('wedding_date')
                                                ->get();

        // 婚期紀錄陣列
        $dateCounts = $messageDates->pluck('count', 'wedding_date');

        // 依月份分群
        $groupMonths = $messageDates->groupBy(function ($model, $key) {
            return date('Y-m', strtotime($model->wedding_date));
        });

        $currentDate = Carbon::createFromFormat('Y-m-d', $startMonth.'-01');
        $lastDate    = Carbon::createFromFormat('Y-m-d', $endMonth.'-01');
        while ($currentDate < $lastDate) {

            // 當月份無資料，則略過
            $currentMonth = $currentDate->format('Y-m');
            if (!isset($groupMonths[$currentMonth])) {
                $currentDate->addMonth();
                continue;
            }

            // 取前後共三個月份的總數
            $prevMonth         = date('Y-m', strtotime('previous month '.$currentMonth));
            $nextMonth         = date('Y-m', strtotime('next month '.$currentMonth));
            $currentMonthCount = $groupMonths[$currentMonth]->sum('count');
            $prevMonthCount    = isset($groupMonths[$prevMonth]) ? $groupMonths[$prevMonth]->sum('count') : 0;
            $nextMonthCount    = isset($groupMonths[$nextMonth]) ? $groupMonths[$nextMonth]->sum('count') : 0;
            $total             = $currentMonthCount + $prevMonthCount + $nextMonthCount;

            // 當月份無資料，則略過
            if (!$total) {
                $currentDate->addMonth();
                continue;
            }

            // 更新熱門婚期
            foreach ($groupMonths[$currentMonth] as $model) {
                $weddingDate = $this->weddingDate->where('date', $model->wedding_date)->first();
                if (!$weddingDate) {
                    $weddingDate       = clone $this->weddingDate;
                    $weddingDate->date = $model->wedding_date;
                    $weddingDate->week = date('w', strtotime($model->wedding_date));
                }
                $weddingDate->message_count = $dateCounts[$model->wedding_date];
                $weddingDate->message_rank  = $dateCounts[$model->wedding_date] / $total * 100;
                $weddingDate->save();
            }

            // 下個月
            $currentDate->addMonth();
        }
    }
}
