<?php
/*
 |--------------------------------------
 |  GA搜尋婚期紀錄 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Date;

use App\Services\Google\BigQuery\BigQueryHandle;
use App\Models\LogGaSearchDate;

class LogGaSearchService
{
    private $bigQueryHandle;
    private $logGaSearchDate;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        BigQueryHandle $bigQueryHandle,
        LogGaSearchDate $logGaSearchDate
    ) {
        $this->bigQueryHandle  = $bigQueryHandle;
        $this->logGaSearchDate = $logGaSearchDate;
    }

    /**
     * GA搜尋婚期紀錄
     *
     * @param date $startDate Y-m-d
     * @param date $endDate Y-m-d
     * @return int $total 新娘搜尋婚期的數量
     */
    public function run($startDate, $endDate = NULL)
    {
        $endDate      = $endDate ?: $startDate;
        $currentMonth = date('Y-m', strtotime($startDate));

        // 新娘搜尋婚期的Analytics資料
        $results = $this->bigQueryHandle->handle('wedding_day_page_views', [$startDate, $endDate]);
        foreach ($results as $date => $count) {
            $diffMonth = $this->getDiffMonth($date, $currentMonth);
            if ($diffMonth < 0) {
                continue;
            }

            // 更新 GA搜尋婚期紀錄
            $logGaSearchDate = $this->logGaSearchDate->where('wedding_date', $date)
                                                        ->where('diff_month', $diffMonth)
                                                        ->first();
            if (!$logGaSearchDate) {
                $logGaSearchDate = clone $this->logGaSearchDate;
            }
            $logGaSearchDate->search_month = $currentMonth;
            $logGaSearchDate->wedding_date = $date;
            $logGaSearchDate->diff_month   = $diffMonth;
            $logGaSearchDate->count        += $count;
            $logGaSearchDate->save();
        }

        return count($results);
    }

    /**
     * 取得月份差
     */
    private function getDiffMonth($after, $before)
    {
        $diffYear  = date('Y', strtotime($after)) - date('Y', strtotime($before));
        $diffMonth = date('m', strtotime($after)) - date('m', strtotime($before));

        return $diffYear * 12 + $diffMonth;
    }
}
