<?php
/*
 |--------------------------------------
 |  計算詢問單婚期比例 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Date;

use App\Models\Wdv2\Reserve;
use App\Models\WeddingDate;
use Carbon\Carbon;
use DB;

class GetReserveRankService
{
    private $reserve;
    private $weddingDate;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Reserve $reserve,
        WeddingDate $weddingDate
    ) {
        $this->reserve     = $reserve;
        $this->weddingDate = $weddingDate;
    }

    /**
     * 計算詢問單婚期比例
     */
    public function run($startMonth, $endMonth)
    {
        $startDate = date('Y-m-d', strtotime('first day of '.$startMonth.' -1 Month'));
        $endDate   = date('Y-m-d', strtotime('last day of '.$endMonth.' +1 Month'));

        // 詢問單婚期紀錄
        $reserveDates = $this->reserve->select(DB::raw('reserve_date, COUNT(`id`) count'))
                                        ->whereBetween('reserve_date', [$startDate, $endDate])
                                        ->groupBy('reserve_date')
                                        ->get();

        // 婚期紀錄陣列
        $dateCounts = $reserveDates->pluck('count', 'reserve_date');

        // 依月份分群
        $groupMonths = $reserveDates->groupBy(function ($model, $key) {
            return date('Y-m', strtotime($model->reserve_date));
        });

        $currentDate = Carbon::createFromFormat('Y-m-d', $startMonth.'-01');
        $lastDate    = Carbon::createFromFormat('Y-m-d', $endMonth.'-01');
        while ($currentDate < $lastDate) {

            // 當月份無資料，則略過
            $currentMonth = $currentDate->format('Y-m');
            if (!isset($groupMonths[$currentMonth])) {
                $currentDate->addMonth();
                continue;
            }

            // 取前後共三個月份的總數
            $prevMonth         = date('Y-m', strtotime('previous month '.$currentMonth));
            $nextMonth         = date('Y-m', strtotime('next month '.$currentMonth));
            $currentMonthCount = $groupMonths[$currentMonth]->sum('count');
            $prevMonthCount    = isset($groupMonths[$prevMonth]) ? $groupMonths[$prevMonth]->sum('count') : 0;
            $nextMonthCount    = isset($groupMonths[$nextMonth]) ? $groupMonths[$nextMonth]->sum('count') : 0;
            $total             = $currentMonthCount + $prevMonthCount + $nextMonthCount;

            // 當月份無資料，則略過
            if (!$total) {
                $currentDate->addMonth();
                continue;
            }

            // 更新熱門婚期
            foreach ($groupMonths[$currentMonth] as $model) {
                $weddingDate = $this->weddingDate->where('date', $model->reserve_date)->first();
                if (!$weddingDate) {
                    $weddingDate       = clone $this->weddingDate;
                    $weddingDate->date = $model->reserve_date;
                    $weddingDate->week = date('w', strtotime($model->reserve_date));
                }
                $weddingDate->reserve_count = $dateCounts[$model->reserve_date];
                $weddingDate->reserve_rank  = $dateCounts[$model->reserve_date] / $total * 100;
                $weddingDate->save();
            }

            // 下個月
            $currentDate->addMonth();
        }
    }
}
