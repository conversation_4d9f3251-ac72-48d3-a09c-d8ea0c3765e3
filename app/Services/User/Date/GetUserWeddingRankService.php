<?php
/*
 |--------------------------------------
 |  計算新娘提供婚期比例 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Date;

use App\Models\WeddingDate;
use Carbon\Carbon;

class GetUserWeddingRankService
{
    private $weddingDate;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        WeddingDate $weddingDate
    ) {
        $this->weddingDate = $weddingDate;
    }

    /**
     * 計算新娘提供婚期比例
     */
    public function run($startMonth, $endMonth)
    {
        $startDate = date('Y-m-d', strtotime('first day of '.$startMonth.' -1 Month'));
        $endDate   = date('Y-m-d', strtotime('last day of '.$endMonth.' +1 Month'));

        // 新娘提供婚期紀錄
        $weddingDates = $this->weddingDate->whereBetween('date', [$startDate, $endDate])->get();

        // 婚期紀錄陣列
        $dateCounts = $weddingDates->pluck('user_wedding_count', 'date');

        // 依月份分群
        $groupMonths = $weddingDates->groupBy(function ($model, $key) {
            return date('Y-m', strtotime($model->date));
        });

        $currentDate = Carbon::createFromFormat('Y-m-d', $startMonth.'-01');
        $lastDate    = Carbon::createFromFormat('Y-m-d', $endMonth.'-01');
        while ($currentDate < $lastDate) {

            // 當月份無資料，則略過
            $currentMonth = $currentDate->format('Y-m');
            if (!isset($groupMonths[$currentMonth])) {
                $currentDate->addMonth();
                continue;
            }

            // 取前後共三個月份的總數
            $prevMonth         = date('Y-m', strtotime('previous month '.$currentMonth));
            $nextMonth         = date('Y-m', strtotime('next month '.$currentMonth));
            $currentMonthCount = $groupMonths[$currentMonth]->sum('user_wedding_count');
            $prevMonthCount    = isset($groupMonths[$prevMonth]) ? $groupMonths[$prevMonth]->sum('user_wedding_count') : 0;
            $nextMonthCount    = isset($groupMonths[$nextMonth]) ? $groupMonths[$nextMonth]->sum('user_wedding_count') : 0;
            $total             = $currentMonthCount + $prevMonthCount + $nextMonthCount;

            // 當月份無資料，則略過
            if (!$total) {
                $currentDate->addMonth();
                continue;
            }

            // 更新熱門婚期
            foreach ($groupMonths[$currentMonth] as $model) {
                $model->user_wedding_rank = $dateCounts[$model->date] / $total * 100;
                $model->save();
            }

            // 下個月
            $currentDate->addMonth();
        }
    }
}
