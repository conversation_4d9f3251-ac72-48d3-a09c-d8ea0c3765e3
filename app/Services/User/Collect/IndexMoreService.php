<?php
/*
 |--------------------------------------
 |  我的收藏 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Collect;

use App\Traits\ApiErrorTrait;

class IndexMoreService
{
    use ApiErrorTrait;

    /**
     * 我的收藏-載入更多
     */
    public function run($request)
    {
        $user      = $request['user'];
        $storeType = $request['store_type'];
        $perPage   = $request['per_page'];

        // 取得使用者的收藏
        $list = [];
        switch ($request['type']) {

            // 商家
            case 'store':
                $list = $user->storeCollects($storeType)->paginate($perPage);
                break;

            // 作品
            case 'work':
                $list = $user->storeWorkCollects($storeType)->paginate($perPage);
                break;

            // 服務方案
            case 'service':
                $list = $user->storeServiceCollects($storeType)->paginate($perPage);
                break;

            // 婚宴場地廳房
            case 'venue_room':
                $list = $user->storeVenueRoomCollects($storeType)->paginate($perPage);
                break;

            default:
                $this->setException('傳入參數有誤！');
                break;
        }

        return [
            'type' => $request['type'],
            'list' => $list,
        ];
    }
}
