<?php
/*
 |--------------------------------------
 |  我的收藏 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Collect;

use App\Models\Store;

class IndexService
{
    private $store;

    /**
     * IndexService constructor.
     * @param Store $store
     */
    public function __construct(Store $store)
    {
        $this->store = $store;
    }

    /**
     * 我的收藏
     */
    public function run($request)
    {
        $user = $request['user'];
        $storeType = $request['store_type'];
        $perPage = $request['per_page'];

        return [
            'stores'     => $user->storeCollects($storeType)->paginate($perPage),
            'services'   => $user->storeServiceCollects($storeType)->paginate($perPage),
            'works'      => $user->storeWorkCollects($storeType)->paginate($perPage),
            'venueRooms' => $user->storeVenueRoomCollects($storeType)->paginate($perPage),
        ];
    }

    /**
     * 神之後台新娘個人頁用的
     * 收藏不分頁
     * type全撈..
     * @param $user : user model
     * @return array
     */
    public function runYzcube($user)
    {
        $storeTypes = collect($this->store->collectTypeList)->keys()->all();
        $collect = [];

        foreach ($storeTypes as $storeType) {
            $collect[] = [
                'storeType' => $storeType,
                'stores'    => $user->storeCollects($storeType)->get(),
                'services'  => $user->storeServiceCollects($storeType)->with('store')->get()->groupBy('store_id'),
                'works'     => $this->getWorks($user, $storeType)
            ];
        }

        return $collect;
    }

    /**
     * 取得works的收藏
     * works比較特殊..不同的類型要關聯不同的model..
     * @param $user : user model
     * @param $storeType : 商家類型
     * @return mixed
     */
    private function getWorks($user, $storeType)
    {
        // 婚紗禮服/喜餅-作品集
        if (in_array($storeType, [2, 10])) {
            return $user->storeWorkCollects($storeType)->with('store')->get()->groupBy('stores.id');
        }
        return $user->storeWorkCollects($storeType)->with('album.store')->get()->groupBy('album.store_id');
    }
}
