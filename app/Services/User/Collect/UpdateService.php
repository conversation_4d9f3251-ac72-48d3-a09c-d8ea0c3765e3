<?php
/*
 |--------------------------------------
 |  更新收藏 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Collect;

use App\Traits\ApiErrorTrait;
use App\Models\UserCollect;
use App\Models\StoreAlbum;
use App\Models\StoreAlbumImage;
use App\Models\Store;
use App\Models\StoreService;
use App\Models\VenueRoom;

class UpdateService
{
    private $userCollect;
    private $storeAlbum;
    private $storeAlbumImage;
    private $store;
    private $storeService;
    private $venueRoom;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        UserCollect $userCollect,
        StoreAlbum $storeAlbum,
        StoreAlbumImage $storeAlbumImage,
        Store $store,
        StoreService $storeService,
        VenueRoom $venueRoom
    ) {
        $this->userCollect     = $userCollect;
        $this->storeAlbum      = $storeAlbum;
        $this->storeAlbumImage = $storeAlbumImage;
        $this->store           = $store;
        $this->storeService    = $storeService;
        $this->venueRoom       = $venueRoom;
    }

    /**
     * 更新收藏
     */
    public function run($request)
    {
        // 取得使用者
        $user = $request['user'];

        // 商家
        if ($request['type'] == 'store') {

            // 驗證商家
            $store = $this->store->published()->find($request['target_id']);
            if (!$store) {
                $this->setException('查無此商家！');
            }

            $collectType = 'store';
        }

        // 商家服務方案
        if ($request['type'] == 'service') {

            // 驗證服務方案
            $storeService = $this->storeService->status('show')->has('store')->find($request['target_id']);
            if (!$storeService) {
                $this->setException('查無此商家服務方案！');
            }

            $collectType = 'service';
        }

        // 商家作品
        if ($request['type'] == 'work') {

            // 婚紗禮服/喜餅-驗證作品集
            if (in_array($request['store_type'], [2, 10])) {
                $storeWork   = $this->storeAlbum->status('show')->has('store');
                $collectType = 'album';

            // 其他商家類型-驗證單一作品
            } else {
                $storeWork   = $this->storeAlbumImage->has('album.store');
                $collectType = 'album_image';
            }

            // 驗證作品
            $storeWork = $storeWork->find($request['target_id']);
            if (!$storeWork) {
                $this->setException('查無此商家作品！');
            }
        }

        // 婚宴場地廳房
        if ($request['type'] == 'venue_room') {

            // 驗證服務方案
            $venueRoom = $this->venueRoom->status('show')->has('store')->find($request['target_id']);
            if (!$venueRoom) {
                $this->setException('查無此婚宴場地廳房！');
            }

            $collectType = 'venue_room';
        }

        // 更新收藏
        if ($request['collect']) {
            $this->userCollect->withTrashed()->updateOrCreate([
                'user_id'   => $user->id,
                'type'      => $collectType,
                'target_id' => $request['target_id'],
            ],[
                'deleted_at'=> null
            ]);
        } else {
            $this->userCollect->where('user_id', $user->id)
                                ->where('type', $collectType)
                                ->where('target_id', $request['target_id'])
                                ->delete();
        }
    }
}
