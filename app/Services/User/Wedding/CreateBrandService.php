<?php
/*
 |--------------------------------------
 |  我的婚禮-新增品牌 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Wedding;

use App\Models\Brand;
use App\Services\Tools\GetWebData\GetFacebookUrlInfoService;
use App\Traits\ApiErrorTrait;

class CreateBrandService
{
    private $brand;
    private $getFacebookUrlInfoService;

    use ApiErrorTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Brand $brand,
        GetFacebookUrlInfoService $getFacebookUrlInfoService
    ) {
        $this->brand                     = $brand;
        $this->getFacebookUrlInfoService = $getFacebookUrlInfoService;
    }

    /**
     * 新增品牌
     */
    public function run($request)
    {
        // 若有選擇品牌
        if ($request['brand_id']) {

            // 取得品牌
            $brand = $this->brand->find($request['brand_id']);
            if (!$brand) {
                $this->setException('找不到此品牌！');
            }

            // 若有主要商家，則不更新資料
            if ($brand->primaryStores->first()) {
                return $brand;
            }
        }

        // 新增品牌
        if (empty($brand)) {
            $brand = clone $this->brand;
        }

        // 若品牌已審核
        if ($brand->status == 'published') {
            return $brand;
        }

        // 紀錄Email重複更新
        if ($brand->email && $request['email']) {
            $brand->logRepeats()->updateOrCreate(['email' => $request['email']]);
        }

        // 僅更新沒資料的
        $brand->name      = $brand->name ?: $request['name'];
        $brand->website   = $brand->website ?: $request['website'];
        $brand->fb_page   = $brand->fb_page ?: $request['fb_page'];
        $brand->instagram = $brand->instagram ?: $request['instagram'];
        $brand->email     = $brand->email ?: $request['email'];
        $brand->phone     = $brand->phone ?: $request['phone'];
        $brand->tel       = $brand->tel ?: $request['tel'];
        // $brand->status = 'pending'; // 預設未審核

        // 更新FB紛絲專頁ID
        if (!$brand->fb_id && $brand->fb_page) {
            $brand->fb_id = $this->getFacebookUrlInfoService->encode($brand->fb_page);
        }

        // 儲存品牌
        $brand->save();

        return $brand;
    }
}
