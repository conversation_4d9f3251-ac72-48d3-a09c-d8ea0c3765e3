<?php
/*
 |--------------------------------------
 |  我的婚禮-更新我的婚禮 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Wedding;

class UpdateService
{
    /**
     * 更新我的婚禮
     */
    public function run($request)
    {
        // 取得使用者
        $user = $request['user'];

        // 新郎/新娘的暱稱 & 公開我的黃金團隊
        $user->wedding()->updateOrCreate([], [
            'name_groom'     => $request['name_groom'],
            'name_bride'     => $request['name_bride'],
            'show_gold_team' => $request['show_gold_team'],
        ]);

        // 婚禮形式
        $user->weddingTypes()->delete();
        foreach ($request['types'] as $type) {
            $user->weddingTypes()->withTrashed()->updateOrCreate(['type' => $type], ['deleted_at' => NULL]);
        }
    }
}
