<?php
/*
 |--------------------------------------
 |  我的婚禮-取得品牌列表 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Wedding;

use App\Traits\ParseKeywordTrait;
use App\Models\Brand;
use App\Models\Wdv2\ShareStoreLink;
use App\Models\Store;
use App\Services\Tools\GetWebData\GetFacebookUrlInfoService;

class GetBrandService
{
    private $brand;
    private $shareStoreLink;
    private $store;
    private $getFacebookUrlInfoService;
    private $checkFbPage = false;

    use ParseKeywordTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Brand $brand,
        ShareStoreLink $shareStoreLink,
        Store $store,
        GetFacebookUrlInfoService $getFacebookUrlInfoService
    ) {
        $this->brand                     = $brand;
        $this->shareStoreLink            = $shareStoreLink;
        $this->store                     = $store;
        $this->getFacebookUrlInfoService = $getFacebookUrlInfoService;
    }

    /**
     * 取得品牌列表
     */
    public function run($request)
    {
        // 關鍵字簡化空白
        $request['keyword'] = $this->simplifySpace($request['keyword']);

        // 取得品牌Model
        $brands = $this->getBrands($request);

        // 若精準關鍵字搜尋無結果，則執行模糊關鍵字搜尋
        if ($request['keyword'] && strpos($request['keyword'], ' ') && !$brands->count()) {
            $request['keyword_array'] = $this->splitToArray($request['keyword']);
            $brands = $this->getBrands($request);
        }

        return $brands;
    }

    /**
     * 取得品牌Model
     */
    private function getBrands($request)
    {
        // 僅搜尋出品牌資料
        $model = $this->brand->select('brands.*');

        // 提供分享文數量
        $model = $model->withCount('articles');

        // 關聯商家
        $model = $model->leftJoin($this->shareStoreLink->getTable().' AS shareStoreLink', 'brands.id', '=', 'shareStoreLink.share_store_id');
        $model = $model->leftJoin($this->store->getTable(), 'stores.id', '=', 'shareStoreLink.store_id');

        // 排除已刪除的品牌
        // $model = $model->where('brands.show_flag', '!=', 0);

        // 排除已刪除關聯的商家
        // $model = $model->where(function ($query) {
        //     $query = $query->where('shareStoreLink.show_flag', '!=', 0)
        //                     ->orWhereNull('shareStoreLink.id');
        // });

        // 排除已停用的商家
        // $model = $model->where(function ($query) {
        //     $query = $query->where('stores.status', '!=', 'delete')
        //                     ->orWhereNull('stores.id');
        // });

        // 排除其餘的商家類別
        if ($request['team_type']) {
            $model = $model->where(function ($query) use ($request) {
                $query->orWhereNull('stores.type');
                // 婚禮週邊 => 除了「婚禮小物/禮車/喜帖/婚禮週邊」，其餘的都排除
                if ($request['team_type'] == 15) {
                    $query->orWhereIn('stores.type', [7, 13, 14, 15]);
                } else {
                    $query->orWhere('stores.type', $request['team_type']);
                }
            });
        }

        // Request 篩選
        $model = $model->where(function ($query) use ($request) {

            // 模糊關鍵字搜尋
            if ($request['keyword_array']) {
                foreach ($request['keyword_array'] as $keyword) {
                    $this->getNameWhereCondition($query, $keyword);
                }

            // 精準關鍵字搜尋
            } elseif ($request['keyword']) {
                $this->getNameWhereCondition($query, $request['keyword']);

            // 搜尋商家相關資訊
            } else {

                // 商家名稱
                $name = trim($request['name']);
                if ($name) {
                    $this->getNameWhereCondition($query, $name);
                }

                // 官網連結
                // FOR https://domain.com/xxx OR domain.com/xxx OR domain.com
                $website  = trim($request['website']);
                $parseUrl = parse_url($website, PHP_URL_HOST) ?: parse_url($website, PHP_URL_PATH);
                $domain   = explode('/', $parseUrl)[0];
                if ($domain == 'www.facebook.com') {
                    $query = $this->getFbPageWhereCondition($query, $website);
                } elseif ($domain == 'www.instagram.com') {
                    $query = $this->getInstagramWhereCondition($query, $website);
                } elseif ($domain) {
                    $jsonString = substr(json_encode($domain), 1, -1);
                    $query = $query->orWhere('brands.website', 'like', '%' . $domain . '%')
                                    ->orWhere('brands.fb_page', 'like', '%' . $domain . '%')
                                    ->orWhere('brands.instagram', 'like', '%' . $domain . '%')
                                    ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                                    ->orWhere('stores.website', 'like', '%' . $domain . '%')
                                    ->orWhere('stores.fb_page', 'like', '%' . $domain . '%')
                                    ->orWhere('stores.instagram', 'like', '%' . $domain . '%');
                }

                // instagram
                $instagram = trim($request['instagram']);
                if ($instagram) {
                    $query = $this->getInstagramWhereCondition($query, $instagram);
                }

                // FB粉絲團連結
                $fb_page = trim($request['fb_page']);
                if ($fb_page) {
                    $query = $this->getFbPageWhereCondition($query, $fb_page);
                }

                // Email
                $email = trim($request['email']);
                if ($email) {
                    $jsonString = substr(json_encode($email), 1, -1);
                    $query = $query->orWhere('brands.email', 'like', '%' . $email . '%')
                                    ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                                    ->orWhere('stores.email', 'like', '%' . $email . '%')
                                    ->orWhere('stores.contact_email', 'like', '%' . $email . '%');
                }

                // 行動電話
                $phone = trim($request['phone']);
                if ($phone) {
                    $jsonString = substr(json_encode($phone), 1, -1);
                    $query = $query->orWhere('brands.phone', 'like', '%' . $phone . '%')
                                    ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                                    ->orWhere('stores.phone', 'like', '%' . $phone . '%')
                                    ->orWhere('stores.phone_info', 'like', '%' . $phone . '%')
                                    ->orWhere('stores.tel', 'like', '%' . $phone . '%')
                                    ->orWhere('stores.tel_info', 'like', '%' . $phone . '%');
                }

                // 市話
                $tel = trim($request['tel']);
                if ($tel) {
                    $jsonString = substr(json_encode($tel), 1, -1);
                    $query = $query->orWhere('brands.phone', 'like', '%' . $tel . '%')
                                    ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                                    ->orWhere('stores.phone', 'like', '%' . $tel . '%')
                                    ->orWhere('stores.phone_info', 'like', '%' . $tel . '%')
                                    ->orWhere('stores.tel', 'like', '%' . $tel . '%')
                                    ->orWhere('stores.tel_info', 'like', '%' . $tel . '%');
                }
            }
        });

        // 有主要商家的排序在最前面
        $model = $model->orderBy('shareStoreLink.show_flag', 'DESC');

        // 同團隊類型的排序到前面
        if ($request['team_type']) {
            $model = $model->orderByRaw('FIELD(stores.type, '.$request['team_type'].') DESC');
        }

        // 已審核的排序到前面
        $model = $model->orderByRaw('FIELD(brands.status, "published", "pending") ASC');

        // Query
        $model = $model->get();

        // 去重複
        $model = $model->unique();

        return $model;
    }

    /**
     * 取得商家名稱的Where條件句
     */
    private function getNameWhereCondition($query, $name)
    {
        // $jsonString = substr(json_encode($name), 1, -1);
        $query = $query->orWhere('brands.name', 'like', '%' . $name . '%')
                        // ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                        ->orWhere('stores.name', 'like', '%' . $name . '%');

        return $query;
    }

    /**
     * 取得FB紛絲專頁的Where條件句
     */
    private function getFbPageWhereCondition($query, $fbPageUrl)
    {
        // 已執行過
        if ($this->checkFbPage) {
            return $query;
        }

        // 取得FB紛絲專頁路徑 FOR https://www.facebook.com/xxx OR www.facebook.com/xxx
        $parseUrl   = parse_url($fbPageUrl, PHP_URL_PATH);
        $fbPagePath = explode('/', $parseUrl)[1] ?? '';
        if ($fbPagePath) {
            $jsonString = substr(json_encode($fbPagePath), 1, -1);
            $query = $query->orWhere('brands.website', 'like', '%' . $fbPagePath . '%')
                            ->orWhere('brands.fb_page', 'like', '%' . $fbPagePath . '%')
                            ->orWhere('brands.instagram', 'like', '%' . $fbPagePath . '%')
                            ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                            ->orWhere('stores.website', 'like', '%' . $fbPagePath . '%')
                            ->orWhere('stores.fb_page', 'like', '%' . $fbPagePath . '%')
                            ->orWhere('stores.instagram', 'like', '%' . $fbPagePath . '%');
        }

        // 取得FB紛絲專頁ID
        $fbPageId = $this->getFacebookUrlInfoService->encode($fbPageUrl);
        if ($fbPageId) {
            $query = $query->orWhere('brands.fb_id', $fbPageId)
                            ->orWhere('stores.fb_id', $fbPageId);
        }

        $this->checkFbPage = true;
        return $query;
    }

    /**
     * 取得Instagram的Where條件句
     */
    private function getInstagramWhereCondition($query, $instagramUrl)
    {
        $parseUrl      = parse_url($instagramUrl, PHP_URL_PATH);
        $instagramPath = explode('/', $parseUrl)[1] ?? '';
        if ($instagramPath) {
            $jsonString = substr(json_encode($instagramPath), 1, -1);
            $query = $query->orWhere('brands.website', 'like', '%' . $instagramPath . '%')
                            ->orWhere('brands.fb_page', 'like', '%' . $instagramPath . '%')
                            ->orWhere('brands.instagram', 'like', '%' . $instagramPath . '%')
                            ->orWhere('brands.description', 'like', '%' . $jsonString . '%')
                            ->orWhere('stores.website', 'like', '%' . $instagramPath . '%')
                            ->orWhere('stores.fb_page', 'like', '%' . $instagramPath . '%')
                            ->orWhere('stores.instagram', 'like', '%' . $instagramPath . '%');
        }

        return $query;
    }
}
