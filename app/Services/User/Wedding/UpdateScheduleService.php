<?php
/*
 |--------------------------------------
 |  我的婚禮-更新黃金團隊進度 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\User\Wedding;

class UpdateScheduleService
{
    /**
     * 更新黃金團隊進度
     */
    public function run($request, $forceSync = true)
    {
        // 取得使用者
        $user = $request['user'];

        // 取得婚禮形式
        $weddingType = $user->weddingTypes()
                            ->withTrashed()
                            ->updateOrCreate(['type' => $request['wedding_type']], ['deleted_at' => NULL]);

        // 若已下訂狀態但沒有設定品牌，則狀態需改為尋找中
        $status = ($request['status'] == 'booked' && $forceSync && !$request['brand_ids']) ? 'pending' : $request['status'];

        // 更新黃金團隊進度
        $schedule = $weddingType->schedules()->updateOrCreate([
            'store_type' => $request['team_type'],
        ], [
            'user_id' => $user->id,
            'status'  => $status,
        ]);

        // 同步關聯黃金團隊品牌
        if ($forceSync) {
            $pivotArray = [];
            foreach ($request['brand_ids'] as $store_id) {
                $pivotArray[$store_id] = [
                    'user_id'              => $user->id,
                    'user_wedding_type_id' => $weddingType->id,
                ];
            }
            $schedule->brands()->sync($pivotArray);

        // 僅新增關聯黃金團隊品牌
        } else {
            $schedule->brands()->syncWithoutDetaching([
                $request['brand_id'] => [
                    'user_id'              => $user->id,
                    'user_wedding_type_id' => $weddingType->id,
                ]
            ]);
        }
    }
}
