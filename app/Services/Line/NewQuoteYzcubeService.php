<?php

namespace App\Services\Line;

use App\Services\Line\LineService;
use App\Repositories\StoreUserRepository;
use App\Models\Wdv2\UserQuote;

class NewQuoteYzcubeService
{
    private $lineService;
    private $storeUserRepository;

    /**
     * __construct
     *
     * @param  LineService  $lineService
     * @return void
     */
    public function __construct(
        LineService $lineService,
        StoreUserRepository $storeUserRepository
    ) {
        $this->lineService         = $lineService;
        $this->storeUserRepository = $storeUserRepository;
    }

    /**
     * 寄出Line訊息
     *
     * @param  UserQuote $userQuote
     * @return void
     */
    public function sendLine(UserQuote $userQuote)
    {
        // 主動報價須通知商家的Line IDs
        $storeLineIds      = $this->storeUserRepository->getNewQuoteLineIdsByStoreType($userQuote->type);
        $storeLineIdsCount = count($storeLineIds);

        // 取得須通知的管理者Line IDs
        $devices = $this->storeUserRepository->getAdminLineIds();

        // 通知訊息
        $message = '[管理員限定] '.$storeLineIdsCount.'則, '.now()->format('H:i')."\n";
        $message .= '【📣公開詢價】'."\n";
        $message .= '新人 '.$userQuote->user->name.'('.$userQuote->user_id.') 想找「'.$userQuote->present()->store_type_name.'」'."\n";
        $message .= '日期：'.date('Y/m/d', strtotime($userQuote->date))."\n";
        $message .= '型式：'.$userQuote->present()->wedding_type_name."\n";
        if ($userQuote->city_code || $userQuote->wedding_venue_type) {
            $message .= '地點：';
            $message .= $userQuote->city_code ? $userQuote->present()->cityArea : '';
            $message .= $userQuote->wedding_venue ?: $userQuote->present()->wedding_venue_type_name;
            $message .= "\n";
        }
        $message .= "\n";
        $message .= '👇點連結去報價👇'."\n";
        $message .= config('params.wdv2.admin_url').'/quote/'.$userQuote->id.'?openExternalBrowser=1';

        // 送出訊息
        $this->lineService->send($message, $devices);
    }
}
