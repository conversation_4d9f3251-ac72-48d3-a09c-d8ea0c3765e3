<?php
/*
 |--------------------------------------
 |  Line Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Line;

class LineService
{
    /**
     * 實作送出訊息
     *
     */
    public function send($message = '', $devices = [])
    {
        // 測試環境
        if (env('APP_DEBUG') && $devices) {
            $devices = explode(',', env('LINE_TESTERS'));
            $message = "【".env('APP_NAME')."】\n".$message;
        }

        // 驗證是否有值
        if (!$message || count($devices) == 0) {
            return false;
        }

        $dataArray = [];
        // max_length of to user of line api
        $deviceArray = array_chunk($devices, 150);
        foreach ($deviceArray as $device) {
            $dataArray[] = json_encode([
                'to'       => $device,
                'type'     => 'multi',
                'messages' => [
                    'type' => 'text',
                    'text' => $message,
                ],
            ]);
        }

        // array of curl handles
        $curly = [];
        // data to be returned
        $result = [];

        // multi handle
        $mh = curl_multi_init();
        $uri = env('LINE_API').env('LINE_PUSH_PATH');

        foreach ($dataArray as $id => $data) {
            $curly[$id] = curl_init();
            $hash = hash_hmac('sha256', json_encode($data), env('LINE_CHANNEL_SECRET'), true);
            $signature = base64_encode($hash);
            // Compare X-Line-Signature request header string and the signature

            $headers = [
                'Content-Type: application/json; charset=utf-8',
                'X-Line-Signature: '.$signature
            ];

            curl_setopt($curly[$id], CURLOPT_URL, $uri);
            curl_setopt($curly[$id], CURLOPT_POST, true);
            curl_setopt($curly[$id], CURLOPT_POSTFIELDS, $data);
            curl_setopt($curly[$id], CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curly[$id], CURLOPT_CUSTOMREQUEST, 'POST');
            if (!is_null($headers)) {
                curl_setopt($curly[$id], CURLOPT_HTTPHEADER, $headers);
            }

            curl_setopt($curly[$id], CURLOPT_CONNECTTIMEOUT, 60);
            curl_setopt($curly[$id], CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curly[$id], CURLOPT_TIMEOUT, 60);

            curl_multi_add_handle($mh, $curly[$id]);
        }

        // execute the handles
        $running = null;
        do {
            curl_multi_exec($mh, $running);
        } while ($running > 0);

        // get content and remove handles
        foreach ($curly as $id => $c) {
            $result[$id] = curl_multi_getcontent($c);
            curl_multi_remove_handle($mh, $c);
        }

        // all done
        curl_multi_close($mh);

        return $result;
    }
}
