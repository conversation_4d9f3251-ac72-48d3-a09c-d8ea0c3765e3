<?php
/*
 |--------------------------------------
 |  公開詢價的試吃優惠卷 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Coupon;

use App\Models\Coupon;
use App\Models\CouponMultiple;
use App\Services\Mail\User\TastingCouponService;
use App\Services\Mail\Yzcube\MessageNotificationService;

class UserQuoteTastingService
{
    private $coupon;
    private $couponMultiple;
    private $messageNotificationService;
    private $tastingCouponService;

    // 活動期間
    public $dateRange = ['2024-01-01 00:00:00', '2024-09-30 23:59:59'];

    // 公開詢價 新秘 & 婚攝/婚錄
    public $quoteStoreTypes = [3, 4];

    // 提醒庫存量
    private $alertInventory = 100;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Coupon $coupon,
        CouponMultiple $couponMultiple,
        MessageNotificationService $messageNotificationService,
        TastingCouponService $tastingCouponService
    ) {
        $this->coupon                   = $coupon;
        $this->couponMultiple           = $couponMultiple;
        $this->messageNotificationService = $messageNotificationService;
        $this->tastingCouponService     = $tastingCouponService;
    }

    /**
     * 領取公開詢價的試吃優惠卷
     *
     */
    public function run($user)
    {
        // 驗證截止日期
        if (now() >= $this->dateRange[1]) {
            return false;
        }

        // 限定活動期間內，公開詢價指定項目
        if (
            $user->quotes()
                    ->whereIn('type', $this->quoteStoreTypes)
                    ->whereBetween('created_at', $this->dateRange)
                    ->havingRaw('COUNT(DISTINCT type) = ?', [count($this->quoteStoreTypes)])
                    ->doesntExist()
        ) {
            return false;
        }

        // 找出公開詢價的試吃優惠卷
        $couponIds = $this->coupon->published()
                                    ->where('issuance_type', 'user_quote_tasting')
                                    ->pluck('id');

        // 驗證是否已領取
        if (
            $this->couponMultiple->whereIn('coupon_id', $couponIds)
                                    ->where('user_id', $user->id)
                                    ->exists()
        ) {
            return true;
        }

        // 找出優惠代碼
        $couponMultiples = $this->couponMultiple->whereIn('coupon_id', $couponIds)
                                                ->whereNull('user_id')
                                                ->whereNull('used_at');

        // 驗證是否已發完
        $couponMultiple = $couponMultiples->first();
        if (!$couponMultiple) {
            $this->messageNotificationService->sendMail('【餘額用盡】公開詢價的試吃優惠代碼已全數用盡，目前已停止發送！');
            return false;
        }

        // 記錄已領取
        $couponMultiple->user_id = $user->id;
        $couponMultiple->save();

        // 將公開詢價的試吃優惠代碼寄到信箱
        $this->tastingCouponService->sendMail($user, $couponMultiple);

        // 提醒庫存量
        $couponMultipleCount = $couponMultiples->count();
        if ($couponMultipleCount <= $this->alertInventory) {
            $this->messageNotificationService->sendMail('【餘額不足】已剩'.$couponMultipleCount.'組公開詢價的試吃優惠代碼！');
        }
    }
}
