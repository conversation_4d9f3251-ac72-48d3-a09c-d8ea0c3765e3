<?php
/*
 |--------------------------------------
 |  活動表單-儲存優惠券 Service
 |--------------------------------------
 |
 |
 */

namespace App\Services\Coupon\Yzcube;

use App\Models\Coupon;
use App\Models\CouponMultiple;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;

class SaveService
{
    private $coupon;
    private $couponMultiple;
    private $addMultipleCodes = NULL;

    use ApiErrorTrait;
    use RandStringTrait;

    public function __construct(
        Coupon $coupon,
        CouponMultiple $couponMultiple
    ) {
        $this->coupon         = $coupon;
        $this->couponMultiple = $couponMultiple;
    }

    /**
     * 儲存優惠券
     *
     */
    public function run($request)
    {
        // 取得優惠券Model
        $coupon = $this->coupon->find($request['coupon_id']);
        if (!$coupon) {
            $coupon = clone $this->coupon;
        }

        // 驗證優惠名稱重複
        if ($this->coupon->where('title', $request['title'])->where('id', '!=', $coupon->id)->exists()) {
            $this->setException('優惠名稱已被使用，請更換新的', 4013);
        }

        // 編輯優惠券
        if ($coupon->id) {

            // 驗證優惠卷的可使用次數，不得低於目前已使用數量
            if ($request['limit'] && $request['limit'] < $coupon->used_count) {
                $this->setException('優惠卷的可使用次數，不得低於目前已使用數量！');
            }
            $coupon->limit = $request['limit'];

        // 新增優惠券
        } else {

            // 單組通用代碼
            if ($request['type'] == 'single') {

                // 驗證優惠代碼重複
                if ($this->coupon->where('type', 'single')
                                    ->whereRaw('BINARY `code` = ?', $request['code'])
                                    ->exists()
                ) {
                    $this->setException('優惠代碼已被使用，請更換新的', 4014);
                }
                $coupon->code  = $request['code'];
                $coupon->limit = $request['limit'];

            // 多組獨立代碼
            } else {

                // 要搶走優惠券編號
                if ($request['take_coupon_id']) { // take_start_number & take_end_number

                    // 取得要被搶走的優惠券Model
                    $takingCoupon = $this->coupon->find($request['take_coupon_id']);
                    if (!$takingCoupon) {
                        $this->setException('找不到要搶走的優惠券！');
                    }

                    // 沒有任何可搶走的優惠代碼
                    if ($takingCoupon->multiple()->whereBetween('number', $request['take_number_range'])->doesntExist()) {
                        $this->setException($takingCoupon->title.'：找不到這些序號！');
                    }

                    // 要搶走的優惠代碼中，已有序號被使用
                    if ($takingCoupon->multiple()->whereBetween('number', $request['take_number_range'])->used()->exists()) {
                        $this->setException('序號中包含已使用過的代碼', 4015);
                    }

                    // 沿用前綴碼
                    $coupon->code  = $takingCoupon->code;
                    $coupon->limit = $takingCoupon->multiple()->whereBetween('number', $request['take_number_range'])->count();

                    // 搶走多組獨立代碼
                    $this->addMultipleCodes = 'take';

                // 新增多組獨立代碼
                } else {

                    // 驗證優惠卷的可使用次數，多組獨立代碼不可設定無上限
                    if (!$request['limit']) {
                        $this->setException('多組獨立代碼的可使用次數，不可設定無上限！');
                    }

                    // 產生唯一的前綴碼
                    $coupon->code  = $this->getCouponMultipleCode();
                    $coupon->limit = $request['limit'];

                    // 產生多組獨立代碼
                    $this->addMultipleCodes = 'rand';
                }
            }
        }

        // 儲存優惠券
        $coupon->type            = $request['type'];
        $coupon->title           = $request['title'];
        $coupon->condition_type  = $request['condition_type'];
        $coupon->condition_value = $request['condition_value'];
        $coupon->discount_type   = $request['discount_type'];
        $coupon->discount_value  = $request['discount_value'];
        $coupon->start_date      = $request['start_date'];
        $coupon->end_date        = $request['end_date'];
        $coupon->note            = $request['note'];
        $coupon->save();

        // 更新可使用此優惠券的表單
        $coupon->events()->sync($request['event_ids']);

        // 搶走多組獨立代碼
        if ($this->addMultipleCodes == 'take') {
            $takingCoupon->multiple()->whereBetween('number', $request['take_number_range'])->update(['coupon_id' => $coupon->id]);

            // 被搶走的優惠卷，要更新數量限制
            $takingCoupon->limit = $takingCoupon->multiple->count();
            $takingCoupon->save();
        }

        // 產生多組獨立代碼
        if ($this->addMultipleCodes == 'rand') {
                $this->createCouponMultipleList($coupon);
        }

        return $coupon;
    }

    /**
     * 產生唯一的優惠代碼 (用於多組獨立代碼的前綴碼)
     *
     * @return string $coupon->code
     */
    private function getCouponMultipleCode()
    {
        // 驗證重複
        $code = $this->getRandString(3);
        while ($this->coupon->where('type', 'multiple')
                            ->whereRaw('BINARY `code` = ?', $code)
                            ->exists()
        ) {
            $code = $this->getRandString(3);
        }

        return $code;
    }

    /**
     * 產生不重複的多組獨立代碼
     *
     * @param model $coupon 優惠券
     * @return void
     */
    public function createCouponMultipleList($coupon)
    {
        for ($number = 1; $number <= $coupon->limit; $number++) {

            // 驗證重複
            $code = $this->getRandString(5);
            while ($this->couponMultiple->where('coupon_id', $coupon->id)
                                        ->whereRaw('BINARY `code` = ?', $code)
                                        ->exists()
            ) {
                $code = $this->getRandString(5);
            }

            // 儲存多組獨立代碼
            $coupon->multiple()->create([
                'number' => $number,
                'code'   => $code,
            ]);
        }
    }
}
