<?php
/*
 |--------------------------------------
 |  發簡訊的 Service
 |
 |  透過三竹簡訊發送 （https://msg2.mitake.com.tw/SMS/）
 |--------------------------------------
 |
 |
 */


namespace App\Services\Notification;

class ShortMessageService
{

    public function __construct()
    {
    }

    /**
     * @param $phone : 十碼手機號碼
     * @param $content : 發送內容
     * @return
     */
    public function send($phone, $content)
    {

        $sendUrl = env('MESSAGE_MITAKE_RUL').'?'.http_build_query([
                        'username' => env('MESSAGE_MITAKE_ACCOUNT'),
                        'password' => env('MESSAGE_MITAKE_PASSWORD'),
                        'dstaddr' => $phone,
                        'smbody' => $content,
                        'CharsetURL' => 'utf-8'
                    ]);

        if (env('MESSAGE_ENV') == 'DEV') {
            //測試時，回傳值
            return [
                'msgid' => '@DEV'.$phone,
                'statusstr' => 'OK',
                'sendType' => 'DEV',
                'statuscode' => 0
            ];
        } else {
            return $this->getResultMsg(file_get_contents($sendUrl));
        }


    }


    /**
     * 解析送出Message結果
     * @param $resultMessage : Message結果
     * @return array
     *
     * msgid
     * statuscode=0 (如果有錯,值為負,見表1)
     * statusstr=OK or 錯誤訊息
     */
    private function getResultMsg($resultMessage){
        $resultMessage = mb_convert_encoding($resultMessage, 'UTF-8', 'BIG-5');

        //===============================
        //  回傳範例 array
        //
        //  [msgid] => $009B3897F,
        //  [statuscode] => 1,
        //  [AccountPoint] => 4631.
        //
        //===============================

        return parse_ini_string($resultMessage);
    }

}
