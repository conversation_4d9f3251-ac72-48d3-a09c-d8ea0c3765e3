<?php
/*
 |--------------------------------------
 |  Slack Incoming WebHook Service
 |--------------------------------------
 |  https://weddingdaygroup.slack.com/apps/A0F7XDUAZ-incoming-webhook
 |
 |  {
 |      "fallback": "Required text summary of the attachment that is shown by clients that understand attachments but choose not to show them.",
 |
 |      "text": "Optional text that should appear within the attachment",
 |      "pretext": "Optional text that should appear above the formatted data",
 |
 |      "color": "#36a64f", // Can either be one of 'good', 'warning', 'danger', or any hex color code
 |
 |      // Fields are displayed in a table on the message
 |      "fields": [
 |          {
 |              "title": "Required Field Title", // The title may not contain markup and will be escaped for you
 |              "value": "Text value of the field. May contain standard message markup and must be escaped as normal. May be multi-line.",
 |              "short": false // Optional flag indicating whether the `value` is short enough to be displayed side-by-side with other values
 |          }
 |      ]
 |  }
 */

namespace App\Services\Slack;

use GuzzleHttp\Client;

class IncomingWebhookService
{
    private $client;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client
    ) {
        $this->client = $client;
    }

    /**
     * Slack 發佈訊息
     *
     * @param string $text
     * @param array $attachments
     * @return void
     */
    public function sendMessage($text, $attachments = [])
    {
        $apiPath = env('SLACK_INCOMING_WEBHOOK_URL');
        $params  = [
            'payload' => json_encode([
                'text'        => $text,
                'attachments' => $attachments,
            ]),
        ];

        // Incoming WebHook API
        $resp = $this->client->request('POST', $apiPath, [
            'form_params' => $params,
        ]);
    }
}
