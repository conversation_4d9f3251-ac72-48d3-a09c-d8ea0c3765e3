<?php
/*
 |--------------------------------------
 |  Slack Outgoing WebHook Service
 |--------------------------------------
 |  https://weddingdaygroup.slack.com/apps/A0F7VRG6Q-outgoing-webhook
 |
 */

namespace App\Services\Slack;

use App\Repositories\UserRepository;
use App\Repositories\StoreRepository;
use Illuminate\Support\Str;
use Illuminate\Http\Request;

class OutgoingWebhookService
{
    private $triggerWord = '@譚菜菜';
    private $commands = [];
    private $result = '';

    private $request;
    private $userRepository;
    private $storeRepository;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Request $request,
        UserRepository $userRepository,
        StoreRepository $storeRepository
    ) {
        $this->request         = $request;
        $this->userRepository  = $userRepository;
        $this->storeRepository = $storeRepository;
    }

    /**
     * Slack 指令
     *
     * @param string $text
     * @return $data
     */
    public function console($text)
    {
        $text           = str_replace($this->triggerWord, '', $text);
        $this->commands = explode(' ', trim($text));
        $method         = Str::camel($this->commands[0]);

        // 找不到指令，就輸出 help()
        if (!method_exists($this, $method)) {
            if ($method != '') {
                $this->result .= '*【 錯誤 】找不到此相關指令：'.$this->commands[0].'*'.PHP_EOL;
                $this->result .= '　'.PHP_EOL;
            }
            $method = 'help';
        }

        // 輸出指令
        $this->{$method}();
        return $this->result;
    }

    /**
     * 查看 Slack 的指令列表
     *
     * @return data
     */
    private function help()
    {
        $this->result .= '*如何使用指令 (Usage)：*'.PHP_EOL;
        $this->result .= '　　command [arguments]'.PHP_EOL;
        $this->result .= '　'.PHP_EOL;

        $this->result .= '*可用的指令 (Available commands)：*'.PHP_EOL;
        $this->result .= '　　help                             查看 Slack 的指令列表'.PHP_EOL;
        $this->result .= '　　line-command            查看 Line 的指令列表'.PHP_EOL;
        $this->result .= '　　find-user                     列出新娘資訊 ( id=? k=? keyword=? )'.PHP_EOL;
        $this->result .= '　　find-store                    列出商家資訊 ( id=? k=? keyword=? )'.PHP_EOL;
        $this->result .= '　'.PHP_EOL;

        $this->result .= '*選用參數設定 (arguments)：*'.PHP_EOL;
        $this->result .= '　　id=                               ID'.PHP_EOL;
        $this->result .= '　　k=, keyword=             關鍵字'.PHP_EOL;
        // $this->result .= '　　d=, date=                    日期'.PHP_EOL;
        $this->result .= '　'.PHP_EOL;
    }

    /**
     * 查看 Line 的指令列表
     *
     * @return data
     */
    private function lineCommand()
    {
        $this->result .= '*Line 指令列表*'.PHP_EOL;
        $this->result .= '　[綁定帳號]                    點擊連結登入商家後台後，即可綁定Line帳號'.PHP_EOL;
        $this->result .= '　[取消綁定帳號]            直接取消綁定商家帳號'.PHP_EOL;
        $this->result .= '　[好日子]                        可選擇「本月好日子」、「下個月好日子」'.PHP_EOL;
        $this->result .= '　[本月好日子]                列出本月的超級大日子，和其他好日子'.PHP_EOL;
        $this->result .= '　[下個月好日子]            列出下個月的超級大日子，和其他好日子'.PHP_EOL;
        $this->result .= '　'.PHP_EOL;
    }

    /**
     * 列出新娘資訊
     *
     * @return data
     */
    private function findUser()
    {
        // 沒有參數
        if (empty($this->commands[1])) {
            $this->result .= '*【 錯誤 】請輸入正確指令*,　例如：find-user k=譚菜菜'.PHP_EOL;
            $this->result .= '　'.PHP_EOL;
            return;
        }

        // 參數錯誤
        $arguments = explode('=', $this->commands[1]);
        if (!in_array($arguments[0], ['id', 'k', 'keyword']) OR empty($arguments[1])) {
            $this->result .= '*【 錯誤 】請輸入正確指令*,　例如：find-user k=譚菜菜'.PHP_EOL;
            $this->result .= '　'.PHP_EOL;
            return;
        }

        // 搜尋ID
        if ($arguments[0] == 'id') {
            $user = $this->userRepository->getFirst(['id' => $arguments[1]]);
            if ($user) {
                $this->getUserInfo($user);
            } else {
                $this->result .= '*【 錯誤 】找不到此新娘資訊*'.PHP_EOL;
            }
            $this->result .= '　'.PHP_EOL;
            return;
        }

        // 搜尋Keyword
        if (in_array($arguments[0], ['k', 'keyword'])) {
            $keyword = $this->stripEmailTag($arguments[1]);
            $request = $this->request->merge(['keyword' => $keyword]);
            $users = $this->userRepository->getYzcubeListByRequest($request);

            $this->result .= '*【 總計 】共'.$users->total().'筆 ( 最多僅列出前'.$users->perPage().'筆 )*'.PHP_EOL;
            foreach ($users as $user) {
                $this->result .= '　';
                $this->getUserInfo($user);
            }
            $this->result .= '　'.PHP_EOL;
        }
    }

    // 取得user資訊
    private function getUserInfo($user)
    {
        $this->result .= $user->id.', '.$user->name.', '.$user->email;
        $this->result .= ' | ';
        $this->result .= '<'.config('params.wdv3.user_url').'/'.$user->id.'|個人主頁>';
        $this->result .= ' | ';
        $this->result .= '<'.config('params.wdv3.yzcube_url').'/#/user/list/content/'.$user->id.'|神之後台-新娘資料>';
        $this->result .= '　'.PHP_EOL;
    }

    // 移除email標籤
    private function stripEmailTag($text)
    {
        preg_match_all('/<mailto:([\w\-+&@#\/%=~_$;?!:,.]+)\|([\w\-+&@#\/%=~_$;?!:,.]+)>/Usi', $text, $matches, PREG_SET_ORDER);
        if ($matches) {
            $text = str_replace($matches[0][0], $matches[0][1], $text);
        }

        return $text;
    }

    /**
     * 列出商家資訊
     *
     * @return data
     */
    private function findStore()
    {
        // 沒有參數
        if (empty($this->commands[1])) {
            $this->result .= '*【 錯誤 】請輸入正確指令*,　例如：find-store k=點點'.PHP_EOL;
            $this->result .= '　'.PHP_EOL;
            return;
        }

        // 參數錯誤
        $arguments = explode('=', $this->commands[1]);
        if (!in_array($arguments[0], ['id', 'k', 'keyword']) OR empty($arguments[1])) {
            $this->result .= '*【 錯誤 】請輸入正確指令*,　例如：find-store k=點點'.PHP_EOL;
            $this->result .= '　'.PHP_EOL;
            return;
        }

        // 搜尋ID
        if ($arguments[0] == 'id') {
            $store = $this->storeRepository->getFirst(['id' => $arguments[1]]);
            if ($store) {
                $this->getStoreInfo($store);
            } else {
                $this->result .= '*【 錯誤 】找不到此商家資訊*'.PHP_EOL;
            }
            $this->result .= '　'.PHP_EOL;
            return;
        }

        // 搜尋Keyword
        if (in_array($arguments[0], ['k', 'keyword'])) {
            $keyword = $this->stripEmailTag($arguments[1]);
            $request = $this->request->merge(['keyword' => $keyword]);
            $stores = $this->storeRepository->getYzcubeListByRequest($request);

            $this->result .= '*【 總計 】共'.$stores->total().'筆 ( 最多僅列出前'.$stores->perPage().'筆 )*'.PHP_EOL;
            foreach ($stores as $store) {
                $this->result .= '　';
                $this->getStoreInfo($store);
            }
            $this->result .= '　'.PHP_EOL;
        }
    }

    // 取得商家資訊
    private function getStoreInfo($store)
    {
        $this->result .= $store->id.', '.$store->name.', '.$store->email;
        $this->result .= ' | ';
        $this->result .= '<'.config('params.wdv3.www_url').'/store/'.$store->id.'|商家主頁>';
        $this->result .= ' | ';
        $this->result .= '<'.config('params.wdv3.yzcube_url').'/#/store/detail/'.$store->id.'|神之後台-商家資料>';
        $this->result .= '　'.PHP_EOL;
    }
}
