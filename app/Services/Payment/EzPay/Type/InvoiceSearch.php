<?php
declare(strict_types=1);
/*------------------------------------
 | 查詢電子發票
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Payment\EzPay\Type;

use App\Services\Payment\EzPay\Contract\AbsInvoiceTypeHandle;
use Illuminate\Support\Str;

class InvoiceSearch extends AbsInvoiceTypeHandle
{
    /**
     * 查詢電子發票
     * @return void
     */
    public function run()
    {
        // 發票資訊
        $invoice = $this->data['invoice'];

        // API 環境參數設定
        $parames = [
            'MerchantOrderNo' => $invoice->merchant_order_no,
            'MerchantID'      => $invoice->setting->merchant_id,
            'HashKey'         => $invoice->setting->hash_key,
            'HashIV'          => $invoice->setting->hash_iv,
        ];

        // postData 欄位資料
        $postData = [
            'RespondType'     => 'JSON', // 回傳格式：JSON 或是 String。
            'Version'         => '1.3', // 串接程式版本：固定帶 1.3。
            'TimeStamp'       => time(), // 時間戳記：請以 time() 格式
            'MerchantOrderNo' => $invoice->merchant_order_no, // 自訂編號：此次查詢的訂單編號。
            'TotalAmt'        => $invoice->total, // 發票金額：開立發票的總金額。
            'InvoiceNumber'   => $invoice->invoice_number, // 發票號碼：此次查詢的發票號碼。
            'RandomNum'       => $invoice->random_number, // 發票防偽隨機碼：開立發票時，回傳的 4 碼發票防偽隨機碼。
        ];

        // 執行 ezPay API
        $this->executeAPI($parames, $postData);

        // 回傳付款結果
        return $this->respObj;
    }
}
