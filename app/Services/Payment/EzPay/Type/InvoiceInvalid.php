<?php
declare(strict_types=1);
/*------------------------------------
 | 作廢電子發票
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Payment\EzPay\Type;

use App\Services\Payment\EzPay\Contract\AbsInvoiceTypeHandle;
use Illuminate\Support\Str;

class InvoiceInvalid extends AbsInvoiceTypeHandle
{
    /**
     * 作廢電子發票
     * @return void
     */
    public function run()
    {
        // 發票資訊＆作廢原因
        $invoice       = $this->data['invoice'];
        $invalidReason = $this->data['invalidReason'];

        // API 環境參數設定
        $parames = [
            'MerchantOrderNo' => $invoice->merchant_order_no,
            'MerchantID'      => $invoice->setting->merchant_id,
            'HashKey'         => $invoice->setting->hash_key,
            'HashIV'          => $invoice->setting->hash_iv,
        ];

        // postData 欄位資料
        $postData = [
            'RespondType'   => 'JSON', // 回傳格式：JSON 或是 String。
            'Version'       => '1.0', // 串接程式版本：固定帶 1.0。
            'TimeStamp'     => time(), // 時間戳記：請以 time() 格式
            'InvoiceNumber' => $invoice->invoice_number, // 發票號碼：欲執行作廢之發票號碼。
            'InvalidReason' => $invalidReason, // 作廢原因：字數限中文 6 字或英文 20 字。
        ];

        // 執行 ezPay API
        $this->executeAPI($parames, $postData);

        // 回傳付款結果
        return $this->respObj;
    }
}
