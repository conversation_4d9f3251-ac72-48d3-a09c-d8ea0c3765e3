<?php
declare(strict_types=1);
/*------------------------------------
 | 電子發票作廢折讓
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Payment\EzPay\Type;

use App\Services\Payment\EzPay\Contract\AbsInvoiceTypeHandle;
use Illuminate\Support\Str;

class AllowanceInvalid extends AbsInvoiceTypeHandle
{
    /**
     * 電子發票作廢折讓
     * @return void
     */
    public function run()
    {
        // 折讓資訊
        $allowance     = $this->data['allowance'];
        $invalidReason = $this->data['invalidReason'];

        // API 環境參數設定
        $parames = [
            'MerchantOrderNo' => $allowance->invoice->merchant_order_no,
            'MerchantID'      => $allowance->invoice->setting->merchant_id,
            'HashKey'         => $allowance->invoice->setting->hash_key,
            'HashIV'          => $allowance->invoice->setting->hash_iv,
        ];

        // postData 欄位資料
        $postData = [
            'RespondType'   => 'JSON', // 回傳格式：JSON 或是 String。
            'Version'       => '1.0', // 串接程式版本：固定帶 1.0。
            'TimeStamp'     => time(), // 時間戳記：請以 time() 格式
            'AllowanceNo'   => $allowance->allowance_no, // 折讓號：欲執行作廢之折讓號。
            'InvalidReason' => $invalidReason, // 作廢原因：作廢原因，字數限中文 6 字或英文 20 字。
        ];

        // 修正 ezPay API 路徑...
        $this->type = 'allowanceInvalid';

        // 執行 ezPay API
        $this->executeAPI($parames, $postData);

        // 回傳付款結果
        return $this->respObj;
    }
}
