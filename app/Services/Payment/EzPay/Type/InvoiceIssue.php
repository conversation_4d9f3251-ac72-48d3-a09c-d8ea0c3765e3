<?php
declare(strict_types=1);
/*------------------------------------
 | 開立電子發票
 |------------------------------------
 |
 |  @input string $merchantOrderNo 商店自訂訂單編號：限英、數字、_格式
 |  @input model $invoiceSetting 商家的發票設定
 |  @input array $data 開立發票內容欄位
    [
        'type'           => 'email', // 發票類型 paper:紙本列印 email:E-mail通知 carrier:存載具 *
        'buyer_ubn'      => NULL, // 買受人統一編號
        'buyer_name'     => '', // 買受人名稱 *
        'buyer_email'    => '', // 買受人E-mail (required_if: type=email)
        'carrier_type'   => NULL, // 載具類型 phone:手機條碼 citizen:自然人憑證條碼 donate:愛心捐贈碼 (required_if: type=carrier)
        'carrier_number' => NULL, // 載具編號 (required_if: type=carrier)
        'order_no'       => '', // 訂單編號 *
        'items'          => json_encode($items), // 品項 *
        'note'           => '', // 備註 (開立統一發票時，於發票備註欄載明簽帳卡號末四碼)
        'sales'          => $sales, // 銷售額(未稅) *
        'tax'            => $total - $sales, // 稅額 *
        'total'          => $total, // 發票金額
    ];
 |
 |  @output $respObj
 |
 |  return
 */

namespace App\Services\Payment\EzPay\Type;

use App\Services\Payment\EzPay\Contract\AbsInvoiceTypeHandle;
use Illuminate\Support\Str;

class InvoiceIssue extends AbsInvoiceTypeHandle
{
    /**
     * 開立電子發票
     * @return void
     */
    public function run()
    {
        // 發票資訊
        $merchantOrderNo = $this->data['merchantOrderNo'];
        $invoiceSetting  = $this->data['invoiceSetting'];
        $data            = $this->data['data'];

        // API 環境參數設定
        $parames = [
            'MerchantOrderNo' => $merchantOrderNo,
            'MerchantID'      => $invoiceSetting->merchant_id,
            'HashKey'         => $invoiceSetting->hash_key,
            'HashIV'          => $invoiceSetting->hash_iv,
        ];

        // 發票種類
        $category = $data['buyer_ubn'] ? 'B2B' : 'B2C';

        // 買受人電子信箱
        $buyerEmail = env('APP_DEBUG') ? env('MAIL_TEST') : $data['buyer_email'];

        // 載具類別：當 Category=B2C 時，才適用此參數。
        $carrierType = '';
        $carrierNum  = '';
        $loveCode    = '';
        if ($category == 'B2C') {
            // 發票類型：E-mail通知 (ezPay 電子發票載具)
            if ($data['type'] == 'email') {
                $carrierType = 2;
                $carrierNum  = $buyerEmail;
            }
            // 發票類型：存載具
            if ($data['type'] == 'carrier') {
                // 手機條碼
                if ($data['carrier_type'] == 'phone') {
                    $carrierType = 0;
                    $carrierNum  = $data['carrier_number'];
                }
                // 自然人憑證條碼
                if ($data['carrier_type'] == 'citizen') {
                    $carrierType = 1;
                    $carrierNum  = $data['carrier_number'];
                }
                // 愛心捐贈碼
                if ($data['carrier_type'] == 'donate') {
                    $loveCode = $data['carrier_number'];
                }
            }
        }

        // 品項明細
        $itemName   = [];
        $itemCount  = [];
        $itemUnit   = [];
        $itemPrice  = [];
        $itemAmt    = [];
        $total      = 0;
        $itemResult = [];
        $items      = json_decode($data['items']);
        foreach ($items as $item) {
            $itemName[]  = Str::limit(str_replace('|', ' ', $item->name), 30);
            $itemCount[] = $item->quantity;
            $itemUnit[]  = '個';
            $itemPrice[] = $item->price;
            $itemAmt[]   = $item->quantity * $item->price;
            $total      += $item->quantity * $item->price;
            $itemResult[] = [
                'ItemName'   => $item->name,
                'ItemCount'  => $item->quantity,
                'ItemPrice'  => $item->price,
                'ItemAmount' => $item->quantity * $item->price,
            ];
        }

        // 銷售額(未稅)
        $sales = round($total / 1.05);

        // postData 欄位資料
        $postData = [
            'RespondType'     => 'JSON', // 回傳格式：JSON 或是 String。
            'Version'         => '1.5', // 串接程式版本：固定帶 1.5。
            'TimeStamp'       => time(), // 時間戳記：請以 time() 格式
            'MerchantOrderNo' => str_replace('-', '_', $merchantOrderNo), // 商店自訂訂單編號：限英、數字、_格式。
            'Status'          => 1, // 開立發票方式：1=立即開立 0=待開立 3=延遲開立
            'Category'        => $category, // 發票種類
            'BuyerName'       => ($category == 'B2B') ? $data['buyer_name'] : Str::limit($data['buyer_name'], 30), // 買受人名稱
            'BuyerUBN'        => $data['buyer_ubn'] ?: '', // 買受人統一編號
            // 'BuyerAddress'    => stripslashes($data['buyer_address']), // 買受人地址
            'BuyerEmail'      => $buyerEmail, // 買受人電子信箱
            'CarrierType'     => $carrierType, // 載具類別：當 Category=B2C 時，才適用此參數。2=ezPay 電子發票載具
            'CarrierNum'      => rawurlencode(trim($carrierNum)), // 載具編號：若 CarrierType 參數有提供數值時，則此參數為必填。
            'LoveCode'        => $loveCode, // 捐贈碼
            'PrintFlag'       => ($category == 'B2B' || ($carrierType === '' && $loveCode === '')) ? 'Y' : 'N', // 索取紙本發票：Y=索取(營業人可於本平台列印此發票) N=不索取(買受人以載具索取發票或捐贈)
            'KioskPrintFlag'  => ($carrierType == 2) ? 1 : '', // 是否開放至合作超商 Kiosk 列印：當 CarrierType=2 時，才適用此參數。
            'TaxType'         => 1, // 課稅別
            'TaxRate'         => 5, // 稅率
            'Amt'             => $sales, // 銷售額(未稅)
            'TaxAmt'          => $total - $sales, // 稅額
            'TotalAmt'        => $total, // 發票金額
            'ItemName'        => implode('|', $itemName), // 商品名稱：多項商品時，以「|」分開
            'ItemCount'       => implode('|', $itemCount), // 商品數量：多項商品時，以「|」分開
            'ItemUnit'        => implode('|', $itemUnit), // 商品單位：多項商品時，以「|」分開
            'ItemPrice'       => implode('|', $itemPrice), // 商品單價：多項商品時，以「|」分開
            'ItemAmt'         => implode('|', $itemAmt), // 商品小計：多項商品時，以「|」分開
            'Comment'         => Str::limit($data['note'], 200), // 開立統一發票時，於發票備註欄載明簽帳卡號末四碼
        ];

        // 執行 ezPay API
        $this->executeAPI($parames, $postData);

        // 提供更詳細的發票資訊供前端可以直接列印
        if ($this->respObj->Status == 'SUCCESS') {
            $this->respObj->Result->BuyerUBN   = $data['buyer_ubn'] ?: ''; // 買受人統一編號
            $this->respObj->Result->Amt        = $sales; // 銷售額(未稅)
            $this->respObj->Result->TaxAmt     = $total - $sales; // 稅額
            $this->respObj->Result->ItemDetail = json_encode($itemResult); // 品項明細
        }

        // 回傳付款結果
        return $this->respObj;
    }
}
