<?php
declare(strict_types=1);
/*------------------------------------
 | 電子發票開立折讓
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Payment\EzPay\Type;

use App\Services\Payment\EzPay\Contract\AbsInvoiceTypeHandle;
use Illuminate\Support\Str;

class AllowanceIssue extends AbsInvoiceTypeHandle
{
    /**
     * 電子發票開立折讓
     * @return void
     */
    public function run()
    {
        // 發票資訊＆折讓金額
        $invoice = $this->data['invoice'];
        $amount  = $this->data['amount'];

        // 折讓品項：若無同金額的發票品項，則取金額最高的品項，皆以排序較前面的為主
        $item = $invoice->items->firstWhere('amount', $amount);
        if (!$item) {
            $item = $invoice->items->sortByDesc('amount')->first();
        }

        // 折讓商品單價：折讓金額/1.05 (四捨五入）
        // 折讓商品小計：折讓商品單價 * 折讓商品數量(預設1個)
        $itemAmt = $itemPrice = round($amount / 1.05);

        // 折讓稅額：折讓金額 - 小計
        $itemTaxAmt = $amount - $itemAmt;

        // API 環境參數設定
        $parames = [
            'MerchantOrderNo' => $invoice->merchant_order_no,
            'MerchantID'      => $invoice->setting->merchant_id,
            'HashKey'         => $invoice->setting->hash_key,
            'HashIV'          => $invoice->setting->hash_iv,
        ];

        // postData 欄位資料
        $postData = [
            'RespondType'     => 'JSON', // 回傳格式：JSON 或是 String。
            'Version'         => '1.3', // 串接程式版本：固定帶 1.3。
            'TimeStamp'       => time(), // 時間戳記：請以 time() 格式
            'InvoiceNo'       => $invoice->invoice_number, // 發票號碼：此次開立折讓的發票號碼。
            'MerchantOrderNo' => $invoice->merchant_order_no, // 商店自訂訂單編號：此次開立折讓的發票，於開立發票時，提供之自訂編號。
            'ItemName'        => Str::limit(str_replace('|', ' ', $item->name), 30), // 折讓商品名稱：多項商品時，以「|」分開
            'ItemCount'       => 1, // 折讓商品數量：多項商品時，以「|」分開
            'ItemUnit'        => '個', // 折讓商品單位：多項商品時，以「|」分開
            'ItemPrice'       => $itemPrice, // 折讓商品單價：多項商品時，以「|」分開
            'ItemAmt'         => $itemAmt, // 折讓商品小計：多項商品時，以「|」分開
            'ItemTaxAmt'      => $itemTaxAmt, // 折讓商品稅額：多項商品時，以「|」分開
            'TotalAmt'        => $amount, // 折讓總金額：此次開立折讓加總金額。
            'Status'          => 1, // 確認折讓方式：1=開立折讓後，立即確認折讓。
        ];

        // 執行 ezPay API
        $this->executeAPI($parames, $postData);

        // 回傳付款結果
        return $this->respObj;
    }
}
