<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | EzPay Invoice Type 處理的 abstract class
 |--------------------------------------
 | 電子發票 API 文件
 | https://cinv.ezpay.com.tw/Invoice_index/download
 |
 */

namespace App\Services\Payment\EzPay\Contract;

use App\Models\LogInvoice;
use GuzzleHttp\Client;
use Log;

abstract class AbsInvoiceTypeHandle
{
    /** invoice 參數 */
    protected $type;
    protected $data;
    protected $respObj;

    private $client;
    private $logInvoice;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client,
        LogInvoice $logInvoice
    ) {
        $this->client     = $client;
        $this->logInvoice = $logInvoice;
    }

    /**
     * Handler logic
     * @param $type
     * @param $data
     * @return void
     */
    public function handle($type, $data)
    {
        $this->type = $type;
        $this->data = $data;

        return $this->run();
    }

    /**
     * 執行 Payment
     * @return void
     */
    abstract public function run();

    /**
     *  執行 ezPay API
     *
     * @param string $parames['MerchantOrderNo']: 自訂訂單編號
     * @param string $parames['MerchantID']: ezPay 的商店代號
     * @param string $parames['HashKey']: ezPay 的商店專屬串接金鑰 HashKey 值
     * @param string $parames['HashIV']: ezPay 的商店專屬串接金鑰 HashIV 值
     * @param array $postData: 欄位資料
     * @return json.object
    **/
    public function executeAPI($parames, $postData)
    {
        // 驗證 API 環境參數設定
        if (empty($parames['MerchantOrderNo']) OR empty($parames['MerchantID']) OR empty($parames['HashKey']) OR empty($parames['HashIV']) OR empty($postData)) {
            Log::error('ezPay API 環境參數設定有誤！', [
                'class'    => class_basename(get_class($this)),
                'type'     => $this->type,
                'parames'  => $parames,
                'postData' => $postData,
            ]);

            return false;
        }

        // postData 欄位資料，轉成字串排列
        $postData   = $this->addpadding(http_build_query($postData));
        $apiPath    = env('EZPAY_API_URL').'/'.$this->type;
        $merchantID = $parames['MerchantID'];
        $hashKey    = $parames['HashKey'];
        $hashIV     = $parames['HashIV'];

        // php7 以上版本加密
        if (phpversion() > 7) {
            $postData = trim(bin2hex(openssl_encrypt($postData, 'AES-256-CBC', $hashKey, OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING, $hashIV)));

        // php7 之前版本加密
        } else {
            $postData = trim(bin2hex(mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $hashKey, $postData, MCRYPT_MODE_CBC, $hashIV)));
        }

        // 執行 ezPay API
        $resp = $this->client->request('POST', $apiPath, [
            'form_params' => [
                'MerchantID_' => $merchantID,
                'PostData_'   => $postData,
            ],
        ]);

        // API Error Log
        if ($resp->getStatusCode() != 200) {
            Log::error('ezPay API Error', [
                'class'      => class_basename(get_class($this)),
                'apiPath'    => $apiPath,
                'MerchantID' => $merchantID,
                'PostData'   => $postData,
            ]);
        }

        // 回傳付款結果
        $this->respObj = json_decode($resp->getBody()->getContents());

        // 成功
        if ($this->respObj->Status == 'SUCCESS') {
            $this->respObj->Result = json_decode($this->respObj->Result);
        }

        // 新增發票回傳紀錄
        $this->addInvoiceLog($parames['MerchantOrderNo']);
    }

    /**
     *  補足字串長度
     *
    **/
    protected function addpadding($string, $blocksize = 32)
    {
        $len     = strlen($string);
        $pad     = $blocksize - ($len % $blocksize);
        $string .= str_repeat(chr($pad), $pad);

        return $string;
    }

    /**
     * 新增發票回傳紀錄
     * @return void
     */
    private function addInvoiceLog($merchantOrderNo)
    {
        $this->logInvoice->create([
            'merchant_order_no' => $merchantOrderNo,
            'type'              => $this->type,
            'status'            => $this->respObj->Status,
            'message'           => $this->respObj->Message,
            'result'            => json_encode($this->respObj->Result, JSON_UNESCAPED_UNICODE),
        ]);
    }
}
