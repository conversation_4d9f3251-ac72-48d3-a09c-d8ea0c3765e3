<?php
declare(strict_types=1);
/*
 |------------------------------------
 | ezPay 電子發票
 |------------------------------------
 |
 |
 */

namespace App\Services\Payment\EzPay;

use Illuminate\Support\Str;
use App\Services\Payment\EzPay\Contract\AbsInvoiceTypeHandle;

class InvoiceHandle
{
    /**
     * 電子發票處理邏輯
     * @param $type
     * @param $data
     * @return array
     */
    public function handle($type, $data)
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Payment\EzPay\Type\EventOrder
         |
         */
        $classPath = 'App\Services\Payment\EzPay\Type\\' . Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsInvoiceTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data);
    }
}
