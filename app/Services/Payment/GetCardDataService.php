<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | 取得信用卡記錄的service
 |--------------------------------------
 |
 |
 */


namespace App\Services\Payment;

use App\Repositories\CardRecordRepository;

class GetCardDataService
{
    /** @var CardRecordRepository */
    private $cardRecordRepository;

    /**
     * GetCardDataService constructor.
     * @param CardRecordRepository $cardRecordRepository
     */
    public function __construct(
        CardRecordRepository $cardRecordRepository
    )
    {
        $this->cardRecordRepository = $cardRecordRepository;
    }

    /**
     * 依商家ID取得
     * @param $storeID : 商家ID
     * @return mixed
     */
    public function getByStore($storeID)
    {
        $this->cardRecordRepository->setOrderBy('sort', 'ASC');
        return $this->cardRecordRepository->getData(['store_id' => $storeID]);
    }
}
