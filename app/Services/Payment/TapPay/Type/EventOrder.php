<?php
declare(strict_types=1);
/*------------------------------------
 | 活動訂購表單的線上支付
 |------------------------------------
 |
 |
 |
 */

namespace App\Services\Payment\TapPay\Type;

use App\Services\Payment\TapPay\Contract\AbsPaymentTypeHandle;

class EventOrder extends AbsPaymentTypeHandle
{
    /**
     * 活動訂購表單的線上支付
     * @return void
     */
    public function run()
    {
        $prime       = $this->data['prime'];
        $eventReport = $this->data['eventReport'];

        // TapPay - Pay By Prime API (排除免費訂單)
        if ($eventReport->order->amount) {
            $this->payByPrime($prime, [
                'amount'       => $eventReport->order->amount,
                'order_number' => $eventReport->order->order_no,
                'details'      => $eventReport->event->title,
                'phone'        => $eventReport->phone,
                'name'         => $eventReport->name,
                'email'        => $eventReport->email,
            ]);
        }

        // 更新活動訂單的付款狀態
        $eventReport->order->updatePaymentStatus();
    }
}
