<?php
declare(strict_types=1);
/*
 |--------------------------------------
 | TapPay Payment Type 處理的 abstract class
 |--------------------------------------
 | template pattern
 |
 */

namespace App\Services\Payment\TapPay\Contract;

use App\Models\LogPayment;
use App\Models\CreditCard;
use App\Traits\Regex\RegexPhoneTrait;
use GuzzleHttp\Client;
use Log;

abstract class AbsPaymentTypeHandle
{
    /** payment 參數 */
    protected $type;
    protected $data;

    private $client;
    private $logPayment;
    private $creditCard;
    private $respObj;

    use RegexPhoneTrait;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        Client $client,
        LogPayment $logPayment,
        CreditCard $creditCard
    ) {
        $this->client     = $client;
        $this->logPayment = $logPayment;
        $this->creditCard = $creditCard;
    }

    /**
     * Handler logic
     * @param $type
     * @param $data
     * @return void
     */
    public function handle($type, $data)
    {
        $this->type = $type;
        $this->data = $data;

        return $this->run();
    }

    /**
     * 執行 Payment
     * @return void
     */
    abstract public function run();


    /**
     * TapPay - Pay By Prime API
     * https://docs.tappaysdk.com/tutorial/zh/back.html#pay-by-prime-api
     * @param string $prime TapPay的付款憑證
     * @param int $data['amount']
     * @param string $data['order_number']
     * @param string $data['details']
     * @param string $data['phone']
     * @param string $data['name']
     * @param string $data['email']
     * @return array
     */
    protected function payByPrime($prime, $data)
    {
        // 支付串接API
        $apiPath = env('TAPPAY_PAY_BY_PRIME_URL');
        $this->contactApi($apiPath, [
            'prime'        => $prime, // 用卡號所換得的字串，由 getPrime 成功時回傳
            'partner_key'  => env('TAPPAY_PARTNER_KEY'), // 綁定 Portal 帳戶的驗證金鑰
            'merchant_id'  => env('TAPPAY_MERCHANT_ID'), // 於 Portal 登錄商家時所產生的識別碼
            'amount'       => (int)$data['amount'], // 交易金額
            'currency'     => 'TWD', // 貨幣種類，預設為 TWD
            'order_number' => $data['order_number'], // 您自定義的訂單編號，用於 TapPay 做訂單識別
            'details'      => $data['details'], // 交易品項內容，為符合 PCI 要求至少必須要有品項名稱，建議填寫的資訊能越詳細越好
            'remember'     => true, // 是否記憶卡號
            'cardholder'   => [ // 持卡人或購買人資訊，以下資料將為「詐欺檢測器」，資料越詳細，可獲得越完整的保護
                'phone_number' => $this->addCountryCode($data['phone']), // 手機號碼，可為 09 開頭的電話或是包含加號之 E.164 格式(“+886923456789”)
                'name'         => $data['name'], // 姓名
                'email'        => $data['email'], // 電子信箱
            ],
        ]);

        // 付款成功，才更新信用卡資訊
        if ($this->respObj->status == 0) {
            $this->updateCreditCard();
        }

        // 新增付款紀錄
        $this->addPaymentLog($data['order_number']);
    }

    /**
     * 支付串接API
     * @return void
     */
    private function contactApi($apiPath, $body)
    {
        $headers = [
            'Content-Type' => 'application/json',
            'x-api-key'    => env('TAPPAY_PARTNER_KEY'),
        ];

        // TapPay API
        $resp = $this->client->request('POST', $apiPath, [
            'headers' => $headers,
            'body'    => json_encode($body, JSON_UNESCAPED_UNICODE),
        ]);

        // API Error Log
        if ($resp->getStatusCode() != 200) {
            Log::error('Tappay API Error', [
                'class'   => class_basename(get_class($this)),
                'type'    => $this->type,
                'data'    => $this->data,
                'apiPath' => $apiPath,
                'body'    => $body,
            ]);
        }

        // 回傳付款結果
        $this->respObj = json_decode($resp->getBody()->getContents());
    }

    /**
     * 更新信用卡資訊
     * 卡片保管資訊。不支援:Apple Pay, Google Pay, Samsung Pay, JKOPAY, 悠遊付
     * card_info 卡片資訊。不支援: LINE Pay, JKOPAY, 悠遊付
     * @return Model CreditCard
     */
    private function updateCreditCard()
    {
        // 更新信用卡資訊
        $this->creditCard = $this->creditCard->updateOrCreate([
            'issuer'    => $this->respObj->card_info->issuer ?? NULL, // 發卡銀行
            'bin_code'  => $this->respObj->card_info->bin_code ?? NULL, // 卡片前六碼
            'last_four' => $this->respObj->card_info->last_four ?? NULL, // 卡片後四碼
        ], [
            'card_token'   => $this->respObj->card_secret->card_token ?? NULL,
            'card_key'     => $this->respObj->card_secret->card_key ?? NULL,
            'funding'      => $this->respObj->card_info->funding ?? NULL,
            'type'         => $this->respObj->card_info->type ?? NULL,
            'level'        => $this->respObj->card_info->level ?? NULL,
            'country'      => $this->respObj->card_info->country ?? NULL,
            'issuer_zh_tw' => $this->respObj->card_info->issuer_zh_tw ?? NULL,
            'bank_id'      => $this->respObj->card_info->bank_id ?? NULL,
            'country_code' => $this->respObj->card_info->country_code ?? NULL,
            'expiry_date'  => $this->respObj->card_info->expiry_date ?? NULL,
        ]);
    }

    /**
     * 新增付款紀錄
     * @return void
     */
    private function addPaymentLog($order_number = NULL)
    {
        $this->logPayment->create([
            'type'                => $this->type,
            'order_number'        => $this->respObj->order_number ?? $order_number,
            'credit_card_id'      => $this->creditCard->id ?? NULL,
            'status'              => $this->respObj->status,
            'msg'                 => $this->respObj->msg,
            'amount'              => $this->respObj->amount ?? NULL,
            'acquirer'            => $this->respObj->acquirer ?? NULL,
            'currency'            => $this->respObj->currency ?? NULL,
            'rec_trade_id'        => $this->respObj->rec_trade_id ?? NULL,
            'bank_transaction_id' => $this->respObj->bank_transaction_id ?? NULL,
            'auth_code'           => $this->respObj->auth_code ?? NULL,
            'bank_result_code'    => $this->respObj->bank_result_code ?? NULL,
            'bank_result_msg'     => $this->respObj->bank_result_msg ?? NULL,
        ]);
    }
}
