<?php
declare(strict_types=1);
/*
 |------------------------------------
 | TapPay 線上支付
 |------------------------------------
 |
 |
 */

namespace App\Services\Payment\TapPay;

use Illuminate\Support\Str;
use App\Services\Payment\TapPay\Contract\AbsPaymentTypeHandle;

class PaymentHandle
{
    /**
     * 線上支付處理邏輯
     * @param $type
     * @param $data
     * @return array
     */
    public function handle($type, $data)
    {
        /*
         |--------------------------------
         | make class ref: https://laravel.com/docs/8.x/container#resolving
         |--------------------------------
         |  App\Services\Payment\TapPay\Type\EventOrder
         |
         */
        $classPath = 'App\Services\Payment\TapPay\Type\\' . Str::studly($type);

        if (!class_exists($classPath)) { //class不存在
            return [];
        }

        $typeHandle = resolve($classPath); //make object

        if (!$typeHandle instanceof AbsPaymentTypeHandle){ //沒有繼承 abstract class
            return [];
        }

        return $typeHandle->handle($type, $data);
    }
}
