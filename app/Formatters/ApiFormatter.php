<?php

namespace App\Formatters;

class ApiFormatter
{
    /**
     * response json
     *
     * @return json
     */
    public function json($data = NULL, $base = NULL)
    {
        $result['now']         = now()->format('Y-m-d H:i:s');
        $result['status']      = 'success';
        $result['status_code'] = 2000;
        if ($data) {
            $result['data'] = $data;
        }

        // 覆寫基礎欄位值
        if ($base) {
            $result = array_merge($result, $base);
        }

        return response()->json($result);
    }

    /**
     * export csv file
     *
     * @return csv
     */
    public function exportCSV($filename, $data, $big5 = false)
    {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);
        ob_implicit_flush(1);

        // 處理中文檔名
        $encoded_filename = urlencode($filename);
        $encoded_filename = str_replace("+", "%20", $encoded_filename);
        header('Content-Disposition: attachment; filename="'.$encoded_filename.'"');
        // $ua = $_SERVER["HTTP_USER_AGENT"];
        // if (preg_match("/MSIE/", $ua)) {
        //     header('Content-Disposition: attachment; filename="'.$encoded_filename.'"');
        // } else if (preg_match("/Firefox/", $ua)) {
        //     header("Content-Disposition: attachment; filename*=\"utf8''".$filename.'"');
        // } else {
        //     header('Content-Disposition: attachment; filename="'.$filename.'"');
        // }

        header("Content-Transfer-Encoding: binary");
        header("Access-Control-Allow-Origin: *");
        header('Access-Control-Expose-Headers: Content-Disposition');
        header("Cache-Control: cache, must-revalidate");
        header('Cache-Control: private');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Content-Description: File Transfer');
        header("Pragma: Public");
        header("Expires: 0");

        //  default set utf-8 in csv file
        if ($big5) {
            $data = iconv('UTF-8', 'Big5//TRANSLIT//IGNORE', $data);
        } else {
            $data = "\xEF\xBB\xBF".$data;
        }

        echo $data;
        exit;
    }
}
