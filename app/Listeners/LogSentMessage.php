<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSent;
use Illuminate\Support\Facades\Log;
use App\Repositories\MailEventRepository;

class LogSentMessage
{
    /** @var MailEventRepository  */
    private  $mailEventRepository;

    /**
     * Create the event listener.
     * @param MailEventRepository $mailEventRepository
     */
    public function __construct(MailEventRepository $mailEventRepository)
    {
        $this->mailEventRepository = $mailEventRepository;
    }

    /**
     * 拿到SES-ID後update到DB
     * 詳細可以看 App\Services\Mail\MailService@saveMailNotifications 的註解
     * @param  MessageSent  $event
     * @return void
     */
    public function handle(MessageSent $event)
    {
        // 僅限用SES寄信時執行
        if (env('MAIL_DRIVER') != 'ses') {
            return;
        }

        $header = $event->message->getHeaders();
        $sesID = $header->get('x-ses-message-id')->getValue();
        $dataID = $header->get('event-id')->getValue();

        $this->mailEventRepository->updateData(['id'=>$dataID], ['ses_message_id' => $sesID]);
//        Log::debug('HEADER FROM EVENT', ['Header' => $header]);
    }
}
