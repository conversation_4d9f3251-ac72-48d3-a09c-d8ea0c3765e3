<?php

namespace App\Observers;

use App\Models\StudioAlbumImage;
use App\Models\StudioAlbumImageLocation;

class StudioAlbumImageObserver
{
    private $studioAlbumImage;
    private $studioAlbumImageLocation;

    public function __construct(
        StudioAlbumImage $studioAlbumImage,
        StudioAlbumImageLocation $studioAlbumImageLocation
    ) {
        $this->studioAlbumImage         = $studioAlbumImage;
        $this->studioAlbumImageLocation = $studioAlbumImageLocation;
    }

    /**
     * Handle the StudioAlbumImage "created" event.
     *
     * @param  \App\Models\StudioAlbumImage  $studioAlbumImage
     * @return void
     */
    public function created(StudioAlbumImage $studioAlbumImage)
    {
        // 統計拍婚紗作品照的拍攝地點
        $this->updateStudioAlbumImageLocation($studioAlbumImage->location);
    }

    /**
     * Handle the StudioAlbumImage "updated" event.
     *
     * @param  \App\Models\StudioAlbumImage  $studioAlbumImage
     * @return void
     */
    public function updated(StudioAlbumImage $studioAlbumImage)
    {
        // 統計拍婚紗作品照的拍攝地點
        $this->updateStudioAlbumImageLocation($studioAlbumImage->location);
    }

    /**
     * Handle the StudioAlbumImage "deleted" event.
     *
     * @param  \App\Models\StudioAlbumImage  $studioAlbumImage
     * @return void
     */
    public function deleted(StudioAlbumImage $studioAlbumImage)
    {
        // 統計拍婚紗作品照的拍攝地點
        $this->updateStudioAlbumImageLocation($studioAlbumImage->location);
    }

    /**
     * Handle the StudioAlbumImage "restored" event.
     *
     * @param  \App\Models\StudioAlbumImage  $studioAlbumImage
     * @return void
     */
    public function restored(StudioAlbumImage $studioAlbumImage)
    {
        // 統計拍婚紗作品照的拍攝地點
        $this->updateStudioAlbumImageLocation($studioAlbumImage->location);
    }

    /**
     * Handle the StudioAlbumImage "force deleted" event.
     *
     * @param  \App\Models\StudioAlbumImage  $studioAlbumImage
     * @return void
     */
    public function forceDeleted(StudioAlbumImage $studioAlbumImage)
    {
        // 統計拍婚紗作品照的拍攝地點
        $this->updateStudioAlbumImageLocation($studioAlbumImage->location);
    }

    /**
     * 統計拍婚紗作品照的拍攝地點
     *
     * @param string $location 拍攝地點
     * @return void
     */
    private function updateStudioAlbumImageLocation($location)
    {
        // 若沒有地點資訊，直接跳出
        if (!$location) {
            return;
        }

        $this->studioAlbumImageLocation->updateOrCreate([
            'name' => $location,
        ], [
            'use_count' => $this->studioAlbumImage->where('location', $location)->count(),
        ]);

    }
}
