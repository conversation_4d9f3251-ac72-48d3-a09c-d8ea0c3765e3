<?php

namespace App\Observers;

use App\Models\StoreService;
use App\Models\Wdv2\StoreService as ServiceWdv2;

class StoreServiceObserver
{
    private $serviceWdv2;

    public function __construct(
        ServiceWdv2 $serviceWdv2
    ) {
        $this->serviceWdv2 = $serviceWdv2;
    }

    /**
     * Handle the StoreService "created" event.
     *
     * @param  \App\Models\StoreService  $storeService
     * @return void
     */
    public function created(StoreService $storeService)
    {
        $this->serviceWdv2->updateOrCreate([
            'id'       => $storeService->id,
            'store_id' => $storeService->store_id,
        ]);
    }

    /**
     * Handle the StoreService "updated" event.
     *
     * @param  \App\Models\StoreService  $storeService
     * @return void
     */
    public function updated(StoreService $storeService)
    {
        //
    }

    /**
     * Handle the StoreService "deleted" event.
     *
     * @param  \App\Models\StoreService  $storeService
     * @return void
     */
    public function deleted(StoreService $storeService)
    {
        //
    }

    /**
     * Handle the StoreService "restored" event.
     *
     * @param  \App\Models\StoreService  $storeService
     * @return void
     */
    public function restored(StoreService $storeService)
    {
        //
    }

    /**
     * Handle the StoreService "force deleted" event.
     *
     * @param  \App\Models\StoreService  $storeService
     * @return void
     */
    public function forceDeleted(StoreService $storeService)
    {
        //
    }
}
