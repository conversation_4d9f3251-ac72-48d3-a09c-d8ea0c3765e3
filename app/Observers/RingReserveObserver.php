<?php

namespace App\Observers;

use App\Models\RingReserve;
use App\Jobs\RingEvent\AddGoogleSheets;

class RingReserveObserver
{
    /**
     * Handle the RingReserve "created" event.
     *
     * @param  \App\Models\RingReserve  $ringReserve
     * @return void
     */
    public function created(RingReserve $ringReserve)
    {
        // 若是執行婚戒大賞的帳號綁定功能，則不需要新增Google試算表的預約記錄
        if (request('ring_auth_token')) {
            return;
        }

        // 驗證Google雲端試算表ID
        if (!$ringReserve->ringBrand->spreadsheet_id) {
            return;
        }

        // 婚戒大賞-Google試算表-新增紀錄 use Job
        AddGoogleSheets::dispatch($ringReserve);
    }

    /**
     * Handle the RingReserve "updated" event.
     *
     * @param  \App\Models\RingReserve  $ringReserve
     * @return void
     */
    public function updated(RingReserve $ringReserve)
    {
        //
    }

    /**
     * Handle the RingReserve "deleted" event.
     *
     * @param  \App\Models\RingReserve  $ringReserve
     * @return void
     */
    public function deleted(RingReserve $ringReserve)
    {
        //
    }

    /**
     * Handle the RingReserve "restored" event.
     *
     * @param  \App\Models\RingReserve  $ringReserve
     * @return void
     */
    public function restored(RingReserve $ringReserve)
    {
        //
    }

    /**
     * Handle the RingReserve "force deleted" event.
     *
     * @param  \App\Models\RingReserve  $ringReserve
     * @return void
     */
    public function forceDeleted(RingReserve $ringReserve)
    {
        //
    }
}
