<?php

namespace App\Observers;

use App\Models\Store;
use App\Models\Wdv2\Store as StoreWdv2;

class StoreObserver
{
    private $storeWdv2;

    public function __construct(
        StoreWdv2 $storeWdv2
    ) {
        $this->storeWdv2 = $storeWdv2;
    }

    /**
     * Handle the Store "created" event.
     *
     * @param  \App\Models\Store  $store
     * @return void
     */
    public function created(Store $store)
    {
        // 同步新增 wdv2 商家
        $this->storeWdv2->updateOrCreate([
            'id' => $store->id,
        ], [
            'email'=> $store->email,
            'type' => array_search($store->type, $this->storeWdv2->convertTypeKeyList),
            'name' => $store->name,
            'notify_mail_set' => '{"message":1,"quote":1}',
        ]);

        // 同步新增品牌
        $brand = $store->allBrands()->create([
            'email'=> $store->email,
            'name' => $store->name,
        ]);

        // 使用中介模型設定 pivot 資料
        $brand->stores()->updateExistingPivot($store->id, [
            'show_flag'  => 2,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Handle the Store "updated" event.
     *
     * @param  \App\Models\Store  $store
     * @return void
     */
    public function updated(Store $store)
    {
        $data = [
            'name'      => $store->name,
            'type'      => array_search($store->type, $this->storeWdv2->convertTypeKeyList),
            'show_flag' => 2,
        ];

        if ($store->status == 'delete') {
            $data['show_flag'] = 0;
        } else if ($store->status == 'pending') {
            $data['show_flag'] = 1;
        } else if ($store->status == 'leave') {
            $data['level'] = 1;
        }

        // 同步更新 wdv2 的商家狀態
        $store->storeWdv2->update($data);
    }

    /**
     * Handle the Store "deleted" event.
     *
     * @param  \App\Models\Store  $store
     * @return void
     */
    public function deleted(Store $store)
    {
        //
    }

    /**
     * Handle the Store "restored" event.
     *
     * @param  \App\Models\Store  $store
     * @return void
     */
    public function restored(Store $store)
    {
        //
    }

    /**
     * Handle the Store "force deleted" event.
     *
     * @param  \App\Models\Store  $store
     * @return void
     */
    public function forceDeleted(Store $store)
    {
        //
    }
}
