<?php

namespace App\Observers;

use App\Models\EventReport;

class EventReportObserver
{
    /**
     * Handle the EventReport "saving" event.
     *
     * @param  \App\Models\EventReport  $report
     * @return void
     */
    public function saving(EventReport $report)
    {
        // 姓名/電子信箱(去左右空白)、電子信箱(去左右空白 & 轉小寫)
        $report->name  = trim($report->name);
        $report->email = trim(strtolower($report->email));
        $report->phone = trim($report->phone);
    }

    /**
     * Handle the EventReport "creating" event.
     *
     * @param  \App\Models\EventReport  $report
     * @return void
     */
    public function creating(EventReport $report)
    {
        // 姓名/電子信箱(去左右空白)、電子信箱(去左右空白 & 轉小寫)
        $report->name  = trim($report->name);
        $report->email = trim(strtolower($report->email));
        $report->phone = trim($report->phone);
    }

    /**
     * Handle the EventReport "updating" event.
     *
     * @param  \App\Models\EventReport  $report
     * @return void
     */
    public function updating(EventReport $report)
    {
        // 姓名/電子信箱(去左右空白)、電子信箱(去左右空白 & 轉小寫)
        $report->name  = trim($report->name);
        $report->email = trim(strtolower($report->email));
        $report->phone = trim($report->phone);
    }
}
