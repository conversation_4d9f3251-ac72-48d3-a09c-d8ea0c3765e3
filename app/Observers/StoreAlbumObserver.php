<?php

namespace App\Observers;

use App\Models\StoreAlbum;
use App\Models\Wdv2\StoreAlbum as AlbumWdv2;

class StoreAlbumObserver
{
    private $albumWdv2;

    public function __construct(
        AlbumWdv2 $albumWdv2
    ) {
        $this->albumWdv2 = $albumWdv2;
    }

    /**
     * Handle the StoreAlbum "created" event.
     *
     * @param  \App\Models\StoreAlbum  $storeAlbum
     * @return void
     */
    public function created(StoreAlbum $storeAlbum)
    {
        $this->albumWdv2->updateOrCreate([
            'id'       => $storeAlbum->id,
            'store_id' => $storeAlbum->store_id,
        ]);
    }

    /**
     * Handle the StoreAlbum "updated" event.
     *
     * @param  \App\Models\StoreAlbum  $storeAlbum
     * @return void
     */
    public function updated(StoreAlbum $storeAlbum)
    {
        //
    }

    /**
     * Handle the StoreAlbum "deleted" event.
     *
     * @param  \App\Models\StoreAlbum  $storeAlbum
     * @return void
     */
    public function deleted(StoreAlbum $storeAlbum)
    {
        //
    }

    /**
     * Handle the StoreAlbum "restored" event.
     *
     * @param  \App\Models\StoreAlbum  $storeAlbum
     * @return void
     */
    public function restored(StoreAlbum $storeAlbum)
    {
        //
    }

    /**
     * Handle the StoreAlbum "force deleted" event.
     *
     * @param  \App\Models\StoreAlbum  $storeAlbum
     * @return void
     */
    public function forceDeleted(StoreAlbum $storeAlbum)
    {
        //
    }
}
