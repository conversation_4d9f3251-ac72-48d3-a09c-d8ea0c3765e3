<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        \Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\TrustProxies::class,
        \App\Http\Middleware\ConvertStringBooleans::class,
        \Fruitcake\Cors\HandleCors::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        // 公開的API
        'api.public' => [
            // 取消API限制同IP訪問次數, Too Many Attempts.
            // 'throttle:60,1',
            'bindings',
        ],

        // 私用的API
        'api.private' => [
            'api.public',
            'access.token',
            'user.token',
        ],

        // 限作者的文章
        'forum.article.author' => [
            'user.token.forcibly',
            \App\Http\Middleware\Forum\ArticleAuthorMiddleware::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,

        // Base
        'debugmode' => \App\Http\Middleware\DebugModeMiddleware::class,
        'api.session' => \Illuminate\Session\Middleware\StartSession::class,
        'access.token' => \App\Http\Middleware\AccessTokenMiddleware::class,
        'user.token' => \App\Http\Middleware\UserTokenMiddleware::class,
        'user.token.forcibly' => \App\Http\Middleware\UserTokenForciblyMiddleware::class,
        'api.response.time' => \App\Http\Middleware\ApiResponseTimeMiddleware::class,

        // Admin
        'admin.token' => \App\Http\Middleware\Admin\AuthTokenMiddleware::class,
        'admin.store' => \App\Http\Middleware\Admin\StoreMiddleware::class,
        'admin.store.isEffectived' => \App\Http\Middleware\Admin\StoreIsEffectivedMiddleware::class,
        'admin.access.permission' => \App\Http\Middleware\Admin\AccessPermissionMiddleware::class,
        'admin.service' => \App\Http\Middleware\Admin\ServiceMiddleware::class,
        'admin.album' => \App\Http\Middleware\Admin\AlbumMiddleware::class,
        'admin.video' => \App\Http\Middleware\Admin\VideoMiddleware::class,
        'admin.member' => \App\Http\Middleware\Admin\MemberMiddleware::class,
        'admin.venueRoom' => \App\Http\Middleware\Admin\VenueRoomMiddleware::class,
        'admin.invoiceSetting' => \App\Http\Middleware\Admin\InvoiceSettingMiddleware::class,
        'admin.invoice' => \App\Http\Middleware\Admin\InvoiceMiddleware::class,
        'admin.token.forcibly' => \App\Http\Middleware\Admin\StoreTokenForciblyMiddleware::class,

        // Forum
        'forum.article' => \App\Http\Middleware\Forum\ArticleMiddleware::class,
        'forum.article.published' => \App\Http\Middleware\Forum\ArticlePublishedMiddleware::class,

        // Store
        'store.typeName' => \App\Http\Middleware\Store\StoreTypeNameMiddleware::class,
        'store' => \App\Http\Middleware\Store\StoreMiddleware::class,
        'store.hasPaid' => \App\Http\Middleware\Store\StoreHasPaidMiddleware::class,

        // Event
        'event' => \App\Http\Middleware\Event\EventMiddleware::class,
        'event.published' => \App\Http\Middleware\Event\EventPublishedMiddleware::class,

        // Yzcube
        'yzcube.token' => \App\Http\Middleware\Yzcube\AuthTokenMiddleware::class,
        'yzcube.brand' => \App\Http\Middleware\Yzcube\BrandMiddleware::class,
        'yzcube.user' => \App\Http\Middleware\Yzcube\UserMiddleware::class,
        'yzcube.push' => \App\Http\Middleware\Yzcube\PushMiddleware::class,

        // Blog
        'blog.token' => \App\Http\Middleware\Blog\AuthTokenMiddleware::class,

    ];
}
