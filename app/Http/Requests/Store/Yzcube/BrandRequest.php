<?php

namespace App\Http\Requests\Store\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Transformers\CityCodeTransformer;

class BrandRequest extends ApiRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $cityCodeTransformer = new CityCodeTransformer();
        $cityCode = array_keys($cityCodeTransformer->getArray());

        return [
            'location' => 'nullable|integer|in:'.implode(',', $cityCode),
        ];
    }
}
