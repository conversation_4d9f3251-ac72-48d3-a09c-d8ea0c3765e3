<?php

namespace App\Http\Requests\Store\Yzcube;

use App\Http\Requests\ApiRequest;

class StoreRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'is_prepay_setting'  => 'nullable|boolean',
            'free_setting_times' => 'nullable|max:10',
            'free_usage_times'   => 'nullable|max:10',
        ];
    }
}
