<?php

namespace App\Http\Requests\Store\Yzcube;

use App\Http\Requests\ApiRequest;

class ArticleRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'article_id' => 'required|integer|exists:blog_articles,id',
            'title'      => 'required|max:100',
            'image'      => 'required',
            'tag'        => 'nullable|string|max:10',
        ];
    }
}
