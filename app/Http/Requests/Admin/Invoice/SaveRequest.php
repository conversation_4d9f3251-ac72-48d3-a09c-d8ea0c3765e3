<?php

namespace App\Http\Requests\Admin\Invoice;

use App\Http\Requests\ApiRequest;
use Illuminate\Support\Facades\Validator;
use App\Models\Invoice;

class SaveRequest extends ApiRequest
{
    private $invoice;

    public function __construct(
        Invoice $invoice
    ) {
        $this->invoice = $invoice;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $typeList        = array_keys($this->invoice->typeList);
        $carrierTypeList = array_keys($this->invoice->carrierTypeList);

        return [
            'type'             => 'required|in:'.implode(',', $typeList),
            'order_no'         => 'required|max:20',
            'buyer_name'       => 'required|max:60',
            'buyer_ubn'        => 'nullable|max:8',
            'buyer_email'      => 'nullable|required_if:type,email|email|max:100',
            'carrier_type'     => 'nullable|required_if:type,carrier|in:'.implode(',', $carrierTypeList),
            'carrier_number'   => 'nullable|required_if:type,carrier|max:20',
            'items'            => 'required|json',
            'note'             => 'nullable',
            'sales'            => 'required|integer',
            'tax'              => 'required|integer',
            'total'            => 'required|integer',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            // 驗證發票品項
            if (!$this->validateItems()) {
                $validator->errors()->add('items', 'The items format is invalid.');
            }
        });
    }

    /**
     * 驗證發票品項
     *
     * @return bool 資料正確true, 錯誤false
     */
    public function validateItems()
    {
        $data  = json_decode(request('items'), true);
        $rules = [
            '*.name'     => 'required|max:100',
            '*.price'    => 'required|integer',
            '*.quantity' => 'required|integer',
            '*.amount'   => 'required|integer',
        ];

        $validator = Validator::make($data, $rules);

        return $validator->passes();
    }
}
