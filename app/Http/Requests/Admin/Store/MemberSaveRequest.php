<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreMember;
use App\Models\Image;

class MemberSaveRequest extends ApiRequest
{
    private $storeMember;
    private $image;

    public function __construct(
        StoreMember $storeMember,
        Image $image
    ) {
        $this->storeMember = $storeMember;
        $this->image       = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'member_id'   => 'nullable|integer|exists:'.$this->storeMember->getTable().',id',
            'name'        => 'required|max:100',
            'title'       => 'required|max:100',
            'description' => 'required',
            'cover'       => 'required|exists:'.$this->image->getTable().',file_name',
        ];
    }
}
