<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreAlbum;
use App\Models\StudioAlbum;
use App\Models\PhotographerAlbum;
use App\Models\DecorationAlbum;
use App\Models\Brand;
use App\Models\Image;
use Illuminate\Support\Facades\Validator;

class AlbumChangeStatusRequest extends ApiRequest
{
    private $storeAlbum;
    private $studio;
    private $photographer;
    private $decoration;
    private $brand;
    private $image;

    public function __construct(
        StoreAlbum $storeAlbum,
        StudioAlbum $studio,
        PhotographerAlbum $photographer,
        DecorationAlbum $decoration,
        Brand $brand,
        Image $image
    ) {
        $this->storeAlbum   = $storeAlbum;
        $this->studio       = $studio;
        $this->photographer = $photographer;
        $this->decoration   = $decoration;
        $this->brand        = $brand;
        $this->image        = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList = array_keys($this->storeAlbum->statusList);

        return [
            'status' => 'required|in:'.implode(',', $statusList),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            $storeAlbum = request()->route()->storeAlbum;

            // 驗證資料
            $data = $storeAlbum->toArray();
            $data['images'] = $storeAlbum->images->toArray();

            // 商家類別的作品資訊
            if ($storeAlbum->storeTypeAlbum()) {
                $data += $storeAlbum->storeTypeAlbum->toArray();
            }

            // 預設的驗證規則
            $rules = [
                'name'     => 'required|max:100',
                'images'   => 'required|array',
                'cover_id' => 'required|exists:'.$this->image->getTable().',id',
            ];

            // 相本狀態 show:顯示
            if (request('status') == 'show') {

                // 商家類別
                $store = request()->route()->store;
                switch ($store->typeKeyList[$store->type]) {

                    // 拍婚紗
                    case 'studio':
                        $typeList = array_keys($this->studio->typeList);
                        $rules['type'] = 'required|integer|in:'.implode(',', $typeList); // 作品類型
                        break;

                    // 婚紗禮服
                    case 'dress':
                        $rules['is_private'] = 'required|boolean'; // 價格不公開
                        // 價格公開
                        if (!$data['is_private']) {
                            $rules['price'] = 'required|integer';
                        }
                        break;

                    // 婚攝婚錄
                    case 'photographer':
                        $locationTypeList = array_keys($this->photographer->locationTypeList);
                        $rules['location_type'] = 'nullable|integer|required_without:brand_id|in:'.implode(',', $locationTypeList); // 地點類型
                        $rules['brand_id']      = 'nullable|integer|required_without:location_type|exists:'.$this->brand->getTable().',id'; // 宴客地點
                        break;

                    // 婚禮佈置
                    case 'decoration':
                        $locationTypeList = array_keys($this->decoration->locationTypeList);
                        $rules['is_private']    = 'required|boolean'; // 價格不公開
                        $rules['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                        $rules['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 佈置地點
                        // 價格公開
                        if (!$data['is_private']) {
                            $rules['min_price'] = 'required|integer';
                            $rules['max_price'] = 'required|integer';
                        }
                        break;
                }
            }

            // 驗證
            $_validator = Validator::make($data, $rules);
            if ($_validator->fails()) {
                $validator->errors()->add('status', $_validator->errors());
            }
        });
    }
}
