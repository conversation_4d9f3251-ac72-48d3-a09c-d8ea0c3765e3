<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\VenueRoom;
use App\Models\Image;

class VenueRoomSaveRequest extends ApiRequest
{
    private $venueRoom;
    private $image;

    public function __construct(
        VenueRoom $venueRoom,
        Image $image
    ) {
        $this->venueRoom = $venueRoom;
        $this->image     = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList    = array_keys($this->venueRoom->statusList);
        $tablesList    = array_keys($this->venueRoom->tablesList);
        $backplaneList = array_keys($this->venueRoom->backplaneList);

        $result = [
            'room_id'            => 'nullable|integer|exists:'.$this->venueRoom->getTable().',id',
            'status'             => 'required|in:'.implode(',', $statusList),
            'name'               => 'required|max:100',
            'narrates'           => 'nullable|array',
            'is_underground'     => 'nullable|required_if:status,show|boolean',
            'floor'              => 'nullable|required_if:status,show|integer',
            'min_number'         => 'nullable|required_if:status,show|integer',
            'max_number'         => 'nullable|required_if:status,show|integer',
            'has_banquet'        => 'nullable|required_if:status,show|boolean',
            'has_witness'        => 'nullable|required_if:status,show|boolean',
            'has_ceremony'       => 'nullable|required_if:status,show|boolean',
            'tables'             => 'nullable|in:'.implode(',', $tablesList),
            'guest_table_number' => 'nullable|integer',
            'main_table_number'  => 'nullable|integer',
            'has_bridal_room'    => 'nullable|boolean',
            'combine_rooms'      => 'nullable|array',
            'combine_min_number' => 'nullable|integer',
            'combine_max_number' => 'nullable|integer',
            'has_wifi'           => 'nullable|required_if:status,show|boolean',
            'has_projection'     => 'nullable|required_if:status,show|boolean',
            'has_led'            => 'nullable|required_if:status,show|boolean',
            'has_sound'          => 'nullable|required_if:status,show|boolean',
            'has_light'          => 'nullable|required_if:status,show|boolean',
            'has_stage'          => 'nullable|required_if:status,show|boolean',
            'has_pillar'         => 'nullable|required_if:status,show|boolean',
            'backplane'          => 'nullable|required_if:status,show|integer|in:'.implode(',', $backplaneList),
            'description'        => 'nullable|required_if:status,show',
            'images'             => 'nullable|required_if:status,show|array',
            'cover'              => 'nullable|required_if:status,show|exists:'.$this->image->getTable().',file_name',
        ];

        // 廳房狀態 show:顯示
        if (request('status') == 'show') {

            // 供宴客使用
            if (request('has_banquet')) {
                $result['tables']          = 'required|in:'.implode(',', $tablesList);
                $result['has_bridal_room'] = 'required|boolean';

                    // 桌型 round:中式圓桌
                    if (request('tables') == 'round') {
                        $result['guest_table_number'] = 'required|integer';
                        $result['main_table_number']  = 'required|integer';
                    }
            }
        }

        return $result;
    }
}
