<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreVideo;
use App\Models\PhotographerVideo;
use App\Models\DecorationVideo;
use App\Models\Brand;
use Illuminate\Support\Facades\Validator;

class VideoChangeStatusRequest extends ApiRequest
{
    private $storeVideo;
    private $photographer;
    private $decoration;
    private $brand;

    public function __construct(
        StoreVideo $storeVideo,
        PhotographerVideo $photographer,
        DecorationVideo $decoration,
        Brand $brand
    ) {
        $this->storeVideo   = $storeVideo;
        $this->photographer = $photographer;
        $this->decoration   = $decoration;
        $this->brand        = $brand;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList = array_keys($this->storeVideo->statusList);

        return [
            'status' => 'required|in:'.implode(',', $statusList),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            // 驗證資料
            $storeVideo = request()->route()->storeVideo;
            $data = $storeVideo->toArray();

            // 商家類別的作品資訊
            if ($storeVideo->storeTypeVideo()) {
                $data += $storeVideo->storeTypeVideo->toArray();
            }

            // 預設的驗證規則
            $rules = [
                'name' => 'required|max:100',
            ];

            // 相本狀態 show:顯示
            if (request('status') == 'show') {

                // 商家類別
                $store = request()->route()->store;
                switch ($store->typeKeyList[$store->type]) {

                    // 婚攝婚錄
                    case 'photographer':
                        $locationTypeList = array_keys($this->photographer->locationTypeList);
                        $rules['location_type'] = 'nullable|integer|required_without:brand_id|in:'.implode(',', $locationTypeList); // 地點類型
                        $rules['brand_id']      = 'nullable|integer|required_without:location_type|exists:'.$this->brand->getTable().',id'; // 宴客地點
                        break;

                    // 婚禮佈置
                    case 'decoration':
                        $locationTypeList = array_keys($this->decoration->locationTypeList);
                        $rules['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                        $rules['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 佈置地點
                        break;
                }
            }

            // 驗證
            $_validator = Validator::make($data, $rules);
            if ($_validator->fails()) {
                $validator->errors()->add('status', $_validator->errors());
            }
        });
    }
}
