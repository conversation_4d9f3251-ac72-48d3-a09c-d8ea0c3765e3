<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\Image;


class UpdateTastingRequest extends ApiRequest
{
    private $image;

    public function __construct(
        Image $image
    ) {
        $this->image = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $store = request()->route()->store;

        // 只驗證喜餅商家
        if ($store->type != 10) {
            return [];
        }

        return [
            'has_shop_tasting'                  => 'required|boolean',
            'shop_tasting_min_fee'              => 'nullable|required_if:has_shop_tasting,1|integer',
            'shop_tasting_max_fee'              => 'nullable|required_if:has_shop_tasting,1|integer',
            'shop_tasting_time'                 => 'nullable|required_if:has_shop_tasting,1',
            'shop_tasting_content'              => 'nullable|required_if:has_shop_tasting,1',
            'shop_tasting_method'               => 'nullable|required_if:has_shop_tasting,1',
            'shop_tasting_image'                => 'nullable|required_if:has_shop_tasting,1|exists:'.$this->image->getTable().',file_name',
            'has_delivery_tasting'              => 'required|boolean',
            'delivery_tasting_min_fee'          => 'nullable|required_if:has_delivery_tasting,1|integer',
            'delivery_tasting_max_fee'          => 'nullable|required_if:has_delivery_tasting,1|integer',
            'delivery_tasting_min_delivery_fee' => 'nullable|required_if:has_delivery_tasting,1|integer',
            'delivery_tasting_max_delivery_fee' => 'nullable|required_if:has_delivery_tasting,1|integer',
            'delivery_tasting_content'          => 'nullable|required_if:has_delivery_tasting,1',
            'delivery_tasting_method'           => 'nullable|required_if:has_delivery_tasting,1',
            'delivery_tasting_image'            => 'nullable|required_if:has_delivery_tasting,1|exists:'.$this->image->getTable().',file_name',
        ];
    }
}
