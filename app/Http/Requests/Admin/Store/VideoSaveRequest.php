<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreVideo;
use App\Models\PhotographerVideo;
use App\Models\DecorationVideo;
use App\Models\Brand;

class VideoSaveRequest extends ApiRequest
{
    private $storeVideo;
    private $photographer;
    private $decoration;
    private $brand;

    public function __construct(
        StoreVideo $storeVideo,
        PhotographerVideo $photographer,
        DecorationVideo $decoration,
        Brand $brand
    ) {
        $this->storeVideo   = $storeVideo;
        $this->photographer = $photographer;
        $this->decoration   = $decoration;
        $this->brand        = $brand;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList = array_keys($this->storeVideo->statusList);

        $result = [
            'video_id'    => 'nullable|integer|exists:'.$this->storeVideo->getTable().',id',
            'status'      => 'required|in:'.implode(',', $statusList),
            'name'        => 'required|max:100',
            'url'         => 'required|url',
            'oembed_data' => 'required|json',
            'description' => 'nullable',
        ];

        // 商家類別
        $store = request()->route()->store;
        switch ($store->typeKeyList[$store->type]) {

            // 婚攝婚錄
            case 'photographer':
                $locationTypeList = array_keys($this->photographer->locationTypeList);
                $result['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                $result['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 宴客地點
                $result['members']       = 'nullable|array'; // 參與成員
                // 顯示作品
                if (request('status') == 'show') {
                    $result['location_type'] = 'nullable|integer|required_without:brand_id|in:'.implode(',', $locationTypeList); // 地點類型
                    $result['brand_id']      = 'nullable|integer|required_without:location_type|exists:'.$this->brand->getTable().',id'; // 宴客地點
                }
                break;

            // 婚禮佈置
            case 'decoration':
                $locationTypeList = array_keys($this->decoration->locationTypeList);
                $result['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                $result['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 佈置地點
                // 顯示作品
                if (request('status') == 'show') {
                    $result['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                    $result['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 佈置地點
                }
                break;

            // 拍婚紗 & 新娘秘書 & 婚禮主持人
            case 'studio':
            case 'makeup':
            case 'host':
                $result['members'] = 'nullable|array'; // 參與成員
                break;
        }

        return $result;
    }
}
