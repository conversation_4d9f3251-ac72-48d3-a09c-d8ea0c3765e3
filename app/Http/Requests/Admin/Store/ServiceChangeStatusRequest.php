<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreService;
use App\Models\VenueService;
use App\Models\WeddingcakeService;
use App\Models\Image;
use Illuminate\Support\Facades\Validator;

class ServiceChangeStatusRequest extends ApiRequest
{
    private $storeService;
    private $venueService;
    private $weddingcakeService;
    private $image;

    public function __construct(
        StoreService $storeService,
        VenueService $venueService,
        WeddingcakeService $weddingcakeService,
        Image $image
    ) {
        $this->storeService       = $storeService;
        $this->venueService       = $venueService;
        $this->weddingcakeService = $weddingcakeService;
        $this->image              = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList = array_keys($this->storeService->statusList);

        return [
            'status' => 'required|in:'.implode(',', $statusList),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            $store        = request()->route()->store;
            $storeService = request()->route()->storeService;

            // 驗證資料
            $data = $storeService->toArray();
            $data['status'] = request('status'); // 因為 required_if:status,show 會用到
            $data['cover']  = $storeService->cover->file_name ?? NULL;
            $data['tags']   = $storeService->tags;
            $data['images'] = $storeService->images;

            // 預設的驗證規則
            $rules = [
                'name'        => 'required|max:100',
                'cover'       => 'nullable|required_if:status,show|exists:'.$this->image->getTable().',file_name',
                'description' => 'nullable|required_if:status,show',
            ];

            // 喜餅
            if ($store->type == 10) {
                $data['limit_type']     = $storeService->weddingcakeInfo->limit_type;
                $data['limit_value']    = $storeService->weddingcakeInfo->limit_value;
                $data['condition_type'] = $storeService->weddingcakeInfo->condition_type;
                $data['discount_type']  = $storeService->weddingcakeInfo->discount_type;

                $limitTypeList     = array_keys($this->weddingcakeService->limitTypeList);
                $conditionTypeList = array_keys($this->weddingcakeService->conditionTypeList);
                $discountTypeList  = array_keys($this->weddingcakeService->discountTypeList);
                $rules['limit_type']     = 'nullable|required_if:status,show|in:'.implode(',', $limitTypeList);
                $rules['limit_value']    = 'nullable|integer';
                $rules['condition_type'] = 'nullable|required_if:status,show|in:'.implode(',', $conditionTypeList);
                $rules['discount_type']  = 'nullable|in:'.implode(',', $discountTypeList);
                $rules['discounts']      = 'nullable|json';
            }

            // 方案狀態 show:顯示 (除了喜餅)
            if ($store->type != 10 && request('status') == 'show') {
                $rules['is_private'] = 'required|boolean';
                $rules['images']     = 'required|json';
                $rules['tags']       = 'required|json';

                // 價格不公開 is_private
                if (!$data['is_private']) {
                    $rules['min_price'] = 'required|integer';
                    $rules['max_price'] = 'required|integer';
                }

                // 方案類型 type
                if (isset($this->storeService->demandStructure[$store->type]['type'])) {
                    $typeList = array_keys($this->storeService->demandStructure[$store->type]['type']['list']);
                    $rules['type'] = 'required|in:'.implode(',', $typeList);
                }

                // 婚宴場地
                if ($store->type == 5) {
                    $data['has_tip']            = $storeService->venueInfo->has_tip;
                    $data['priced_by']          = $storeService->venueInfo->priced_by;
                    $data['seater']             = $storeService->venueInfo->seater;
                    $data['has_child_discount'] = $storeService->venueInfo->has_child_discount;

                    $pricedByList = array_keys($this->venueService->pricedByList);
                    $rules['has_tip']     = 'required|boolean';
                    $rules['priced_by']   = 'required|in:'.implode(',', $pricedByList);
                    // 計費方式 table:按桌數計費
                    if ($data['priced_by'] == 'table') {
                        $seaterList = array_keys($this->venueService->seaterList);
                        $rules['seater'] = 'required|integer|in:'.implode(',', $seaterList);
                    }
                    // 計費方式 person:按人數計費
                    if ($data['priced_by'] == 'person') {
                        $rules['has_child_discount'] = 'required|boolean';
                    }
                }
            }

            // 驗證
            $_validator = Validator::make($data, $rules);
            if ($_validator->fails()) {
                $validator->errors()->add('status', $_validator->errors());
            }
        });
    }
}
