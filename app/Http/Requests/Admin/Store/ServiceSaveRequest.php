<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreService;
use App\Models\VenueService;
use App\Models\WeddingcakeService;
use App\Models\Image;

class ServiceSaveRequest extends ApiRequest
{
    private $storeService;
    private $venueService;
    private $weddingcakeService;
    private $image;

    public function __construct(
        StoreService $storeService,
        VenueService $venueService,
        WeddingcakeService $weddingcakeService,
        Image $image
    ) {
        $this->storeService       = $storeService;
        $this->venueService       = $venueService;
        $this->weddingcakeService = $weddingcakeService;
        $this->image              = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $store      = request()->route()->store;
        $statusList = array_keys($this->storeService->statusList);

        $result = [
            'service_id'  => 'nullable|integer|exists:'.$this->storeService->getTable().',id',
            'status'      => 'required|in:'.implode(',', $statusList),
            'name'        => 'required|max:100',
            'cover'       => 'nullable|required_if:status,show|exists:'.$this->image->getTable().',file_name',
            'description' => 'nullable|required_if:status,show',
        ];

        // 喜餅
        if ($store->type == 10) {
            $limitTypeList     = array_keys($this->weddingcakeService->limitTypeList);
            $conditionTypeList = array_keys($this->weddingcakeService->conditionTypeList);
            $discountTypeList  = array_keys($this->weddingcakeService->discountTypeList);
            $result['limit_type']     = 'nullable|required_if:status,show|in:'.implode(',', $limitTypeList);
            $result['limit_value']    = 'nullable|integer';
            $result['condition_type'] = 'nullable|required_if:status,show|in:'.implode(',', $conditionTypeList);
            $result['discount_type']  = 'nullable|in:'.implode(',', $discountTypeList);
            $result['discounts']      = 'nullable|json';

            return $result;
        }

        // 除了喜餅的基本欄位
        $result['min_price']  = 'nullable|integer';
        $result['max_price']  = 'nullable|integer';
        $result['is_private'] = 'nullable|required_if:status,show|boolean';
        $result['images']     = 'nullable|required_if:status,show|json';
        $result['tags']       = 'nullable|required_if:status,show|json';

        // 方案狀態顯示 & 價格公開
        if (request('status') == 'show' && !request('is_private')) {
            $result['min_price'] = 'required|integer';
            $result['max_price'] = 'required|integer';
        }

        // 方案類型 type
        if (isset($this->storeService->demandStructure[$store->type]['type'])) {
            $typeList = array_keys($this->storeService->demandStructure[$store->type]['type']['list']);
            $result['type'] = 'nullable|required_if:status,show|in:'.implode(',', $typeList);
        }

        // 婚宴場地
        if ($store->type == 5) {
            $pricedByList = array_keys($this->venueService->pricedByList);
            $seaterList   = array_keys($this->venueService->seaterList);
            $result['has_tip']            = 'nullable|required_if:status,show|boolean';
            $result['priced_by']          = 'nullable|required_if:status,show|in:'.implode(',', $pricedByList);
            $result['seater']             = 'nullable|integer|in:'.implode(',', $seaterList);
            $result['has_child_discount'] = 'nullable|boolean';
            // 計費方式 table:按桌數計費
            if (request('priced_by') == 'table') {
                $result['seater'] = 'required|integer|in:'.implode(',', $seaterList);
            }
            // 計費方式 person:按人數計費
            if (request('priced_by') == 'person') {
                $result['has_child_discount'] = 'required|boolean';
            }
        }

        return $result;
    }
}
