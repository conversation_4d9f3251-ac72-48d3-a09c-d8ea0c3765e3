<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\VenueRoom;
use App\Models\Image;
use Illuminate\Support\Facades\Validator;

class VenueRoomChangeStatusRequest extends ApiRequest
{
    private $venueRoom;
    private $image;

    public function __construct(
        VenueRoom $venueRoom,
        Image $image
    ) {
        $this->venueRoom = $venueRoom;
        $this->image     = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList = array_keys($this->venueRoom->statusList);

        return [
            'status' => 'required|in:'.implode(',', $statusList),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            // 驗證資料
            $venueRoom = request()->route()->venueRoom;
            $data = $venueRoom->toArray();
            $data['images'] = $venueRoom->images;

            // 預設的驗證規則
            $rules = [
                'name'     => 'required|max:100',
                'narrates' => 'nullable|array',
            ];

            // 廳房狀態 show:顯示
            if (request('status') == 'show') {
                $backplaneList = array_keys($this->venueRoom->backplaneList);
                $rules['is_underground'] = 'required|boolean';
                $rules['floor']          = 'required|integer';
                $rules['min_number']     = 'required|integer';
                $rules['max_number']     = 'required|integer';
                $rules['has_banquet']    = 'required|boolean';
                $rules['has_witness']    = 'required|boolean';
                $rules['has_ceremony']   = 'required|boolean';
                $rules['has_wifi']       = 'required|boolean';
                $rules['has_projection'] = 'required|boolean';
                $rules['has_led']        = 'required|boolean';
                $rules['has_sound']      = 'required|boolean';
                $rules['has_light']      = 'required|boolean';
                $rules['has_stage']      = 'required|boolean';
                $rules['has_pillar']     = 'required|boolean';
                $rules['backplane']      = 'required|integer|in:'.implode(',', $backplaneList);
                $rules['description']    = 'required';
                $rules['images']         = 'required|json';
                $rules['cover_id']       = 'required|exists:'.$this->image->getTable().',id';

                // 供宴客使用
                if ($data['has_banquet']) {
                    $tablesList = array_keys($this->venueRoom->tablesList);
                    $rules['tables']          = 'required|in:'.implode(',', $tablesList);
                    $rules['has_bridal_room'] = 'required|boolean';

                    // 桌型 round:中式圓桌
                    if ($data['tables'] == 'round') {
                        $rules['guest_table_number'] = 'required|integer';
                        $rules['main_table_number']  = 'required|integer';
                    }
                }
            }

            // 驗證
            $_validator = Validator::make($data, $rules);
            if ($_validator->fails()) {
                $validator->errors()->add('status', $_validator->errors());
            }
        });
    }
}
