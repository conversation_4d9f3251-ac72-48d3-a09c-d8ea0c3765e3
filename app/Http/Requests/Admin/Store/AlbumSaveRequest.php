<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\StoreAlbum;
use App\Models\StudioAlbum;
use App\Models\PhotographerAlbum;
use App\Models\DecorationAlbum;
use App\Models\WeddingcakeAlbum;
use App\Models\Brand;
use App\Models\Image;

class AlbumSaveRequest extends ApiRequest
{
    private $storeAlbum;
    private $studio;
    private $photographer;
    private $decoration;
    private $weddingcake;
    private $brand;
    private $image;

    public function __construct(
        StoreAlbum $storeAlbum,
        StudioAlbum $studio,
        PhotographerAlbum $photographer,
        DecorationAlbum $decoration,
        WeddingcakeAlbum $weddingcake,
        Brand $brand,
        Image $image
    ) {
        $this->storeAlbum   = $storeAlbum;
        $this->studio       = $studio;
        $this->photographer = $photographer;
        $this->decoration   = $decoration;
        $this->weddingcake  = $weddingcake;
        $this->brand        = $brand;
        $this->image        = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $statusList = array_keys($this->storeAlbum->statusList);

        $result = [
            'album_id'    => 'nullable|integer|exists:'.$this->storeAlbum->getTable().',id',
            'status'      => 'required|in:'.implode(',', $statusList),
            'name'        => 'required_without:order_dress_type|max:100', // 如果有禮服類型，則不用驗證名稱有值
            'description' => 'nullable',
            'images'      => 'required|json',
            'cover_id'    => 'required|exists:'.$this->image->getTable().',id',
        ];

        // 商家類別
        $store = request()->route()->store;
        switch ($store->typeKeyList[$store->type]) {

            // 拍婚紗
            case 'studio':
                $typeList = array_keys($this->studio->typeList);
                $result['type']    = 'nullable|integer|required_if:status,show|in:'.implode(',', $typeList); // 作品類型
                $result['members'] = 'nullable|array'; // 參與成員
                break;

            // 婚紗禮服
            case 'dress':
                $orderDressTypeList = array_keys($this->storeAlbum->orderDressTypeList);
                $result['price']            = 'nullable|integer'; // 租金
                $result['is_private']       = 'nullable|required_if:status,show|boolean'; // 價格不公開
                $result['tags']             = 'nullable|array'; // 禮服標籤
                $result['order_dress_type'] = 'nullable|in:'.implode(',', $orderDressTypeList);
                $result['is_watermark']     = 'nullable|boolean';
                // 顯示作品 & 價格公開
                if (request('status') == 'show' && !request('is_private')) {
                    $result['price'] = 'required|integer';
                }
                break;

            // 婚攝婚錄
            case 'photographer':
                $locationTypeList = array_keys($this->photographer->locationTypeList);
                $result['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                $result['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 宴客地點
                $result['members']       = 'nullable|array'; // 參與成員
                // 顯示作品
                if (request('status') == 'show') {
                    $result['location_type'] = 'nullable|integer|required_without:brand_id|in:'.implode(',', $locationTypeList); // 地點類型
                    $result['brand_id']      = 'nullable|integer|required_without:location_type|exists:'.$this->brand->getTable().',id'; // 宴客地點
                }
                break;

            // 婚禮佈置
            case 'decoration':
                $locationTypeList = array_keys($this->decoration->locationTypeList);
                $typeList         = array_keys($this->decoration->typeList);
                $result['narrates']      = 'nullable|array'; // 特色標語
                $result['min_price']     = 'nullable|integer'; // 最低金額
                $result['max_price']     = 'nullable|integer'; // 最高金額
                $result['is_private']    = 'nullable|required_if:status,show|boolean'; // 價格不公開
                $result['location_type'] = 'nullable|integer|in:'.implode(',', $locationTypeList); // 地點類型
                $result['brand_id']      = 'nullable|integer|exists:'.$this->brand->getTable().',id'; // 佈置地點
                $result['type']          = 'nullable|integer|in:'.implode(',', $typeList); // 佈置類型
                $result['tags']          = 'nullable|array'; // 作品集標籤
                // 顯示作品 & 價格公開
                if (request('status') == 'show' && !request('is_private')) {
                    $result['min_price'] = 'required|integer';
                    $result['max_price'] = 'required|integer';
                }
                break;

            // 喜餅
            case 'weddingcake':
                $result['can_customized']          = 'nullable|required_if:status,show|boolean'; // 是否可以客製化
                $result['min_price']               = 'nullable|required_if:status,show|integer'; // 最低金額
                $result['max_price']               = 'nullable|required_if:status,show|integer'; // 最高金額
                $result['min_category']            = 'nullable|required_if:status,show|integer'; // 最少種類數
                $result['max_category']            = 'nullable|required_if:status,show|integer'; // 最多種類數
                $result['min_quantity']            = 'nullable|required_if:status,show|integer'; // 最少數量
                $result['max_quantity']            = 'nullable|required_if:status,show|integer'; // 最多數量
                $result['box_length']              = 'nullable|required_if:status,show|numeric'; // 禮盒長度
                $result['box_width']               = 'nullable|required_if:status,show|numeric'; // 禮盒寬度
                $result['box_high']                = 'nullable|required_if:status,show|numeric'; // 禮盒高度
                $result['is_meat']                 = 'nullable|required_if:status,show|boolean'; // 葷
                $result['is_lacto_vegetarian']     = 'nullable|required_if:status,show|boolean'; // 奶素
                $result['is_ovo_lacto_vegetarian'] = 'nullable|required_if:status,show|boolean'; // 蛋奶素
                $result['is_vegetarian']           = 'nullable|required_if:status,show|boolean'; // 全素
                $result['preservation_method']     = 'nullable|required_if:status,show'; // 保存方式
                $result['tags']                    = 'nullable|required_if:status,show|array'; // 禮盒標籤
                $result['services']                = 'nullable|array'; // 適用方案
                break;

            // 新娘秘書 & 婚禮主持人
            case 'makeup':
            case 'host':
                $result['members'] = 'nullable|array'; // 參與成員
                break;
        }

        return $result;
    }
}
