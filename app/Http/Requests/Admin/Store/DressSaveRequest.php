<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\DressContract;

class DressSaveRequest extends ApiRequest
{
    private $dressContract;

    public function __construct(
        DressContract $dressContract
    ) {
        $this->dressContract       = $dressContract;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $store      = request()->route()->store;

        $result = [
            'contract_id'        => 'nullable|integer|exists:'.$this->dressContract->getTable().',id',
            'bride_name'         => 'required_without:bridegroom_name|max:100',
            'bride_phone'        => 'required_without:bridegroom_phone|max:20',
            'bridegroom_name'    => 'required_without:bride_name|max:100',
            'bridegroom_phone'   => 'required_without:bride_phone|max:20',
        ];

        return $result;
    }
}
