<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use App\Models\Image;

class UpdateRequest extends ApiRequest
{
    private $image;

    public function __construct(
        Image $image
    ) {
        $this->image = $image;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $store = request()->route()->store;

        // 下架商家只有簡單幾個欄位
        if ($store->present()->is_leave) {
            return [
                'logo'    => 'required|exists:'.$this->image->getTable().',file_name',
                'name'    => 'required|max:100',
                'city_id' => ($store->type == 10) ? 'nullable|integer' : 'required|integer',
                'area_id' => ($store->type == 10 OR request('city_id') == 29) ? 'nullable|integer' : 'required|integer', // 海外
                'address' => (in_array($store->type, [2, 5])) ? 'required' : 'nullable',
            ];
        }

        return [
            'logo'            => 'required|exists:'.$this->image->getTable().',file_name',
            'cover'           => 'required|exists:'.$this->image->getTable().',file_name',
            'name'            => 'required|max:100',
            'narrates'        => 'nullable|array',
            'about'           => 'required',
            'extra_type'      => isset($store->extraServiceList[$store->type]) ? 'required|integer' : 'nullable',
            'tags'            => 'required|json',
            'city_id'         => ($store->type == 10 && request('has_multiple_shops')) ? 'nullable|integer' : 'required|integer', // 喜餅有多間門市
            'area_id'         => (($store->type == 10 && request('has_multiple_shops')) OR request('city_id') == 29) ? 'nullable|integer' : 'required|integer', // 喜餅有多間門市 & 海外
            'address'         => (($store->type == 10 && !request('has_multiple_shops')) OR in_array($store->type, [2, 5])) ? 'required' : 'nullable', // 喜餅單一門市 & 拍婚紗 & 婚宴場地
            'address_info'    => 'nullable',
            'map_title'       => 'nullable|max:100',
            'open_time'       => (in_array($store->type, [1, 2, 5])) ? 'nullable|required_if:round_clock,0|date_format:H:i' : 'nullable',
            'close_time'      => (in_array($store->type, [1, 2, 5])) ? 'nullable|required_if:round_clock,0|date_format:H:i' : 'nullable',
            'round_clock'     => ($store->type == 10) ? 'nullable' : 'required|boolean', // 喜餅
            'public_holidays' => 'nullable',
            'tel'             => 'nullable|max:20',
            'tel_info'        => 'nullable',
            'phone'           => 'nullable|max:20',
            'phone_info'      => 'nullable',
            'contact_email'   => 'nullable|email|max:100',
            'line'            => 'nullable|max:100',
            'website'         => 'nullable',
            'fb_page'         => 'nullable',
            'instagram'       => 'nullable',
            'features'        => 'nullable|json',
            'discount'        => 'nullable',
            'traffic'         => 'nullable',
            'device'          => 'nullable',
            'experience'      => 'nullable',
            'other'           => 'nullable',

            // 拍婚紗
            'has_files' => ($store->type == 1) ? 'required|integer' : 'nullable',

            // 婚紗禮服 (新娘秘書)
            'look_time'      => ($store->type == 2) ? 'required|integer' : 'nullable',
            'reserve_time'   => ($store->type == 2) ? 'required|integer' : 'nullable',
            'has_trial_fee'  => (in_array($store->type, [2, 4])) ? 'required|integer' : 'nullable',
            'min_trial_fee'  => (in_array($store->type, [2, 4])) ? 'nullable|required_if:has_trial_fee,1|integer' : 'nullable',
            'max_trial_fee'  => (in_array($store->type, [2, 4])) ? 'nullable|required_if:has_trial_fee,1|integer' : 'nullable',
            'trial_fee_info' => 'nullable',
            'pieces'         => ($store->type == 2) ? 'required|integer' : 'nullable',
            'rooms'          => ($store->type == 2 && request('reserve_time')) ? 'required|integer' : 'nullable',
            'frequency'      => ($store->type == 2 && request('reserve_time')) ? 'required|integer' : 'nullable',
            'use_time'       => ($store->type == 2 && request('reserve_time')) ? 'required|numeric' : 'nullable',
            'people'         => ($store->type == 2) ? 'required|integer' : 'nullable',
            'is_photograph'  => ($store->type == 2) ? 'required|boolean' : 'nullable',
            'is_pet'         => ($store->type == 2) ? 'required|boolean' : 'nullable',
            'is_eat'         => ($store->type == 2) ? 'required|boolean' : 'nullable',

            // 婚宴場地
            'room_count'   => ($store->type == 5) ? 'required|integer' : 'nullable',
            'min_number'   => ($store->type == 5) ? 'required|integer' : 'nullable',
            'max_number'   => ($store->type == 5) ? 'required|integer' : 'nullable',
            'dish_tasting' => 'nullable',

            // 喜餅
            'opening_hours'          => 'nullable|json',
            'countdown_wedding_days' => ($store->type == 10) ? 'required|integer' : 'nullable',
            'additional_orders'      => ($store->type == 10) ? 'required' : 'nullable',
            'free_delivery_method'   => ($store->type == 10) ? 'required' : 'nullable',
            'logistics_info'         => ($store->type == 10) ? 'required' : 'nullable',
            'logistics_date_info'    => ($store->type == 10) ? 'required' : 'nullable',
            'logistics_main_island'  => 'nullable',
            'logistics_outer_island' => 'nullable',
            'logistics_overseas'     => 'nullable',
            'has_multiple_shops'     => ($store->type == 10) ? 'required|boolean' : 'nullable',
            'shops'                  => 'nullable|json',
        ];
    }
}
