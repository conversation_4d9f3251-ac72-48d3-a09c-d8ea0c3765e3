<?php

namespace App\Http\Requests\Admin\Store;

use App\Http\Requests\ApiRequest;
use Illuminate\Support\Facades\Validator;
use App\Models\DressOrder;

class DressOrderSaveRequest extends ApiRequest
{
    private $dressOrder;

    public function __construct(
        DressOrder $dressOrder
    ) {
        $this->dressOrder       = $dressOrder;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $store      = request()->route()->store;

        $dressUseList = array_keys($this->dressOrder->dressUseList);

        $result = [
            'order_id'         => 'nullable|integer|exists:'.$this->dressOrder->getTable().',id',
            'wedding_date'     => 'required|date_format:Y-m-d',
            'dress_use'        => 'nullable|in:'.implode(',', $dressUseList),
            'prepare_date'     => 'required|date_format:Y-m-d',
            'return_date'      => 'required|date_format:Y-m-d',
            'tidy_date'        => 'required|date_format:Y-m-d',
            'pickup_date'      => 'nullable|date_format:Y-m-d H:i:s',
            'albums'           => 'nullable|array'
        ];

        return $result;
    }
}
