<?php

namespace App\Http\Requests\Auth\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\YzcubeUser;

class SaveRequest extends ApiRequest
{
    private $yzcubeUser;

    public function __construct(
        YzcubeUser $yzcubeUser
    ) {
        $this->yzcubeUser = $yzcubeUser;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id'    => 'nullable|integer|exists:'.$this->yzcubeUser->getTable().',id',
            'name'  => 'required|max:100',
            'email' => 'required|email|max:100|unique:'.$this->yzcubeUser->getTable().',email,'.request('id'),
        ];
    }
}
