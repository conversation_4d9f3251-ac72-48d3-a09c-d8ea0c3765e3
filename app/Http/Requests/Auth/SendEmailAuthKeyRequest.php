<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\ApiRequest;

class SendEmailAuthKeyRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email'    => 'required|max:100',
            'type'     => 'required|in:email,fb,line,google,store_email',
            'third_id' => 'required_if:type,fb,line,google',
            'third_name' => 'required_if:type,fb,line,google',
        ];
    }
}
