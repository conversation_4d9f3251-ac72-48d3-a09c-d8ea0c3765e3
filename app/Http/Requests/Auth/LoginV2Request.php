<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\ApiRequest;

class LoginV2Request extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email'    => 'nullable|required_if:type,email|email|max:100',
            'password' => 'required_if:type,email',
            'type'     => 'required|in:email,fb,google,line',
            'third_id' => 'required_if:type,fb,google,line',
        ];
    }
}
