<?php

namespace App\Http\Requests\Event\Yzcube;

use App\Http\Requests\ApiRequest;
use Illuminate\Support\Facades\Validator;
use App\Models\EventTool;
use App\Models\InvoiceSetting;
use App\Repositories\EventRepository;
use App\Services\Event\GoogleSheetsService;

class SaveRequest extends ApiRequest
{
    private $eventTool;
    private $invoiceSetting;
    private $eventRepository;
    private $googleSheetsService;

    public function __construct(
        EventTool $eventTool,
        InvoiceSetting $invoiceSetting,
        EventRepository $eventRepository,
        GoogleSheetsService $googleSheetsService
    ) {
        $this->eventTool           = $eventTool;
        $this->invoiceSetting      = $invoiceSetting;
        $this->eventRepository     = $eventRepository;
        $this->googleSheetsService = $googleSheetsService;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'event_id'             => 'nullable|integer',
            'use_payment'          => 'required|boolean',
            'start_date'           => 'nullable|date_format:Y-m-d H:i:s',
            'end_date'             => 'nullable|date_format:Y-m-d H:i:s',
            'path'                 => 'required|max:60|unique:events,path,'.request('event_id'),
            'title'                => 'required|max:100',
            'description'          => 'nullable|string',
            'image'                => 'required',
            'image_xs'             => 'required',
            'image_meta'           => 'required',
            'columns'              => 'required|json',
            'share_url'            => 'nullable|url',
            'float_btn'            => 'required|max:10',
            'pending_info'         => 'nullable',
            'result_new_info'      => 'nullable',
            'result_new_btn_show'  => 'required|boolean',
            'result_new_btn'       => 'required|max:20',
            'result_new_url'       => 'nullable|url',
            'result_old_info'      => 'nullable',
            'result_old_btn'       => 'required|max:20',
            'result_old_url'       => 'nullable|url',
            'completed_info'       => 'nullable',
            'email_title'          => 'nullable',
            'email_info'           => 'nullable',
            'email_info_more'      => 'nullable',
            'spreadsheet_id'       => 'required|max:60',
            'use_report_no'        => 'required|boolean',
            'show_report_no_page'  => 'required|boolean',
            'use_qrcode'           => 'required|boolean',
            'show_qrcode_page'     => 'required|boolean',
            'event_tool_id'        => 'nullable|exists:'.$this->eventTool->getTable().',id',
            'onsite_signup_dates'  => 'nullable',
            'purchase_item_unique' => 'nullable|boolean|required_if:use_payment,1',
            'need_invoice'         => 'nullable|boolean|required_if:use_payment,1',
            'invoice_now'          => 'nullable|boolean|required_if:need_invoice,1',
            'invoice_setting_id'   => 'nullable|exists:'.$this->invoiceSetting->getTable().',id',
            'seller_name'          => 'nullable|max:60',
            'seller_ubn'           => 'nullable|max:8',
            'use_calendar'         => 'nullable|boolean',
            'calendar_summary'     => 'nullable|max:100',
            'calendar_location'    => 'nullable|max:100',
            'calendar_description' => 'nullable|string',
            'calendar_start'       => 'nullable|date_format:Y-m-d H:i:s',
            'calendar_end'         => 'nullable|date_format:Y-m-d H:i:s',
            'products'             => 'nullable|json',
            'exhibitions'          => 'nullable|json',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            // 取得活動報名資訊
            $event   = $this->eventRepository->getFirst(['id' => request('event_id')]);
            $sheetId = $event->sheet_id ?? NULL;

            // 驗證活動產品
            if (!$this->validateProducts()) {
                $validator->errors()->add('products', 'The products format is invalid.');
            }

            // 驗證婚展多選項目
            if (!$this->validateExhibitions()) {
                $validator->errors()->add('exhibitions', 'The exhibitions format is invalid.');
            }

            // 都沒有錯誤，最後才驗證Google試算表權限(因為會更新試算表)
            if ($validator->errors()->isEmpty()) {
                try {
                    $this->googleSheetsService->initGoogleSheets(request('spreadsheet_id'), $sheetId, request('path'));
                } catch (\Exception $e) {
                    $message = json_decode($e->getMessage());
                    $validator->errors()->add('spreadsheet_id', $message->error->status);
                }
            }
        });
    }

    /**
     * 驗證活動產品
     *
     * @return bool 資料正確true, 錯誤false
     */
    public function validateProducts()
    {
        // 驗證活動是否使用付款功能
        if (!request('use_payment')) {
            return true;
        }

        // 驗證為空
        $data = json_decode(request('products'), true);
        if (!$data) {
            return false;
        }

        $rules = [
            '*.product_id'             => 'nullable|integer',
            '*.name'                   => 'required|max:100',
            '*.image'                  => 'required',
            '*.items'                  => 'required|array',
            '*.items.*.item_id'        => 'nullable|integer',
            '*.items.*.name'           => 'required|max:100',
            '*.items.*.original_price' => 'nullable|integer',
            '*.items.*.selling_price'  => 'required|integer',
            '*.items.*.inventory'      => 'nullable|integer',
            '*.items.*.purchase_limit' => 'nullable|integer',
        ];

        $validator = Validator::make($data, $rules);

        return $validator->passes();
    }

    /**
     * 驗證婚展多選項目
     *
     * @return bool 資料正確true, 錯誤false
     */
    public function validateExhibitions()
    {
        $data  = json_decode(request('exhibitions'), true);
        $rules = [
            '*.exhibition_id'        => 'nullable|integer',
            '*.tool_id'              => 'required|integer',
            '*.name'                 => 'required|max:100',
            '*.expired_at'           => 'required|date_format:Y-m-d H:i:s',
            '*.onsite_signup_dates'  => 'nullable',
            '*.use_calendar'         => 'required|boolean',
            '*.calendar_summary'     => 'nullable|max:100',
            '*.calendar_location'    => 'nullable|max:100',
            '*.calendar_description' => 'nullable|string',
            '*.calendar_start'       => 'nullable|date_format:Y-m-d H:i:s',
            '*.calendar_end'         => 'nullable|date_format:Y-m-d H:i:s',
        ];

        $validator = Validator::make($data, $rules);

        return $validator->passes();
    }
}
