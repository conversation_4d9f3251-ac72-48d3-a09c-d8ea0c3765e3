<?php

namespace App\Http\Requests\Event;

use App\Http\Requests\ApiRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Traits\Request\EventOrderTrait;
use App\Traits\Request\EventCouponTrait;
use App\Formatters\ApiFormatter;

class SaveRequest extends ApiRequest
{
    use EventOrderTrait;
    use EventCouponTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email'       => 'nullable|email|max:100|required_with:password',
            'password'    => 'nullable|string',
            'content'     => 'required|json',
            'order'       => 'nullable|json',
            'coupon_code' => 'nullable|max:100',
            'prime'       => 'nullable|string',
            'exhibitions' => 'nullable|array',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            // 驗證活動是否使用付款功能
            if (!$this->event->use_payment) {
                return false;
            }

            // 驗證訂購內容，並整理格式
            $this->validateFormatOrder();

            // 驗證活動表單優惠卷
            $this->validateCouponCode();

            // 訂單明細加上額外費用(優惠卷不可折抵額外費用)
            $this->orderListAddExtraFees();

            // 優惠代碼錯誤，還是要返回訂購內容
            if ($this->baseResponse) {
                $formatter = resolve(ApiFormatter::class);
                throw new HttpResponseException($formatter->json($this->orderList, $this->baseResponse));
            }

            // 驗證TapPay的付款憑證
            if ($this->orderList['total'] && !$this->prime) {
                $validator->errors()->add('prime', 'TapPay的付款憑證有誤！');
            }
        });
    }
}
