<?php

namespace App\Http\Requests\Event;

use App\Http\Requests\ApiRequest;
use Illuminate\Support\Facades\Validator;
use App\Traits\Request\EventOrderTrait;
use App\Traits\Request\EventCouponTrait;

class CheckOrderRequest extends ApiRequest
{
    use EventOrderTrait;
    use EventCouponTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order'       => 'required|json',
            'coupon_code' => 'nullable|max:100',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {

            // 驗證訂購內容，並整理格式
            $this->validateFormatOrder();

            // 驗證活動表單優惠卷
            $this->validateCouponCode();

            // 訂單明細加上額外費用(優惠卷不可折抵額外費用)
            $this->orderListAddExtraFees();
        });
    }
}













































