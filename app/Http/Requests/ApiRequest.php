<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Traits\ApiErrorTrait;

class ApiRequest extends FormRequest
{
    protected $status_code = 4001;
    protected $message = '欄位輸入驗證錯誤！';

    use ApiErrorTrait;

    protected function failedValidation(Validator $validator)
    {
        $this->setException($this->message, $this->status_code, $validator->errors());
    }
}
