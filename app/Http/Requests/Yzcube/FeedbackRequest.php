<?php

namespace App\Http\Requests\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\Feedback;

class FeedbackRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $feedback = new Feedback;
        $statusList = array_keys($feedback->statusList);

        return [
            'feedback_id' => 'nullable|integer|exists:feedback,id',
            'reply'       => 'nullable|string',
            'note'        => 'nullable|string',
            'status'      => 'nullable|in:'.implode(',', $statusList),
        ];
    }
}
