<?php

namespace App\Http\Requests\Yzcube\AdCampaign;

use App\Http\Requests\ApiRequest;
use App\Models\AdImage;

class AdImageRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $adImage = new AdImage;
        $campaignTypeList = array_keys($adImage->campaignTypeList);
        $linkTypeList     = array_keys($adImage->linkTypeList);

        return [
            'ad_image_id'     => 'nullable|integer|exists:ad_images,id',
            'name'            => 'required|max:100',
            'campaign_type'   => 'required|in:'.implode(',', $campaignTypeList),
            'is_backup'       => 'nullable|boolean',
            'image'           => 'nullable|required_if:campaign_type,banner,float', // 手機側邊欄Banner不需必填
            'link_type'       => 'nullable|in:'.implode(',', $linkTypeList),
            'url_internal'    => 'nullable|required_if:link_type,internal',
            'url_external'    => 'nullable|required_if:link_type,external|url',
            'image_xs'        => 'nullable|required_if:campaign_type,banner,mobile_banner', // 浮動廣告不需必填
            'link_type_xs'    => 'nullable|in:'.implode(',', $linkTypeList),
            'url_xs_internal' => 'nullable|required_if:link_type_xs,internal',
            'url_xs_external' => 'nullable|required_if:link_type_xs,external|url',
        ];
    }
}
