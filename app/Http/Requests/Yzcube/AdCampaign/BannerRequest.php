<?php

namespace App\Http\Requests\Yzcube\AdCampaign;

use App\Http\Requests\ApiRequest;

class BannerRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'banner_id'    => 'nullable|integer|exists:ad_campaigns,id,type,banner',
            'name'         => 'required|max:100',
            'start_date'   => 'required|date',
            'end_date'     => 'required|date',
            'use_slots'    => 'required|max:20',
            'ad_image_ids' => 'required|array',
        ];
    }
}
