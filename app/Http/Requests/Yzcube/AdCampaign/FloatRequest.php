<?php

namespace App\Http\Requests\Yzcube\AdCampaign;

use App\Http\Requests\ApiRequest;

class FloatRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'float_id'    => 'nullable|integer|exists:ad_campaigns,id,type,float',
            'name'        => 'required|max:100',
            'start_date'  => 'required|date',
            'end_date'    => 'required|date',
            'ad_image_id' => 'required|integer|exists:ad_images,id,campaign_type,float',
        ];
    }
}
