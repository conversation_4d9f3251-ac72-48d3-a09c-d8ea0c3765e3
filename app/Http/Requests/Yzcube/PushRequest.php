<?php

namespace App\Http\Requests\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\PushMessage;

class PushRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $pushMessage = new PushMessage;
        $pushMessageTable = $pushMessage->getTable();
        $receiverTypeList = array_keys($pushMessage->receiverTypeList);
        unset($pushMessage->statusList['published']);
        $statusList = array_keys($pushMessage->statusList);

        $adminList = array_keys(config('params.firebase.admins'));

        return [
            'push_id'        => 'nullable|integer|exists:'.$pushMessageTable.',id',
            'admin_id'       => 'required|in:'.implode(',', $adminList),
            'receiver_type'  => 'required|in:'.implode(',', $receiverTypeList),
            'title'          => 'required|max:100',
            'content'        => 'required',
            'images'         => 'nullable|array',
            'push_count'     => 'required|integer',
            'status'         => 'required|in:'.implode(',', $statusList),
            'set_publish_at' => 'nullable|required_if:status,scheduling|date|after:now',
        ];
    }
}
