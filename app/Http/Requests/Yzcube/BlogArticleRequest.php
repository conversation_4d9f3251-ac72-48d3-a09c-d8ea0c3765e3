<?php

namespace App\Http\Requests\Yzcube;

use App\Http\Requests\ApiRequest;

class BlogArticleRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title'    => 'required|max:100',
            'image'    => 'required',
            'category' => 'nullable|string|max:10',
        ];
    }
}
