<?php

namespace App\Http\Requests\Forum;

use App\Http\Requests\ApiRequest;
use App\Models\ForumArticle as Article;
use App\Models\ForumCategory as Category;

class ArticleRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $articleTable  = (new Article)->getTable();
        $categoryTable = (new Category)->getTable();

        return [
            'article_id'   => 'nullable|integer|exists:'.$articleTable.',id',
            'category_id'  => 'required_if:status,published|nullable|integer|exists:'.$categoryTable.',id,status,published,deleted_at,NULL',
            'title'        => 'required_if:status,published|max:100',
            'content'      => 'required_if:status,published',
            'is_anonymous' => 'required_if:status,published|boolean',
            'status'       => 'required|in:pending,published',
        ];
    }
}
