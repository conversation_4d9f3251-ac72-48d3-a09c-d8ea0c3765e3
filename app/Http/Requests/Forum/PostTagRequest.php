<?php

namespace App\Http\Requests\Forum;

use App\Http\Requests\ApiRequest;

class PostTagRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'tags'   => 'nullable|array',
            'status' => 'required|in:pending,published',
        ];
    }
}
