<?php

namespace App\Http\Requests\Forum;

use App\Http\Requests\ApiRequest;
use App\Models\ForumComment as Comment;

class CommentRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $commentTable = (new Comment)->getTable();

        return [
            'comment_id'   => 'nullable|integer|exists:'.$commentTable.',id,article_id,'.request()->route()->article_id,
            'parent_id'    => 'nullable|integer|exists:'.$commentTable.',id,article_id,'.request()->route()->article_id.',parent_id,NULL',
            'content'      => 'required',
            'is_anonymous' => 'required|boolean',
        ];
    }
}
