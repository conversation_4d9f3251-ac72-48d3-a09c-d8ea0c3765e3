<?php

namespace App\Http\Requests\Forum\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\ForumCategory as Category;

class CategoryRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $categoryTable = (new Category)->getTable();

        // 儲存分類
        return [
            'category_id'         => 'nullable|integer|exists:'.$categoryTable.',id',
            'name'                => 'required|max:100',
            'description'         => 'required',
            'article_placeholder' => 'nullable|string',
            'is_public_use'       => 'required|boolean',
        ];
    }
}
