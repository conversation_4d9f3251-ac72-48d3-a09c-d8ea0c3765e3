<?php

namespace App\Http\Requests\Forum\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\ForumArticle as Article;
use App\Models\ForumCategory as Category;
use App\Models\User;

class ArticleRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $article = new Article;
        $statusList = array_keys($article->statusList);

        $articleTable  = $article->getTable();
        $userTable     = (new User)->getTable();
        $categoryTable = (new Category)->getTable();

        return [
            'article_id'     => 'nullable|integer|exists:'.$articleTable.',id',
            'user_id'        => 'nullable|integer|exists:mysql.'.$userTable.',id',
            'category_id'    => 'nullable|integer|exists:'.$categoryTable.',id,status,published,deleted_at,NULL',
            'title'          => 'nullable|max:100',
            'content'        => 'nullable|string',
            'is_anonymous'   => 'nullable|boolean',
            'status'         => 'nullable|in:'.implode(',', $statusList),
            'is_top'         => 'nullable|boolean',
            'top_stop_at'    => 'nullable|date',
            'set_publish_at' => 'nullable|date',
            'tags'           => 'nullable|array',
        ];
    }
}
