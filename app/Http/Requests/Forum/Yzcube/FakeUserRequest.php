<?php

namespace App\Http\Requests\Forum\Yzcube;

use App\Http\Requests\ApiRequest;

class FakeUserRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // 更新假帳號
        if (request()->route()->user_id) {
            return [
                'wedding_date'   => 'nullable|date',
                'tags'           => 'nullable|array',
                'is_random_like' => 'nullable|boolean',
                'note'           => 'nullable|string',
            ];
        }

        // 新增假帳號
        return [
            'email'    => 'required|email|max:100',
            'password' => 'required',
            'count'    => 'required|integer',
        ];
    }
}
