<?php

namespace App\Http\Requests\Forum\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\ForumComment as Comment;

class CommentRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $comment = new Comment;
        $statusList = array_keys($comment->statusList);

        $commentTable = $comment->getTable();

        return [
            'comment_id' => 'required|integer|exists:'.$commentTable.',id,article_id,'.request()->route()->article_id,
            'status'     => 'required|in:'.implode(',', $statusList),
        ];
    }
}
