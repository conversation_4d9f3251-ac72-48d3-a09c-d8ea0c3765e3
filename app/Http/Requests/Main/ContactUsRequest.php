<?php

namespace App\Http\Requests\Main;

use App\Http\Requests\ApiRequest;
use App\Models\Feedback;

class ContactUsRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $feedback = new Feedback;
        $identityList = array_keys($feedback->identityList);

        return [
            'identity' => 'required|in:'.implode(',', $identityList),
            'name'     => 'required|max:100',
            'email'    => 'required|email|max:100',
            'content'  => 'required',
            'images'   => 'nullable|array',
        ];
    }
}
