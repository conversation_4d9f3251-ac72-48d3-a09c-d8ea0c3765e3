<?php

namespace App\Http\Requests\User;

use App\Http\Requests\ApiRequest;
use App\Models\Store;
use App\Models\UserWeddingType;
use App\Models\Wdv2\UserQuote;
use App\Transformers\CityCodeTransformer;

class QuoteRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $store = new Store;
        $quoteTypeList = array_keys($store->quoteTypeList);

        $weddingType = new UserWeddingType;
        $typeList = array_keys($weddingType->typeList);

        $userQuote = new UserQuote;
        $weddingVenueTypeList = array_keys($userQuote->weddingVenueTypeList);

        $cityCodeTransformer = new CityCodeTransformer;
        $cityCode = array_keys($cityCodeTransformer->getArray());

        return [
            'store_type'         => 'required|integer|in:'.implode(',', $quoteTypeList),
            'wedding_type'       => 'required|integer|in:'.implode(',', $typeList),
            'wedding_date'       => 'required|date_format:Y-m-d',
            'city_code'          => 'nullable|integer|in:'.implode(',', $cityCode),
            'wedding_venue_type' => 'nullable|integer|in:'.implode(',', $weddingVenueTypeList),
            'wedding_venue'      => 'nullable|string',
            'detail'             => 'nullable|json',
        ];
    }
}
