<?php

namespace App\Http\Requests\User;

use App\Http\Requests\ApiRequest;

class FeedbackRequest extends ApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'content' => 'required',
            'images'  => 'nullable|array',
        ];
    }
}
