<?php

namespace App\Http\Requests\Coupon\Yzcube;

use App\Http\Requests\ApiRequest;
use App\Models\Coupon;
use App\Models\CouponMultiple;
use App\Models\Event;

class SaveRequest extends ApiRequest
{
    private $coupon;
    private $couponMultiple;
    private $event;

    public function __construct(
        Coupon $coupon,
        CouponMultiple $couponMultiple,
        Event $event
    ) {
        $this->coupon         = $coupon;
        $this->couponMultiple = $couponMultiple;
        $this->event          = $event;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $typeList          = array_keys($this->coupon->typeList);
        $conditionTypeList = array_keys($this->coupon->conditionTypeList);
        $discountTypeList  = array_keys($this->coupon->discountTypeList);

        return [
            'coupon_id'           => 'nullable|integer',
            'type'                => 'required|in:'.implode(',', $typeList),
            'title'               => 'required|max:100',
            'code'                => 'nullable|required_if:type,single|max:100',
            'condition_type'      => 'required|in:'.implode(',', $conditionTypeList),
            'condition_value'     => 'required|integer',
            'discount_type'       => 'required|in:'.implode(',', $discountTypeList),
            'discount_value'      => 'required|integer',
            'limit'               => 'nullable|integer',
            'take_coupon_id'      => 'nullable|integer|exists:'.$this->coupon->getTable().',id,type,multiple,deleted_at,NULL',
            'take_number_range'   => 'nullable|array|required_with:take_coupon_id',
            'take_number_range.*' => 'required|integer|exists:'.$this->couponMultiple->getTable().',number,coupon_id,'.request('take_coupon_id'),
            'start_date'          => 'nullable|date_format:Y-m-d H:i:s',
            'end_date'            => 'nullable|date_format:Y-m-d H:i:s',
            'event_ids'           => 'required|array',
            'event_ids.*'         => 'required|exists:'.$this->event->getTable().',id,use_payment,1,deleted_at,NULL',
            'note'                => 'nullable|string',
        ];
    }
}
