<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     * 功能名稱
     *
     * @url api/xxx/{xxx}
     * @method GET / POST
     * @header string Access-Token *
     * @header string User-Token|Store-Token|Yzcube-Token (*)
     * @path int $xxx 網址的參數 *
     * @param array $xxx 傳入的參數 (*)
     * @return json
     */
}
