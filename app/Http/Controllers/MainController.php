<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\Main\FrontedError;
use App\Http\Requests\Main\ContactUsRequest;
use App\Repositories\AdCampaignRepository;
use App\Repositories\SystemInfoRepository;
use App\Models\Store;
use App\Models\Brand;
use App\Models\Wdv2\UserQuote;
use App\Models\UserWeddingType;
use App\Models\Image;
use App\Models\Feedback;
use App\Models\WebNotification;
use App\Models\LogRedirectQuote;
use App\Models\ForumArticle;
use App\Services\Main\FrontendErrorLogService;
use App\Services\Main\IndexService;
use App\Services\Tools\GetWebData\ImageToBase64Service;
use App\Services\Image\CheckImageService;
use App\Services\Image\GetImageUrlService;
use App\Services\Image\CreateImageService;
use App\Services\Mail\User\FeedbackService;
use App\Services\Store\GetRedirectUrlByKeyService;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\TransformerTrait;
use App\Traits\GoogleSheetTrait;
use App\Transformers\MainTransformer;
use App\Transformers\User\QuoteTransformer;
use App\Formatters\ApiFormatter;
use App\Jobs\Invoice\AddGoogleSheets;
use Illuminate\Support\Str;
use Log;
use Sheets;

/*
 |--------------------------------------
 |  前台-主要功能
 |--------------------------------------
 |
 |
 */
class MainController extends Controller
{
    use ApiErrorTrait;
    use ConvertPasswordTrait;
    use TransformerTrait;
    use GoogleSheetTrait;

    /**
     * 取得存取權狀
     *
     * @url api/access-token
     * @method GET
     * @return json
     */
    public function getAccessToken(
        SystemInfoRepository $systemInfoRepository,
        ApiFormatter $formatter
    ) {
        return $formatter->json($systemInfoRepository->get('access_token'));
    }

    /**
     * 網站首頁
     *
     * @url api/index
     * @method GET
     * @header string Access-Token *
     * @header string User-Token
     * @return json
     */
    public function index(
        IndexService $indexService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // 取得cache
        if (!env('APP_DEBUG') && cache('main:index')) {
            $result = cache('main:index');

            return $formatter->json($result);
        }

        // Service
        $data = $indexService->run();

        // Transformer
        $result = $mainTransformer->index($data);

        // 儲存cache，有效時間5分鐘
        if (!env('APP_DEBUG')) {
            cache(['main:index' => $result], 5 * 60);
        }

        return $formatter->json($result);
    }

    /**
     * SEO MetaData
     *
     * @url api/seo-metadata/{route_name}
     * @method GET
     * @header string Access-Token *
     * @path string $route_name 路由名稱 *
     * @return json
     */
    public function seoMetaData(
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        $route_name
    ) {
        $result = $mainTransformer->getSeoMetaData($route_name);

        return $formatter->json($result);
    }

    /**
     * 廣告活動
     *
     * @url api/ad-campaign
     * @method GET
     * @header string Access-Token *
     * @param array $types 類型 mobile_banner:手機側邊欄Banner float:浮動廣告
     * @return json
     */
    public function getAdCampaign(
        AdCampaignRepository $adCampaignRepository,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        $types = request('types', []);

        // 取得cache
        $cacheKey = 'main:ad-campaign-'.implode('-', $types);
        if (!env('APP_DEBUG') && cache($cacheKey)) {
            $result = cache($cacheKey);

            return $formatter->json($result);
        }

        // 取得發佈中的版位
        $data = [];
        foreach ($types as $type) {
            $data[$type] = $adCampaignRepository->getPublishedList($type);
        }

        // Transformer
        $result = $mainTransformer->getAdCampaign($data);

        // 儲存cache，有效時間5分鐘
        if (!env('APP_DEBUG')) {
            cache([$cacheKey => $result], 5 * 60);
        }

        return $formatter->json($result);
    }

    /**
     * 系統資訊
     *
     * @url api/system-info/{key}
     * @method GET
     * @header string Access-Token *
     * @path string $key 索引 plate_gauge:板規 access_token:存取權狀 *
     * @return json
     */
    public function getSystemInfo(
        SystemInfoRepository $systemInfoRepository,
        ApiFormatter $formatter,
        $key
    ) {
        return $formatter->json($systemInfoRepository->get($key));
    }

    /**
     * 取消訂閱
     *
     * @url api/unsubscribe/{identity}/{type}/{token}
     * @method POST
     * @header string Access-Token *
     * @path string $identity 身份 user:使用者 store:商家 brand:品牌 marry_studio:結婚吧商家 *
     * @path string $type 訂閱類型 *
     * @path string $token 驗證碼 *
     * @return json
     */
    public function unsubscribe(
        ApiFormatter $formatter,
        $identity,
        $type,
        $token
    ) {
        // 驗證身份
        $classPath = 'App\Models\\'.Str::studly($identity);
        if (!class_exists($classPath)) {
            $this->setException('發生錯誤了，網址連結不是有效的。');
        }

        // init Model
        $model = resolve($classPath);

        $email = $this->feDecode($token);
        $model = $model->where('email', $email)->first();
        if (!$model) {
            $this->setException('發生錯誤了，網址連結不是有效的。');
        }

        $model->subscription()->updateOrCreate([$identity.'_id' => $model->id], [$type => 0]);

        return $formatter->json();
    }

    /**
     * 詢價說明頁
     *
     * @url api/quote
     * @method GET
     * @header string Access-Token *
     * @return json
     */
    public function quote(
        Store $store,
        UserQuote $userQuote,
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    ) {
        // 主動報價商家各類型及數量
        $storeTypesCount = [];
        foreach ($store->quoteTypeList as $type => $name) {
            $storeTypesCount[] = [
                'key'   => $type,
                'value' => $name,
                'count' => $store->select('id')->where('type', $type)->count(),
            ];
        }

        $result = $quoteTransformer->create();
        $result['store_count']  = $store->select('id')->count();
        $result['quote_count']  = $userQuote->select('id')->count();
        $result['store_types_count'] = $storeTypesCount;

        return $formatter->json($result);
    }

    /**
     * 新增聯絡我們
     *
     * @url api/contact-us
     * @method POST
     * @header string Access-Token *
     * @param string $identity 身份 user:新人 store:商家 *
     * @param string $name 姓名 *
     * @param string $email 電子信箱 *
     * @param string $content 詳細內容 *
     * @param array $images 已上傳圖檔名稱
     * @return json
     */
    public function contactUs(
        ContactUsRequest $request,
        Feedback $feedback,
        CreateImageService $createImageService,
        FeedbackService $feedbackService,
        ApiFormatter $formatter
    ) {
        // 新增意見回饋
        $feedback->identity = $request['identity'];
        $feedback->name     = $request['name'];
        $feedback->email    = $request['email'];
        $feedback->content  = $request['content'];
        $feedback->save();

        // 更新上傳的圖檔的來源
        foreach ($request['images'] as $image) {
            $createImageService->add([
                'file_name' => $image,
                'type'      => 'feedback',
                'target_id' => $feedback->id,
            ]);
        }

        // 意見回饋通知信
        $feedbackService->sendMail($feedback);

        return $formatter->json();
    }

    /**
     * 外站連結轉址
     *
     * @url api/redirect/{key}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token
     * @path string $key 轉址索引值 *
     * @param string $route_name 路由名稱
     * @param string $label 來源標籤
     * @param string $cid Client ID
     * @param string $user_agent User-Agent
     * @return json
     */
    public function redirectUrl(
        GetRedirectUrlByKeyService $getRedirectUrlByKeyService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $getRedirectUrlByKeyService->run($request);

        return $formatter->json($data);
    }

    /**
     * 外站連結轉址-主動報價
     *
     * @url api/redirect-quote
     * @method GET
     * @header string Access-Token *
     * @header string User-Token
     * @param int $store_id 商家ID
     * @param int $target_id 商家報價ID
     * @param string $location 位置 user_quote:婚禮中心的詢價頁 auto_mail:商家報價信件內容
     * @param string $click_type 點擊目的地 store.(website|fanpage|instagram|phone|tel|line|email|address):商家資訊 quote.link:報價內容裡的Link
     * @param string $redirect 轉址去處
     * @param string $cid Client ID
     * @param string $user_agent User-Agent
     * @return json
     */
    public function redirectQuote(
        LogRedirectQuote $logRedirectQuote,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 需排除機器人
        if (preg_match('/(Googlebot|PetalBot)/', $request['user_agent'])) {
            return $formatter->json();
        }

        // 新增主動報價的轉址資訊紀錄
        $logRedirectQuote->create([
            'store_id'       => $request['store_id'],
            'store_quote_id' => $request['target_id'],
            'location'       => $request['location'],
            'click_type'     => $request['click_type'],
            'redirect'       => $request['redirect'],
            'user_id'        => $request['user']->id ?? NULL,
            'client_id'      => $request['cid'],
            'user_agent'     => $request['user_agent'],
            'created_at'     => now(),
        ]);

        return $formatter->json();
    }

    /**
     * 錯誤紀錄API
     *
     * @url api/error-log
     * @method POST
     * @header string Access-Token *
     * @param string $type 辨識錯誤類型 *
     * @param string $content 詳細內容 *
     * @return json
     */
    public function errorLog(
        ApiFormatter $formatter
    ) {
        // Error Log
        Log::error('錯誤紀錄API', [
            'type'    => request('type'),
            'content' => request('content'),
        ]);

        return $formatter->json();
    }

    /**
     * 檢查圖片及縮圖
     *
     * @url api/check-image/{file_name}
     * @method GET / POST
     * @header string Access-Token *
     * @path string $file_name 圖檔名 *
     * @return json
     */
    public function checkImage(
        Image $image,
        CheckImageService $checkImageService,
        GetImageUrlService $getImageUrlService,
        ApiFormatter $formatter,
        $file_name
    ) {
        // 找出圖片
        $images = $image->where('file_name', $file_name)->get();
        if (!$images->count()) {
            $this->setException('資料庫找不到任何圖片！');
        }

        // 找出所有圖片
        foreach ($images as $image) {

            // 檢查圖片及縮圖
            $data = $checkImageService->run($image);

            // 額外修改項目
            // switch ($image->type) {

            //     // 更新W姐妹分享文簡介
            //     case 'share_post':
            //         $image->post->summary = str_replace($file_name, $data['file_name'], $image->post->summary);
            //         $image->post->save();
            //         break;

            //     // 更新論壇文章簡介
            //     case 'forum_article':
            //         $image->article->summary = str_replace($file_name, $data['file_name'], $image->article->summary);
            //         $image->article->save();
            //         break;
            // }
        }

        $data['image_ids'] = $images->pluck('id');
        // $data['file_url']  = $getImageUrlService->get($data['file_name']);
        $data['file_url']  = $getImageUrlService->get($file_name);

        return $formatter->json($data);
    }

    /**
     * 圖片網址輸出Base64
     *
     * @url api/image-base64
     * @method GET
     * @header string Access-Token *
     * @param string $url 圖片網址 *
     * @return json
     */
    public function imageBase64(
        ImageToBase64Service $imageToBase64Service,
        ApiFormatter $formatter
    ) {
        $data = $imageToBase64Service->run(request('url'));
        if (!$data) {
            $this->setException('圖片網址解析錯誤。');
        }

        return $formatter->json($data);
    }

    /**
     * 記錄前端error
     *
     * @url api/frontend-error-log
     * @method POST
     * @header string Access-Token *
     * @param string $method request method *
     * @param string $status response status code *
     * @param string $url response url *
     * @param string $detail error stack *
     * @param string $ip ip位址 *
     * @param string $token_type token類型 user or yzcube
     * @param string $token auth token
     * @return json
     */
    public function frontendErrorLog(
        FrontedError $request,
        FrontendErrorLogService $frontendErrorLogService,
        ApiFormatter $formatter
    ) {
        //整理參數
        $data = [
            'method' => $request->input('method'),
            'status' => $request->status,
            'url' => $request->url,
            'detail' => $request->detail ?? null,
            'ip' => $request->ip ?? null,
            'token_type' => $request->token_type ?? 'user',
            'token' => $request->token ?? null
        ];

        $code = $frontendErrorLogService->run($data);

        if (!$code) {
            $this->setException('儲存Log錯誤。');
        }

        return $formatter->json($code);
    }

    /**
     * 更新瀏覽器推播通知的Token列表
     *
     * @url api/web-notification-tokens 前台
     * @url api/yzcube/web-notification-tokens 神之後台
     * @url api/admin/web-notification-tokens 商家後台
     * @method POST
     * @header string Access-Token *
     * @header string User-Token|Store-Token|Yzcube-Token (*)
     * @param string $target_type 目標類型 user:新娘 store_user:商家帳號 admin:管理員 *
     * @param int $admin_id 管理員ID (* required if target_type=admin)
     * @param array $tokens Token列表 *
     * @return json
     */
    public function updateWebNotificationTokens(
        WebNotification $webNotification,
        ApiFormatter $formatter
    ) {
        // 更新瀏覽器推播通知
        $webNotification->updateOrCreate([
            'target_type' => request('target_type'),
            'target_id'   => (request('target_type') == 'admin') ? request('admin_id') : request(request('target_type'))->id,
        ], [
            'tokens' => request('tokens'),
        ]);

        return $formatter->json();
    }

    /**
     * 重新新增一筆 google 試算表
     *
     * @param int $logId
     */
    public function processGoogleSheet(int $logId)
    {
        $info = $this->getLogData($logId);

        if (
            !empty($info) &&
            !empty($info->spreadsheet_id) &&
            !empty($info->sheet_id) &&
            !empty($info->input_data)

        ) {
            $spreadsheet = Sheets::spreadsheet($info->spreadsheet_id);

            $sheet = $spreadsheet->sheetById($info->sheet_id);

            $this->appendRecordWithGoogleSheet($sheet, [json_decode($info->input_data)], false);
        }
    }
}
