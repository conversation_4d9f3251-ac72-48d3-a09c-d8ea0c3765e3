<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use  App\Services\Slack\OutgoingWebhookService;

class SlackController extends Controller
{
    /**
     * Slack Outgoing WebHook
     *
     * @url api/slack/outgoing
     * @method POST
     * @param texrt $texrt
     * @return json
     */
    public function outgoing(
        OutgoingWebhookService $outgoingWebhookService
    ) {
        $data = $outgoingWebhookService->console(request('text'));

        return response()->json(['text' => $data]);
    }
}
