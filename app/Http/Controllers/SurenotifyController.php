<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\Message\Surenotify\SmsHandle;
use App\Models\LogSurenotifyWebhook;

class SurenotifyController extends Controller
{
    /**
     * 簡訊-查詢 Webhook
     *
     * @url api/surenotify/sms/check-webhooks
     * @method GET
     * @param SmsHandle $smsHandle
     * @return json
     */
    public function smsCheckWebhooks(
        SmsHandle $smsHandle
    ) {
        // 查詢 Webhook
        return $smsHandle->handle('check_webhooks');
    }

    /**
     * 簡訊-新增/修改 Webhook
     *
     * @url api/surenotify/sms/set-webhooks
     * @method GET
     * @param SmsHandle $smsHandle
     * @return json
     */
    public function smsSetWebhooks(
        SmsHandle $smsHandle
    ) {
        // 新增/修改 Webhook 限正式環境
        if (!env('APP_DEBUG')) {
            $smsHandle->handle('set_webhooks');
        }

        // 查詢 Webhook
        return $smsHandle->handle('check_webhooks');
    }

    /**
     * Webhook 簡訊-更新狀態
     *
     * @url api/surenotify/webhook/sms-status
     * @method POST
     * @body json
     * {
     *     "event": "delivery",
     *     "sms": {
     *         "id": "20191217065433-0-08d0c68d-bd83-422b-9457-8cc7f0804ab7",
     *         "content": "豹小編誠心邀請你加入電子豹的大家庭",
     *         "recipient": "************",
     *         "address": "0912345678",
     *         "country_code": "886",
     *         "variables": {
     *             "UUID": "1234-56-78-9012"
     *         },
     *         "alive_mins": 5
     *     },
     *     "delivery": {
     *         "timestamp": 1577836800000,
     *         "point": 1,
     *         "srcaddr": "0988777666",
     *     }
     * }
     * @return json
     */
    public function updateSmsStatus(
        LogSurenotifyWebhook $logSurenotifyWebhook
    ) {
        $data      = request()->all();
        $messageId = request('sms.id');
        $event     = request('event');

        // 新增電子豹 Webhook 記錄
        $logSurenotifyWebhook->create([
            'message_id' => $messageId,
            'type'       => 'sms',
            'event'      => $event,
            'content'    => $data,
        ]);

        return response()->json($data);
    }
}
