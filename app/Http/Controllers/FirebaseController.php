<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Store;
use App\Models\StoreAutoReplyDefault;
use App\Models\Wdv2\StoreQuote;
use App\Models\LogPushMessage;
use App\Repositories\StoreUserRepository;
use App\Services\Google\Firebase\CloudMessaging\MessagingHandle;
use App\Services\Line\LineService;
use App\Services\Mail\Firebase\NewMessageStoreToUserService;
use App\Services\Mail\Firebase\NewMessageAdminToUserService;
use App\Services\Mail\Firebase\NewMessageUserToStoreService;
use App\Services\Mail\Firebase\NewMessageAdminToStoreService;
use App\Jobs\Firebase\Notification;
use App\Traits\Model\SummaryTrait;
use App\Traits\ApiErrorTrait;
use App\Transformers\FirebaseTransformer;
use App\Formatters\ApiFormatter;

class FirebaseController extends Controller
{
    use SummaryTrait;
    use ApiErrorTrait;

    /**
     * Firestore取得使用者個資
     *
     * @url api/firestore/profile-data 前台
     * @url api/yzcube/firestore/profile-data 神之後台
     * @url api/admin/firestore/profile-data 商家後台
     * @method GET
     * @header string Access-Token *
     * @header string User-Token|Store-Token|Yzcube-Token (*)
     * @param array $user_ids 使用者ID
     * @param array $store_ids 商家ID
     * @return json
     */
    public function getProfileData(
        User $user,
        Store $store,
        StoreAutoReplyDefault $storeAutoReplyDefault,
        FirebaseTransformer $firebaseTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者 (需包含已停用、已刪除)
        $users = $user->withTrashed()->whereIn('id', request('user_ids', []))->get();

        // 取得商家 (需包含已停用)
        $stores = $store->withTrashed()->whereIn('id', request('store_ids', []))->get();

        // 取得商家的自動回覆預設訊息
        $storeTypes         = $stores->pluck('type')->unique()->flatten();
        $storeAutoReplyList = [];
        $storeAutoReplyDefault->where('type', '!=', 'first_auto_reply')
                                ->where(function($query) use (&$storeTypes) {
                                    $query->whereNull('store_type')
                                            ->orWhereIn('store_type', $storeTypes);
                                })
                                ->get()
                                ->each(function($item) use (&$storeAutoReplyList) {
                                    if ($item->store_type) {
                                        $storeAutoReplyList['store_type'][$item->store_type][$item->type] = $item->content;
                                    } else {
                                        $storeAutoReplyList[$item->type] = $item->content;
                                    }
                                });

        // Transformer
        $result = $firebaseTransformer->getProfileData($users, $stores, $storeAutoReplyList);

        return $formatter->json($result);
    }

    /**
     * 小鈴鐺通知-商家回應主動報價
     *
     * @url api/admin/firestore/notification/store-quote/{reply_id}
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $reply_id 商家報價ID *
     * @return json
     */
    public function newStoreQuote(
        StoreQuote $storeQuote,
        ApiFormatter $formatter,
        $reply_id
    ) {
        // 取得(已上架)商家的報價紀錄
        $storeUser  = request('store_user');
        $storeIds   = $storeUser->stores->pluck('id');
        $storeQuote = $storeQuote->whereIn('store_id', $storeIds)
                                    ->whereHas('userQuote', function ($q) {
                                        $q->release();
                                    })
                                    ->find($reply_id);
        if (!$storeQuote) {
            $this->setException('找不到此帳號擁有的商家報價紀錄！');
        }

        // 小鈴鐺通知使用者 use Job
        Notification::dispatch('reply_user_quote', $storeQuote);

        return $formatter->json();
    }

    /**
     * 小鈴鐺通知-新上架商家
     *
     * @url api/admin/firestore/notification/release-store/{store_id}
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function releaseStore(
        Store $store,
        ApiFormatter $formatter,
        $store_id
    ) {
        // 取得所有(已上架)的商家
        $storeUser = request('store_user');
        $store     = $storeUser->stores()->find($store_id);
        if (!$store) {
            $this->setException('找不到此帳號擁有的商家！', 3006);
        }

        // 小鈴鐺通知使用者 use Job
        Notification::dispatch('release_store', $store);

        return $formatter->json();
    }

    /**
     * 商家後台-訊息管理-新訊息通知新娘
     *
     * @url api/firestore/new-message-notice-user
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @param int $store_id 私訊的商家ID *
     * @param string $user_id 需通知的使用者ID *
     * @param string $message 訊息內容 *
     * @param datetime $created_at 訊息建立時間 *
     * @return json
     */
    public function newMessageNoticeStoreToUser(
        User $user,
        MessagingHandle $messagingHandle,
        NewMessageStoreToUserService $newMessageStoreToUserService,
        ApiFormatter $formatter
    ) {
        // 此帳號的所有商家
        $storeUser = request('store_user');
        $store     = $storeUser->liveStores()->find(request('store_id'));
        if (!$store) {
            $this->setException('找不到此帳號擁有的商家！', 3006);
        }

        // 驗證使用者
        $user_id = request('user_id');
        $user    = $user->live()->find($user_id);
        if (!$user) {
            $this->setException('找不到此新娘！');
        }

        // 瀏覽器推播通知
        if (!empty($user->liveWebNotification->tokens)) {
            $messagingHandle->handle('push_web_notification', [
                'webNotification' => $user->liveWebNotification,
                'title'           => '你有來自 婚禮商家 的回覆',
                'message'         => '說「'.$this->getSummaryStripTags(request('message'), 60).'」',
                'link'            => config('params.wdv3.user_url').'/message?store_id='.$store->id,
            ]);
        }

        // Email通知
        $newMessageStoreToUserService->sendMail($store, $user, request('message'), request('created_at'));

        return $formatter->json();
    }

    /**
     * 神之後台-私訊管理-新訊息通知新娘
     *
     * @url api/yzcube/firestore/new-message-notice-user
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $admin_id 私訊的管理員ID *
     * @param string $user_id 需通知的使用者ID *
     * @param string $message 訊息內容 *
     * @param datetime $created_at 訊息建立時間 *
     * @return json
     */
    public function newMessageNoticeAdminToUser(
        User $user,
        MessagingHandle $messagingHandle,
        NewMessageAdminToUserService $newMessageAdminToUserService,
        ApiFormatter $formatter
    ) {
        // 取得管理者
        $admin = config('params.firebase.admins.'.request('admin_id'));

        // 驗證使用者
        $user_id = request('user_id');
        $user    = $user->live()->find($user_id);
        if (!$user) {
            $this->setException('找不到此新娘！');
        }

        // 瀏覽器推播通知
        if (!empty($user->liveWebNotification->tokens)) {
            $messagingHandle->handle('push_web_notification', [
                'webNotification' => $user->liveWebNotification,
                'title'           => $admin['name'].' 傳訊息給您',
                'message'         => '說「'.$this->getSummaryStripTags(request('message'), 60).'」',
                'link'            => config('params.wdv3.user_url').'/message?admin_id='.$admin['id'],
            ]);
        }

        // Email通知
        $newMessageAdminToUserService->sendMail($admin, $user, request('message'), request('created_at'));

        return $formatter->json();
    }

    /**
     * 前台-新娘私訊商家-新訊息通知商家
     *
     * @url api/firestore/new-message-notice-store
     * @method POST
     * @header string Access-Token *
     * @header string User-Token *
     * @param string $store_id 需通知的商家ID *
     * @param string $message 訊息內容 *
     * @param datetime $created_at 訊息建立時間 *
     * @return json
     */
    public function newMessageNoticeUserToStore(
        Store $store,
        StoreUserRepository $storeUserRepository,
        MessagingHandle $messagingHandle,
        LineService $lineService,
        NewMessageUserToStoreService $newMessageUserToStoreService,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 驗證商家
        $store_id = request('store_id');
        $store    = $store->live()->find($store_id);
        if (!$store) {
            $this->setException('找不到此商家！');
        }

        $summary = $this->getSummaryStripTags(request('message'), 60);
        $link    = config('params.wdv2.admin_url').'/redirect/'.$store_id.'/message?user_id='.$user->id;

        // 取得須通知的商家Line IDs
        $devices = $storeUserRepository->getMessageLineIdsByStoreId($store_id);

        // Line通知
        $message = '【💌新人訊息】'."\n";
        $message .= '新人 '.$user->name.' 傳送私人訊息給你，快看看他說了什麼吧😊'."\n";
        $message .= "\n";
        $message .= '👇點連結回覆他👇'."\n";
        $message .= $link.'&openExternalBrowser=1';
        $lineService->send($message, $devices);

        // Email通知
        $newMessageUserToStoreService->sendMail($user, $store, request('message'), request('created_at'));

        // 瀏覽器推播通知
        foreach ($store->accounts as $account) {
            if ($account->liveWebNotificatio && $account->liveWebNotification->tokens[0]) {
                $messagingHandle->handle('push_web_notification', [
                    'webNotification' => $account->liveWebNotification,
                    'title'           => '你有來自 新人 的訊息',
                    'message'         => '說「'.$summary.'」',
                    'link'            => $link,
                ]);
            }
        }

        return $formatter->json();
    }

    /**
     * 神之後台-私訊管理-新訊息通知商家
     *
     * @url api/yzcube/firestore/new-message-notice-store
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $admin_id 私訊的管理者ID *
     * @param string $store_id 需通知的商家ID *
     * @param string $message 訊息內容 *
     * @param datetime $created_at 訊息建立時間 *
     * @return json
     */
    public function newMessageNoticeAdminToStore(
        Store $store,
        StoreUserRepository $storeUserRepository,
        MessagingHandle $messagingHandle,
        LineService $lineService,
        NewMessageAdminToStoreService $newMessageAdminToStoreService,
        ApiFormatter $formatter
    ) {
        // 取得管理者
        $admin = config('params.firebase.admins.'.request('admin_id'));

        // 驗證商家
        $store_id = request('store_id');
        $store    = $store->live()->find($store_id);
        if (!$store) {
            $this->setException('找不到此商家！');
        }

        $summary = $this->getSummaryStripTags(request('message'), 60);
        $link    = config('params.wdv2.admin_url').'/redirect/'.$store_id.'/message?admin_id='.$admin['id'];

        // 瀏覽器推播通知
        foreach ($store->accounts as $account) {
            if (!empty($account->liveWebNotification->tokens)) {
                $messagingHandle->handle('push_web_notification', [
                    'webNotification' => $account->liveWebNotification,
                    'title'           => $admin['name'].' 傳訊息給您',
                    'message'         => '說「'.$summary.'」',
                    'link'            => $link,
                ]);
            }
        }

        // 取得須通知的商家Line IDs
        $devices = $storeUserRepository->getLineIdsByStoreId($store_id);

        // Line通知
        $message = '【💗官方來訊】'."\n";
        $message .= $admin['name'].' 傳訊息給您：「'.$summary.'」'."\n";
        $message .= "\n";
        $message .= '👇點連結看完整訊息👇'."\n";
        $message .= $link.'&openExternalBrowser=1';
        $lineService->send($message, $devices);

        // Email通知
        $newMessageAdminToStoreService->sendMail($admin, $store, request('message'), request('created_at'));

        return $formatter->json();
    }

    /**
     * 已讀推播訊息
     *
     * @url api/firestore/read-push-message 前台
     * @url api/admin/firestore/read-push-message 商家後台
     * @method POST
     * @header string Access-Token *
     * @header string User-Token|Store-Token *
     * @param string $receiver_type 接收者類型 user:新娘|store:商家 *
     * @param string $store_id 商家ID (* required if receiver_type=store)
     * @return json
     */
    public function readPushMessage(
        LogPushMessage $logPushMessage,
        ApiFormatter $formatter
    ) {
        // 驗證接收者
        if (request('receiver_type') == 'user') {
            $receiver = request('user');
        } elseif (request('receiver_type') == 'store') {
            // 此帳號的所有商家
            $storeUser = request('store_user');
            $receiver  = $storeUser->liveStores()->find(request('store_id'));
            if (!$receiver) {
                $this->setException('找不到此帳號擁有的商家！', 3006);
            }
        } else {
            $this->setException('接收者類型有誤！');
        }

        // 紀錄已讀推播訊息
        $logPushMessage->join('push_messages', function($join) {
                            $join->on('push_messages.id', '=', 'log_push_messages.push_id')
                                    ->where('receiver_type', request('receiver_type'))
                                    ->whereIn('status', ['immediately', 'published'])
                                    ->whereNotNull('published_at');
                        })
                        ->where('receiver_id', $receiver->id)
                        ->update([
                            'read_at' => now(),
                        ]);

        return $formatter->json();
    }

    /**
     * 神之後台-偷看對話-匯出即時通訊
     *
     * @url api/yzcube/firestore/export-csv
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $data 聊天室資訊
     * @return json
     */
    public function exportCSV(
        ApiFormatter $formatter
    ) {
        $data     = request('data');
        $filename = '[即時通訊紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $data);
    }
}
