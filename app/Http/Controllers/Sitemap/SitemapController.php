<?php

namespace App\Http\Controllers\Sitemap;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    /**
     * sitemap
     *
     * @url auth/register
     * @method POST
     *
     * @return json
     */
    public function get()
    {
        $request = request();

        if (!Storage::disk('local')->exists('sitemap/' . $request->file_name)) {
            abort(404);
        }

        $file = Storage::disk('local')->get('sitemap/' . $request->file_name);

        return Response($file, 200)
                ->header("Content-Type", 'text/xml');
    }
}
