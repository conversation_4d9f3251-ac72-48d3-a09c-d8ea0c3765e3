<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Wdv2\UserQuote;
use App\Transformers\User\DashboardTransformer;
use App\Formatters\ApiFormatter;

class DashboardController extends Controller
{
    /**
     * 婚禮中心
     *
     * @url api/user
     * @method GET
     * @header string Access-Token *
     * @header string User-Token *
     * @return json
     */
    public function index(
        UserQuote $userQuote,
        DashboardTransformer $dashboardTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者、主動報價數據
        $data['user']       = request('user');
        $data['quoteCount'] = $userQuote->select('id')->count();

        // Transformer
        $result = $dashboardTransformer->index($data);

        return $formatter->json($result);
    }
}
