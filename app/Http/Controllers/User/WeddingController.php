<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\User\Wedding\UpdateScheduleService;
use App\Services\User\Wedding\UpdateService;
use App\Services\User\Wedding\GetBrandService;
use App\Services\User\Wedding\CreateBrandService;
use App\Services\File\UploadImageFormService;
use App\Services\Image\CreateImageService;
use App\Transformers\User\WeddingTransformer;
use App\Formatters\ApiFormatter;

class WeddingController extends Controller
{
    /**
     * @SWG\ Get(
     *   path = "/user/wedding",
     *   summary = "我的婚禮",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "我的婚禮頁面資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function index(
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // Transformer
        $result = $weddingTransformer->index($user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/wedding/not-remind",
     *   summary = "不再提示教學訊息框",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "不再提示教學訊息框",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "type",
     *     in = "formData",
     *     description = "類型 edit:編輯 rank:評價",
     *     type = "string",
     *     enum = {"edit", "rank"},
     *     default = "edit"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function notRemind(
        ApiFormatter $formatter
    ) {
        $user = request('user');
        $user->wedding()->updateOrCreate([], ['not_remind_'.request('type') => 1]);

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/user/wedding/update-date",
     *   summary = "更新婚禮形式日期",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "更新婚禮形式日期",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "type",
     *     in = "formData",
     *     description = "婚禮形式 1:訂婚 2:結婚 3:訂結同天 4:歸寧",
     *     required = true,
     *     type = "integer",
     *     enum = {1, 2, 3, 4},
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "date",
     *     in = "formData",
     *     description = "日期",
     *     required = true,
     *     type = "string",
     *     default = "2019-09-01"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function updateDate(
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');
        $type = request('type');
        $date = request('date');

        // 取得婚禮形式
        $weddingType = $user->weddingTypes()
                            ->withTrashed()
                            ->updateOrCreate(['type' => $type], ['deleted_at' => NULL]);

        // 更新婚禮形式的婚期
        $weddingType->updateDate($date);

        // Transformer
        $result = $weddingTransformer->getWeddingTypeInfo($weddingType, $user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/wedding/update-schedule",
     *   summary = "更新黃金團隊進度",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "更新婚禮形式中黃金團隊的進度",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "wedding_type",
     *     in = "formData",
     *     description = "婚禮形式 1:訂婚 2:結婚 3:訂結同天 4:歸寧",
     *     required = true,
     *     type = "integer",
     *     enum = {1, 2, 3, 4},
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "team_type",
     *     in = "formData",
     *     description = "團隊類型 1:拍婚紗 2:婚紗禮服 301:婚禮攝影 302:婚禮錄影 4:新娘秘書 5:婚宴場地 6:婚禮佈置 8:婚禮主持人 9:婚戒/金飾 11:西服 12:喜餅",
     *     required = true,
     *     type = "integer",
     *     enum = {1, 2, 301, 302, 4, 5, 6, 8, 9, 11, 12},
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "formData",
     *     description = "團隊狀態 pending:尋找中 booked:已下訂 nullable:不需要",
     *     required = true,
     *     type = "string",
     *     enum = {"pending", "booked", "nullable"},
     *     default = "booked"
     *   ),
     *   @SWG\ Parameter(
     *     name = "brand_ids[]",
     *     in = "formData",
     *     description = "品牌編號",
     *     required = true,
     *     type = "array",
     *     items = {
     *         "type": "integer",
     *     },
     *     collectionFormat = "multi",
     *     default = {217, 880}
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function updateSchedule(
        UpdateScheduleService $updateScheduleService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $updateScheduleService->run($request);

        return $formatter->json();
    }

    /**
     * 婚禮中心-我的婚禮-取得品牌列表
     *
     * @url api/user/wedding/brands
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @param string $team_type 團隊類型
     * @param string $keyword 搜尋關鍵字
     * @param string $name 商家名稱
     * @param string $website 官網連結
     * @param string $fb_page FB粉絲團連結
     * @param string $instagram Instagram
     * @param string $email Email
     * @param string $phone 行動電話
     * @param string $tel 市話
     * @return json
     */
    public function getBrands(
        GetBrandService $getBrandService,
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $keyword = request('keyword');
        $user    = request('user');

        // Service
        $data = $getBrandService->run($request);

        // Transformer
        $result = $weddingTransformer->getBrandList($data, $user);

        return $formatter->json([
            'keyword' => $keyword,
            'list'    => $result,
        ]);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/wedding/brand",
     *   summary = "新增品牌",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "新增品牌",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "brand_id",
     *     in = "formData",
     *     description = "品牌編號",
     *     type = "integer",
     *     default = 4747
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "商家名稱",
     *     required = true,
     *     type = "string",
     *     default = "卡樂思影像/專業錄影團隊"
     *   ),
     *   @SWG\ Parameter(
     *     name = "website",
     *     in = "formData",
     *     description = "官網連結",
     *     type = "string",
     *     default = "https://colorseed.tw"
     *   ),
     *   @SWG\ Parameter(
     *     name = "fb_page",
     *     in = "formData",
     *     description = "FB粉絲團連結",
     *     type = "string",
     *     default = "https://www.facebook.com/colorseedchoupom"
     *   ),
     *   @SWG\ Parameter(
     *     name = "email",
     *     in = "formData",
     *     description = "Email",
     *     type = "string",
     *     default = "<EMAIL>"
     *   ),
     *   @SWG\ Parameter(
     *     name = "phone",
     *     in = "formData",
     *     description = "電話",
     *     type = "string",
     *     default = "0912345678"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */

    /**
     * 婚禮中心-我的婚禮-新增品牌
     *
     * @url api/user/wedding/brand
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @param int $brand_id 品牌編號
     * @param string $name 商家名稱
     * @param string $website 官網連結
     * @param string $fb_page FB粉絲團連結
     * @param string $instagram Instagram
     * @param string $email Email
     * @param string $phone 行動電話
     * @param string $tel 市話
     * @return json
     */
    public function createBrand(
        CreateBrandService $createBrandService,
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $user    = request('user');

        // Service
        $data = $createBrandService->run($request);

        // Transformer
        $result = $weddingTransformer->getBrandInfo($data, $user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/user/wedding/edit",
     *   summary = "編輯我的婚禮",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "我的婚禮編輯頁頁面資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function edit(
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // Transformer
        $result = $weddingTransformer->edit($user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/wedding/update",
     *   summary = "更新我的婚禮",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "更新我的婚禮",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "name_groom",
     *     in = "formData",
     *     description = "新郎暱稱",
     *     type = "string",
     *     default = "宋基基"
     *   ),
     *   @SWG\ Parameter(
     *     name = "name_bride",
     *     in = "formData",
     *     description = "新娘暱稱",
     *     type = "string",
     *     default = "詹安安"
     *   ),
     *   @SWG\ Parameter(
     *     name = "show_gold_team",
     *     in = "formData",
     *     description = "公開我的黃金團隊",
     *     required = true,
     *     type = "string",
     *     enum = {0, 1},
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "types[]",
     *     in = "formData",
     *     description = "婚禮形式 1:訂婚 2:結婚 3:訂結同天 4:歸寧",
     *     required = true,
     *     type = "array",
     *     items = {
     *         "type": "integer",
     *         "enum": {1, 2, 3, 4},
     *     },
     *     collectionFormat = "multi",
     *     default = "1,2"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function update(
        UpdateService $updateService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $updateService->run($request);

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/user/wedding/upload-avatar",
     *   summary = "上傳新郎/新娘頭像",
     *   tags = {"前台-婚禮中心-我的婚禮：/user/wedding"},
     *   description = "上傳新郎/新娘頭像",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "type",
     *     in = "formData",
     *     description = "bride:新娘 groom:新郎",
     *     required = true,
     *     type = "string",
     *     enum = {"bride", "groom"},
     *     default = "bride"
     *   ),
     *   @SWG\ Parameter(
     *     name = "image_base64",
     *     in = "formData",
     *     description = "base64圖片",
     *     required = true,
     *     type = "string",
     *     default = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAM0AAAD
 NCAMAAAAsYgRbAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5c
 cllPAAAABJQTFRF3NSmzMewPxIG//ncJEJsldTou1jHgAAAARBJREFUeNrs2EEK
 gCAQBVDLuv+V20dENbMY831wKz4Y/VHb/5RGQ0NDQ0NDQ0NDQ0NDQ0NDQ
 0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0PzMWtyaGhoaGhoaGhoaGhoaGhoxtb0QGho
 aGhoaGhoaGhoaGhoaMbRLEvv50VTQ9OTQ5OpyZ01GpM2g0bfmDQaL7S+ofFC6x
 v3ZpxJiywakzbvd9r3RWPS9I2+MWk0+kbf0Hih9Y17U0nTHibrDDQ0NDQ0NDQ0
 NDQ0NDQ0NTXbRSL/AK72o6GhoaGhoRlL8951vwsNDQ0NDQ1NDc0WyHtDTEhD
 Q0NDQ0NTS5MdGhoaGhoaGhoaGhoaGhoaGhoaGhoaGposzSHAAErMwwQ2HwRQ
 AAAAAElFTkSuQmCC"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function uploadAvatar(
        UploadImageFormService $uploadImageFormService,
        CreateImageService $createImageService,
        ApiFormatter $formatter
    ) {
        // 上傳圖片
        $result = $uploadImageFormService->uploadBase64(request('image_base64'));

        // 更新頭像
        $user = request('user');
        $user->wedding()->updateOrCreate([]);

        // 更新上傳的圖檔的來源
        $createImageService->add([
            'file_name' => $result['file_name'],
            'type'      => 'wedding_'.request('type'),
            'target_id' => $user->id,
            'only'      => true,
        ]);

        return $formatter->json($result);
    }
}
