<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\User\Collect\IndexService;
use App\Services\User\Collect\IndexMoreService;
use App\Services\User\Collect\UpdateService;
use App\Transformers\User\CollectTransformer;
use App\Formatters\ApiFormatter;

class CollectController extends Controller
{
    /**
     * 我的收藏
     *
     * @url api/user/collect
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人 *
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param int $per_page 每頁數量
     * @return json
     */
    public function index(
        IndexService $indexService,
        CollectTransformer $collectTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 每頁預設50筆
        $request['per_page'] = request('per_page', 50);

        // Service
        $data = $indexService->run($request);

        // Transformer
        $result = $collectTransformer->index($data);

        return $formatter->json($result);
    }

    /**
     * 我的收藏-載入更多
     *
     * @url api/user/collect/more
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人 *
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param string $type 類別 store:商家 service:服務方案 work:作品 venue_room:婚宴場地廳房 *
     * @param int $per_page 每頁數量
     * @param int $page 頁碼
     * @return json
     */
    public function indexMore(
        IndexMoreService $indexMoreService,
        CollectTransformer $collectTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 每頁預設50筆
        $request['per_page'] = request('per_page', 50);

        // Service
        $data = $indexMoreService->run($request);

        // Transformer
        $result = $collectTransformer->indexMore($data);

        return $formatter->json($result);
    }

    /**
     * 更新收藏
     *
     * @url api/user/collect
     * @method POST
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人 *
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param string $type 類別 store:商家 service:服務方案 work:作品 venue_room:婚宴場地廳房 *
     * @param int $target_id 所屬類型ID *
     * @param int $collect 是否收藏 *
     * @return json
     */
    public function update(
        UpdateService $updateService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $updateService->run($request);

        return $formatter->json();
    }
}
