<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Services\User\Main\ShowService;
use App\Services\User\Main\ShowMoreService;
use App\Models\LogSetBrowserNotify;
use App\Transformers\User\MainTransformer;
use App\Formatters\ApiFormatter;

class MainController extends Controller
{
    /**
     * 個人主頁
     *
     * @url api/user/main/{user_id}
     * @method GET
     * @header string Access-Token *
     * @path int $user_id 使用者ID *
     * @param int $per_page 每頁數量
     * @return json
     */
    public function show(
        ShowService $showService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        $user_id
    ) {
        // Request
        $request = request();

        // 每頁預設4筆
        $request['per_page'] = request('per_page', 4);

        // Service
        $data = $showService->run($user_id, $request);

        // Transformer
        $result = $mainTransformer->show($data);

        return $formatter->json($result);
    }

    /**
     * 個人主頁-載入更多
     *
     * @url api/user/main/{user_id}/more
     * @method GET
     * @header string Access-Token *
     * @path int $user_id 使用者ID *
     * @param string $type 類別 articles:聊聊文章 article_comments:聊聊文章回應 *
     * @param int $page 頁碼
     * @param int $per_page 每頁數量
     * @return json
     */
    public function showMore(
        ShowMoreService $showMoreService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        $user_id
    ) {
        // Request
        $request = request();

        // 每頁預設4筆
        $request['per_page'] = request('per_page', 4);

        // Service
        $data = $showMoreService->run($user_id, $request);

        // Transformer
        $result = $mainTransformer->showMore($data);

        return $formatter->json($result);
    }

    /**
     * 更新瀏覽器推播通知
     *
     * @url api/user/web-notification
     * @method POST
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @param bool $is_approve 是否同意
     * @param array $tokens Token列表 (* required if is_approve)
     * @param bool $btn_action 按鈕動作 0:暫不需要 1:好的 *
     * @param string $initial_value 初始值 default:預設 granted:允許 denied:封鎖 *
     * @param string $action_value 設定值 (空值):無 default:預設 granted:允許 denied:封鎖
     * @param string $device 裝置系統 *
     * @param string $browser 瀏覽器 *
     * @param string $version 瀏覽器版本
     * @return json
     */
    public function updateWebNotification(
        LogSetBrowserNotify $logSetBrowserNotify,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 更新瀏覽器推播通知
        if (!is_null(request('is_approve'))) {
            $user->webNotification()->updateOrCreate([
                'target_type' => 'user',
            ], [
                'canceled_at' => request('is_approve') ? NULL : now(),
                'tokens'      => request('tokens'),
            ]);
        }

        // 新增瀏覽器通知的設定紀錄
        $logSetBrowserNotify->create([
            'target_type'   => 'user',
            'target_id'     => $user->id,
            'btn_action'    => request('btn_action'),
            'initial_value' => request('initial_value'),
            'initial_value' => request('initial_value'),
            'action_value'  => request('action_value'),
            'device'        => request('device'),
            'browser'       => request('browser'),
            'version'       => request('version'),
        ]);

        return $formatter->json();
    }
}
