<?php

namespace App\Http\Controllers\User\Yzcube;

use App\Formatters\ApiFormatter;
use App\Repositories\UserQuoteRepository;
use App\Transformers\User\Yzcube\QuoteTransformer;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class QuoteController extends Controller
{
    /**
     * 公開詢價列表
     *
     * @url api/user/yzcube/quote
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $start_date 起始時間 Y-m-d
     * @param string $end_date 結束時間 Y-m-d
     * @param string $status 狀態 valid:有效 invalid:停用
     * @param string $store_type 商家類別 3:婚攝/婚錄 4:新娘秘書 6:婚禮佈置 8:婚禮主持人
     * @param string $city_code 地區 0:全部 1:台北市 2:新北市 ...
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $page 頁碼
     * @return json
     */
    public function index(
        UserQuoteRepository $userQuoteRepository,
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    )
    {
        $request = request();
        $request->validate([
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date'   => 'nullable|date_format:Y-m-d',
        ]);
        $userQuotes = $userQuoteRepository->getYzcubeListByRequest($request);
        $result = $quoteTransformer->list($userQuotes);
        return $formatter->json($result);
    }

    /**
     * 匯出公開詢價
     *
     * @url api/user/yzcube/quote/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $start_date 起始時間 Y-m-d
     * @param string $end_date 結束時間 Y-m-d
     * @param string $status 狀態 valid:有效 invalid:停用
     * @param string $store_type 商家類別 3:婚攝/婚錄 4:新娘秘書 6:婚禮佈置 8:婚禮主持人
     * @param string $city_code 地區 0:全部 1:台北市 2:新北市 ...
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @return json
     */
    public function exportCSV(
        UserQuoteRepository $userQuoteRepository,
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    ) {
        $request = request();
        $userQuotes = $userQuoteRepository->getYzcubeListByRequest($request, false);

        // Transformer
        $result = $quoteTransformer->exportCSV($userQuotes);
        $filename = '[公開詢價紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 公開詢價內容
     *
     * @url api/user/yzcube/quote/{quote_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $quote_id 公開詢價ID
     * @return json
     */
    public function show(
        $quote_id,
        UserQuoteRepository $userQuoteRepository,
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    )
    {
        $data = $userQuoteRepository->getContentByQuoteID($quote_id);
        $result = $quoteTransformer->show($data);
        return $formatter->json($result);
    }
}
