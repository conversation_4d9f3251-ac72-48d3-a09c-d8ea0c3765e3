<?php
/*--------------------------------
 | 神之後台 新娘管理 Controller
 |--------------------------------
 |
 |
 |
 */

namespace App\Http\Controllers\User\Yzcube;

use App\Http\Controllers\Controller;
use App\Formatters\ApiFormatter;
use App\Models\AuthToken;
use App\Models\EventReport;
use App\Models\EmailLegalize;
use App\Repositories\UserRepository;
use App\Services\Auth\SendPhoneTokenService;
use App\Services\Forum\Yzcube\Article\GetListService as GetForumListService;
use App\Services\Mail\Auth\EmailAuthKeyService;
use App\Services\User\Collect\IndexService;
use App\Services\User\Yzcube\DashboardService;
use App\Services\User\Yzcube\GenUserTokenService;
use App\Transformers\Event\Yzcube\EventTransformer;
use App\Transformers\Forum\Yzcube\ArticleTransformer;
use App\Transformers\User\CollectTransformer;
use App\Transformers\User\QuoteTransformer;
use App\Transformers\User\WeddingTransformer;
use App\Transformers\User\Yzcube\UserTransformer;
use App\Transformers\Store\Yzcube\ReserveTransformer;
use App\Transformers\Yzcube\FeedbackTransformer;
use App\Transformers\Yzcube\AuthTransformer;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;

class UserController extends Controller
{
    use ApiErrorTrait;
    use RandStringTrait;

    /**
     * 資訊看板
     *
     * @url user/yzcube/report
     * @method GET
     * @haeder User-Token
     *
     * @return json
     */
    public function dashboard(
        DashboardService $dashboardService,
        ApiFormatter $formatter
    )
    {
        $request = request();

        $result = $dashboardService->run($request);

        // Transformer
        //$result = $profileTransformer->show($user);

        return $formatter->json($result);
    }

    /**
     * 新娘列表
     *
     * @url user/yzcube/bride
     * @method GET
     * @header Yzcube-Token *
     * @param string $status 狀態 valid:有效 invalid:無效
     * @param string $start_date 起始時間 Y-m-d
     * @param string $end_date 結束時間 Y-m-d
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $page 頁碼
     * @return json
     */
    public function index(
        UserRepository $userRepository,
        ApiFormatter $formatter,
        UserTransformer $userTransformer
    )
    {
        // Request
        $request = request();
        $request->validate([
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d',
        ]);
        $data = $userRepository->getYzcubeListByRequest($request);
        $result = $userTransformer->list($data);
        return $formatter->json($result);
    }

    /**
     * 新娘資料
     *
     * @url user/yzcube/bride/{user_id}
     * @method GET
     * @header Yzcube-Token *
     * @param int $user_id 使用者ID *
     * @return json
     */
    public function show(
        ApiFormatter $formatter,
        UserTransformer $userTransformer,
        WeddingTransformer $weddingTransformer,
        ReserveTransformer $reserveTransformer,
        QuoteTransformer $quoteTransformer,
        IndexService $indexService,
        CollectTransformer $collectTransformer,
        GetForumListService $getForumListService,
        ArticleTransformer $articleTransformer,
        FeedbackTransformer $feedbackTransformer,
        EventTransformer $eventTransformer,
        AuthTransformer $authTransformer,
        EventReport $eventReport
    )
    {
        ini_set('memory_limit', '-1');
        set_time_limit(300);//時間設定在5分鐘

        // 取得使用者
        $user = request('user');

        /** Transformer */
        $result = [];
        ///基本資料
        $result['profile'] = $userTransformer->yzcubeUserPage($user);

        ///電子報設定
        $result['subscriptions'] = $result['profile']['subscriptions'];
        unset($result['profile']['subscriptions']);

        ///婚期
        $result['wedding'] = $weddingTransformer->yzcubeUserPage($user);

        ///預約單
        $result['reserve'] = $reserveTransformer->yzcubeUserPage($user);

        ///詢價
        $result['quote'] = $quoteTransformer->yzcubeUserPage($user);

        ///收藏櫃
        $data = $indexService->runYzcube($user);
        $result['collect'] = $collectTransformer->yzcubeUserPage($data);

        ///好婚聊聊
        $forumData = $getForumListService->getBoth($user);
        $result['forum'] = $articleTransformer->yzcubeUserPage($forumData);

        ///意見回饋
        $feedback = $user->feedback;
        $result['feedback'] = $feedbackTransformer->yzcubeUserPage($feedback);

        ///活動報名
        $event = $eventReport->where('user_id', $user->id)->with(['event'])->get();
        $result['event'] = $eventTransformer->yzcubeUserPage($event);

        ///登入者的身份驗證紀錄
        $result['auth_logs'] = $authTransformer->getLogAuthToken($user);

        return $formatter->json($result);
    }

    /**
     * 更改電話
     *
     * @url api/user/yzcube/bride/{user_id}/edit-phone
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $user_id 使用者ID *
     * @param string $phone 電話 *
     * @return json
     */
    public function editPhone(
        SendPhoneTokenService $sendPhoneTokenService,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        $phone = empty(request('phone')) ? null : request('phone');
        // 驗證手機是否重複
        if ($phone) {
            $sendPhoneTokenService->validateUniquePhone($user->id, $phone, 'user');
        }

        // 更改電話
        $user->phone = request('phone');
        $user->phone_legalize = 0;
        $user->save();

        return $formatter->json();
    }

    /**
     * 重新寄送手機驗證簡訊
     *
     * @url api/user/yzcube/bride/{user_id}/resend-phone-auth-key
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $user_id 使用者ID *
     * @return json
     */
    public function resendPhoneAuthKey(
        ApiFormatter $formatter,
        SendPhoneTokenService $sendPhoneTokenService
    )
    {
        // 取得使用者
        $user = request('user');

        if ($user->phone) {
            // Service
            $data = $sendPhoneTokenService->send($user->id, $user->phone, 'user', 'yzcube');
        } else {
            $this->setException('請先設定手機號碼', 4000, ['phone' => '請先設定手機號碼']);
        }

        return $formatter->json($data, ['message' => '請收手機簡訊驗證碼！']);
    }

    /**
     * 更改信箱
     *
     * @url api/user/yzcube/bride/{user_id}/edit-email
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $user_id 使用者ID *
     * @param string $email 信箱 *
     * @return json
     */
    public function editEmail(
        AuthToken $authToken,
        EmailAuthKeyService $emailAuthKeyService,
        ApiFormatter $formatter
    ) {
        // 必填欄位
        try {
            $this->validate(request(), [
                'email' => 'required|email',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // 取得使用者
        $user = request('user');

        // 驗證信箱是否重複
        $email = request('email');
        $emailAuthKeyService->validateUniqueEmail('user', $email, $user->id);

        // 更改信箱
        $user->email = $email;
        $user->email_legalize = 0;
        $user->save();

        // 強制登出
        $authToken->where('target_id', $user->id)->where('type', 'user')->delete();

        return $formatter->json();
    }

    /**
     * 重新寄送信箱驗證信
     *
     * @url api/user/yzcube/bride/{user_id}/resend-email-auth-key
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $user_id 使用者ID *
     * @return json
    */
    public function resendEmailAuthKey(
        EmailLegalize $authKey,
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    )
    {
        // 取得使用者
        $user = request('user');

        preg_match('/^(.*?)@/', $user->email, $matches);
        $mailName = $matches[1] ?? '';
        $recipient = isset($user->name) ? $user->name : $mailName;
        $mailType = 'user_email';

        // 驗證碼寫進資料庫
        $authKey = $authKey->create([
            'email'       => $user->email,
            'key'         => $this->getRandString(6),
            'source'      => 'yzcube',
            'recipient'   => $recipient,
            'deadline_at' => now()->addHour(24),
        ]);

        // 寄出驗證信
        $emailAuthKeyService->sendAuthMail($authKey, $mailType);
        return $formatter->json(NULL, ['message' => '請至您的「 '.$user->email.' 」信箱收取驗證信！']);
    }

    /**
     * 盜帳號
     *
     * @url api/user/yzcube/bride/{user_id}/steal-token
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $user_id 使用者ID *
     * @return json
     */
    public function stealAccount(
        GenUserTokenService $genUserTokenService,
        ApiFormatter $formatter
    )
    {
        // 取得使用者
        $user = request('user');

        // 取得 user token
        $token = $genUserTokenService->generate($user);
        return $formatter->json(['user_token' => $token]);
    }

    /**
     * 變更新娘帳號狀態
     *
     * @url api/user/yzcube/bride/{user_id}/status
     * @method PUT
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $user_id 使用者ID *
     * @param int $status 狀態 0:停用 1:啟用 *
     * @return json
     */
    public function changeStatus(
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    )
    {
        // 取得使用者
        $user = request('user');

        // 變更狀態
        $origStatus = $user->status; //先取出原始的狀態
        $status = request('status');

        if ($status == 1){
            if($user->email_legalize == 1){
                $newStatus = 'published';
            } else {
                $newStatus = 'pending';
            }
        } elseif($status == 0){
            $newStatus = 'delete';
        }
        $user->status = $newStatus;
        $user->disabled_at = $status == 0 ? date('Y-m-d H:i:s') : null;
        $user->save();

        //如果是從停用變啟用..就寄送認證信
        if ($origStatus == 'delete' && $status == 1) {
            $emailAuthKeyService->sendReEnableMail($user, 'user');
        }

        //回傳帳號信用時間
        $result = $user->disabled_at;
        return $formatter->json($result);
    }

    /**
     * 更改手機驗證狀態
     *
     * @url api/user/yzcube/bride/{user_id}/phone-status
     * @method PUT
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param int $status 狀態 0:未驗證 1:已驗證 *
     * @return json
     */
    public function changePhoneStatus(
        ApiFormatter $formatter,
        SendPhoneTokenService $sendPhoneTokenService
    )
    {
        // 取得使用者
        $user = request('user');

        // 改成已驗證要先確認在資料庫中是否為唯一手機
        if (request('status') == 1) {
            if ($user->phone) {
                $sendPhoneTokenService->validateUniquePhone($user->id, $user->phone, 'user');
            } else {
                $this->setException('請先設定手機號碼', 4000, ['phone' => '請先設定手機號碼']);
            }
        }

        $status = request('status');
        $user->phone_legalize = $status;
        $user->save();

        return $formatter->json(NULL, ['message' => '手機驗證狀態更改成功']);
    }

    /**
     * 更改信箱驗證狀態
     *
     * @url api/user/yzcube/bride/{user_id}/email-status
     * @method PUT
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param int $status 狀態 0:未驗證 1:已驗證 *
     * @return json
     */
    public function changeEmailStatus(
        AuthToken $authToken,
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    )
    {
        // 取得使用者
        $user = request('user');

        // 改成已驗證要先確認在資料庫中是否為唯一信箱
        if (request('status') == 1) {
            $emailAuthKeyService->validateUniqueEmail('user', $user->email, $user->id);
        } else {
            // 強制登出
            $authToken->where('target_id', $user->id)->where('type', 'user')->delete();
        }

        $status = request('status');
        $user->email_legalize = $status;
        $user->save();

        return $formatter->json(NULL, ['message' => '信箱驗證狀態更改成功']);
    }

    /**
     * 移除黑名單
     *
     * @url api/user/yzcube/bride/{user_id}/remove-blacklist
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @return json
     */
    public function removeBlacklist(
        ApiFormatter $formatter
    )
    {
        // 取得使用者
        $user = request('user');

        if ($user->mailBase && $user->mailBase->valid == 0) {
            $user->mailBase->valid = 1;
            $user->mailBase->save();
        }

        return $formatter->json(NULL, ['message' => '解除黑名單成功']);
    }
}
