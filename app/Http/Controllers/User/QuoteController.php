<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\QuoteRequest;
use App\Services\User\Quote\StoreService;
use App\Models\Wdv2\UserQuote;
use App\Models\Wdv2\StoreQuote;
use App\Traits\ApiErrorTrait;
use App\Transformers\User\QuoteTransformer;
use App\Formatters\ApiFormatter;

class QuoteController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Get(
     *   path = "/user/quote/create",
     *   summary = "我要詢價",
     *   tags = {"前台-婚禮中心-主動報價：/user/quote"},
     *   description = "我要詢價表單資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function create(
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // Transformer
        $result = $quoteTransformer->create($user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/quote",
     *   summary = "新增我要詢價",
     *   tags = {"前台-婚禮中心-主動報價：/user/quote"},
     *   description = "新增我要詢價表單",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "store_type",
     *     in = "formData",
     *     description = "商家類型 3:婚攝/婚錄 4:新娘秘書 6:婚禮佈置 8:婚禮主持人",
     *     required = true,
     *     type = "integer",
     *     enum = {3, 4, 6, 7, 8},
     *     default = 3
     *   ),
     *   @SWG\ Parameter(
     *     name = "wedding_type",
     *     in = "formData",
     *     description = "婚禮形式 1:訂婚 2:結婚 3:訂結同天 4:歸寧",
     *     required = true,
     *     type = "integer",
     *     enum = {1, 2, 3, 4},
     *     default = 2
     *   ),
     *   @SWG\ Parameter(
     *     name = "wedding_date",
     *     in = "formData",
     *     description = "婚期",
     *     required = true,
     *     type = "string",
     *     default = "2020-12-12"
     *   ),
     *   @SWG\ Parameter(
     *     name = "city_code",
     *     in = "formData",
     *     description = "婚宴地區 1:台北市 2:新北市 3:基隆市 4:桃園市 5:新竹縣 6:新竹市 7:宜蘭市 8:台中市 9:彰化縣 10:苗栗縣 11:雲林縣 12:南投縣 13:高雄市 14:屏東縣 15:台南市 16:嘉義市 17:嘉義縣 18:花蓮縣 19:台東縣 20:澎湖縣 21:金門縣 22:連江縣",
     *     type = "integer",
     *     enum = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22},
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "wedding_venue_type",
     *     in = "formData",
     *     description = "婚宴場地類型 1:飯店 2:婚宴會館 3:餐廳 4:流水席 5:自宅",
     *     type = "integer",
     *     enum = {1, 2, 3, 4, 5},
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "wedding_venue",
     *     in = "formData",
     *     description = "婚宴場地",
     *     type = "string",
     *     default = "萬豪大飯店"
     *   ),
     *   @SWG\ Parameter(
     *     name = "detail",
     *     in = "formData",
     *     description = "不同商家類型的詳細內容",
     *     type = "string",
     *     default = {
     *         "ceremony":"morning",
     *         "time":"lunch",
     *         "amount":3,
     *         "min_budget":10000,
     *         "max_budget":14000,
     *         "extra_amount":"媽媽妝,婆婆妝,伴娘妝,親友妝,新郎妝",
     *         "extra_service":"試妝,免車馬費,妝前保養,安瓶,噴槍彩妝,身體水粉,鮮花造型,乾燥/不凋花藝頭飾,飾品配件,指甲油",
     *         "remark":"我是備註訊息!!"
     *     },
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     *
     */
    public function store(
        QuoteRequest $request,
        StoreService $storeService,
        ApiFormatter $formatter
    ) {
        // Service
        $userQuote = $storeService->run($request);

        return $formatter->json(['quote_id' => $userQuote->id]);
    }

    /**
     * @SWG\ Get(
     *   path = "/user/quote",
     *   summary = "我的詢價紀錄",
     *   tags = {"前台-婚禮中心-主動報價：/user/quote"},
     *   description = "我的詢價紀錄頁面資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function index(
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // Transformer
        $result = $quoteTransformer->index($user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/quote/{quote_id}/close",
     *   summary = "我的詢價紀錄-關閉需求",
     *   tags = {"前台-婚禮中心-主動報價：/user/quote"},
     *   description = "我的詢價紀錄-關閉需求",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "quote_id",
     *     in = "path",
     *     description = "詢價單編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "feedback",
     *     in = "formData",
     *     description = "意見回饋 found_wd:在WD找到商家 found_other:在其他地方找到商家 silent:不想再接收訊息 unhelp:沒有幫助 other:其他",
     *     required = true,
     *     type = "string",
     *     enum = {"found_wd", "found_other", "silent", "unhelp", "other"},
     *     default = "other"
     *   ),
     *   @SWG\ Parameter(
     *     name = "feedback_other",
     *     in = "formData",
     *     description = "其他意見回饋",
     *     type = "string",
     *     default = "打錯需求了！"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function close(
        $quote_id,
        UserQuote $userQuote,
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者、主動報價數據
        $user      = request('user');
        $userQuote = $userQuote->where('user_id', $user->id)
                                ->find($quote_id);
        if (!$userQuote) {
            $this->setException('找不到此詢價紀錄！');
        }

        // 關閉需求
        $userQuote->show_flag = 0;
        $userQuote->feedback  = request('feedback');
        if (request('feedback') == 'other') {
            $_detail = $userQuote->detail;
            $_detail->feedback = request('feedback_other');
            $userQuote->detail = $_detail;
        }
        $userQuote->save();

        return $formatter->json();
    }

    /**
     * @SWG\ Get(
     *   path = "/user/quote/{quote_id}",
     *   summary = "我的詢價紀錄-商家報價列表",
     *   tags = {"前台-婚禮中心-主動報價：/user/quote"},
     *   description = "我的詢價紀錄-商家報價列表頁面資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "quote_id",
     *     in = "path",
     *     description = "詢價單編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function storeReplyList(
        $quote_id,
        UserQuote $userQuote,
        QuoteTransformer $quoteTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者、主動報價數據
        $user      = request('user');
        $userQuote = $userQuote->where('user_id', $user->id)
                                ->find($quote_id);
        if (!$userQuote) {
            $this->setException('找不到此詢價紀錄！');
        }

        // Transformer
        $result = $quoteTransformer->storeReplyList($user, $userQuote);

        // 已讀所有商家的報價
        $userQuote->storeQuotes()->update(['read' => 1]);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/quote/{quote_id}/like/{reply_id}",
     *   summary = "我的詢價紀錄-按喜歡",
     *   tags = {"前台-婚禮中心-主動報價：/user/quote"},
     *   description = "我的詢價紀錄-商家報價按喜歡鈕",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "quote_id",
     *     in = "path",
     *     description = "詢價單編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "reply_id",
     *     in = "path",
     *     description = "商家報價編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "like",
     *     in = "formData",
     *     description = "是否喜歡",
     *     required = true,
     *     type = "integer",
     *     enum = {0, 1},
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function likeStoreReply(
        $quote_id,
        $reply_id,
        StoreQuote $storeQuote,
        ApiFormatter $formatter
    ) {
        // 取得使用者、主動報價數據
        $user       = request('user');
        $storeQuote = $storeQuote->whereHas('userQuote', function ($q) use ($user, $quote_id) {
                                    $q->where('user_id', $user->id)
                                        ->where('id', $quote_id);
                                })
                                ->find($reply_id);
        if (!$storeQuote) {
            $this->setException('找不到此詢價的商家報價紀錄！');
        }

        $storeQuote->is_like = request('like');
        $storeQuote->save();

        return $formatter->json();
    }
}
