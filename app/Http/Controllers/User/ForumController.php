<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Transformers\User\ForumTransformer;
use App\Formatters\ApiFormatter;

class ForumController extends Controller
{
    /**
     * @SWG\ Get(
     *   path = "/user/forum",
     *   summary = "我的討論",
     *   tags = {"前台-婚禮中心-我的討論：/user/forum"},
     *   description = "我的討論頁面資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function index(
        CategoryRepository $categoryRepository,
        ForumTransformer $forumTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者 & 使用者的討論，排除悄悄話&(軟)刪除文章和留言
        $user       = request('user');
        $articles   = $user->articles()->status('published')->get();
        $comments   = $user->comments()->status('published')->autoReply(0)->sortCreatedAt()->get();
        $categories = $categoryRepository->getSimpleList();

        // Transformer
        $result = $forumTransformer->index($user, $articles, $comments, $categories);

        return $formatter->json($result);
    }
}
