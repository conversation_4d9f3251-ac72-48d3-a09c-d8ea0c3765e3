<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\FeedbackRequest;
use App\Models\Feedback;
use App\Services\Image\CreateImageService;
use App\Services\Mail\User\FeedbackService;
use App\Formatters\ApiFormatter;

class FeedbackController extends Controller
{
    /**
     * @SWG\ Post(
     *   path = "/user/feedback",
     *   summary = "新增意見回饋",
     *   tags = {"前台-婚禮中心-意見回饋：/user/feedback"},
     *   description = "新增意見回饋",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "content",
     *     in = "formData",
     *     description = "詳細內容",
     *     required = true,
     *     type = "string",
     *     default = "歡迎在這邊留下您的問題或建議 :)"
     *   ),
     *   @SWG\ Parameter(
     *     name = "images[]",
     *     in = "formData",
     *     description = "已上傳圖檔名稱",
     *     type = "array",
     *     items = {
     *         "type": "string",
     *     },
     *     collectionFormat = "multi",
     *     default = {"7be4bb148cd354411907e5b3bb1c69525b73a79857122.jpg", "bc7adf138475277f19d0790a951560565b73a7f453d4d.jpg"}
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function store(
        FeedbackRequest $request,
        Feedback $feedback,
        CreateImageService $createImageService,
        FeedbackService $feedbackService,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = $request['user'];

        // 新增意見回饋
        $feedback->identity = 'user';
        $feedback->user_id  = $user->id;
        $feedback->content  = $request['content'];
        $feedback->save();

        // 更新上傳的圖檔的來源
        foreach ($request['images'] as $image) {
            $createImageService->add([
                'file_name' => $image,
                'type'      => 'feedback',
                'target_id' => $feedback->id,
            ]);
        }

        // 意見回饋通知信
        $feedbackService->sendMail($feedback);

        return $formatter->json();
    }
}
