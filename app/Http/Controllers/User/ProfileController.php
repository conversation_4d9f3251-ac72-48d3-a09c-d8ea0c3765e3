<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\ProfileRequest;
use App\Http\Requests\User\EditEmailRequest;
use App\Services\Auth\LoginService;
use App\Services\Mail\User\EditEmailService;
use App\Services\Mail\Auth\EmailAuthKeyService;
use App\Services\File\UploadImageFormService;
use App\Services\Image\CreateImageService;
use App\Models\User;
use App\Models\EmailLegalize;
use App\Repositories\UserRepository;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;
use App\Transformers\User\ProfileTransformer;
use App\Transformers\User\ProfileV2Transformer;
use App\Formatters\ApiFormatter;
use Illuminate\Auth\Events\Login;
use Socialite;

class ProfileController extends Controller
{
    use ApiErrorTrait;
    use RandStringTrait;

    /**
     * @SWG\ Get(
     *   path = "/user/profile",
     *   summary = "個人資訊",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "個人資訊頁面資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function index(
        ProfileTransformer $profileTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // Transformer
        $result = $profileTransformer->index($user);

        return $formatter->json($result);
    }

    /**
     *
     * 婚禮中心 - 個人資料
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/user/profile/v2/index
     * @method GET
     * @return json
     */
    public function indexV2(
        ProfileV2Transformer $profileTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // Transformer
        $result = $profileTransformer->index($user);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/profile",
     *   summary = "修改個人資訊",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "修改個人資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "使用者名稱",
     *     required = true,
     *     type = "string",
     *     default = "Tim"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function update(
        ProfileRequest $request,
        UserRepository $userRepository,
        ApiFormatter $formatter
    ) {
        $user       = $request['user'];
        $user->name = $request['name'];
        $user->save();

        return $formatter->json(NULL, ['message' => '更新基本資料成功！']);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/profile/edit-email",
     *   summary = "更改信箱",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "更改信箱",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "email",
     *     in = "formData",
     *     description = "信箱",
     *     required = true,
     *     type = "string",
     *     default = "<EMAIL>"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function editEmail(
        EditEmailRequest $request,
        UserRepository $userRepository,
        EditEmailService $editEmailService,
        ApiFormatter $formatter
    ) {
        if ($userRepository->getFirst(['email' => $request['email']])) {
            $this->setException('此信箱已有人使用！');
        }

        // 取得 User
        $user  = request('user');
        $token = $this->getRandString(4, ['number']);

        // 驗證碼寫進資料庫
        $user->emailLegalizes()->create([
            'user_id' => $user->id,
            'email'   => $request['email'],
            'token'   => $token,
        ]);

        // WeddingDay 更改信箱驗證信
        $editEmailService->sendMail($user, $request['email'], $token);

        return $formatter->json(NULL, ['message' => '請收信箱驗證碼！']);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/profile/edit-email-verified",
     *   summary = "更改信箱驗證",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "更改信箱驗證",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "email",
     *     in = "formData",
     *     description = "信箱",
     *     required = true,
     *     type = "string",
     *     default = "<EMAIL>"
     *   ),
     *   @SWG\ Parameter(
     *     name = "email_token",
     *     in = "formData",
     *     description = "更改信箱驗證碼",
     *     required = true,
     *     type = "integer",
     *     default = 1234
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function editEmailVerified(
        EditEmailRequest $request,
        UserRepository $userRepository,
        ApiFormatter $formatter
    ) {
        if ($userRepository->getFirst(['email' => $request['email']])) {
            $this->setException('此帳號信箱已重複使用，請重新更改！');
        }

        // 取得 User
        $user  = request('user');

        // 檢查驗證碼
        $userEmailLegalize = $user->emailLegalizes()->where('user_id', $user->id)
                                                    ->where('email', $request['email'])
                                                    ->where('token', $request['email_token'])
                                                    ->first();
        if (!$userEmailLegalize) {
            $this->setException('驗證碼有誤，請重新輸入！');
        }
        $userEmailLegalize->delete();

        $user->email     = $request['email'];
        $user->show_flag = 2;
        $user->save();

        return $formatter->json(NULL, ['message' => '更改帳號信箱成功！']);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/profile/not-remind-avatar",
     *   summary = "不再提示上傳頭像的教學訊息框",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "不再提示上傳頭像的教學訊息框",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function notRemindAvatar(
        ApiFormatter $formatter
    ) {
        $user = request('user');
        $user->not_remind_avatar = 1;
        $user->save();

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/user/profile/upload-avatar",
     *   summary = "上傳大頭照",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "上傳大頭照",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "image_base64",
     *     in = "formData",
     *     description = "base64圖片",
     *     required = true,
     *     type = "string",
     *     default = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAM0AAAD
 NCAMAAAAsYgRbAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5c
 cllPAAAABJQTFRF3NSmzMewPxIG//ncJEJsldTou1jHgAAAARBJREFUeNrs2EEK
 gCAQBVDLuv+V20dENbMY831wKz4Y/VHb/5RGQ0NDQ0NDQ0NDQ0NDQ0NDQ
 0NDQ0NDQ0NDQ0NDQ0NDQ0NDQ0PzMWtyaGhoaGhoaGhoaGhoaGhoxtb0QGho
 aGhoaGhoaGhoaGhoaMbRLEvv50VTQ9OTQ5OpyZ01GpM2g0bfmDQaL7S+ofFC6x
 v3ZpxJiywakzbvd9r3RWPS9I2+MWk0+kbf0Hih9Y17U0nTHibrDDQ0NDQ0NDQ0
 NDQ0NDQ0NTXbRSL/AK72o6GhoaGhoRlL8951vwsNDQ0NDQ1NDc0WyHtDTEhD
 Q0NDQ0NTS5MdGhoaGhoaGhoaGhoaGhoaGhoaGhoaGposzSHAAErMwwQ2HwRQ
 AAAAAElFTkSuQmCC"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function uploadAvatar(
        UploadImageFormService $uploadImageFormService,
        CreateImageService $createImageService,
        ApiFormatter $formatter
    ) {
        // 上傳圖片
        $result = $uploadImageFormService->uploadBase64(request('image_base64'));

        // 更新頭像
        $user = request('user');
        $user->avatar = $result['file_name'];
        $user->save();

        // 更新上傳的圖檔的來源
        $createImageService->add([
            'file_name' => $user->avatar,
            'type'      => 'user_profile',
            'target_id' => $user->id,
            'only'      => true,
        ]);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/user/profile/subscription/{type}",
     *   summary = "電子報設定",
     *   tags = {"前台-婚禮中心-個人資訊：/user/profile"},
     *   description = "使用者選取電子報設定",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "User-Token",
     *     in = "header",
     *     description = "使用者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "e8eb610f8ea23c3da3d8a21ab61ca696a6ff96a7"
     *   ),
     *   @SWG\ Parameter(
     *     name = "type",
     *     in = "path",
     *     description = "類別 store_recommend:商家推薦 forum_new_article:好婚聊聊發佈新文章",
     *     required = true,
     *     type = "string",
     *     enum = {"store_recommend", "forum_new_article"},
     *     default = "store_recommend"
     *   ),
     *   @SWG\ Parameter(
     *     name = "check",
     *     in = "formData",
     *     description = "是否訂閱",
     *     required = true,
     *     type = "boolean",
     *     default = true
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     *
     */
    public function subscription(
        ApiFormatter $formatter,
        $type
    ) {
        // 取得使用者
        $user = request('user');
        $user->subscription()->updateOrCreate(['user_id' => $user->id], [$type => request('check')]);

        return $formatter->json(NULL, ['message' => '更新電子報設定成功！']);
    }

    /**
     *
     * 婚禮中心 寄通知信箱驗證信
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/user/profile/send-notify-email-auth-key
     * @method POST
     * @param string $notify_email 通知信箱 *
     * @return json
     *
     */
    public function sendNotifyEmailAuthKey(
        EmailAuthKeyService $emailAuthKeyService,
        EmailLegalize $authKey,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = request('user');

        // 必填欄位
        try {
            $this->validate(request(), [
                'notify_email' => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        if ($user->notify_email_legalize == 1 && request('notify_email') == $user->notify_email) {
            $this->setException('輸入了相同的信箱 ', 4000, ['notify_email' => '輸入了相同的信箱']);
        }

        // 驗證碼寫進資料庫
        $authKey = $authKey->create([
            'email'       => request('notify_email'),
            'key'         => $this->getRandString(6),
            'recipient'   => $user->name,
            'deadline_at' => now()->addMinutes(5),
        ]);

        // 寄出驗證信
        $emailAuthKeyService->sendAuthMail($authKey, 'user_notify_email');
        return $formatter->json(NULL, ['message' => '請至您的「'.request('notify_email').'」信箱收取驗證信！']);
    }

    /**
     *
     * 婚禮中心 通知信箱驗證
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/user/profile/verify-notify-email-auth-key
     * @method POST
     * @param string $notify_email 通知信箱 *
     * @param string $key 驗證碼 *
     * @return json
     *
     */
    public function verifyNotifyEmailAuthKey(
        EmailLegalize $authKey,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = request('user');

        // 必填欄位
        try {
            $this->validate(request(), [
                'notify_email' => 'required',
                'key'          => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // 檢查驗證碼
        $authKey = $authKey->whereRaw('BINARY `key` = ?', [request('key')])
                                ->where('email', request('notify_email'))
                                ->where('deadline_at', '>', now())
                                ->first();
        if (!$authKey) {
            $this->setException('請輸入正確的 6 位數驗證碼 ', 4000, ['key' => '請輸入正確的 6 位數驗證碼']);
        }

        // 刪除驗證碼
        $authKey->delete();

        // 通知信箱驗證成功
        $user->notify_email = request('notify_email');
        $user->notify_email_legalize = 1;
        $user->save();

        return $formatter->json(NULL, ['message' => '信箱驗證成功！']);
    }

    /**
     *
     * 婚禮中心 - 綁定第三方
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/user/profile/bind-third
     * @method POST
     * @param string $type 第三方登入方式 *
     * @param string $third_id 第三方登入id *
     * @param string $third_name 第三方登入名稱 *
     * @param string $third_avatar 第三方登入頭像 *
     * @return json
     */
    public function bindThird(
        User $model,
        LoginService $loginService,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = request('user');

        // 必填欄位
        try {
            $this->validate(request(), [
                'type'        => 'required|in:fb,line,google',
                'third_id'    => 'required',
                'third_name'  => 'required',
                'third_avatar'=> 'nullable',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // 檢查第三方id是否存在
        if ($model->where(request('type').'_id', request('third_id'))->first()) {
            switch (request('type')) {
                case 'fb':
                    $this->setException('無法綁定，此 Facebook 帳號已與其他登入信箱綁定 ', 4000);
                    break;
                case 'line':
                    $this->setException('無法綁定，此 LINE 帳號已與其他登入信箱綁定 ', 4000);
                    break;
                case 'google':
                    $this->setException('無法綁定，此 Google 帳號已與其他登入信箱綁定 ', 4000);
                    break;
            }
        }

        // 更新第三方id
        $user->{request('type').'_id'} = request('third_id');
        $user->save();

        // 紀錄第三方登入資訊
        $loginService->recordThirdInfo(request()->all(), $user);

        return $formatter->json(NULL, ['message' => '綁定成功！']);
    }

    /**
     *
     * 婚禮中心 - 解除第三方綁定
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/user/profile/unbind-third
     * @method POST
     * @param string $type 第三方登入方式 *
     * @return json
     */
    public function unbindThird(
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = request('user');

        // 必填欄位
        try {
            $this->validate(request(), [
                'type' => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // 解除綁定
        $user->{request('type').'_id'} = NULL;
        $user->save();

        return $formatter->json(NULL, ['message' => '解除綁定成功！']);
    }
}
