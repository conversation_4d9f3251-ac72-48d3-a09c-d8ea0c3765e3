<?php

namespace App\Http\Controllers\Yzcube\AdCampaign;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\AdCampaign\MobileBannerRequest;
use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;
use App\Services\AdCampaign\Yzcube\SaveMobileBannerService;
use App\Transformers\Yzcube\AdCampaign\MobileBannerTransformer;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Formatters\ApiFormatter;

class MobileBannerController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/mobile-banner",
     *   summary = "手機側邊欄Banner-列表",
     *   tags = {"神之後台-廣告活動管理-手機側邊欄Banner：/ad-campaign/yzcube/mobile-banner"},
     *   description = "神之後台-廣告活動管理-手機側邊欄Banner-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "query",
     *     description = "狀態 published:發佈中 scheduling:排程中 completed:已結束",
     *     type = "string",
     *     enum = {"published", "scheduling", "completed"},
     *     default = "published"
     *   ),
     *   @SWG\ Parameter(
     *     name = "keyword",
     *     in = "query",
     *     description = "搜尋關鍵字",
     *     type = "string",
     *     default = "周胖"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        AdCampaignRepository $adCampaignRepository,
        MobileBannerTransformer $mobileBannerTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得手機側邊欄Banner列表
        $adCampaigns = $adCampaignRepository->getYzcubeListByRequest($request, 'mobile_banner', false);

        // Transformer
        $result = $mobileBannerTransformer->list($adCampaigns);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/mobile-banner/sort",
     *   summary = "手機側邊欄Banner-排序",
     *   tags = {"神之後台-廣告活動管理-手機側邊欄Banner：/ad-campaign/yzcube/mobile-banner"},
     *   description = "神之後台-廣告活動管理-手機側邊欄Banner-排序",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "sequence[]",
     *     in = "formData",
     *     description = "編號排序",
     *     required = true,
     *     type = "array",
     *     items = {
     *         "type": "integer",
     *     },
     *     collectionFormat = "multi",
     *     default = {6, 7, 8}
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function sort(
        ApiFormatter $formatter
    ) {
        // Request
        $sequence = request('sequence');
        if (!$sequence) {
            $this->setException('找不到排序參數！');
        }

        // Trait
        $this->sortListBySequence('AdCampaign', $sequence);

        return $formatter->json();
    }

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/mobile-banner/show",
     *   summary = "手機側邊欄Banner-詳細頁",
     *   tags = {"神之後台-廣告活動管理-手機側邊欄Banner：/ad-campaign/yzcube/mobile-banner"},
     *   description = "神之後台-廣告活動管理-手機側邊欄Banner-詳細頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "mobile_banner_id",
     *     in = "query",
     *     description = "手機側邊欄Banner編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository,
        MobileBannerTransformer $mobileBannerTransformer,
        ApiFormatter $formatter
    ) {
        // 取得手機側邊欄Banner
        $data['adCampaign'] = $adCampaignRepository->getFirst(['type' => 'mobile_banner', 'id' => request('mobile_banner_id')]);

        // 取得未使用的廣告素材
        $data['pendingAdImages'] = $adImageRepository->getPendingList('mobile_banner');

        // Transformer
        $result = $mobileBannerTransformer->show($data);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/mobile-banner/{mobile_banner_id}/delete",
     *   summary = "手機側邊欄Banner-刪除",
     *   tags = {"神之後台-廣告活動管理-手機側邊欄Banner：/ad-campaign/yzcube/mobile-banner"},
     *   description = "神之後台-廣告活動管理-手機側邊欄Banner-刪除",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "mobile_banner_id",
     *     in = "path",
     *     description = "手機側邊欄Banner編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function delete(
        $mobile_banner_id,
        AdCampaignRepository $adCampaignRepository,
        ApiFormatter $formatter
    ) {
        // 取得手機側邊欄Banner
        $adCampaign = $adCampaignRepository->getFirst(['type' => 'mobile_banner', 'id' => $mobile_banner_id]);
        if (!$adCampaign) {
            $this->setException('找不到這個手機側邊欄Banner！');
        }

        // 釋放廣告素材
        $adCampaign->mobileBannerAdImage()->update(['ad_campaign_id' => NULL]);

        $adCampaign->delete();

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/mobile-banner/save",
     *   summary = "手機側邊欄Banner-儲存",
     *   tags = {"神之後台-廣告活動管理-手機側邊欄Banner：/ad-campaign/yzcube/mobile-banner"},
     *   description = "神之後台-廣告活動管理-手機側邊欄Banner-儲存",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "mobile_banner_id",
     *     in = "formData",
     *     description = "手機側邊欄Banner編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "名稱",
     *     required = true,
     *     type = "string",
     *     default = "名稱"
     *   ),
     *   @SWG\ Parameter(
     *     name = "start_date",
     *     in = "formData",
     *     description = "上架時間",
     *     required = true,
     *     type = "string",
     *     default = "2019-12-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "end_date",
     *     in = "formData",
     *     description = "下架時間",
     *     required = true,
     *     type = "string",
     *     default = "2020-06-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_id",
     *     in = "formData",
     *     description = "廣告素材編號",
     *     required = true,
     *     type = "integer",
     *     default = 25
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function save(
        MobileBannerRequest $request,
        SaveMobileBannerService $saveMobileBannerService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $adCampaign = $saveMobileBannerService->run($request);

        return $formatter->json(['mobile_banner_id' => $adCampaign->id]);
    }
}
