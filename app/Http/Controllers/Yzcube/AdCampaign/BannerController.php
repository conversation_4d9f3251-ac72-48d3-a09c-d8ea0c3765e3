<?php

namespace App\Http\Controllers\Yzcube\AdCampaign;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\AdCampaign\BannerRequest;
use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;
use App\Services\AdCampaign\Yzcube\SaveBannerService;
use App\Transformers\Yzcube\AdCampaign\BannerTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class BannerController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/banner",
     *   summary = "首頁輪播-列表",
     *   tags = {"神之後台-廣告活動管理-首頁輪播：/ad-campaign/yzcube/banner"},
     *   description = "神之後台-廣告活動管理-首頁輪播-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "query",
     *     description = "狀態 published:發佈中 scheduling:排程中 completed:已結束",
     *     type = "string",
     *     enum = {"published", "scheduling", "completed"},
     *     default = "published"
     *   ),
     *   @SWG\ Parameter(
     *     name = "keyword",
     *     in = "query",
     *     description = "搜尋關鍵字",
     *     type = "string",
     *     default = "周胖"
     *   ),
     *   @SWG\ Parameter(
     *     name = "page",
     *     in = "query",
     *     description = "頁碼",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        AdCampaignRepository $adCampaignRepository,
        BannerTransformer $bannerTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得首頁輪播發佈中的版位
        $data['banners'] = $adCampaignRepository->getPublishedList('banner');

        // 取得首頁輪播列表
        $data['adCampaigns'] = $adCampaignRepository->getYzcubeListByRequest($request, 'banner');

        // Transformer
        $result = $bannerTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/banner/show",
     *   summary = "首頁輪播-詳細頁",
     *   tags = {"神之後台-廣告活動管理-首頁輪播：/ad-campaign/yzcube/banner"},
     *   description = "神之後台-廣告活動管理-首頁輪播-詳細頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "banner_id",
     *     in = "query",
     *     description = "首頁輪播編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository,
        BannerTransformer $bannerTransformer,
        ApiFormatter $formatter
    ) {
        // 取得首頁輪播
        $data['adCampaign'] = $adCampaignRepository->getFirst(['type' => 'banner', 'id' => request('banner_id')]);

        // 取得未使用的廣告素材
        $data['pendingAdImages'] = $adImageRepository->getPendingList('banner');

        // Transformer
        $result = $bannerTransformer->show($data);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/banner/check-date",
     *   summary = "首頁輪播-查詢版位狀況",
     *   tags = {"神之後台-廣告活動管理-首頁輪播：/ad-campaign/yzcube/banner"},
     *   description = "神之後台-廣告活動管理-首頁輪播-查詢版位狀況",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "start_date",
     *     in = "query",
     *     description = "上架時間",
     *     type = "string",
     *     default = "2019-12-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "end_date",
     *     in = "query",
     *     description = "下架時間",
     *     type = "string",
     *     default = "2020-06-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "banner_id",
     *     in = "query",
     *     description = "首頁輪播編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function checkDate(
        AdCampaignRepository $adCampaignRepository,
        BannerTransformer $bannerTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $startDate = request('start_date');
        $endDate   = request('end_date');
        $banner_id = request('banner_id');

        // 取得日期區間中有發佈中的版位
        $banners = $adCampaignRepository->getPublishedByDateRange('banner', $startDate, $endDate, $banner_id);

        // Transformer
        $result = $bannerTransformer->checkDate($banners);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/banner/{banner_id}/delete",
     *   summary = "首頁輪播-刪除",
     *   tags = {"神之後台-廣告活動管理-首頁輪播：/ad-campaign/yzcube/banner"},
     *   description = "神之後台-廣告活動管理-首頁輪播-刪除",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "banner_id",
     *     in = "path",
     *     description = "首頁輪播編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function delete(
        $banner_id,
        AdCampaignRepository $adCampaignRepository,
        ApiFormatter $formatter
    ) {
        // 取得首頁輪播
        $adCampaign = $adCampaignRepository->getFirst(['type' => 'banner', 'id' => $banner_id]);
        if (!$adCampaign) {
            $this->setException('找不到這個首頁輪播！');
        }

        // 釋放廣告素材
        $adCampaign->bannerAdImages()->update(['ad_campaign_id' => NULL]);

        $adCampaign->delete();

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/banner/save",
     *   summary = "首頁輪播-儲存",
     *   tags = {"神之後台-廣告活動管理-首頁輪播：/ad-campaign/yzcube/banner"},
     *   description = "神之後台-廣告活動管理-首頁輪播-儲存",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "banner_id",
     *     in = "formData",
     *     description = "首頁輪播編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "名稱",
     *     required = true,
     *     type = "string",
     *     default = "名稱"
     *   ),
     *   @SWG\ Parameter(
     *     name = "start_date",
     *     in = "formData",
     *     description = "上架時間",
     *     required = true,
     *     type = "string",
     *     default = "2019-12-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "end_date",
     *     in = "formData",
     *     description = "下架時間",
     *     required = true,
     *     type = "string",
     *     default = "2020-06-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "use_slots",
     *     in = "formData",
     *     description = "使用版位 (逗號分隔，用於首頁輪播)",
     *     required = true,
     *     type = "string",
     *     default = "1,3,5"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_ids[]",
     *     in = "formData",
     *     description = "廣告素材編號",
     *     required = true,
     *     type = "array",
     *     items = {
     *         "type": "integer",
     *     },
     *     collectionFormat = "multi",
     *     default = {1, 2}
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function save(
        BannerRequest $request,
        SaveBannerService $saveBannerService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $adCampaign = $saveBannerService->run($request);

        return $formatter->json(['banner_id' => $adCampaign->id]);
    }
}
