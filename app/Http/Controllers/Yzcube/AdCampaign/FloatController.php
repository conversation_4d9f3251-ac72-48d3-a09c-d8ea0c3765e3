<?php

namespace App\Http\Controllers\Yzcube\AdCampaign;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\AdCampaign\FloatRequest;
use App\Repositories\AdCampaignRepository;
use App\Repositories\AdImageRepository;
use App\Services\AdCampaign\Yzcube\SaveFloatService;
use App\Transformers\Yzcube\AdCampaign\FloatTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class FloatController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/float",
     *   summary = "浮動廣告-列表",
     *   tags = {"神之後台-廣告活動管理-浮動廣告：/ad-campaign/yzcube/float"},
     *   description = "神之後台-廣告活動管理-浮動廣告-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "query",
     *     description = "狀態 published:發佈中 scheduling:排程中 completed:已結束",
     *     type = "string",
     *     enum = {"published", "scheduling", "completed"},
     *     default = "published"
     *   ),
     *   @SWG\ Parameter(
     *     name = "keyword",
     *     in = "query",
     *     description = "搜尋關鍵字",
     *     type = "string",
     *     default = "周胖"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        AdCampaignRepository $adCampaignRepository,
        FloatTransformer $floatTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得浮動廣告列表
        $adCampaigns = $adCampaignRepository->getYzcubeListByRequest($request, 'float');

        // Transformer
        $result = $floatTransformer->list($adCampaigns);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/float/show",
     *   summary = "浮動廣告-詳細頁",
     *   tags = {"神之後台-廣告活動管理-浮動廣告：/ad-campaign/yzcube/float"},
     *   description = "神之後台-廣告活動管理-浮動廣告-詳細頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "float_id",
     *     in = "query",
     *     description = "浮動廣告編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        AdCampaignRepository $adCampaignRepository,
        AdImageRepository $adImageRepository,
        FloatTransformer $floatTransformer,
        ApiFormatter $formatter
    ) {
        // 取得浮動廣告
        $data['adCampaign'] = $adCampaignRepository->getFirst(['type' => 'float', 'id' => request('float_id')]);

        // 取得未使用的廣告素材
        $data['pendingAdImages'] = $adImageRepository->getPendingList('float');

        // Transformer
        $result = $floatTransformer->show($data);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/float/{float_id}/delete",
     *   summary = "浮動廣告-刪除",
     *   tags = {"神之後台-廣告活動管理-浮動廣告：/ad-campaign/yzcube/float"},
     *   description = "神之後台-廣告活動管理-浮動廣告-刪除",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "float_id",
     *     in = "path",
     *     description = "浮動廣告編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function delete(
        $float_id,
        AdCampaignRepository $adCampaignRepository,
        ApiFormatter $formatter
    ) {
        // 取得浮動廣告
        $adCampaign = $adCampaignRepository->getFirst(['type' => 'float', 'id' => $float_id]);
        if (!$adCampaign) {
            $this->setException('找不到這個浮動廣告！');
        }

        // 釋放廣告素材
        $adCampaign->floatAdImage()->update(['ad_campaign_id' => NULL]);

        $adCampaign->delete();

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/float/save",
     *   summary = "浮動廣告-儲存",
     *   tags = {"神之後台-廣告活動管理-浮動廣告：/ad-campaign/yzcube/float"},
     *   description = "神之後台-廣告活動管理-浮動廣告-儲存",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "float_id",
     *     in = "formData",
     *     description = "浮動廣告編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "名稱",
     *     required = true,
     *     type = "string",
     *     default = "名稱"
     *   ),
     *   @SWG\ Parameter(
     *     name = "start_date",
     *     in = "formData",
     *     description = "上架時間",
     *     required = true,
     *     type = "string",
     *     default = "2019-12-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "end_date",
     *     in = "formData",
     *     description = "下架時間",
     *     required = true,
     *     type = "string",
     *     default = "2020-06-01 00:00:00"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_id",
     *     in = "formData",
     *     description = "廣告素材編號",
     *     required = true,
     *     type = "integer",
     *     default = 25
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function save(
        FloatRequest $request,
        SaveFloatService $saveFloatService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $adCampaign = $saveFloatService->run($request);

        return $formatter->json(['float_id' => $adCampaign->id]);
    }
}
