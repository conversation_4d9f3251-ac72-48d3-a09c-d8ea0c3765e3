<?php

namespace App\Http\Controllers\Yzcube\AdCampaign;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\AdCampaign\AdImageRequest;
use App\Repositories\AdImageRepository;
use App\Services\AdCampaign\Yzcube\SaveImageService;
use App\Transformers\Yzcube\AdCampaign\ImageTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class ImageController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/image",
     *   summary = "素材管理-列表",
     *   tags = {"神之後台-廣告活動管理-素材管理：/ad-campaign/yzcube/image"},
     *   description = "神之後台-廣告活動管理-素材管理-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "campaign_type",
     *     in = "query",
     *     description = "廣告活動類型 banner:首頁輪播 mobile_banner:手機側邊欄Banner float:浮動廣告",
     *     type = "string",
     *     enum = {"banner", "mobile_banner"},
     *     default = "banner"
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "query",
     *     description = "使用狀態 pending:未使用 backup:補位圖片 using:活動使用中",
     *     type = "string",
     *     enum = {"pending", "backup", "using"},
     *     default = "pending"
     *   ),
     *   @SWG\ Parameter(
     *     name = "keyword",
     *     in = "query",
     *     description = "搜尋關鍵字",
     *     type = "string",
     *     default = "周胖"
     *   ),
     *   @SWG\ Parameter(
     *     name = "page",
     *     in = "query",
     *     description = "頁碼",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        AdImageRepository $adImageRepository,
        ImageTransformer $imageTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得廣告素材列表
        $adImages = $adImageRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $imageTransformer->list($adImages);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/ad-campaign/yzcube/image/{ad_image_id}",
     *   summary = "素材管理-詳細頁",
     *   tags = {"神之後台-廣告活動管理-素材管理：/ad-campaign/yzcube/image"},
     *   description = "神之後台-廣告活動管理-素材管理-詳細頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_id",
     *     in = "path",
     *     description = "廣告素材編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        $ad_image_id,
        AdImageRepository $adImageRepository,
        ImageTransformer $imageTransformer,
        ApiFormatter $formatter
    ) {
        // 取得廣告素材
        $adImage = $adImageRepository->getFirst(['id' => $ad_image_id]);
        if (!$adImage) {
            $this->setException('找不到這個廣告素材！');
        }

        // Transformer
        $result = $imageTransformer->show($adImage);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/image/{ad_image_id}/backup",
     *   summary = "素材管理-設定補位圖片",
     *   tags = {"神之後台-廣告活動管理-素材管理：/ad-campaign/yzcube/image"},
     *   description = "神之後台-廣告活動管理-素材管理-設定補位圖片",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_id",
     *     in = "path",
     *     description = "廣告素材編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "is_backup",
     *     in = "formData",
     *     description = "是否為補位圖片(首頁輪播，並有浮水印)",
     *     required = true,
     *     type = "integer",
     *     enum = {0, 1},
     *     default = 0
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function backup(
        $ad_image_id,
        AdImageRepository $adImageRepository,
        ApiFormatter $formatter
    ) {
        // 取得廣告素材
        $adImage = $adImageRepository->getFirst(['id' => $ad_image_id]);
        if (!$adImage) {
            $this->setException('找不到這個廣告素材！');
        }

        $adImage->is_backup = (!$adImage->campaign && request('is_backup')) ? 1 : 0 ;
        $adImage->save();

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/image/{ad_image_id}/delete",
     *   summary = "素材管理-刪除",
     *   tags = {"神之後台-廣告活動管理-素材管理：/ad-campaign/yzcube/image"},
     *   description = "神之後台-廣告活動管理-素材管理-刪除",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_id",
     *     in = "path",
     *     description = "廣告素材編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function delete(
        $ad_image_id,
        AdImageRepository $adImageRepository,
        ApiFormatter $formatter
    ) {
        // 取得廣告素材
        $adImage = $adImageRepository->getFirst(['id' => $ad_image_id]);
        if (!$adImage) {
            $this->setException('找不到這個廣告素材！');
        }

        // 有廣告活動則無法刪除
        if ($adImage->ad_campaign_id) {
            $this->setException('此廣告素材有所屬廣告活動無法刪除，請先至廣告活動中移除此素材！');
        }

        $adImage->delete();

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/ad-campaign/yzcube/image/save",
     *   summary = "素材管理-儲存",
     *   tags = {"神之後台-廣告活動管理-素材管理：/ad-campaign/yzcube/image"},
     *   description = "神之後台-廣告活動管理-素材管理-儲存",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "ad_image_id",
     *     in = "formData",
     *     description = "廣告素材編號",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "名稱",
     *     required = true,
     *     type = "string",
     *     default = "名稱"
     *   ),
     *   @SWG\ Parameter(
     *     name = "campaign_type",
     *     in = "formData",
     *     description = "廣告活動類型 banner:首頁輪播 mobile_banner:手機側邊欄Banner float:浮動廣告",
     *     required = true,
     *     type = "string",
     *     enum = {"banner", "mobile_banner"},
     *     default = "banner"
     *   ),
     *   @SWG\ Parameter(
     *     name = "is_backup",
     *     in = "formData",
     *     description = "是否為補位圖片(首頁輪播，並有浮水印)",
     *     type = "integer",
     *     enum = {0, 1},
     *     default = 0
     *   ),
     *   @SWG\ Parameter(
     *     name = "image",
     *     in = "formData",
     *     description = "圖檔名稱",
     *     type = "string",
     *     default = "../wdv3/banner/321schoolwedding.jpg"
     *   ),
     *   @SWG\ Parameter(
     *     name = "link_type",
     *     in = "formData",
     *     description = "連結類型 none:無連結 internal:內部連結 external:外部連結",
     *     type = "string",
     *     enum = {"none", "internal", "external"},
     *     default = "external"
     *   ),
     *   @SWG\ Parameter(
     *     name = "url_internal",
     *     in = "formData",
     *     description = "內部連結",
     *     type = "string",
     *     default = "user/profile"
     *   ),
     *   @SWG\ Parameter(
     *     name = "url_external",
     *     in = "formData",
     *     description = "外部連結",
     *     type = "string",
     *     default = "https://lihi.weddingday.com.tw/hXu5X/index_pcbn"
     *   ),
     *   @SWG\ Parameter(
     *     name = "image_xs",
     *     in = "formData",
     *     description = "圖檔名稱",
     *     type = "string",
     *     default = "../wdv3/banner/321schoolwedding.jpg"
     *   ),
     *   @SWG\ Parameter(
     *     name = "link_type_xs",
     *     in = "formData",
     *     description = "連結類型 none:無連結 internal:內部連結 external:外部連結",
     *     type = "string",
     *     enum = {"none", "internal", "external"},
     *     default = "external"
     *   ),
     *   @SWG\ Parameter(
     *     name = "url_xs_internal",
     *     in = "formData",
     *     description = "內部連結",
     *     type = "string",
     *     default = "user/profile"
     *   ),
     *   @SWG\ Parameter(
     *     name = "url_xs_external",
     *     in = "formData",
     *     description = "外部連結",
     *     type = "string",
     *     default = "https://lihi.weddingday.com.tw/hXu5X/index_mbbn"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function save(
        AdImageRequest $request,
        SaveImageService $saveImageService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $adImage = $saveImageService->run($request);

        return $formatter->json(['ad_image_id' => $adImage->id]);
    }
}
