<?php

namespace App\Http\Controllers\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\BlogArticleRequest;
use App\Repositories\BlogArticleRepository;
use App\Transformers\Yzcube\BlogArticleTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class BlogArticleController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Get(
     *   path = "/blog-article/yzcube",
     *   summary = "部落格文章列表",
     *   tags = {"神之後台-部落格文章：/blog-article/yzcube"},
     *   description = "神之後台-部落格文章-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "type",
     *     in = "query",
     *     description = "類別 kol:好婚鑑定團 blog:好婚專欄",
     *     type = "string",
     *     enum = {"kol", "blog"},
     *     default = "kol"
     *   ),
     *   @SWG\ Parameter(
     *     name = "category",
     *     in = "query",
     *     description = "分類名稱 2:禮服試穿 4:新秘試妝 5:婚宴場地直擊 wedding_shoes:婚鞋試穿 bride_cake:喜餅試吃 wedding_event:婚禮活動 diamond_ring:鑽戒體驗 other:其他",
     *     type = "string",
     *     enum = {2, 4, 5, "wedding_shoes", "bride_cake", "wedding_event", "diamond_ring", "other"},
     *     default = "other"
     *   ),
     *   @SWG\ Parameter(
     *     name = "keyword",
     *     in = "query",
     *     description = "搜尋關鍵字",
     *     type = "string",
     *     default = "婚紗"
     *   ),
     *   @SWG\ Parameter(
     *     name = "sort",
     *     in = "query",
     *     description = "排序欄位 id:編號 published_at:發佈日期",
     *     type = "string",
     *     enum = {"id", "published_at"},
     *     default = "published_at"
     *   ),
     *   @SWG\ Parameter(
     *     name = "direction",
     *     in = "query",
     *     description = "升冪降冪 asc:升冪 desc:降冪",
     *     type = "string",
     *     enum = {"asc", "desc"},
     *     default = "desc"
     *   ),
     *   @SWG\ Parameter(
     *     name = "page",
     *     in = "query",
     *     description = "頁碼",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        BlogArticleRepository $blogArticleRepository,
        BlogArticleTransformer $blogArticleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得商家列表
        $blogArticles = $blogArticleRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $blogArticleTransformer->list($blogArticles);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/blog-article/yzcube/{blog_article_id}",
     *   summary = "部落格文章詳細頁",
     *   tags = {"神之後台-部落格文章：/blog-article/yzcube"},
     *   description = "神之後台-部落格文章-詳細頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "blog_article_id",
     *     in = "path",
     *     description = "部落格文章編號",
     *     required = true,
     *     type = "integer",
     *     default = 1027
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        $blog_article_id,
        BlogArticleRepository $blogArticleRepository,
        BlogArticleTransformer $blogArticleTransformer,
        ApiFormatter $formatter
    ) {
        // 取得部落格文章
        $blogArticle = $blogArticleRepository->getFirst(['id' => $blog_article_id]);
        if (!$blogArticle) {
            $this->setException('找不到這個部落格文章！');
        }

        // Transformer
        $result = $blogArticleTransformer->show($blogArticle);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/blog-article/yzcube/{blog_article_id}/update",
     *   summary = "更新部落格文章",
     *   tags = {"神之後台-部落格文章：/blog-article/yzcube"},
     *   description = "神之後台-部落格文章-更新",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "blog_article_id",
     *     in = "path",
     *     description = "部落格文章編號",
     *     required = true,
     *     type = "integer",
     *     default = 1027
     *   ),
     *   @SWG\ Parameter(
     *     name = "title",
     *     in = "formData",
     *     description = "文章標題",
     *     required = true,
     *     type = "string",
     *     default = "Miss E.試衣間｜六款質感爆表的絕美風格婚紗 發現你的獨一無二！"
     *   ),
     *   @SWG\ Parameter(
     *     name = "image",
     *     in = "formData",
     *     description = "文章封面照",
     *     required = true,
     *     type = "string",
     *     default = "https://cdn.weddingday.com.tw/wordpress/kol/wp-content/uploads/20191015171112/%E5%B0%81%E9%9D%A25-07-768x403.jpg"
     *   ),
     *   @SWG\ Parameter(
     *     name = "category",
     *     in = "formData",
     *     description = "分類名稱",
     *     type = "string",
     *     default = "婚紗試穿"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function update(
        $blog_article_id,
        BlogArticleRequest $request,
        BlogArticleRepository $blogArticleRepository,
        ApiFormatter $formatter
    ) {
        // 取得部落格文章
        $blogArticle = $blogArticleRepository->getFirst(['id' => $blog_article_id]);
        if (!$blogArticle) {
            $this->setException('找不到這個部落格文章！');
        }

        // 更新部落格文章
        $blogArticle->allow_import_title = ($blogArticle->allow_import_title && $blogArticle->title == $request['title']);
        $blogArticle->allow_import_image = ($blogArticle->allow_import_image && $blogArticle->image == $request['image']);
        $blogArticle->title              = $request['title'];
        $blogArticle->image              = $request['image'];
        $blogArticle->category           = $request['category'];
        $blogArticle->save();

        return $formatter->json();
    }
}
