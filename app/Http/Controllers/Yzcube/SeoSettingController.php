<?php

namespace App\Http\Controllers\Yzcube;

use App\Http\Controllers\Controller;
use App\Models\SeoSetting;
use App\Models\SeoStoreRoute;
use App\Repositories\SeoStoreRouteRepository;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class SeoSettingController extends Controller
{
    use ApiErrorTrait;

    /**
     * SEO設定-詳細內容
     *
     * @url api/seo-setting/yzcube/{route_name}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path string $route_name 路由名稱 *
     * @return json
     */
    public function show(
        SeoSetting $seoSetting,
        ApiFormatter $formatter,
        $route_name
    ) {
        // 取得該路由的設定紀錄
        $result = $seoSetting->getList($route_name);

        return $formatter->json($result);
    }

    /**
     * SEO設定-儲存
     *
     * @url api/seo-setting/yzcube/{route_name}/save
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path string $route_name 路由名稱 *
     * @param string $title 標題 (required_if: description)
     * @param string $description 描述 (required_if: title)
     * @param int $seo_setting_id SEO設定 ID (required_if: note)
     * @param string $note 備註 (required_if: seo_setting_id)
     * @return json
     */
    public function save(
        SeoSetting $seoSetting,
        ApiFormatter $formatter,
        $route_name
    ) {
        // 新增SEO設定
        if (request('title') && request('description')) {
            $seoSetting->create([
                'route_name'  => $route_name,
                'title'       => request('title'),
                'description' => request('description'),
            ]);
        }

        // 更新備註訊息
        if (request('seo_setting_id') && request('note')) {
            $seoSetting = $seoSetting->find(request('seo_setting_id'));
            $seoSetting->note = request('note');
            $seoSetting->save();
        }

        // 取得該路由的設定紀錄
        $result = $seoSetting->getList($route_name);

        return $formatter->json($result);
    }

    /**
     * SEO商家路由客製-列表
     *
     * @url api/seo-setting/yzcube/store-route
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param array.string $route_name[] 路由名稱
     * @param int $store_id 商家ID
     * @param string $target_type 目標類型 album:相本/作品/禮服/禮盒 service:方案 room:廳房 video:影片
     * @param int $target_id 目標ID
     * @param string $keyword 搜尋關鍵字
     * @return json
     */
    public function storeRouteList(
        SeoStoreRouteRepository $seoStoreRouteRepository,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得SEO商家路由客製列表
        $seoStoreRoutes = $seoStoreRouteRepository->getYzcubeListByRequest($request);

        // SEO商家路由客製列表，顯示分頁資訊
        $result = $seoStoreRoutes->toArray();
        unset($result['data']);

        // SEO商家路由客製列表
        $result['data'] = [];
        foreach ($seoStoreRoutes as $seoStoreRoute) {
            $result['data'][] = [
                'id'          => $seoStoreRoute->id,
                'route_name'  => $seoStoreRoute->route_name,
                'store_id'    => $seoStoreRoute->store_id,
                'store_name'  => $seoStoreRoute->store->name,
                'store_type'  => $seoStoreRoute->store->type,
                'target_type' => $seoStoreRoute->target_type ?: '',
                'target_id'   => $seoStoreRoute->target_id,
                'updated_at'  => $seoStoreRoute->updated_at->format('Y-m-d H:i:s'),
                'logs'        => $seoStoreRoute->logs->map(function($log) {
                                    return [
                                        'title'            => $log->title ?: '',
                                        'description'      => $log->description ?: '',
                                        'yzcube_user_id'   => $log->yzcube_user_id,
                                        'yzcube_user_name' => $log->yzcubeUser->name,
                                        'created_at'       => $log->created_at->format('Y-m-d H:i:s'),
                                    ];
                                }),
            ];
        }

        return $formatter->json($result);
    }

    /**
     * SEO商家路由客製-搜尋
     *
     * @url api/seo-setting/yzcube/store-route/{route_name}/search
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path string $route_name 路由名稱 *
     * @param int $store_id 商家ID *
     * @param int $target_id 目標ID
     * @return json
     */
    public function storeRouteSearch(
        SeoSetting $seoSetting,
        SeoStoreRoute $seoStoreRoute,
        ApiFormatter $formatter,
        $route_name
    ) {
        // 驗證路由名稱
        $seoSetting = $seoSetting->getLatest($route_name);
        if (!$seoSetting) {
            $this->setException('查不到此頁，請前往預設SEO查找！', 4021);
        }

        // 取得該路由最新的設定紀錄
        $default = [
            'title'       => $seoSetting->title,
            'description' => $seoSetting->description,
        ];

        // SEO參數
        $params = $seoSetting->params->map(function($param) {
                                        return $param->only(['key', 'summary']);
                                    });

        // 取得該商家路由最新的設定客製紀錄
        $seoStoreRouteLog = $seoStoreRoute->getLatest($route_name, request('store_id'), request('target_id'));
        $custom = [
            'title'       => $seoStoreRouteLog->title ?? '',
            'description' => $seoStoreRouteLog->description ?? '',
        ];

        return $formatter->json([
            'default' => $default,
            'custom'  => $custom,
            'params'  => $params,
        ]);
    }

    /**
     * SEO商家路由客製-儲存
     *
     * @url api/seo-setting/yzcube/store-route/{route_name}/save
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path string $route_name 路由名稱 *
     * @param int $store_id 商家ID *
     * @param string $target_type 目標類型 album:相本/作品/禮服/禮盒 service:方案 room:廳房 video:影片
     * @param int $target_id 目標ID
     * @param string $title 標題
     * @param string $description 描述
     * @return json
     */
    public function storeRouteSave(
        SeoSetting $seoSetting,
        SeoStoreRoute $seoStoreRoute,
        ApiFormatter $formatter,
        $route_name
    ) {
        // 驗證路由名稱
        if (!$seoSetting->getLatest($route_name)) {
            $this->setException('查不到此頁，請前往預設SEO查找！', 4021);
        }

        // 新增SEO商家路由客製
        $seoStoreRoute = $seoStoreRoute->updateOrCreate([
            'route_name'  => $route_name,
            'store_id'    => request('store_id'),
            'target_type' => request('target_type') ?: NULL,
            'target_id'   => request('target_id'),
        ], [
            'updated_at' => now(),
        ]);

        // 新增SEO商家路由客製記錄
        $seoStoreRoute->logs()->create([
            'title'          => request('title') ?: NULL,
            'description'    => request('description') ?: NULL,
            'yzcube_user_id' => request('yzcube_user')->id,
        ]);

        return $formatter->json();
    }
}
