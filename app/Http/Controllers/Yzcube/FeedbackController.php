<?php

namespace App\Http\Controllers\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\FeedbackRequest;
use App\Repositories\FeedbackRepository;
use App\Transformers\Yzcube\FeedbackTransformer;
use App\Services\Image\CreateImageService;
use App\Services\Mail\User\FeedbackReplyService;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class FeedbackController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Get(
     *   path = "/feedback/yzcube",
     *   summary = "意見回饋列表",
     *   tags = {"神之後台-意見回饋：/feedback/yzcube"},
     *   description = "神之後台-意見回饋-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "query",
     *     description = "狀態 pending:待處理 processing:處理中 completed:已完成",
     *     type = "string",
     *     enum = {"pending", "processing", "completed"},
     *     default = "completed"
     *   ),
     *   @SWG\ Parameter(
     *     name = "keyword",
     *     in = "query",
     *     description = "搜尋關鍵字",
     *     type = "string",
     *     default = "周胖"
     *   ),
     *   @SWG\ Parameter(
     *     name = "sort",
     *     in = "query",
     *     description = "排序欄位 id:編號 updated_at:更新日期",
     *     type = "string",
     *     enum = {"id", "updated_at"},
     *     default = "id"
     *   ),
     *   @SWG\ Parameter(
     *     name = "direction",
     *     in = "query",
     *     description = "升冪降冪 asc:升冪 desc:降冪",
     *     type = "string",
     *     enum = {"asc", "desc"},
     *     default = "desc"
     *   ),
     *   @SWG\ Parameter(
     *     name = "page",
     *     in = "query",
     *     description = "頁碼",
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        FeedbackRepository $feedbackRepository,
        FeedbackTransformer $feedbackTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得意見回饋列表
        $feedbacks = $feedbackRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $feedbackTransformer->list($feedbacks);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Get(
     *   path = "/feedback/yzcube/{feedback_id}",
     *   summary = "意見回饋詳細頁",
     *   tags = {"神之後台-意見回饋：/feedback/yzcube"},
     *   description = "神之後台-意見回饋-意見回饋詳細頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "feedback_id",
     *     in = "path",
     *     description = "意見回饋編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        $feedback_id,
        FeedbackRepository $feedbackRepository,
        FeedbackTransformer $feedbackTransformer,
        ApiFormatter $formatter
    ) {
        // 取得意見回饋
        $feedback = $feedbackRepository->getFirst(['id' => $feedback_id]);
        if (!$feedback) {
            $this->setException('找不到這個意見回饋！');
        }

        // Transformer
        $result = $feedbackTransformer->show($feedback);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/feedback/yzcube/{feedback_id}/update",
     *   summary = "更新意見回饋",
     *   tags = {"神之後台-意見回饋：/feedback/yzcube"},
     *   description = "神之後台-意見回饋-更新",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "feedback_id",
     *     in = "path",
     *     description = "意見回饋編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "reply",
     *     in = "formData",
     *     description = "回覆內容",
     *     type = "string",
     *     default = "我是回覆內容"
     *   ),
     *   @SWG\ Parameter(
     *     name = "images[]",
     *     in = "formData",
     *     description = "已上傳圖檔名稱",
     *     type = "array",
     *     items = {
     *         "type": "string",
     *     },
     *     collectionFormat = "multi",
     *     default = {"7be4bb148cd354411907e5b3bb1c69525b73a79857122.jpg", "bc7adf138475277f19d0790a951560565b73a7f453d4d.jpg"}
     *   ),
     *   @SWG\ Parameter(
     *     name = "note",
     *     in = "formData",
     *     description = "備註訊息",
     *     type = "string",
     *     default = "我是備註訊息"
     *   ),
     *   @SWG\ Parameter(
     *     name = "status",
     *     in = "formData",
     *     description = "狀態 pending:待處理 processing:處理中 completed:已完成",
     *     type = "string",
     *     enum = {"pending", "processing", "completed"},
     *     default = "completed"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function update(
        $feedback_id,
        FeedbackRequest $request,
        FeedbackRepository $feedbackRepository,
        CreateImageService $createImageService,
        FeedbackReplyService $feedbackReplyService,
        ApiFormatter $formatter
    ) {
        // 取得意見回饋
        $feedback = $feedbackRepository->getFirst(['id' => $feedback_id]);
        if (!$feedback) {
            $this->setException('找不到這個意見回饋！');
        }

        // 更新意見回饋
        if ($request['note']) {
            $feedback->note = $request['note'];
        }
        if ($request['status']) {
            $feedback->status = $request['status'];
        }
        $feedback->save();

        // 新增回覆
        if ($request['reply'] || $request['images']) {
            $reply = $feedback->replies()->create([
                'is_admin' => 1,
                'content'  => $request['reply'],
            ]);

            // 更新上傳的圖檔的來源
            foreach ($request['images'] as $image) {
                $createImageService->add([
                    'file_name' => $image,
                    'type'      => 'feedback_reply',
                    'target_id' => $reply->id,
                ]);
            }

            // 回覆意見回饋通知信
            $feedbackReplyService->sendMail($reply);
        }

        return $formatter->json();
    }
}
