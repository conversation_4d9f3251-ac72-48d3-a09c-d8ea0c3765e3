<?php

namespace App\Http\Controllers\Yzcube;

use App\Http\Controllers\Controller;
use App\Repositories\SystemInfoRepository;
use App\Services\Tools\GetWebInfoService;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class MainController extends Controller
{
    use ApiErrorTrait;

    /**
     * @SWG\ Post(
     *   path = "/yzcube/system-info/{key}",
     *   summary = "更新系統資訊",
     *   tags = {"神之後台-主要功能：/yzcube"},
     *   description = "更新系統資訊",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "key",
     *     in = "path",
     *     description = "索引",
     *     required = true,
     *     type = "string",
     *     enum = {"plate_gauge"},
     *     default = "plate_gauge"
     *   ),
     *   @SWG\ Parameter(
     *     name = "content",
     *     in = "formData",
     *     description = "詳細內容",
     *     required = true,
     *     type = "string",
     *     default = "WeddingDay與您的約定<br />"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function postSystemInfo(
        SystemInfoRepository $systemInfoRepository,
        ApiFormatter $formatter,
        $key
    ) {
        $systemInfoRepository->updateData(['key' => $key], ['content' => request('content')]);

        return $formatter->json();
    }

    /**
     * @SWG\ Get(
     *   path = "/yzcube/capture-article",
     *   summary = "擷取文章 (不限WordPress)",
     *   tags = {"神之後台-主要功能：/yzcube"},
     *   description = "擷取文章 (不限WordPress)",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "url",
     *     in = "query",
     *     description = "文章網址",
     *     required = true,
     *     type = "string",
     *     default = "http://timtung.pixnet.net/blog/post/99372059"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function captureArticle(
        GetWebInfoService $getWebInfoService,
        ApiFormatter $formatter
    ) {
        // Request
        $url = request('url');
        if (!$url) {
            $this->setException('找不到文章網址！');
        }

        // Service
        $data = $getWebInfoService->get($url);

        return $formatter->json($data);
    }
}
