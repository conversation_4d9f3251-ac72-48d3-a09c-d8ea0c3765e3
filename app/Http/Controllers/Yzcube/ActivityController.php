<?php

namespace App\Http\Controllers\Yzcube;

use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\Store;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Services\Image\CreateImageService;
use App\Services\Image\DeleteImageService;
use App\Transformers\Yzcube\ActivityTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class ActivityController extends Controller
{
    use ApiErrorTrait;

    /**
     * 活動方案-列表
     *
     * @url api/activity/yzcube
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function list(
        Activity $activity,
        ActivityTransformer $activityTransformer,
        ApiFormatter $formatter
    ) {
        // 取得所有活動方案
        $activities = $activity->withTrashed()->get();

        // Transformer
        $result = $activityTransformer->list($activities);

        return $formatter->json($result);
    }

    /**
     * 活動方案-詳細內容
     *
     * @url api/activity/yzcube/{activity_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $activity_id 活動方案ID *
     * @return json
     */
    public function show(
        ActivityTransformer $activityTransformer,
        ApiFormatter $formatter,
        Activity $activity
    ) {
        // Transformer
        $result = $activityTransformer->show($activity);

        return $formatter->json($result);
    }

    /**
     * 活動方案-收尋參與商家
     *
     * @url api/activity/yzcube/{activity_id}/search-stores
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $activity_id 活動方案ID *
     * @param string $keyword 搜尋關鍵字 *
     * @return json
     */
    public function searchStores(
        Store $store,
        SearchService $keywordSearchService,
        ApiFormatter $formatter,
        Activity $activity
    ) {
        if (!request('keyword')) {
            $this->setException('請搜尋關鍵字！');
        }

        // 排除參與過的商家
        $store = $store->whereNotIn('id', $activity->allStores->pluck('id'));

        // 商家類型 types
        if ($activity->store_types) {
            $store = $store->whereIn('type', $activity->store_types);
        }

        // 關鍵字 keyword
        $keyword = trim(request('keyword') ?? '');
        if ($keyword != '') {
            $store = $keywordSearchService->search($store, $keyword);
        }

        $stores = $store->get()
                        ->map(function($item) {
                            return [
                                'id'   => $item->id,
                                'logo' => $item->logo->file_name ?? '',
                                'type' => $item->type,
                                'name' => $item->name,

                            ];
                        });

        return $formatter->json($stores);
    }

    /**
     * 活動方案-儲存
     *
     * @url api/activity/yzcube/{activity_id}
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $activity_id 活動方案ID *
     * @param string $name 優惠名稱 *
     * @param string $file_name 活動視覺圖
     * @param string $link 活動連結
     * @param date $start_date 上架時間 *
     * @param date $end_date 下架時間 *
     * @param date $release_date 商家後台開放日 *
     * @return json
     */
    public function save(
        CreateImageService $createImageService,
        DeleteImageService $deleteImageService,
        ActivityTransformer $activityTransformer,
        ApiFormatter $formatter,
        Activity $activity
    ) {
        $activity->name         = request('name');
        $activity->link         = request('link');
        $activity->start_date   = request('start_date');
        $activity->end_date     = request('end_date');
        $activity->release_date = request('release_date');
        $activity->save();

        // 活動視覺圖
        if (request('file_name')) {
            $createImageService->add([
                'file_name' => request('file_name'),
                'type'      => 'activity_image',
                'target_id' => $activity->id,
                'only'      => true,
            ]);

        // 移除圖片
        } elseif ($activity->image) {
            $deleteImageService->delete(['id' => $activity->image->id]);
        }

        // 重置已結束的活動方案
        $activity->resetCompleted();

        // Transformer
        $result = $activityTransformer->show($activity);

        return $formatter->json($result);
    }

    /**
     * 活動方案-儲存參與商家
     *
     * @url api/activity/yzcube/{activity_id}/attend-store/{store_id}
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $activity_id 活動方案ID *
     * @path int $store_id 商家ID *
     * @param bool $is_attend 是否參加 *
     * @param string $content 優惠內容
     * @return json
     */
    public function attendStore(
        ApiFormatter $formatter,
        Activity $activity,
        Store $store
    ) {
        // 驗證商家類型
        if ($activity->store_types && !in_array($store->type, $activity->store_types)) {
            $this->setException('商家類型不符！');
        }

        // 更新或新增
        $activity->allStores()->syncWithoutDetaching([
            $store->id => [
                'is_attend' => request('is_attend'),
                'content'   => request('content'),
            ]
        ]);

        // 重置已結束的活動方案
        $activity->resetCompleted();

        return $formatter->json();
    }
}
