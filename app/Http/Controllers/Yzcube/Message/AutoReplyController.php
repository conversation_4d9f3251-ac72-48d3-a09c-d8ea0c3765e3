<?php

namespace App\Http\Controllers\Yzcube\Message;

use App\Http\Controllers\Controller;
use App\Models\StoreAutoReplyDefault;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class AutoReplyController extends Controller
{
    use ApiErrorTrait;

    /**
     * 預設訊息-詳細內容頁
     *
     * @url api/message/yzcube/auto-reply
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function show(
        StoreAutoReplyDefault $storeAutoReplyDefault,
        ApiFormatter $formatter
    ) {
        $result = [];
        $storeAutoReplyDefault::all()->each(function($item) use (&$result) {
            if ($item->store_type) {
                $result['store_type'][$item->store_type][$item->type] = $item->content;
            } else {
                $result[$item->type] = $item->content;
            }
        });

        return $formatter->json($result);
    }

    /**
     * 預設訊息-更新
     *
     * @url api/message/yzcube/auto-reply/update
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string type 欄位類型 *
     * @param int store_type 商家類型
     * @param string content 自動回覆內容 *
     * @return json
     */
    public function update(
        StoreAutoReplyDefault $storeAutoReplyDefault,
        ApiFormatter $formatter
    ) {
        // 驗證必填 自動回覆內容
        if (!request('content')) {
             $this->setException('請輸入自動回覆內容！');
        }

        // 驗證欄位類型
        if (!array_key_exists(request('type'), $storeAutoReplyDefault->typeList) || (!request('store_type') && in_array(request('type'), ['greeting_normal', 'greeting_has_event', 'first_auto_reply']))) {
            $this->setException('系統自動回覆的欄位類型錯誤！');
        }

        // 驗證商家類型
        if (request('store_type') && !in_array(request('type'), ['greeting_normal', 'greeting_has_event', 'first_auto_reply'])) {
            $this->setException('商家類型僅可設定「一般的問候語」、「有表單的問候語」與「首次自動回覆(公版)」！');
        }

        // 更新商家自動回覆預設訊息
        $storeAutoReplyDefault::updateOrCreate([
            'type'       => request('type'),
            'store_type' => request('store_type'),
        ], [
            'content' => request('content'),
        ]);

        return $formatter->json();
    }
}
