<?php

namespace App\Http\Controllers\Yzcube\Message;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Store;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Transformers\FirebaseTransformer;
use App\Formatters\ApiFormatter;

class ChatController extends Controller
{
    /**
     * 私訊管理-取得搜尋列表
     *
     * @url api/message/yzcube/chat/search-list
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $keyword 關鍵字 *
     * @param string $type 類型 user:使用者|store:商家 *
     * @return json
     */
    public function searchList(
        User $user,
        Store $store,
        SearchService $keywordSearchService,
        FirebaseTransformer $firebaseTransformer,
        ApiFormatter $formatter
    ) {
        $model = (request('type') == 'store') ? $store : $user;

        // keyword 關鏈字搜尋
        $models = $keywordSearchService->search($model, request('keyword'));
        $models = $models->limit(20)
                            ->get();

        // Transformer
        $result = [];
        foreach ($models as $model) {
            $method = (request('type') == 'store') ? 'getStoreInfo' : 'getUserInfo';
            $result[] = $firebaseTransformer->{$method}($model);
        }

        return $formatter->json($result);
    }
}
