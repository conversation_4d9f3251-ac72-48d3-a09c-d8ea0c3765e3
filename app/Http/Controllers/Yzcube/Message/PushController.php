<?php

namespace App\Http\Controllers\Yzcube\Message;

use App\Http\Controllers\Controller;
use App\Http\Requests\Yzcube\PushRequest;
use App\Repositories\PushMessageRepository;
use App\Services\Message\Yzcube\Push\SaveService;
use App\Traits\ApiErrorTrait;
use App\Transformers\Yzcube\Message\PushTransformer;
use App\Formatters\ApiFormatter;

class PushController extends Controller
{
    use ApiErrorTrait;

    /**
     * 全站推播-列表
     *
     * @url api/message/yzcube/push
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $receiver_type 接收者類型 user:新娘|store:商家 *
     * @param string $keyword 關鍵字
     * @param string $sort 排序 id|published_at(default)
     * @param string $direction asc|desc(default)
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        PushMessageRepository $pushMessageRepository,
        PushTransformer $pushTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        $data = [
            'scheduling' => $pushMessageRepository->getSchedulingListByReceiverType($request['receiver_type']),
            'published'  => $pushMessageRepository->getPushedListByRequest($request),
        ];

        // Transformer
        $result = $pushTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 全站推播-新增
     *
     * @url api/message/yzcube/push/create
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $receiver_type 接收者類型 user:新娘|store:商家 *
     * @return json
     */
    public function create(
        PushTransformer $pushTransformer,
        ApiFormatter $formatter
    ) {
        // Transformer
        $result = $pushTransformer->create();

        return $formatter->json($result);
    }

    /**
     * 全站推播-儲存
     *
     * @url api/message/yzcube/push/save
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $push_id 全站推播ID
     * @param int $admin_id 推播者ID *
     * @param string $receiver_type 接收者類型 user:新娘|store:商家 *
     * @param string $store_type 商家類型
     * @param string $title 標題 *
     * @param string $content 詳細內容 *
     * @param array $images 已上傳圖檔名稱
     * @param int $push_count 推播數量 *
     * @param string $status scheduling:排程中|immediately:立即推播 *
     * @param date $set_publish_at 設定發佈時間
     * @return json
     */
    public function save(
        PushRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        // Service
        $saveService->run($request);

        return $formatter->json();
    }

    /**
     * 全站推播-詳細內容頁
     *
     * @url api/message/yzcube/push/{push_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $push_id 全站推播ID
     * @param string $receiver_type 接收者類型 user:新娘|store:商家 *
     * @return json
     */
    public function show(
        PushMessageRepository $pushMessageRepository,
        PushTransformer $pushTransformer,
        ApiFormatter $formatter,
        $push_id
    ) {
        // 取得推播訊息
        $pushMessage = $pushMessageRepository->getFirst([
            'receiver_type' => request('receiver_type'),
            'id'            => $push_id,
        ]);
        if (!$pushMessage) {
            $this->setException('找不到此推播訊息！');
        }

        // Transformer
        $result = $pushTransformer->show($pushMessage);

        return $formatter->json($result);
    }

    /**
     * 全站推播-刪除
     *
     * @url api/message/yzcube/push/{push_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $push_id 全站推播ID
     * @param string $receiver_type 接收者類型 user:新娘|store:商家 *
     * @return json
     */
    public function delete(
        PushMessageRepository $pushMessageRepository,
        ApiFormatter $formatter,
        $push_id
    ) {
        // 取得推播訊息
        $pushMessage = $pushMessageRepository->getFirst([
            'receiver_type' => request('receiver_type'),
            'id'            => $push_id,
        ]);
        if (!$pushMessage) {
            $this->setException('找不到此推播訊息！');
        }

        // 僅能刪除排程中的推播訊息
        if ($pushMessage->status != 'scheduling') {
            $this->setException('僅能刪除排程中的推播訊息！');
        }

        $pushMessage->delete();

        return $formatter->json();
    }
}
