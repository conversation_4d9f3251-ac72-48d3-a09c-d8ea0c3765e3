<?php

namespace App\Http\Controllers\Auth\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginV2Request;
use App\Http\Requests\Auth\Admin\RegisterV2Request;
use App\Services\Auth\Admin\LoginService;
use App\Services\Auth\Admin\RegisterService;
use App\Services\Auth\SendPhoneTokenService;
use App\Services\Auth\PhoneVerifiedService;
use App\Services\Auth\Admin\MenuListService;
use App\Services\Admin\IndexService;
use App\Transformers\Admin\AuthTransformer;
use App\Repositories\AuthTokenRepository;
use App\Models\StoreUser;
use App\Models\Store;
use App\Models\EmailLegalize;
use App\Formatters\ApiFormatter;
use App\Services\Auth\Admin\StoreInfoService;
use App\Traits\ApiErrorTrait;
use App\Traits\Auth\ConvertPasswordTrait;
use \Firebase\JWT\JWT;

class AuthController extends Controller
{
    use ApiErrorTrait;
    use ConvertPasswordTrait;

    /**
     * 一般登入
     *
     * @url api/auth/admin/v2/login
     * @method POST
     * @header string Access-Token *
     * @param string $email 帳號 *
     * @param string $password 密碼 (需編碼) *
     * @param string $type 登入方式（fb, email）*
     * @param string $third_id 第三方登入id
     * @return json
     */
    public function login(
        LoginV2Request $request,
        LoginService $loginService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // Service
        $loginResult = $loginService->run($request);

        // Transformer
        $result = $authTransformer->executeLogin($loginResult['storeUser']);

        return $formatter->json($result);
    }

    /**
     * 註冊
     *
     * @url api/auth/admin/v2/register
     * @method POST
     * @header string Access-Token *
     * @param string $email 帳號 *
     * @param string $password 密碼 (需編碼) *
     * @param string $key 驗證碼 *
     * @return json
     */
    public function register(
        RegisterV2Request $request,
        RegisterService $registerService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // Service
        $storeUser = $registerService->run($request);

        // Transformer
        $result = $authTransformer->executeLogin($storeUser);

        return $formatter->json($result);
    }

    /**
     * 驗證信箱驗證碼
     * @url api/auth/admin/v2/verify-email-auth-key
     * @method POST
     * @header string Access-Token *
     * @param string $email 帳號 *
     * @param string $key 驗證碼 *
     * @return json
     */
    public function verifyEmailAuthKey(
        StoreUser $model,
        EmailLegalize $authKey,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 必填欄位
        try {
            $this->validate(request(), [
                'email' => 'required',
                'key'   => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // 檢查驗證碼
        $key = $authKey->whereRaw('BINARY `key` = ?', [request('key')])
                                ->where('email', request('email'))
                                ->where('deadline_at', '>', now())
                                ->first();
        if (!$key) {
            $this->setException('請輸入正確的 6 位數驗證碼 ', 4000, ['key' => '請輸入正確的 6 位數驗證碼']);
        }

        $storeUser = $model->where('email', request('email'))->first();

        // 驗證成功 > 刪掉驗證碼、更新user狀態
        $authKey->where('email', request('email'))->delete();
        $storeUser->email_legalize = 1;
        if ($storeUser->phone_legalize == 1) {
            $storeUser->status = 'published';
        }
        $storeUser->save();

        // 找store_user的store store若信箱未驗證 改成這個email然後已驗證
        $stores = $storeUser->liveStores;
        if ($stores) {
            foreach ($stores as $store) {
                if ($store->email_legalize == 0) {
                    $store->email = request('email');
                    $store->email_legalize = 1;
                    $store->save();
                }
            }
        }

        // Transformer
        $result = $authTransformer->executeLogin($storeUser);

        return $formatter->json($result);
    }

    /**
     * 寄送手機驗證碼
     *
     * @url api/auth/admin/v2/send-phone-verified
     * @method POST
     * @header string Access-Token *
     * @param string $store_user_id 商家ID *
     * @param string $phone 手機 *
     * @return json
     */
    public function sendPhoneVerified(
        SendPhoneTokenService $sendPhoneTokenService,
        ApiFormatter $formatter
    ) {
        // 必填欄位
        try {
            $this->validate(request(), [
                'store_user_id' => 'required',
                'phone'         => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // Service
        $data = $sendPhoneTokenService->send(request('store_user_id'), request('phone'), 'store_user');

        return $formatter->json($data, ['message' => '請收手機簡訊驗證碼！']);
    }

    /**
     * 驗證手機驗證碼
     *
     * @url api/auth/admin/v2/phone-verified
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token （有無store-token判斷是否為登入狀態時的驗證）
     * @param string $phone 手機 *
     * @param string $key 驗證碼 *
     */
    public function phoneVerified(
        PhoneVerifiedService $phoneVerifiedService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 必填欄位
        try {
            $this->validate(request(), [
                'phone'         => 'required',
                'key'           => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // Service
        $storeUser = $phoneVerifiedService->verified(request('phone'), request('key'), 'store_user');

        // 商家 帳號資料修改手機 不用回傳登入資訊
        if (request()->header('Store-Token')) {
            return $formatter->json(NULL, ['message' => '手機驗證成功！']);
        }
        // Transformer
        $result = $authTransformer->executeLogin($storeUser);

        return $formatter->json($result);
    }

    /**
     * 填寫商家資料
     *
     * @url api/auth/admin/v2/store-info
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @param string $type 商家類別 *
     * @param string $name 商家名稱 *
     */
    public function storeInfo(
        StoreInfoService $storeInfoService,
        ApiFormatter $formatter
    ) {
        // 必填欄位
        try {
            $this->validate(request(), [
                'type' => 'required',
                'name' => 'required',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // Service
        $storeId = $storeInfoService->run(request('store_user'), request('type'), request('name'));

        // Transformer
        $result = [
            'store_id' => $storeId,
        ];

        return $formatter->json($result);
    }

    /**
     *
     * 修改密碼
     * @url api/auth/edit-password
     * @method POST
     * @header string Access-Token *
     * @param string $pwd_token 修改密碼token *
     * @param string $password 新密碼 *
     * @return json
     *
     */
    public function editPassword(
        $pwd_token,
        AuthTokenRepository $authTokenRepository,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 驗證 pwd_token
        $authToken = $authTokenRepository->getAuthTokenByValidToken('store_password', $pwd_token);
        if (!$authToken) {
            $this->setException('發生錯誤了，修改密碼的網址連結不是有效的！你可以嘗試點擊下方按鈕，重寄修改密碼信，若還有問題請聯絡客服！ ', 4008);
        }

        // 驗證token若無效 則信箱連結失效
        // 若無密碼欄位，則回傳成功
        if (!request('password')) {
            return $formatter->json();
        }

        // 取得 User
        $storeUser = $authToken->admin;

        // 修改密碼
        $storeUser->password = $this->getInsertHashPassword(request('password'));
        $storeUser->save();

        // 刪除 pwd_token
        $authToken->delete();

        // Transformer
        $result = $authTransformer->executeLogin($storeUser);

        return $formatter->json($result);
    }

    public function bindLine(
        LoginService $loginService,
        ApiFormatter $formatter
    ) {
        // 必填欄位
        try {
            $this->validate(request(), [
                'email'           => 'required',
                'password'        => 'required',
                'line_link_token' => 'required'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        request()->merge(['type' => 'bind_line']);
        $storeUser = $loginService->runV2(request())['storeUser'];

        $key = env('LINE_JWT_TOKEN');
        $timeNow = time();
        $jwtToken = array(
            "iss" => "weddingday",
            "aud" => "weddingday",
            "exp" => $timeNow + 5*60,
            "iat" => $timeNow - 5,
            "nbf" => $timeNow - 5,
            "usr" => $storeUser->id
        );

        $nonce = JWT::encode($jwtToken, $key, 'HS256');
        return $formatter->json(['redirect_url' => 'https://access.line.me/dialog/bot/accountLink?linkToken='.request('line_link_token').'&nonce='.$nonce]);
    }

    /**
     * 驗證StoreToken
     *
     * @url api/auth/admin/check-token
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @return json
     */
    public function checkToken(
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 取得 Store User
        $storeUser = request('store_user');

        // Transformer
        $result = $authTransformer->executeLogin($storeUser);

        return $formatter->json($result);
    }

    /**
     * 檢查訪問權限
     *
     * @url api/auth/admin/check-access/{store_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @param string $current_url 目前網址 *
     * @return json
     */
    public function checkAccess(
        MenuListService $menuListService,
        IndexService $indexService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        return $formatter->json([
            'menu_list'       => $menuListService->run($request),
            'breadcrumb_list' => $menuListService->getBreadcrumbList(),
            'check_launched'  => $indexService->checkLaunched($request['store']),
            'store_user'      => $authTransformer->getUserProfile($request['store_user']),
        ]);
    }

    /**
     * 登出
     *
     * @url api/auth/admin/logout
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @return json
     */
    public function logout(
        ApiFormatter $formatter
    ) {
        // 取得 Store User
        $storeUser  = request('store_user');
        $storeToken = request()->header('Store-Token');

        // 刪除 store_token
        if ($storeUser) {
            $storeUser->tokens()->where('token', $storeToken)->delete();
        }

        // 主網站使用Session驗證身份，主網站也需要跟著登出，移除Session中的user_id
        if (env('APP_ENV') != 'testing') {
            session_id(request('session_id'));
            $domain = '.'.env('APP_DOMAIN');
            session_set_cookie_params(2592000, '/', $domain);
            session_start();
            unset($_SESSION['store_user_id']);
        }

        return $formatter->json();
    }
}
