<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginV2Request;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\SendEmailAuthKeyRequest;
use App\Http\Requests\Auth\ForgotRequest;
use App\Services\Auth\RegisterService;
use App\Services\Auth\LoginService;
use App\Services\Mail\Auth\EmailAuthKeyService;
use App\Services\Mail\Auth\FounderLetterService;
use App\Services\Mail\Auth\EditPasswordService;
use App\Services\Auth\SendPhoneTokenService;
use App\Services\Auth\PhoneVerifiedService;
use App\Repositories\AuthTokenRepository;
use App\Transformers\AuthTransformer;
use App\Formatters\ApiFormatter;
use App\Models\EmailLegalize;
use App\Models\User;
use App\Models\StoreUser;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\Auth\MakeAnonymousKeyTrait;

class AuthController extends Controller
{
    use ApiErrorTrait;
    use RandStringTrait;
    use ConvertPasswordTrait;
    use CreateTokenTrait;
    use MakeAnonymousKeyTrait;


    /**
     *
     * 會員登入
     *
     * @url api/auth/v2/login
     * @method POST
     * @header string Access-Token *
     * @param string email 帳號
     * @param string password 密碼
     * @param string type 登入方式
     * @param string third_id 第三方登入id
     *
     */
    public function login(
        LoginV2Request $request,
        LoginService $loginService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // Service
        $loginResult = $loginService->run($request);
        $user = $loginResult['user'];

        // Transformer
        $result = $authTransformer->login($user, $user? $user->token: NULL);

        return $formatter->json($result);
    }


    /**
     *  寄出信箱登入驗證碼
     *
     * @url api/auth/v2/send-email-auth-key
     * @method GET
     * @header string Access-Token *
     * @param string $email Email *
     * @param string $type 登入方式 *
     * @param string $third_id 第三方登入id (會員用第三方登入才有)
     * @param string $fb_id fb id (商家用fb登入才有)
     * @return json
     */
    public function sendEmailAuthKey(
        SendEmailAuthKeyRequest $request,
        EmailAuthKeyService $emailAuthKeyService,
        EmailLegalize $authKey,
        ApiFormatter $formatter
    ) {
        // Service
        $emailAuthKeyService->checkSendEmail($request);

        $recipient = null;
        $mailType = '';
        switch ($request['type']) {
            case 'email':
                $recipient = User::where('email', $request['email'])->value('name');
                $mailType = 'user_email';
                break;
            case 'fb':
            case 'line':
            case 'google':
                $recipient = $request['third_name'];
                $mailType = 'user_email';
                break;
            case 'store_email':
                if ($request['store_user_id']) {
                    $recipientStore = StoreUser::where('id', $request['store_user_id'])->with('liveStores')->orderBy('id', 'desc')->first();
                    if (count($recipientStore->liveStores)) {
                        $recipient = $recipientStore->liveStores->first()->name;
                    }
                }
                preg_match('/^(.*?)@/', $request['email'], $matches);
                $mailName = $matches[1] ?? '';
                $recipient = $recipient ?: $mailName;
                $mailType = 'store_user_email';
                break;
        }
        // 驗證碼寫進資料庫
        $authKey = $authKey->create([
            'email'       => $request['email'],
            'key'         => $this->getRandString(6),
            'recipient'   => $recipient,
            'deadline_at' => now()->addMinutes(5),
        ]);

        // 寄出驗證信
        $emailAuthKeyService->sendAuthMail($authKey, $mailType);
        return $formatter->json(NULL, ['message' => '請至您的「 '.$request['email'].' 」信箱收取驗證信！']);
    }


    /**
     *  信箱驗證碼快速登入
     *
     * @url api/auth/v2/verify-email-auth-key
     * @method POST
     * @header string Access-Token *
     * @param string $email Email *
     * @param string $key 登入驗證碼 *
     * @param string $type 驗證來源 *(email, fb, line, google)
     * @param string $third_id 第三方登入id
     * @param string $third_name 第三方登入名稱
     * @param string $third_avatar 第三方登入頭像
     * @return json
     */
    public function verifyEmailAuthKey(
        User $model,
        LoginService $loginService,
        RegisterService $registerService,
        EmailLegalize $authKey,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 驗證Email是否存在會員資料庫
        if (request('type') == 'email') {
            $user = $model->where('email', request('email'))->first();
            if (!$user) {
                $this->setException('找不到此會員帳號，請重新輸入！ ');
            }
        }

        // 檢查驗證碼
        $key = $authKey->whereRaw('BINARY `key` = ?', [request('key')])
                                ->where('email', request('email'))
                                ->where('deadline_at', '>', now())
                                ->first();
        if (!$key) {
            $this->setException('請輸入正確的 6 位數驗證碼 ', 4000, ['key' => '請輸入正確的 6 位數驗證碼']);
        }

        // 第三方登入驗證 驗證正確且有user,填入第三方id  若無user則註冊user
        if (in_array(request('type'), ['fb', 'line', 'google'])) {
            if ($user = $model->where('email', request('email'))->first()){
                $user->{request('type').'_id'} = request('third_id');
                $user->save();
            } else {
                $user = $registerService->createThirdUser(
                    request('email'),
                    request('type'),
                    [
                        'id'    => request('third_id'),
                        'name'  => request('third_name'),
                        'email' => request('email')
                    ]
                );
            }
            // 紀錄第三方登入資訊
            $loginService->recordThirdInfo(request()->all(), $user);
        }

         // 刪除驗證碼
         $authKey->where('email', request('email'))->delete();

        // 登入信箱驗證相關
        if (in_array(request('type'), ['email', 'fb', 'line', 'google'])) {
            // 紀錄驗證信箱成功
            $user->email_legalize = 1;
            // 若通知信箱未驗證，則預設通知信箱為註冊信箱，且一起通過驗證
            if ($user->notify_email_legalize == 0) {
                $user->notify_email = $user->email;
                $user->notify_email_legalize = 1;
            }
            $user->status = 'published';
            $user->save();

            // 實作登入
            $user = $loginService->doLoginByUser($user);
        }

         // Transformer
         $result = $authTransformer->login($user, $user->token);

         return $formatter->json($result);
    }

    /**
     * 會員信箱註冊
     *
     * @url api/auth/v2/register
     * @method POST
     * @header string Access-Token *
     * @param string $email Email *
     * @param string $password 密碼 *
     * @param string $name 會員名稱 *
     * @param string $phone 手機號碼 *
     * @return json
     */
    public function register(
        RegisterRequest $request,
        RegisterService $registerService,
        FounderLetterService $founderLetterService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // Service
        $user = $registerService->run($request);
        // 來自於好婚市集共同創辦人的一封信
        $founderLetterService->sendMail($user);

        // Transformer
        $result = $authTransformer->login($user, $user->token);

        return $formatter->json($result);
    }

    /**
     *
     * 寄修改密碼信
     * @url api/auth/forgot-password
     * @method POST
     * @param string $email 帳號(email) *
     * @param string $type 商家(store_user) or 會員(user) *
     * @return json
     *
     */
    public function forgotPassword(
        ForgotRequest $request,
        User $userModel,
        StoreUser $storeUserModel,
        EditPasswordService $editPasswordService,
        ApiFormatter $formatter
    ) {
        // 取得 User
        if (request('type') == 'store_user') {
            $user = $storeUserModel->where('email', $request['email'])->first();
            if (!$user) {
                $this->setException('找不到此商家帳號，請重新輸入 ', 4000, ['email' => '找不到此商家帳號，請重新輸入']);
            }
            if ($user->status == 'delete') {
                $this->setException('此商家帳號已停用，無法使用 ', 4000, ['email' => '此商家帳號已停用，無法使用']);
            }
            preg_match('/^(.*?)@/', $request['email'], $matches);
            $user->name = $matches[1] ?? '';
        }
        if (request('type') == 'user') {
            $user = $userModel->where('email', $request['email'])->first();
            if (!$user) {
                $this->setException('找不到此會員帳號，請重新輸入 ', 4000, ['email' => '找不到此會員帳號，請重新輸入']);
            }
            if ($user->status == 'delete') {
                $this->setException('此會員帳號已停用，無法使用 ', 4000, ['email' => '此會員帳號已停用，無法使用']);
            }
        }

        // 產生 pwd_token
        $tokenType = $request['type'] == 'store_user' ? 'store_password' : 'user_password';
        $pwd_token = $this->getNewToken();
        $user->pwd_tokens()->create([
            'target_id'   => $user->id,
            'type'        => $tokenType,
            'token'       => $pwd_token,
            'deadline_at' => now()->addDay(),
        ]);

        // 好婚市集修改密碼信
        $editPasswordService->sendMail($user, $pwd_token, $request['type']);

        return $formatter->json(NULL, ['message' => '重設密碼信已寄至你的電子信箱 '.$user->email]);
    }

    /**
     *
     * 修改密碼
     * @url api/auth/edit-password
     * @method POST
     * @param string $pwd_token 修改密碼token *
     * @param string $password 新密碼 *
     * @return json
     *
     */
    public function editPassword(
        $pwd_token,
        AuthTokenRepository $authTokenRepository,
        LoginService $loginService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 驗證 pwd_token
        $authToken = $authTokenRepository->getAuthTokenByValidToken('user_password', $pwd_token);
        if (!$authToken) {
            $this->setException('發生錯誤了，修改密碼的網址連結不是有效的！你可以嘗試點擊下方按鈕，重寄修改密碼信，若還有問題請聯絡客服！ ', 4008);
        }

        // 驗證token若無效 則信箱連結失效
        // 若無密碼欄位，則回傳成功
        if (!request('password')) {
            return $formatter->json();
        }

        // 取得 User
        $user = $authToken->user;

        // 修改密碼
        $user->password = $this->getInsertHashPassword(request('password'));
        $user->save();

        // 刪除 pwd_token
        $authToken->delete();

        // 登入
        $loginResult = $loginService->run([
            'email'    => $user->email,
            'password' => request('password'),
            'type'     => 'email',
        ]);
        $user = $loginResult['user'];

        // Transformer
        $result = $authTransformer->login($user, $user->token);

        return $formatter->json($result);
    }

    /**
     *
     * 寄手機驗證碼
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/auth/send-phone-verified
     * @method POST
     * @param string $phone 手機號碼 *
     * @return json
     *
     */
    public function sendPhoneVerified(
        SendPhoneTokenService $sendPhoneTokenService,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = request('user');

        // Service
        $data = $sendPhoneTokenService->send($user->id, request('phone'), 'user');

        return $formatter->json($data, ['message' => '請收手機簡訊驗證碼！']);
    }

    /**
     *
     * 手機驗證
     * @header string Access-Token *
     * @header string User-Token *
     * @url api/auth/phone-verified
     * @method POST
     * @param string $phone 手機號碼 *
     * @param string $key 驗證碼 *
     * @return json
     *
     */
    public function phoneVerified(
        PhoneVerifiedService $phoneVerifiedService,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = request('user');

        // Service
        if (!$user->phone_legalize || $user->phone != request('phone')) {
            $phoneVerifiedService->verified(request('phone'), request('key'), 'user');
        }

        return $formatter->json(NULL, ['message' => '手機驗證成功！']);
    }

    /**
     *
     * 驗證Email是否存在
     * @url api/auth/check-email
     * @method GET
     * @param string $email 帳號(email) *
     * @return json
     *
     */
    public function checkEmail(
        User $model,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user = $model->where('email', request('email'))->first();

        return $formatter->json(['exists' => (bool)$user]);
    }

    /**
     *
     * 驗證UserToken
     * @url api/auth/check-token
     * @method GET
     * @header string User-Token *
     * @param string $User-Token 使用者驗證碼 *
     * @return json
     *
     */
    public function checkToken(
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user       = request('user');
        $user_token = request()->header('User-Token');

        // Transformer
        $result = $authTransformer->login($user, $user_token);

        return $formatter->json($result);
    }

    /**
     *
     * 登出
     * @url api/auth/logout
     * @method GET
     * @header string User-Token *
     * @param string $User-Token 使用者驗證碼 *
     * @return json
     *
     */
    public function logout(
        ApiFormatter $formatter
    ) {
        // 取得 User
        $user       = request('user');
        $user_token = request()->header('User-Token');

        // 刪除 user_token
        if ($user) {
            $user->tokens()->where('token', $user_token)->whereNotNull('deadline_at')->delete();
        }

        // 主網站使用Session驗證身份，主網站也需要跟著登出，移除Session中的user_id
        if (env('APP_ENV') != 'testing') {
            session_id(request('session_id'));
            $domain = '.'.env('APP_DOMAIN');
            session_set_cookie_params(2592000, '/', $domain);
            session_start();
            unset($_SESSION['user_id']);
        }

        return $formatter->json();
    }
}
