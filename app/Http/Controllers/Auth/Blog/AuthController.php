<?php

namespace App\Http\Controllers\Auth\Blog;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\Blog\LoginRequest;
use App\Services\Auth\Blog\LoginService;
// use App\Services\Auth\Blog\MenuListService;
// use App\Services\Blog\IndexService;
use App\Transformers\Blog\AuthTransformer;
// use App\Repositories\AuthTokenRepository;
// use App\Models\Store;
use App\Formatters\ApiFormatter;
// use App\Traits\ApiErrorTrait;
// use App\Traits\Auth\ConvertPasswordTrait;

class AuthController extends Controller
{
    // use ApiErrorTrait;
    // use ConvertPasswordTrait;

    /**
     * 登入
     *
     * @url api/auth/blog/login
     * @method POST
     * @header string Access-Token *
     * @param string $email 帳號 *
     * @param string $password 密碼 (需編碼) *
     * @return json
     */
    public function login(
        LoginRequest $request,
        LoginService $loginService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // Service
        $blogUser = $loginService->run($request);

        // Transformer
        $result = $authTransformer->login($blogUser);

        return $formatter->json($result);
    }

    /**
     *
     * 修改密碼
     * @url api/auth/edit-password
     * @method POST
     * @header string Access-Token *
     * @param string $pwd_token 修改密碼token *
     * @param string $password 新密碼 *
     * @return json
     *
     */
    // public function editPassword(
    //     $pwd_token,
    //     AuthTokenRepository $authTokenRepository,
    //     AuthTransformer $authTransformer,
    //     ApiFormatter $formatter
    // ) {
    //     // 驗證 pwd_token
    //     $authToken = $authTokenRepository->getAuthTokenByValidToken('store_password', $pwd_token);
    //     if (!$authToken) {
    //         $this->setException('發生錯誤了，修改密碼的網址連結不是有效的！你可以嘗試點擊下方按鈕，重寄修改密碼信，若還有問題請聯絡客服！ ', 4008);
    //     }

    //     // 驗證token若無效 則信箱連結失效
    //     // 若無密碼欄位，則回傳成功
    //     if (!request('password')) {
    //         return $formatter->json();
    //     }

    //     // 取得 User
    //     $storeUser = $authToken->blog;

    //     // 修改密碼
    //     $storeUser->password = $this->getInsertHashPassword(request('password'));
    //     $storeUser->save();

    //     // 刪除 pwd_token
    //     $authToken->delete();

    //     // Transformer
    //     $result = $authTransformer->executeLogin($storeUser);

    //     return $formatter->json($result);
    // }

    /**
     * 驗證StoreToken
     *
     * @url api/auth/blog/check-token
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @return json
     */
    // public function checkToken(
    //     AuthTransformer $authTransformer,
    //     ApiFormatter $formatter
    // ) {
    //     // 取得 Store User
    //     $storeUser = request('store_user');

    //     // Transformer
    //     $result = $authTransformer->executeLogin($storeUser);

    //     return $formatter->json($result);
    // }

    /**
     * 檢查訪問權限
     *
     * @url api/auth/blog/check-access/{store_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @param string $current_url 目前網址 *
     * @return json
     */
    // public function checkAccess(
    //     MenuListService $menuListService,
    //     IndexService $indexService,
    //     AuthTransformer $authTransformer,
    //     ApiFormatter $formatter,
    //     Store $store
    // ) {
    //     // Request
    //     $request = request();

    //     return $formatter->json([
    //         'menu_list'       => $menuListService->run($request),
    //         'breadcrumb_list' => $menuListService->getBreadcrumbList(),
    //         'check_launched'  => $indexService->checkLaunched($request['store']),
    //         'store_user'      => $authTransformer->getUserProfile($request['store_user']),
    //     ]);
    // }

    /**
     * 登出
     *
     * @url api/auth/blog/logout
     * @method GET
     * @header string Access-Token *
     * @header string Blog-Token *
     * @return json
     */
    public function logout(
        ApiFormatter $formatter
    ) {
        // 取得 Blog User
        $blogUser  = request('blog_user');
        $blogToken = request()->header('Blog-Token');

        // 刪除 blog_token
        if ($blogUser) {
            $blogUser->tokens()->where('token', $blogToken)->delete();
        }

        return $formatter->json();
    }
}
