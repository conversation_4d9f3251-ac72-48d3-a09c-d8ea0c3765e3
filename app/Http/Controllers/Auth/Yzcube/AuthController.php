<?php

namespace App\Http\Controllers\Auth\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\Yzcube\SaveRequest;
use App\Models\YzcubeUser;
use App\Repositories\AuthTokenRepository;
use App\Services\Auth\Yzcube\LoginService;
use App\Services\Mail\Auth\Yzcube\EditPasswordService;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\ApiErrorTrait;
use App\Transformers\Yzcube\AuthTransformer;
use App\Formatters\ApiFormatter;

class AuthController extends Controller
{
    use ApiErrorTrait;
    use ConvertPasswordTrait;
    use CreateTokenTrait;

    /**
     * 驗證YzcubeToken
     *
     * @url api/auth/yzcube/check-token
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token * 管理者驗證碼
     * @return json
     */
    public function checkToken(
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 取得 Yzcube User
        $yzcube_user  = request('yzcube_user');
        $yzcube_token = request()->header('Yzcube-Token');

        // Transformer
        $result = $authTransformer->login($yzcube_user, $yzcube_token);

        return $formatter->json($result);
    }

    /**
     * 驗證YzcubeToken
     *
     * @url api/auth/yzcube/login
     * @method POST
     * @header string Access-Token *
     * @param string email * 帳號(email)
     * @param string password * 密碼 (需編碼)
     * @return json
     */
    public function login(
        LoginRequest $request,
        LoginService $loginService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // Service
        $yzcube_user = $loginService->run($request);

        // Transformer
        $result = $authTransformer->login($yzcube_user, $yzcube_user->token);

        return $formatter->json($result);
    }

    /**
     * 登出
     *
     * @url api/auth/yzcube/logout
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token * 管理者驗證碼
     * @return json
     */
    public function logout(
        ApiFormatter $formatter
    ) {
        // 取得 Yzcube User
        $yzcube_user  = request('yzcube_user');
        $yzcube_token = request()->header('Yzcube-Token');

        // 刪除 yzcube_token
        if ($yzcube_user) {
            $yzcube_user->tokens()->where('token', $yzcube_token)->delete();
        }

        // 主網站使用Session驗證身份，主網站也需要跟著登出，移除Session中的user_id
        if (env('APP_ENV') != 'testing') {
            session_id(request('session_id'));
            $domain = '.'.env('APP_DOMAIN');
            session_set_cookie_params(2592000, '/', $domain);
            session_start();
            unset($_SESSION['yzcube_user_id']);
        }

        return $formatter->json();
    }

    /**
     * 夥伴管理-列表
     *
     * @url api/auth/yzcube
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token * 管理者驗證碼
     * @return json
     */
    public function list(
        YzcubeUser $yzcubeUser,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 管理員列表
        $yzcubeUsers = $yzcubeUser->live()
                                    ->orderBy('created_at', 'DESC')
                                    ->paginate(20);

        // Transformer
        $result = $authTransformer->list($yzcubeUsers);

        return $formatter->json($result);
    }

    /**
     * 夥伴管理-儲存
     *
     * @url api/auth/yzcube/save
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token * 管理者驗證碼
     * @param int $id 管理員ID
     * @param string $name 暱稱 *
     * @param email $email Email *
     * @return json
     */
    public function save(
        SaveRequest $request,
        YzcubeUser $model,
        EditPasswordService $editPasswordService,
        ApiFormatter $formatter
    ) {
        // 找出管理員
        $yzcubeUser = $model->find($request['id']);
        if (!$yzcubeUser) {
            $yzcubeUser = clone $model;
        }

        // 儲存資料
        $yzcubeUser->name  = $request['name'];
        $yzcubeUser->email = $request['email'];
        $yzcubeUser->save();

        // 密碼未設定
        if (!$yzcubeUser->password) {

            // 產生 pwd_token
            $pwd_token = $this->getNewToken();
            $yzcubeUser->pwd_tokens()->create([
                'target_id'   => $yzcubeUser->id,
                'type'        => 'yzcube_password',
                'token'       => $pwd_token,
                'deadline_at' => now()->addDay(),
            ]);

            // WeddingDay 神之後台-修改密碼信
            $editPasswordService->sendMail($yzcubeUser, $pwd_token);
        }

        return $formatter->json($yzcubeUser->wasRecentlyCreated);
    }

    /**
     * 夥伴管理-重設密碼
     *
     * @url api/auth/yzcube/{yzcube_user_id}/reset-password
     * @method POST
     * @header string Access-Token *
     * @path int $yzcube_user_id 管理員ID *
     * @return json
     */
    public function resetPassword(
        YzcubeUser $yzcubeUser,
        EditPasswordService $editPasswordService,
        ApiFormatter $formatter
    ) {
        // 產生 pwd_token
        $pwd_token = $this->getNewToken();
        $yzcubeUser->pwd_tokens()->create([
            'target_id'   => $yzcubeUser->id,
            'type'        => 'yzcube_password',
            'token'       => $pwd_token,
            'deadline_at' => now()->addDay(),
        ]);

        // WeddingDay 神之後台-修改密碼信
        $editPasswordService->sendMail($yzcubeUser, $pwd_token);

        return $formatter->json(NULL, ['message' => '修改密碼的電子郵件已寄到您的信箱了']);
    }

    /**
     * 夥伴管理-刪除
     *
     * @url api/auth/yzcube/{yzcube_user_id}/delete
     * @method POST
     * @header string Access-Token *
     * @path int $yzcube_user_id 管理員ID *
     * @return json
     */
    public function delete(
        YzcubeUser $yzcubeUser,
        ApiFormatter $formatter
    ) {
        $yzcubeUser->status = 'delete';
        $yzcubeUser->save();

        return $formatter->json();
    }

    /**
     * 夥伴管理-修改密碼
     *
     * @url api/auth/yzcube/edit-password/{pwd_token}
     * @method POST
     * @header string Access-Token *
     * @path string $pwd_token 修改密碼存取權杖 *
     * @param string $password 密碼(需編碼) *
     * @return json
     */
    public function editPassword(
        $pwd_token,
        AuthTokenRepository $authTokenRepository,
        LoginService $loginService,
        AuthTransformer $authTransformer,
        ApiFormatter $formatter
    ) {
        // 驗證 pwd_token
        $authToken = $authTokenRepository->getAuthTokenByValidToken('yzcube_password', $pwd_token);
        if (!$authToken) {
            $this->setException('發生錯誤了，修改密碼的網址連結不是有效的！');
        }

        // 若無密碼欄位，則回傳成功
        if (!request('password')) {
            return $formatter->json();
        }

        // 取得 YzcubeUser
        $yzcubeUser = $authToken->yzcube;

        // 修改密碼
        $yzcubeUser->password = $this->getInsertHashPassword(request('password'));
        $yzcubeUser->status   = 'published';
        $yzcubeUser->save();

        // 刪除 pwd_token
        $authToken->delete();

        // 登入
        $yzcubeUser = $loginService->run([
            'email'    => $yzcubeUser->email,
            'password' => request('password'),
        ]);

        // Transformer
        $result = $authTransformer->login($yzcubeUser, $yzcubeUser->token);

        return $formatter->json($result);
    }
}
