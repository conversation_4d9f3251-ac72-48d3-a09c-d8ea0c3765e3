<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Jobs\Forum\SendEmailNewArticle;
use App\Jobs\Forum\SendEmailArticleAtUsers;
use App\Jobs\Forum\SendEmailArticleTracks;
use App\Jobs\Forum\SendEmailCommentAtUsers;
use App\Jobs\Forum\SendEmailNewCommentArticleTracks;
use App\Jobs\Forum\SendEmailNewComment;
use App\Services\Mail\MailService;
use App\Services\File\GetImageInfoService;
use App\Services\Forum\Article\CommentService;
use App\Services\Mail\Auth\FounderLetterService;
use App\Services\Mail\Auth\EmailVerificationService;
use App\Services\Mail\Auth\EditPasswordService;
use App\Services\Mail\Forum\NewComment\ArticleAuthorByCommentService;
use App\Services\Mail\Forum\NewComment\ParentCommentAuthorService;
use App\Services\Mail\Forum\ArticleAtUsersService;
use App\Services\Mail\Forum\ArticleChangeCategoryService;
use App\Services\Mail\Forum\ArticleTracksService;
use App\Services\Mail\Forum\CommentAtUsersService;
use App\Services\Mail\Forum\DeleteArticleService;
use App\Services\Mail\Forum\DeleteCommentService;
use App\Services\Mail\Forum\LikeArticleService;
use App\Services\Mail\Forum\LikeCommentService;
use App\Services\Mail\Forum\NewArticleService;
use App\Services\Mail\Forum\NewCommentArticleTracksService;
use App\Services\Mail\User\EditEmailService;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Models\Event;
use App\Models\User;
use App\Models\ForumCategory as Category;
use App\Models\ForumArticle as Article;
use App\Models\ForumComment as Comment;
use App\Models\Store;
use App\Models\Wdv2\StoreOrderSet;
use App\Models\Brand;
use App\Models\Wdv2\SystemSetting;
use Carbon\Carbon;
use App\Services\Tools\GetWebData\ImportBlogAllPostsService;
use App\Services\Tools\GetWebData\GetBlogPostInfoService;
use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Event;
use Google_Service_Calendar_EventDateTime;
use Google_Service_Calendar_EventAttendee;
use Google\Cloud\Core\Timestamp;
use DB;
use Artisan;
use Illuminate\Support\Str;
use App\Traits\CurlTrait;
use Google\Cloud\Vision\VisionClient;
use App\Jobs\Firebase\Message;

class TestController extends Controller
{
    /**
     * 首頁
     * @url test
     *
     * @return void
     */
    public function index(
        \App\Models\RingUser $ringUser
    ) {
        $ringUser = $ringUser->where('user_id', 87333)->first();
        $ringOrder = $ringUser->orders->first();

        // 即時通訊處理 use Job
        Message::dispatch('ring_event_audit_order', $ringOrder);
    }

    /**
     * Google Analytics
     * @url test/ga
     *
     * @return void
     */
    public function ga(
        Store $store,
        \App\Services\Google\BigQuery\LogPageViewService $logPageViewService,
        \App\Services\Google\BigQuery\LogEventService $logEventService,
        \App\Services\Store\CreateStoreIntegralService $createStoreIntegralService,
        \App\Services\Store\UpdateStoreRankService $updateStoreRankService,
        \App\Services\Store\UpdateStoreAlbumRankService $updateStoreAlbumRankService,
        \App\Services\Store\UpdateStoreVideoRankService $updateStoreVideoRankService,
        \App\Services\Store\UpdateStoreServiceRankService $updateStoreServiceRankService,
        \App\Services\Store\UpdateVenueRoomRankRankService $updateVenueRoomRankRankService,
        \App\Services\Google\BigQuery\BigQueryHandle $bigQueryHandle
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 能夠背景執行到結束
        ignore_user_abort(true);
        // 設置最大執行時間,0為無限制
        set_time_limit(0);
        // 控制nginx不要cache
        header('X-Accel-Buffering: no');
        // 清空緩衝區並關閉輸出緩衝
        ob_end_clean();
        // 刷新頁面
        ob_implicit_flush(1);

        // 取得昨天的GA網頁瀏覽量
        $date = date('Y-m-d', strtotime('-1 day'));

        // for ($i=12; $i > 0; $i--) {
        //     $date = date('Y-m-d', strtotime('-'.$i.' day'));

        //     // 【商家】GA網頁瀏覽量
        //     $total = $logPageViewService->run('store', $date);
        //     echo $date.': 共新增'.$total.'家昨天的商家GA網頁瀏覽量！<br>';

        //     // 【商家】GA事件
        //     $total = $logEventService->run($date);
        //     echo $date.': 共新增'.$total.'筆昨天的商家GA事件！<br>';
        // }

        // 【商家】統計商家排行積分
        // $total = $createStoreIntegralService->run($date);
        // echo $date.': 共統計'.$total.'個商家昨天的商家排行積分！<br>';

        // // 【商家】更新商家排行
        // $total = $updateStoreRankService->run($date);
        // echo $date.': 今日共更新'.$total.'個商家的排行！<br>';

        // // 【商家】更新商家相本排行
        // $total = $updateStoreAlbumRankService->run($date);
        // echo $date.': 今日共更新'.$total.'個商家相本的排行！<br>';

        // // 【商家】更新商家影片排行
        // $total = $updateStoreVideoRankService->run($date);
        // echo $date.': 今日共更新'.$total.'個商家影片的排行！<br>';

        // // 【商家】更新商家方案排行
        // $total = $updateStoreServiceRankService->run($date);
        // echo $date.': 今日共更新'.$total.'個商家方案的排行！<br>';

        // // 【商家】更新婚宴場地廳房排行
        // $total = $updateVenueRoomRankRankService->run($date);
        // echo $date.': 今日共更新'.$total.'個婚宴場地廳房的排行！<br>';

        // dd('========================================================================');

        // 近30天
        $dateRange = [
            now()->subDays(30)->format('Y-m-d 00:00:00'),
            now()->subDay()->format('Y-m-d 23:59:59'),
        ];

        $dateRange = '2023-02-14';

        // GA4 BigQuery
        // $analytics = $bigQueryHandle->handle('blog_article_analytics', $dateRange);
        // $analytics = $bigQueryHandle->handle('forum_article_analytics', $dateRange);
        // $analytics = $bigQueryHandle->handle('kol_article_analytics', $dateRange);
        // $analytics = $bigQueryHandle->handle('quote_landing_page_views', $dateRange);
        // $analytics = $bigQueryHandle->handle('share_post_analytics', $dateRange);
        $analytics = $bigQueryHandle->handle('store_analytics', $dateRange);
        // $analytics = $bigQueryHandle->handle('wedding_day_page_views', $dateRange);

        // 商家事件總數
        // $analytics = $bigQueryHandle->handle('store_total_events', [
        //     'dateRange'   => $dateRange,
        //     'eventName'   => 'wd_message',
        //     'eventParams' => [
        //         'EXACT' => ['action' => 'open'],
        //         'INT'   => ['store_id' => 3533],
        //     ],
        // ]);

        // 主動報價的外站點擊
        // $analytics = $bigQueryHandle->handle('store_type_events', [
        //     'dateRange' => $dateRange,
        //     'eventName' => 'external_link',
        //     'groupKeys' => [
        //         'string' => 'target_id',
        //     ],
        //     'eventParams' => [
        //         'BEGINS_WITH' => ['target_id' => 'quote_'],
        //     ],
        // ]);

        // 商家的外站點擊
        // $analytics = $bigQueryHandle->handle('store_type_events', [
        //     'dateRange' => $dateRange,
        //     'eventName' => 'external_link',
        //     'groupKeys' => [
        //         'int'    => 'store_id',
        //         'string' => 'click_type',
        //     ],
        //     'eventParams' => [
        //         'INT'   => ['store_id' => 3533],
        //     ],
        // ]);
        dd($analytics);

        // 紀錄當天的GA網頁瀏覽量
        // $type = 'store';
        // $date = '2023-02-01';
        // $count = $logPageViewService->run($type, $date);
        // dd('LogPageViewService('.$type.', '.$date.'): '.$count);

        // 紀錄當天的GA事件
        // $date = '2023-02-01';
        // $count = $logEventService->run($date);
        // dd('LogEventService('.$date.'): '.$count);
    }

    /**
     * google 行事曆
     * @url test
     *
     * @return void
     */
    public function calendar()
    {
        $KEY_FILE_LOCATION = base_path(env('GOOGLE_SERVICE_ACCOUNT_JSON_LOCATION'));

        $client = new Google_Client();
        $client->setAuthConfig($KEY_FILE_LOCATION);
        $client->setScopes([Google_Service_Calendar::CALENDAR]);

        $service = new Google_Service_Calendar($client);
        $calendarId = env('GOOGLE_SERVICE_ACCOUNT_EMAIL');

        // 列表&刪除
        // $events = $service->events->listEvents($calendarId);
        // foreach ($events as $event) {
        //     $service->events->delete($calendarId, $event->id);
        // }
        // dd($events);


        // 新增1
        // $event = new Google_Service_Calendar_Event([
        //   'summary' => '名稱2',
        //   'location' => '地點',
        //   'description' => '這是描述!',
        //   // 'visibility' => 'private',
        //   'guestsCanSeeOtherGuests' => FALSE,
        //   'start' => [
        //     'dateTime' => now()->toRfc3339String(),
        //   ],
        //   'end' => [
        //     'dateTime' => now()->addHour()->toRfc3339String(),
        //   ],
        //   // 'recurrence' => [
        //   //   'RRULE:FREQ=DAILY;COUNT=2'
        //   // ],
        //   'attendees' => [
        //     // ['email' => '<EMAIL>'],
        //     // ['email' => '<EMAIL>'],
        //   ],
        //   // 'reminders' => [
        //   //   'useDefault' => FALSE,
        //   //   'overrides' => [
        //   //     ['method' => 'email', 'minutes' => 24 * 60],
        //   //     ['method' => 'popup', 'minutes' => 10],
        //   //   ],
        //   // ],
        // ]);

        // $event = $service->events->insert($calendarId, $event);
        // dd($event->id, $event->getId());


        // 新增2
        // $event = new Google_Service_Calendar_Event;

        // $event->summary = '名稱2';
        // $event->location = '地點';
        // $event->description = '這是描述!';
        // $event->visibility = 'private';
        // $event->guestsCanSeeOtherGuests = FALSE;
        // $event->start = [
        //     'dateTime' => now()->toRfc3339String(),
        //     'timeZone' => now()->getTimezone(),
        // ];
        // $event->end = [
        //     'dateTime' => now()->addHour()->toRfc3339String(),
        //     'timeZone' => now()->getTimezone(),
        // ];
        // $event->attendees = [];
        // $event->reminders = [
        //     'useDefault' => false,
        //     'overrides'  => [
        //         ['method' => 'email', 'minutes' => 60 * 24],
        //         ['method' => 'popup', 'minutes' => 30],
        //     ],
        // ];

        // $eventDateTime = new Google_Service_Calendar_EventDateTime;
        // $eventDateTime->setDate(now()->format('Y-m-d'));
        // $eventDateTime->setTimezone(now()->getTimezone());
        // $event->setStart($eventDateTime);

        // $eventDateTime = new Google_Service_Calendar_EventDateTime;
        // $eventDateTime->setDate(now()->addHour()->format('Y-m-d'));
        // $eventDateTime->setTimezone(now()->getTimezone());
        // $event->setEnd($eventDateTime);

        // $event = $service->events->insert($calendarId, $event);
        // dd($event->getAttendees(), $event->id, $event->getId());

        // 編輯
        $eventId = 'sbhtt1lq87023vfivhc3ijrrso';
        // $eventId = $event->getId();

        try {
            $event = $service->events->get($calendarId, $eventId);

            $event->attendeesOmitted = false;
            // $event->setAttendees([['email' => '<EMAIL>']]);

            $attendees = $event->getAttendees();
            $attendees[] = ['email' => '<EMAIL>'];
            $event->attendees = $attendees;

            // $event->setAttendeesOmitted(false);

            // $attendee = new Google_Service_Calendar_EventAttendee();
            // $attendee->setEmail('<EMAIL>');
            // $attendee->setOrganizer(true);

            // $attedess = $event->getAttendees();
            // array_push($attedess, $attendee);
            // $event->setAttendees($attedess);
            // $event->setAttendees([['email' => '<EMAIL>']]);
            // $event->setSummary('名稱2345');
            $event = $service->events->update($calendarId, $eventId, $event);

            dd($event);


            $event->summary = '名稱234';
            $event->location = '地點';
            $event->description = '這是描述!';
            $event->visibility = 'private';
            $event->guestsCanSeeOtherGuests = FALSE;
            $event->start = [
                'dateTime' => now()->toRfc3339String(),
                'timeZone' => now()->getTimezone(),
            ];
            $event->end = [
                'dateTime' => now()->addHour()->toRfc3339String(),
                'timeZone' => now()->getTimezone(),
            ];
            $event->attendees = [];


            $event = $service->events->update($calendarId, $eventId, $event);
        } catch (\Exception $e) {
            dd($e, $e->getMessage());
        }
        dd($event);
    }

    /**
     * 匯入部落格所有文章
     * @url test/import-blog-all-posts
     *
     * @return void
     */
    public function importBlogAllPosts(ImportBlogAllPostsService $importBlogAllPostsService)
    {
        $importBlogAllPostsService->run();
        dd('匯入部落格所有文章: done!!');
    }

    /**
     * 取得部落格詳細資訊
     * @url test/get-blog-post-info
     *
     * @return void
     */
    public function getBlogPostInfo(GetBlogPostInfoService $getBlogPostInfoService)
    {
        $getBlogPostInfoService->getBlogPostById('blog', 99252);
        $getBlogPostInfoService->saveBlogArticle();
        dd('取得部落格詳細資訊: done!!');
    }

    /**
     * 會員密碼轉碼大全
     * @url test/user-password/{password}/{hashPassword?}
     *
     * @return void
     */
    use ConvertPasswordTrait;

    public function userPassword($password, $hashPassword = NULL)
    {
        $feEncode = $this->feEncode($password);
        $feDecode = $this->feDecode($feEncode);
        $mysqlEncode = $this->mysqlEncode($password);
        $bcryptEncode = $this->getInsertHashPassword($feEncode);
        $isCorrect = $this->isCorrectPassword($feEncode, $hashPassword);
        dd(
            '前端密碼加密: ' . $feEncode,
            '後端解碼(原始密碼): ' . $feDecode,
            'MySQL password() 加密: ' . $mysqlEncode,
            '存入資料庫的hash密碼 bcrypt() 加密: ' . $bcryptEncode,
            '驗證前端密碼加密是否為正確密碼: ' . ($isCorrect ? '正確' : '錯誤')
        );
    }

    /**
     * 寄信：來自於好婚市集共同創辦人的一封信
     * @url test/mail
     *
     * @return void
     */
    public function mail(
        MailService $mailService,
        FounderLetterService $founderLetterService
    )
    {
        // 收件資訊
        $user = User::where('email', env('MAIL_TEST'))->first();
        $founderLetterService->sendMail($user);
        $title = '【Test】來自於好婚市集共同創辦人的一封信';
//        $view   = 'emails.auth.founder_letter';
//        $params = [
//            'address'      => $user->email,
//            'name'         => $user->name,
//            'from_address' => config('params.mail.founder.address'),
//            'from_name'    => config('params.mail.founder.name'),
//            'subject'      => $title,
//        ];
//        $data = [
//            'user'      => $user,
//            'title'     => $title,
//            'can_reply' => true,
//            'button'    => [
//                'text' => '前往 WeddingDay',
//                'link' => config('params.wdv3.www_url'),
//            ],
//        ];
//
//        // 直接顯示
//        // return view($view, $data);
//
//        // MailService 寄信
//        $mailService->send($params, $view, $data);

        dd($title . ': done!!');
    }

    /**
     * 寄出所有的信
     * @url test/all-mails
     *
     * @return void
     */
    public function allMails(
        FounderLetterService $founderLetterService,
        EmailVerificationService $emailVerificationService,
        EditPasswordService $editPasswordService,
        ArticleAuthorByCommentService $articleAuthorByCommentService,
        ParentCommentAuthorService $parentCommentAuthorService,
        ArticleAtUsersService $articleAtUsersService,
        ArticleChangeCategoryService $articleChangeCategoryService,
        ArticleTracksService $articleTracksService,
        CommentAtUsersService $commentAtUsersService,
        DeleteArticleService $deleteArticleService,
        DeleteCommentService $deleteCommentService,
        LikeArticleService $likeArticleService,
        LikeCommentService $likeCommentService,
        NewArticleService $newArticleService,
        NewCommentArticleTracksService $newCommentArticleTracksService,
        EditEmailService $editEmailService
    )
    {
        // 任務類別使用 SerializesModels trait，當任務被執行時， Eloquent 模型會優雅的被序列話化和解序列化。
        // 如果你的隊列任務在建構子接收一個 Eloquent 模型，只有模型的識別子(identifier)會被序列化被放進隊列中。
        // 當任務真正被處理時，隊列系統會自動的重新從資料庫獲取完整的模型實例。
        $user = User::where('email', env('MAIL_TEST'))->first();
        $article = Article::status('published')->inRandomOrder()->first();
        $comment = Comment::status('published')->inRandomOrder()->first();
        $category = Category::live()->where('id', '!=', $article->category_id)->inRandomOrder()->first();
        $store = Store::where('email', env('MAIL_TEST'))->inRandomOrder()->first();
        $min_token = Str::random(4);
        $max_token = Str::random(45);

        // App\Services\Mail\Auth
        $founderLetterService->sendMail($user);
        $emailVerificationService->sendMail($user);
        $editPasswordService->sendMail($user, $max_token);

        // App\Services\Mail\Forum
        $articleAuthorByCommentService->sendMail($user, $comment);
        $parentCommentAuthorService->sendMail($user, $comment);
        $articleAtUsersService->sendMail($user, $article);
        $articleChangeCategoryService->sendMail($user, $article, $category->id);
        $articleTracksService->sendMail($user, $article);
        $commentAtUsersService->sendMail($user, $comment);
        $deleteArticleService->sendMail($user, $article);
        $deleteCommentService->sendMail($user, $comment);
        $likeArticleService->sendMail($article);
        $likeCommentService->sendMail($comment);
        $newArticleService->sendMail($user, $article);
        $newCommentArticleTracksService->sendMail($user, $comment);

        // App\Services\Mail\User
        $editEmailService->sendMail($user, $user->email, $min_token);

        dd('寄出所有的信！');
    }

    /**
     * 排程：WeddingDay 發佈文章互動通知
     * @url test/job/SendEmailNewArticle
     *
     * 排程：WeddingDay 文章標記通知
     * @url test/job/SendEmailArticleAtUsers
     *
     * 排程：WeddingDay 追蹤文章更新通知
     * @url test/job/SendEmailArticleTracks
     *
     * 排程：WeddingDay 留言標記通知
     * @url test/job/SendEmailCommentAtUsers
     *
     * 排程：WeddingDay 追蹤文章新留言通知
     * @url test/job/SendEmailNewCommentArticleTracks
     *
     * 排程：WeddingDay 文章新留言通知
     * @url test/job/SendEmailNewComment
     *
     * @return void
     */
    public function job($class)
    {
        $user = User::where('email', env('MAIL_TEST'))->first();
        if (!$user) {
            dd('資料庫找不到 ' . env('MAIL_TEST'));
        }
        $article = $user->articles()->status('published')->first();
        if (!$article) {
            dd('資料庫找不到合適的文章！');
        }
        $comment = $article->comments()->status('published')->first();
        if (!$comment) {
            dd('資料庫找不到合適的留言！ (article_id: ' . $article->id . ')');
        }
        $reply = $comment->replies()->status('published')->first();
        if (!$reply) {
            dd('資料庫找不到合適的回應！ (article_id: ' . $article->id . ', comment_id: ' . $comment->id . ')');
        }

        switch ($class) {
            case 'SendEmailNewArticle':
                SendEmailNewArticle::dispatch($article);
                dd('WeddingDay 發佈文章互動通知: done!!');

            case 'SendEmailArticleAtUsers':
                $article->atUsers()->detach($user->id);
                $article->atUsers()->attach($user->id);
                SendEmailArticleAtUsers::dispatch($article);
                dd('WeddingDay 文章標記通知: done!!');

            case 'SendEmailArticleTracks':
                $article->tracks()->detach($user->id);
                $article->tracks()->attach($user->id);
                SendEmailArticleTracks::dispatch($article, $user);
                dd('WeddingDay 追蹤文章更新通知: done!!');

            case 'SendEmailCommentAtUsers':
                $comment->atUsers()->detach($user->id);
                $comment->atUsers()->attach($user->id, ['article_id' => $article->id]);
                SendEmailCommentAtUsers::dispatch($comment);
                dd('WeddingDay 留言標記通知: done!!');

            case 'SendEmailNewCommentArticleTracks':
                $article->tracks()->detach($user->id);
                $article->tracks()->attach($user->id);
                SendEmailNewCommentArticleTracks::dispatch($comment);
                dd('WeddingDay 追蹤文章新留言通知: done!!');

            case 'SendEmailNewComment':
                SendEmailNewComment::dispatch($comment);
                SendEmailNewComment::dispatch($reply);
                dd('WeddingDay 文章新留言通知: done!!');
        }
    }

    /**
     * 在特定文章中，幫譚菜菜回應部分留言
     * @url tim/auto-reply/{last_comment_id}
     *
     * @return void
     */
    public function autoReply(
        $last_comment_id,
        User $user,
        Article $article,
        Comment $comment,
        CommentService $commentService
    )
    {
        // 驗證留言是否正確
        if (!$comment->live()->whereNull('parent_id')->find($last_comment_id)) {
            dd('錯誤的留言編號!!');
        }

        $editor_tan_id = 80162;
        $article_id = 724;

        $editor_tan = $user->find($editor_tan_id);
        $article = $article->find($article_id);
        foreach ($article->comments as $comment) {

            // 僅需部分留言
            if ($comment->id > $last_comment_id) {
                break;
            }

            // 排除譚菜菜已回應的留言
            if ($comment->replies()->where('user_id', $editor_tan_id)->first()) {
                continue;
            }

            $request = [
                'user'         => $editor_tan,
                'article'      => $article,
                'comment_id'   => '',
                'parent_id'    => $comment->id,
                'is_anonymous' => 0,
                'content'      => '寄到你信箱囉！有沒有給菜菜這篇公告一個讚呢？😀',
                'auto_reply'   => true,
            ];
            $commentService->run($request);

            echo $comment->id . "<br />";
        }

        dd('在特定文章中，幫譚菜菜回應部分留言: done!!');
    }

    /**
     * 近3個月內有互動的新娘人數 ＆ 其中第一篇留言是婚禮表格的人數 & 再其中留言超過兩則的人數
     * @url tim/interactive-user
     *
     * @return void
     */
    public function interactiveUser(
        User $userModel
    ) {
        // 近3個月內有互動的新娘
        $afterAt = now()->subMonths(3);
        $users   = $userModel->interactiveByAfterAt($afterAt)
                                // 排除取消訂閱
                                ->whereDoesntHave('subscription', function ($q1) {
                                    $q1->where('forum_new_article', 0);
                                })
                                ->get();

        $targetCount = 0;
        $activeCount = 0;
        foreach ($users as $user) {
            $comment = $user->comments()->orderBy('created_at')->first();
            if ($comment && $comment->article_id == 724) {
                $targetCount++;
                if ($user->comments->count() > 1) {
                    $activeCount++;
                }
            }
        }

        dd(
            '近3個月內有互動的新娘人數: ' . $users->count(),
            // '論壇所有留言的人數: '.$users->count(),
            '其中第一篇留言是婚禮表格的人數: ' . $targetCount,
            '再其中留言超過兩則的人數: ' . $activeCount
        );
    }

    /**
     * 爬結婚吧的商家聯絡資訊
     * @url test/crawler-marry-studio-contact/{studio_id}
     *
     * @return void
     */
    use CurlTrait;
    public function crawlerMarryStudioContact()
    {
        // ================================================
        // 商家列表
        // ================================================
        $url = 'https://www.marry.com.tw/venue-shop';
        $listHtml = $this->getCurlResponseContent($url);

        // 取得目前網址
        $pattern = '/<input type=\"hidden\" id=\"now_link\" href=\"(.*)\"\/>/Usi';
        preg_match_all($pattern, $listHtml, $matches, PREG_SET_ORDER);
        $nowLink = $matches[0][1];

        // 取得 marry_access_toke
        $pattern = '/<input type=\"hidden\" id=\"marry_access_toke\" value=\"(.*)\"\/>/Usi';
        preg_match_all($pattern, $listHtml, $matches, PREG_SET_ORDER);
        $accessToke = $matches[0][1];

        // Ajax 商家列表
        $ajaxUrl = 'https://www.marry.com.tw/ajax/?ma=business_basic-get_list';
        $data = [
            'href'              => $nowLink,
            'pagesize'          => 21,
            'filter_search_ref' => 1,
            'module'            => 'business_basic',
            'action'            => 'get_list',
            'marry_access_toke' => $accessToke,
        ];
        $headers = [
            "X-Requested-With: XMLHttpRequest",
        ];
        $resultJson = $this->getCurlResponseContent($ajaxUrl, $data, 'POST', $headers);
        $resultObj  = json_decode($resultJson);

        // 取得商家ID
        $pattern = '/href=\"https:\/\/www\.marry\.com\.tw\/studio-(\d*)\"/Usi';
        preg_match_all($pattern, $resultObj->item, $matches, PREG_SET_ORDER);
        $studioIds = collect($matches)->pluck(1)->unique();

        // 取得下一頁網址
        $pattern = '/<a id=\"next_pages\" href=\"(.*)\"/Usi';
        preg_match_all($pattern, $resultObj->pages_html, $matches, PREG_SET_ORDER);
        $nextLink = $matches[0][1] ?? NULL;

        dd($studioIds, $nextLink);

        // ================================================
        // 商家主頁
        // ================================================
        $url = 'https://www.marry.com.tw/studio-809700';
        $result = $this->getCurlResponseContent($url);

        // 取得 marry_access_toke
        $pattern = '/<input type=\"hidden\" id=\"marry_access_toke\" value=\"(.*)\"\/>/Usi';
        preg_match_all($pattern, $result, $matches, PREG_SET_ORDER);
        $accessToke = $matches[0][1];

        // 商家主頁-聯絡資訊-點擊查看
        $url = 'https://www.marry.com.tw/ajax/?ma=studio-basic_info';
        $data = [
            'm_id'              => 80970,
            'module'            => 'studio',
            'action'            => 'basic_info',
            'marry_access_toke' => $accessToke,
        ];
        $headers = [
            "X-Requested-With: XMLHttpRequest",
        ];
        $resultJson = $this->getCurlResponseContent($url, $data, 'POST', $headers);
        $resultObj  = json_decode($resultJson);
        $emailLink  = $resultObj->info->email_link;

        $emailLink = 'https://www.marry.com.tw/image-string.html?hide=0&chkkey=WjoHbQl4BmtRO1BhUjUGZgJjVThXOlIXBDpSO1JhATtWOw4nA2gBO1BvAS5QMwVmVjQGZAdrXm9YMFJlVTcBYw==&bgselect=5&fontsize=2';

        $vision = new VisionClient(['keyFilePath' => base_path(env('FIREBASE_CREDENTIALS'))]);
        $imageData = file_get_contents($emailLink);
        $image = $vision->image($imageData, ['text']);
        $result = $vision->annotate($image);

        dd($result, $result->info()['fullTextAnnotation']['text']);
    }

    /**
     * 贈送所有付費商家一個月的設定費 (使用費不折抵)
     */
    public function addPaidStoreFreeSettingTimes(
        Store $store,
        StoreOrderSet $storeOrderSet
    ) {
        $freeSettingTimes = 1;
        $isPrepaid        = 0;
        $yzcube_user_id   = 26;

        $stores = $store->select('stores.*')
                        ->whereIn('status', ['published', 'pending'])
                        ->join($storeOrderSet->getTable(), function($join) {
                            $join->on('store_order_set.store_id', '=', 'stores.id')
                                    ->where('deadline', '>', now())
                                    ->whereNull('free_setting_times');
                        })
                        ->orderBy('stores.id')
                        ->get();
        foreach ($stores as $store) {
            $this->updateFreeSettingTimes($store, $freeSettingTimes, $isPrepaid, $yzcube_user_id);
            echo "({$store->id}) {$store->name}: 贈送一期設定費<br />";
        }
    }

    /**
     * 更新免設定費的設定
     */
    private function updateFreeSettingTimes($store, $freeSettingTimes, $isPrepaid, $yzcube_user_id)
    {
        // 計算免設定費的免費期間
        $timeRange = $this->getFreeSettingTimeRange($store, $freeSettingTimes);

        // 紀錄免設定費設定
        $store->logFreeStoreOrderSets()->create([
            'yzcube_user_id' => $yzcube_user_id,
            'column'         => 'free_setting_times',
            'value'          => $freeSettingTimes,
            'is_prepaid'     => $isPrepaid,
            'free_start_at'  => $timeRange['free_start_at'],
            'free_end_at'    => $timeRange['free_end_at'],
        ]);

        // 儲存
        $store->orderSet->is_prepaid_setting = $isPrepaid;
        $store->orderSet->free_setting_times = $freeSettingTimes;
        $store->orderSet->save();
    }

    /**
     * 新增活動表單流水號欄位
     * @url tim/event-columns-add-serial-number
     *
     * @return void
     */
    public function EventColumnsAddSerialNumber(
        Event $event
    ) {
        $events = $event->withTrashed()
                        ->where('columns', 'not like', '%serial_number%')
                        ->get();
        foreach ($events as $event) {
            $columns = json_decode($event->columns);
            foreach ($columns as $key => $column) {
                $columns[$key]->serial_number = $key;
            }
            $event->columns = json_encode($columns, JSON_UNESCAPED_UNICODE);
            $event->save();
            echo "({$event->id}) {$event->title}<br />";
        }
    }

    /**
     * 計算免設定費的免費期間
     */
    private function getFreeSettingTimeRange($store, $freeSettingTimes)
    {
        // 取出結算日
        $closingDay = str_pad($store->orderSet->closing_day, 2, '0', STR_PAD_LEFT);

        // (下一期)免設定費的起始日
        $deadline = $store->orderSet->deadline;
        $freeStartAt = date('Y-m-d 00:00:00', strtotime($deadline.'+1 day'));

        // (未知期)免設定費的結束日
        $freeEndAt = date('Y-m-d 23:59:59', strtotime($deadline.'+'.$freeSettingTimes.' months'));
        // 驗證小月(28號~31號)的問題
        if (date('d', strtotime($freeEndAt)) != $closingDay) {
            $freeEndAt = date('Y-m-d 23:59:59', strtotime(date('Y-m-01', strtotime($freeEndAt)).'-1 day'));
        }

        return [
            'free_start_at' => $freeStartAt,
            'free_end_at'   => $freeEndAt,
        ];
    }

    /**
     * 新娘未讀訊息
     * @url tim/user-unread-messages
     *
     * @return void
     */
    public function userUnreadMessages()
    {
        echo "store_id,user_id<br />";

        // 未讀時間 use Google\Cloud\Core\Timestamp
        $after_at = new Timestamp(now()->subDays(3));

        $firestore = app('firebase.firestore');
        $roomSanps = $firestore->database()
                                ->collection('messages')
                                ->where('store_last_msg_updated_at', '<=', $after_at)
                                ->documents();
        foreach ($roomSanps as $roomSanp) {
            $roomObj = $roomSanp->data();
            if (empty($roomObj['user_id']) OR empty($roomObj['user_unread'])) {
                continue;
            }
            echo $roomObj['store_id'].",".$roomObj['user_id']."<br />";
        }
    }

    /**
     * 所有圖片重新取寬高
     * @url get-image-geometry/{file_name?}
     *
     * @return void
     */
    public function getImageGeometry(
        GetImageInfoService $getImageInfoService,
        $file_name
    ) {
        $data = $getImageInfoService->run($file_name);
        dd($data);

        // // 釋放memory_limit
        // ini_set('memory_limit', '-1');
        // // 能夠背景執行到結束
        // ignore_user_abort(true);
        // // 設置最大執行時間,0為無限制
        // set_time_limit(0);
        // // 控制nginx不要cache
        // header('X-Accel-Buffering: no');
        // // 清空緩衝區並關閉輸出緩衝
        // ob_end_clean();
        // // 刷新頁面
        // ob_implicit_flush(1);

        // // 取得S3上圖片的資訊
        // $imageModel = new \App\Models\Image;
        // $getImageInfoService = new \App\Services\File\GetImageInfoService;

        // // 所有圖片重新取寬高
        // $imageChunks = $imageModel->where(function($query) {
        //                                 $query->whereNull('width')->orWhereNull('height');
        //                             })
        //                             ->get()->chunk(5000);
        // foreach ($imageChunks as $images) {
        //     foreach ($images as $image) {
        //         $data = $getImageInfoService->run($image->file_name);
        //         if ($data) {
        //             $image->width  = $data['width'];
        //             $image->height = $data['height'];
        //             $image->save();
        //         }
        //     }
        // }
    }

    /**
     * 複製商家相本
     * @url store-copy-albums
     *
     * @return void
     */
    public function storeCopyAlbums(
        Store $store
    ) {
        // 舊商家
        $oldStore = $store->find(68);

        // 新商家
        $newStore = $store->find(4951);

        if ($newStore->showAlbums->count()) {
            dd('誒～想幹嘛？新商家已經有相本囉！！！');
        }

        // 舊商家的所有相本
        foreach ($oldStore->showAlbums as $album) {
            if ($newStore->showAlbums()->where('cover_id', $album->cover_id)->exists()) {
                continue;
            }

            $albumData = $album->getAttributes();
            unset($albumData['id']);
            $albumData['description'] = json_decode($albumData['description']);

            // 新增相本
            $newAlbum = $newStore->showAlbums()->create($albumData);
            echo '新增相本：'.$newAlbum->name;

            // 舊相本的所有照片
            $imageData = [];
            foreach ($album->images as $image) {
                $_temp = $image->getAttributes();
                unset($_temp['id']);
                unset($_temp['album_id']);
                $imageData[] = $_temp;
            }

            // 新增照片
            $newAlbum->images()->createMany($imageData);
            echo '，共'.$newAlbum->images->count().'張相片！<br/>';
        }

        echo '（總計複製'.$newStore->showAlbums->count().'個相本）';
    }

    /**
     * 重寄活動報名完成信
     * @url tim/resend-email-event-reports
     *
     * @return void
     */
    public function resendEmailEventReports(
        Event $event
    ) {
        // 取得活動報名資訊
        $event = $event->find(395);
        if (!$event) {
            echo "找不到此活動！(event_id: 395)";
            return;
        }

        $count      = 0;
        $report_ids = [59542,59543];
        // $report_ids = [60963,60965,60967,60968,60969,60970,60971,60973,60974,60975,60976,60978,60979,60980,60981,60986,60987,60988,60989,60992,60994,60995,60996,60997,60998,61000,61001,61003,61004,61006,61008,61009,61011,61012,61014,61018,61020,61021,61022,61025,61026,61027,61029,61030,61031,61033,61036,61037,61041,61042,61044,61045,61046,61047,61049,61050,61051,61053,61054,61055,61056,61057,61059,61060,61061,61064,61067,61068,61070,61072,61073,61074,61075,61092,61093,61095,61109,61110,61114,61115,61116,61117,61118,61121,61122,61123,61125,61126,61128,61130,61131,61134,61136,61137,61139,61140,61143,61144,61151,61152,61154,61194,61337];
        foreach ($report_ids as $report_id) {

            // 取得活動報名紀錄
            $report = $event->reports()->find($report_id);
            if (!$report) {
                echo "找不到此報名紀錄！(report_id: {$report_id})<br />";
                continue;
            }

            // WeddingDay 活動報名完成信
            $completedService = resolve(\App\Services\Mail\Event\CompletedService::class);
            $completedService->sendMail($event, $report);
            $count++;

            // echo "重寄活動報名完成信：{$report_id}<br />";
        }

        echo '總計重寄 '.$count.' 封活動報名完成信！';
    }

    /**
     * 更新活動表單的日期欄位
     * @url tim/update-event-date-column
     *
     * @return void
     */
    public function updateEventDateColumn()
    {
        echo '<pre>';

        $events = \App\Models\Event::all();
        foreach ($events as $event) {
            $columns = json_decode($event->columns);
            $columns = collect($columns)->each(function($item) use ($event) {
                if ($item->format != 'date') {
                    return $item;
                }

                echo $event->id.'：<br />';

                $item->date_config = [
                    'disabledAfterDate'       => '',
                    'disabledPast'            => 0,
                    'disabledToday'           => 0,
                    'disabledWeek'            => [],
                    'disabledNationalHoliday' => 0,
                    'disabledDate'            => [],
                    'disabledBeforeDay'       => 0,
                    'disabledAfterDay'        => 0,
                    'keepDay'                 => 0,
                ];
                if (isset($item->disabled_before)) {
                    echo 'disabled_before: '.$item->disabled_before.'<br />';

                    if ($item->disabled_before === 0) {
                        $item->date_config['disabledPast'] = 1;
                    }

                    if ($item->disabled_before > 0) {
                        $item->date_config['disabledToday'] = 1;
                        $item->date_config['disabledBeforeDay'] = $item->disabled_before - 1;
                    }
                }

                if (isset($item->disabled_after)) {
                    echo 'disabled_after: '.$item->disabled_after.'<br />';

                    $item->date_config['disabledAfterDate'] = $item->disabled_after;
                }

                print_r($item->date_config);
                echo '<br />';

                return $item;
            });

            // 儲存活動表單
            $event->columns = json_encode($columns, JSON_UNESCAPED_UNICODE);
            $event->save();
        }
    }

    /**
     * 既有的活動訂單記錄，發票資訊欄位塞入預設值
     * @url tim/update-event-order-invoice-data
     *
     * @return void
     */
    public function updateEventOrderInvoiceData(
        Event $event
    ) {
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);
        ob_implicit_flush(1);

        $events = $event->where('use_payment', 1)->withTrashed()->get();
        foreach ($events as $event) {
            $eventOrders = $event->orders()->withTrashed()->get();
            foreach ($eventOrders as $eventOrder) {

                // 免費訂單、發票資訊有值，省略！
                $total = $eventOrder->amount;
                if (!$total OR $eventOrder->invoice_data) {
                    continue;
                }

                // 活動訂單品項
                $items = [];
                foreach ($eventOrder->orderItems as $orderItem) {
                    $price   = $orderItem->original_price ?: $orderItem->selling_price;
                    $items[] = [
                        'name'     => $orderItem->product_name.'：'.$orderItem->item_name,
                        'price'    => $price,
                        'quantity' => $orderItem->quantity,
                        'amount'   => $price * $orderItem->quantity,
                    ];
                }
                // 活動訂單明細
                foreach ($eventOrder->present()->detailList as $detail) {
                    if ($detail['key'] == 'subtotal') {
                        continue;
                    }
                    $price   = $detail['price'] * ($detail['is_positive'] ? 1 : -1);
                    $items[] = [
                        'name'     => $detail['name'],
                        'price'    => $price,
                        'quantity' => 1,
                        'amount'   => $price,
                    ];
                }

                // 銷售額(未稅)
                $sales = round($total / 1.05);

                // 先儲存發票資訊
                $eventOrder->invoice_data = [
                        'type'           => 'email',
                        'buyer_ubn'      => '',
                        'buyer_name'     => $eventOrder->allReport->name,
                        'buyer_email'    => $eventOrder->allReport->email,
                        'carrier_type'   => NULL,
                        'carrier_number' => NULL,
                        'order_no'       => $eventOrder->order_no, // 訂單編號
                        'items'          => json_encode($items), // 品項明細
                        'note'           => $eventOrder->logPayment->creditCard->last_four ?? '', // 開立統一發票時，於發票備註欄載明簽帳卡號末四碼
                        'sales'          => $sales, // 銷售額(未稅)
                        'tax'            => $total - $sales, // 稅額
                        'total'          => $total, // 發票金額
                ];
                $eventOrder->save();
            }
        }
        echo "done!";
    }

    /**
     * 寄出公開詢價的試吃優惠卷
     * @url tim/send-user-quote-tasting-coupon
     *
     * @return void
     */
    public function sendUserQuoteTastingCoupon(
        \App\Models\User $user,
        \App\Models\Wdv2\UserQuote $userQuote,
        \App\Services\Coupon\UserQuoteTastingService $userQuoteTastingService
    ) {
        $users = $user->select('users.*')
                        ->join($userQuote->getTable(), function($join) use ($userQuoteTastingService) {
                            $join->on('user_quote.user_id', '=', 'users.id')
                                    ->whereIn('type', $userQuoteTastingService->quoteStoreTypes)
                                    ->whereBetween('user_quote.created_at', $userQuoteTastingService->dateRange);
                        })
                        ->groupBy('user_id')
                        ->havingRaw('COUNT(DISTINCT type) = ?', [count($userQuoteTastingService->quoteStoreTypes)])
                        ->get();

        // 公開詢價的試吃優惠卷
        foreach ($users as $user) {
            $userQuoteTastingService->run($user);
        }
    }

    /**
     * 簡訊測試
     * @url tim/send-sms
     *
     * @return void
     */
    public function sendSms(
        \App\Services\Message\Surenotify\SmsHandle $smsHandle,
        \App\Models\User $user
    ) {
        dd(
            $smsHandle->handle('send_messages', [
                'content'     => '簡訊內容'.Str::random(4),
                'phones'      => '0987654321,0912345678',
                'targetModel' => $user->find(87333),
            ])
        );
    }

    /**
     * 檢查商家是否有主要品牌
     * @url tim/check-store-primary-brand
     *
     * @return void
     */
    public function checkStorePrimaryBrand(
        \App\Models\Store $store
    ) {
        $store->withTrashed()
                ->whereDoesntHave('primaryBrand')
                ->get()
                ->map(function($store) {

                    // 新增品牌
                    $brand = $store->allBrands()->create([
                        'name' => $store->name,
                        'email'=> $store->email,
                    ]);

                    // 使用中介模型設定 pivot 資料
                    $brand->stores()->updateExistingPivot($store->id, [
                        'show_flag'  => 2,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                });
    }

    /**
     * 周胖測試用
     * @url tim
     *
     * @return void
     */
    public function tim(

    ) {
        dd('test');
    }
}
