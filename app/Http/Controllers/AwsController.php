<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\AWS\SNS\MailEvent\EventHandle;

class AwsController extends Controller
{
    /**
     * AWS SNS Email Event (正式機已改用Lambda紀錄)
     *
     * @url api/aws-sns/mail-event
     * @method POST
     * @param EventHandle $eventHandle
     * @return void
     */
    public function mailEvent(
        EventHandle $eventHandle
    ) {
        // 測試環境以下中斷
        if (env('APP_DEBUG')) {
            return;
        }

        // 建立Log資料夾: storage/logs/sns-event
        $logPath = storage_path('logs/sns-event');
        if (!is_dir($logPath)) {
            mkdir($logPath, 0777);
        }

        // Amazon SES event 通知內容:
        // https://docs.aws.amazon.com/zh_tw/ses/latest/DeveloperGuide/event-publishing-retrieving-sns-examples.html
        $snsResponse = request()->getContent();
        $data = json_decode($snsResponse);

        //第一次訂閱sns的時候..會發認證連結..所以把他存成檔案
        if ($data->Type == 'SubscriptionConfirmation') {
            file_put_contents($logPath . '/SubscriptionConfirmation.log', $snsResponse);
            return;
        }

        //存成檔案
        $snsMessageId = $data->MessageId;
        if (env('SNS_FILE_LOG', false)) {
            file_put_contents($logPath.'/'.$snsMessageId, $snsResponse);
        }

        // 解析SES Message
        $message = json_decode($data->Message);

        //事件處理
        $eventHandle->handle($message);
    }
}
