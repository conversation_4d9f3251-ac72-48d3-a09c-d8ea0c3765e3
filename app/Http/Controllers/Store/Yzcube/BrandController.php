<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Store\Yzcube\BrandRequest;
use App\Repositories\BrandRepository;
use App\Services\Store\Yzcube\Brand\SaveService;
use App\Services\Store\Yzcube\Brand\MergeService;
use App\Services\Store\Yzcube\Brand\UnionService;
use App\Services\Store\Yzcube\Brand\SearchService;
use App\Transformers\Store\Yzcube\BrandTransformer;
use App\Formatters\ApiFormatter;

class BrandController extends Controller
{
    /**
     * 品牌列表
     *
     * @url brand/yzcube
     * @method GET
     * @haeder Yzcube-Token
     * @param type, status, keyword, sort:id(default)|created_at, direction:asc|desc(default), page
     *
     * @return json
     */
    public function list(
        BrandRepository $brandRepository,
        BrandTransformer $brandTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得品牌列表
        $result = $brandRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $brandTransformer->list($result);

        return $formatter->json($result);
    }

    /**
     * 品牌詳細內容頁
     *
     * @url brand/yzcube/{store_id}
     * @method GET
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function show(
        BrandTransformer $brandTransformer,
        ApiFormatter $formatter
    ) {
        // 取得品牌
        $brand = request('brand');

        // Transformer
        $result = $brandTransformer->show($brand);

        return $formatter->json($result);
    }


    /**
     * 儲存品牌資料
     *
     * @url brand/yzcube/{store_id}/save
     * @method POST
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function save(
        BrandRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        $brand = $saveService->run(request());

        return $formatter->json(['brand_id' => $brand->id]);
    }



    /**
     * 合併兩個品牌
     *
     * @url brand/yzcube/{store_id}/merge
     * @method POST
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function merge(
        MergeService $mergeService,
        ApiFormatter $formatter
    ) {

        // Request
        $request = request();

        $brand_id = $mergeService->run(request());

        return $formatter->json(['brand_id' => $brand_id]);
    }



    /**
     * 與線上服務的品牌關聯
     *
     * @url brand/yzcube/{store_id}/union
     * @method POST
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function union(
        UnionService $unionService,
        ApiFormatter $formatter
    ) {

        $request = request();

        $brand_id = $unionService->run($request);

        return $formatter->json(['brand_id' => $brand_id]);
    }


    /**
     * 搜尋
     *
     * @url brand/yzcube/{store_id}/search
     * @method POST
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function search(
        SearchService $searchService,
        BrandTransformer $brandTransformer,
        ApiFormatter $formatter
    ) {
        $request = request();

        $result = $searchService->search($request);

        // Transformer
        $result = $brandTransformer->searchResult($result);

        return $formatter->json($result);
    }


    /**
     * 邀請商家來維護資料
     *
     * @url brand/yzcube/{store_id}/invite
     * @method POST
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function invite(
        ApiFormatter $formatter
    ) {


        //return $formatter->json(['brand_id' => $brand->id]);
    }

}
