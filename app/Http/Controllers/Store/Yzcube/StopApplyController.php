<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Formatters\ApiFormatter;
use App\Http\Controllers\Controller;
use App\Traits\ApiErrorTrait;
use App\Repositories\ServiceStopApplyRepository;
use App\Models\Wdv2\ServiceStopApply;
use App\Models\ServiceStopApplyChangeLog;
use App\Models\ServiceStopApplyComment;
use App\Services\Store\Yzcube\ServiceStopApproveService;
use App\Transformers\Store\Yzcube\ServiceStopTransformer;

class StopApplyController extends Controller
{
    use ApiErrorTrait;

    /**
     * 下架管理-列表
     *
     * @url api/store/yzcube/stop-apply
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param date $start_date 起始日期
     * @param date $end_date 結束時間
     * @param string $store_type 商家類型
     * @param string $status 處理狀態 0:未處理, 1:處理中, 2:已結案
     * @param string $system_flag 是否系統自動架 0:否, 1:是
     * @param string $contract_status 合約狀態 running:方案型 renewing:月付型 ended:合約終止 never:未曾付費 not_need:不需要合約
     * @param string $auto_pay 自動續約 0:不同意, 1:同意, null:未選擇
     * @param string $pay_off_status 商家還款狀態 0:尚未還款 1:已還款 2:無須還款
     * @param int $refund_status WD退款狀態 0:尚未退款 1:已退款 2:無須退款
     * @param int $deposit_status 折讓單開立狀態 0:尚未開立 1:已開立 2:無須開立
     * @param int $keyword 搜尋關鍵字
     * @param string $sort 排序 id(default)|created_at
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪(default)
     * @param int $page 頁碼
     *
     * @return json
     */
    public function index(
        ServiceStopApplyRepository $serviceStopApplyRepository,
        ServiceStopTransformer $serviceStopTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得下架申請列表
        $data = $serviceStopApplyRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $serviceStopTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 下架管理-詳細內容頁
     *
     * @url api/store/yzcube/stop-apply/{apply_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $apply_id 下架申請ID *
     * @return json
     */
    public function show(
        ServiceStopTransformer $serviceStopTransformer,
        ApiFormatter $formatter,
        /// 把變數命名的跟路由參數一樣就能直接注入搜尋結果的model ..
        /// ref https://learnku.com/docs/laravel/8.x/routing/9365#route-model-binding
        ServiceStopApply $apply
    ) {
        // Transformer
        $result = $serviceStopTransformer->show([
            'yzcubeUser' => request('yzcube_user'),
            'apply'      => $apply,
        ]);

        return $formatter->json($result);
    }

    /**
     * 下架管理-變更狀態
     *
     * @url api/store/yzcube/stop-apply/{apply_id}/change-status
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $apply_id 下架申請ID *
     * @param string $type 狀態欄位 pay_off_status:商家還款狀態, refund_status:WD退款狀態, deposit_status:折讓單開立狀態 *
     * @param int $value 變更的狀態值 *
     * @return json
     */
    public function changeStatus(
        ServiceStopApplyChangeLog $serviceStopApplyChangeLog,
        ServiceStopTransformer $serviceStopTransformer,
        ApiFormatter $formatter,
        ServiceStopApply $apply
    ) {
        // 判斷是否有變更
        $type     = request('type');
        $original = $apply->{$type};
        if ($original == request('value')) {
            $this->setException('相同的'.$serviceStopApplyChangeLog->typeList[$type].'，不需要變更！');
        }

        // 新增變更狀態紀錄
        $log = $serviceStopApplyChangeLog->create([
            'service_stop_applies_id' => $apply->id,
            'yzcube_user_id'          => request('yzcube_user')->id,
            'type'                    => $type,
            'original'                => $original,
            'now'                     => request('value'),
        ]);

        // 下架申請的處理狀態，修改為處理中
        $apply->status  = 1;
        $apply->{$type} = request('value');
        $apply->save();

        // Transformer
        $result = $serviceStopTransformer->organizeChangeLog($log);

        return $formatter->json($result);
    }

    /**
     * 下架管理-新增留言
     *
     * @url api/store/yzcube/stop-apply/{apply_id}/comment
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $apply_id 下架申請ID *
     * @param string $comment 留言 *
     * @return json
     */
    public function comment(
        ServiceStopApplyComment $serviceStopApplyComment,
        ServiceStopTransformer $serviceStopTransformer,
        ApiFormatter $formatter,
        ServiceStopApply $apply
    ) {
        // 判斷是否有留言
        if (!request('comment')) {
            $this->setException('Say something...');
        }

        // 下架申請的處理狀態，修改為處理中
        $apply->status = 1;
        $apply->save();

        // 新增留言
        $comment = $serviceStopApplyComment->create([
            'service_stop_applies_id' => $apply->id,
            'yzcube_user_id'          => request('yzcube_user')->id,
            'comment'                 => request('comment')
        ]);

        // Transformer
        $result = $serviceStopTransformer->organizeComment($comment);

        return $formatter->json($result);
    }

    /**
     * 下架管理-下架清算
     *
     * @url api/store/yzcube/stop-apply/{apply_id}/approve
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $apply_id 下架申請ID *
     * @return json
     */
    public function approve(
        ApiFormatter $formatter,
        ServiceStopApproveService $serviceStopApproveService,
        ServiceStopApply $apply
    ) {
        // 判斷是否已清算完畢
        if ($apply->status == 2) {
            $name = $apply->yzcubeUser ? $apply->yzcubeUser->name.'('.$apply->yzcubeUser->email.') ' : '';
            $this->setException($name.'在 '.$apply->resolve_date.' 時，已完成清算！');
        }

        // 執行下架清算
        $serviceStopApproveService->run($apply);

        return $formatter->json();
    }
}
