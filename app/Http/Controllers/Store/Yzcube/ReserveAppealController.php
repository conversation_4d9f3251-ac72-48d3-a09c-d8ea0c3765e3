<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Formatters\ApiFormatter;
use App\Models\Wdv2\ReserveAppeal;
use App\Repositories\ReserveAppealRepository;
use App\Transformers\Store\Yzcube\ReserveAppealTransformer;
use App\Http\Controllers\Controller;

class ReserveAppealController extends Controller
{
    /**
     * 無效詢問申請-列表
     *
     * @url api/store/yzcube/reserve-appeal
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $store_type 商家類型
     * @param int $reason 申請無效的理由
     * @param int $status 處理狀態
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 created_at:申請時間
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        ReserveAppealRepository $reserveAppealRepository,
        ReserveAppealTransformer $reserveAppealTransformer,
        ApiFormatter $formatter
    ) {
        $reserveAppeals = $reserveAppealRepository->getYzcubeListByRequest();

        $result = $reserveAppealTransformer->list($reserveAppeals);

        return $formatter->json($result);
    }
    /**
     * 無效詢問申請-詳細頁
     *
     * @url api/store/yzcube/reserve-appeal/{reserve_appeal_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param path $reserve_appeal_id 無效詢問申請ID
     * @return json
     */
    public function show(
        ReserveAppeal $reserveAppeal,
        ReserveAppealTransformer $reserveAppealTransformer,
        ApiFormatter $formatter
    ) {
        $result = $reserveAppealTransformer->show($reserveAppeal);

        return $formatter->json($result);
    }
}
