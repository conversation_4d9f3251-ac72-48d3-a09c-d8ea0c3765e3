<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Http\Controllers\Controller;
use App\Services\Store\Yzcube\DataAnalysisService;
use App\Transformers\Store\Yzcube\DashboardTransformer;
use App\Formatters\ApiFormatter;
use App\Traits\ApiErrorTrait;

class DashboardController extends Controller
{
    use ApiErrorTrait;

    /**
     * 作品/方案數據-初始化
     *
     * @url api/store/yzcube/data-initialization
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function dataInitialization(
        DashboardTransformer $dashboardTransformer,
        ApiFormatter $formatter
    ) {
        // Transformer
        $result = $dashboardTransformer->dataInitialization();

        return $formatter->json($result);
    }
    /**
     * 作品/方案數據-列表
     *
     * @url api/store/yzcube/data-analysis
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $data_type 資料類型 album:相本 service:方案 venue_room:場地廳房 *
     * @param string $start_date 起始時間 Y-m-d *
     * @param string $end_date 結束時間 Y-m-d *
     * @param int $store_type 商家類型
     * @param string $store_status 商家狀態
     * @param string $data_status 相本/方案/場地廳房 狀態
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 page_views:瀏覽數 collect_count:收藏數
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $per_page 每頁數量
     * @param int $page 頁碼
     * @return json
     */
    public function dataAnalysis(
        DataAnalysisService $dataAnalysisService,
        DashboardTransformer $dashboardTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $dataAnalysisService->run($request);
        if (!$data) {
            $this->setException('無效的資料類型！');
        }

        // Transformer
        $result = $dashboardTransformer->dataAnalysis($data);

        return $formatter->json($result);
    }
}
