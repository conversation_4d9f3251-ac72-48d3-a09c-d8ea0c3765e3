<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Store\Yzcube\StoreRequest;
use App\Repositories\StoreRepository;
use App\Models\Wdv2\SetTag;
use App\Models\Store;
use App\Models\StoreUser;
use App\Services\Store\Yzcube\ShowService;
use App\Services\Store\Yzcube\FilterDateService;
use App\Services\Store\Yzcube\SaveService;
use App\Traits\Auth\ConvertPasswordTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\ApiErrorTrait;
use App\Transformers\Store\Yzcube\StoreTransformer;
use App\Formatters\ApiFormatter;

class StoreController extends Controller
{
    use ConvertPasswordTrait;
    use CreateTokenTrait;
    use ApiErrorTrait;

    /**
     * 商家列表
     *
     * @url api/store/yzcube
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param array $types 商家類型
     * @param string $status 商家狀態 published:上架中 pending:等待上架 checkout:下架清算中 leave:已下架 delete:停用
     * @param string $contract_status 合約狀態 running:方案型 renewing:月付型 ended:合約終止 never:未曾付費 not_need:不需要合約
     * @param array $marks 商家標記
     * @param string $keyword 關鍵字
     * @param string $sort 排序 id(default)|onlined_at
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪(default)
     * @param int $page 頁碼
     *
     * @return json
     */
    public function list(
        StoreRepository $storeRepository,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得商家列表
        $stores = $storeRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $storeTransformer->list($stores);

        return $formatter->json($result);
    }

    /**
     * 匯出商家
     *
     * @url api/store/yzcube/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param array $types 商家類型
     * @param string $status 商家狀態 published:上架中 pending:等待上架 checkout:下架清算中 leave:已下架 delete:停用
     * @param string $contract_status 合約狀態 running:方案型 renewing:月付型 ended:合約終止 never:未曾付費 not_need:不需要合約
     * @param array $tags 商家標記
     * @param string $keyword 關鍵字
     * @param string $sort 排序 id(default)|onlined_at
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪(default)
     *
     * @return json
     */
    public function exportCSV(
        StoreRepository $storeRepository,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得商家列表
        $stores = $storeRepository->getYzcubeListByRequest($request, false);

        // Transformer
        $result = $storeTransformer->exportCSV($stores);
        $filename = '[商家紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 商家詳細內容頁
     *
     * @url api/store/yzcube/{store_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function show(
        ShowService $showService,
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store = request('store');

        // Transformer
        $data = $showService->run($store);

        return $formatter->json($data);
    }

    /**
     * 商家詳細內容頁-篩選日期
     *
     * @url api/store/yzcube/{store_id}/filter-date
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param date $message_date[0] 搜尋私訊狀況-起始日
     * @param date $message_date[1] 搜尋私訊狀況-結束日
     * @param date $reserve_date[0] 搜尋詢問單狀況-起始日
     * @param date $reserve_date[1] 搜尋詢問單狀況-結束日
     * @param date $quote_date[0] 搜尋公開報價狀況-起始日
     * @param date $quote_date[1] 搜尋公開報價狀況-結束日
     * @param date $analytics_date[0] 搜尋流量狀況-起始日
     * @param date $analytics_date[1] 搜尋流量狀況-結束日
     * @return json
     */
    public function filterDate(
        FilterDateService $filterDateService,
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store   = request('store');
        $request = request();

        // Service
        $data = $filterDateService->run($store, $request);

        return $formatter->json($data);
    }

    /**
     * 儲存商家標記
     *
     * @url api/store/yzcube/{store_id}/save-marks
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param array.int $mark_ids 商家標記編號
     * @return json
     */
    public function saveMarks(
        SetTag $setTag,
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store  = request('store');
        $markIds = request('mark_ids', []);

        // 新增商家標記
        $pivotArray = [];
        foreach ($markIds as $markId) {
            $pivotArray[$markId] = ['type' => 1];
        }
        $store->marks()->syncWithoutDetaching($pivotArray);

        // 刪除商家標記
        $detachIds = $setTag->storeMarks()->whereNotIn('id', $markIds)->pluck('id');
        $store->marks()->detach($detachIds);

        return $formatter->json();
    }

    /**
     * 更改通知信箱
     *
     * @url api/store/yzcube/{store_id}/edit-email
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param string $email 通知信箱 *
     * @return json
     */
    public function editEmail(
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store = request('store');

        // 更改通知信箱
        $store->email = request('email');
        $store->save();

        return $formatter->json();
    }

    /**
     * 設定關聯活動表單
     *
     * @url api/store/yzcube/{store_id}/set-relation-events
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param string $shop_tasting 門市試吃的活動表單ID
     * @param string $delivery_tasting 宅配試吃的活動表單ID
     * @return json
     */
    public function setRelationEvents(
        ApiFormatter $formatter
    ) {
        // 取得商家 & 關聯類型
        $store = request('store');
        $types = request()->only(['shop_tasting', 'delivery_tasting']);

        // 先清空關聯
        $store->allEvents()->syncWithPivotValues($store->allEvents->pluck('id'), ['deleted_at' => now()]);

        // 關聯活動表單
        $pivotArray = [];
        foreach ($types as $type => $event_id) {
            $pivotArray[$event_id] = ['type' => $type, 'deleted_at' => NULL];
        }
        $store->allEventsWithTrashed()->syncWithoutDetaching($pivotArray);

        return $formatter->json();
    }

    /**
     * 儲存設定費設定
     *
     * @url api/store/yzcube/{store_id}/save-free-setting-times
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param bool $is_prepaid_setting 是否預付設定費
     * @param string $free_setting_times 免設定費的剩餘期數, 無:NULL|永久|(int)
     * @param string $free_usage_times 免使用費的剩餘期數, 無:NULL|永久|(int)
     * @param string $note 備註訊息
     * @return json
     */
    public function saveFreeSettingTimes(
        StoreRequest $request,
        SaveService $saveService,
        ShowService $showService,
        ApiFormatter $formatter
    ) {
        // Service
        $store = $saveService->run($request);

        // Transformer
        $data = $showService->getFreeSettingTimes($store);

        return $formatter->json($data);
    }

    /**
     * 儲存使用費設定
     *
     * @url api/store/yzcube/{store_id}/save-free-usage-times
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param bool $is_prepaid_setting 是否預付設定費
     * @param string $free_setting_times 免設定費的剩餘期數, 無:NULL|永久|(int)
     * @param string $free_usage_times 免使用費的剩餘期數, 無:NULL|永久|(int)
     * @param string $note 備註訊息
     * @return json
     */
    public function saveFreeUsageTimes(
        StoreRequest $request,
        SaveService $saveService,
        ShowService $showService,
        ApiFormatter $formatter
    ) {
        // Service
        $store = $saveService->run($request);

        // Transformer
        $data = $showService->getFreeUsageTimes($store);

        return $formatter->json($data);
    }

    /**
     * 儲存賣方發票設定 (Store to User)
     *
     * @url api/store/yzcube/{store_id}/save-seller-invoice
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param string $seller_name 賣方營業名稱 *
     * @param string $seller_ubn 賣方統編 *
     * @param string $seller_brand 賣方品牌名稱 *
     * @param string $merchant_id ezPay 商店代號 *
     * @param string $hash_key ezPay HashKey *
     * @param string $hash_iv ezPay HashIV *
     * @param string $spreadsheet_id Google雲端試算表編號 *
     * @param string $printer_ip 列表機IP *
     * @return json
     */
    public function saveSellerInvoice(
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store = request('store');

        // 儲存賣方發票設定
        $store->invoiceSetting()->updateOrCreate([], [
            'seller_name'    => request('seller_name'),
            'seller_ubn'     => request('seller_ubn'),
            'seller_brand'   => request('seller_brand'),
            'merchant_id'    => request('merchant_id'),
            'hash_key'       => request('hash_key'),
            'hash_iv'        => request('hash_iv'),
            'spreadsheet_id' => request('spreadsheet_id'),
            'printer_ip'     => request('printer_ip'),
        ]);

        return $formatter->json();
    }

    /**
     * 共同編輯帳號-新增
     *
     * @url api/store/yzcube/{store_id}/account/add
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param int $account_id 商家帳號ID
     * @param string $email 帳號(Email)
     * @param string $password 前端加密密碼
     * @return json
     */
    public function addAccount(
        StoreUser $storeUser,
        ShowService $showService,
        ApiFormatter $formatter,
        $store_id
    ) {
        // 取得商家
        $store = request('store');

        // 商家帳號ID
        if (request('account_id')) {
            $account = $storeUser->find(request('account_id'));
        }

        // 商家帳號email
        if (request('email')) {
            $account = $storeUser->where('email', request('email'))->first();
        }

        // 新增商家帳號email/password
        if (!$account && request('password')) {
            $account = $storeUser->create([
                'email'    => request('email'),
                'password' => $this->getInsertHashPassword(request('password')),
            ]);
        }

        if (!$account) {
            $this->setException('找不到要關聯的商家帳號！');
        }

        // 關聯商家帳號
        $store->allAccounts()->syncWithoutDetaching([$account->id => ['deleted_at' => NULL]]);

        $account = $store->allAccounts->find($account->id);

        // Transformer
        $data = $showService->getAccountInfo($account);

        return $formatter->json($data);
    }

    /**
     * 共同編輯帳號-設定是否解除關聯
     *
     * @url api/store/yzcube/{store_id}/account/{account_id}/link
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @path int $account_id 商家帳號ID *
     * @param bool $is_link 是否解除關聯 *
     * @return json
     */
    public function linkAccount(
        ApiFormatter $formatter,
        $store_id,
        $account_id
    ) {
        // 取得商家
        $store = request('store');

        // 關聯商家帳號 deleted_at控制有效/無效
        $store->allAccounts()->updateExistingPivot($account_id, ['deleted_at' => request('is_link') ? NULL : now()]);

        return $formatter->json();
    }

    /**
     * 共享W姐妹的夥伴-列表
     *
     * @url api/store/yzcube/{store_id}/brand-store/list
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function listBrandStore(
        StoreRepository $storeRepository,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $store_id
    ) {
        // Request
        $request = request();

        // 取得排除共享W姐妹夥伴的商家列表
        $stores = $storeRepository->getListWithoutBrandStoresByRequest($request);

        // Transformer
        $result = $storeTransformer->listBrandStore($stores);

        return $formatter->json($result);
    }

    /**
     * 共享W姐妹的夥伴-新增
     *
     * @url api/store/yzcube/{store_id}/brand-store/{brand_store_id}/add
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @path int $brand_store_id 共享W姐妹的夥伴ID *
     * @return json
     */
    public function addBrandStore(
        Store $storeModel,
        ShowService $showService,
        ApiFormatter $formatter,
        $store_id,
        $brand_store_id
    ) {
        // 取得商家 & 共享W姐妹夥伴的主要品牌
        $store      = request('store');
        $brandStore = $storeModel->find($brand_store_id);
        $brand      = $brandStore->getPrimaryBrand();

        // 關聯品牌商家 (預設為副品牌 $show_flag = 1)
        $store->allBrands()->syncWithoutDetaching([
            $brand->id => ['show_flag' => 1]
        ]);

        // Transformer
        $data = $showService->getBrandStoreInfo($brandStore, $brand);

        return $formatter->json($data);
    }

    /**
     * 共享W姐妹的夥伴-設定是否解除關聯
     *
     * @url api/store/yzcube/{store_id}/brand-store/{brand_store_id}/link
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @path int $brand_store_id 共享W姐妹的夥伴ID *
     * @param bool $is_link 是否解除關聯 *
     * @return json
     */
    public function linkBrandStore(
        Store $storeModel,
        ShowService $showService,
        ApiFormatter $formatter,
        $store_id,
        $brand_store_id
    ) {
        // 取得商家 & 共享W姐妹夥伴的主要品牌
        $store      = request('store');
        $brandStore = $storeModel->find($brand_store_id);
        $brand      = $brandStore->getPrimaryBrand();

        // 關聯品牌商家 (預設為副品牌 $show_flag = 1)
        $show_flag = request('is_link') ? 1 : 0;
        $store->allBrands()->updateExistingPivot($brand->id, ['show_flag' => $show_flag]);

        return $formatter->json();
    }

    /**
     * 盜商家帳號
     *
     * @url api/store/yzcube/{store_id}/steal-token
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function stealToken(
        ApiFormatter $formatter
    ) {
        // 取得商家、商家帳號
        $store     = request('store');
        $storeUser = $store->accounts()->inRandomOrder()->first();
        if (!$storeUser) {
            $this->setException('找不到此商家的帳號！');
        }

        // 產生 store_token
        $token = $this->getNewToken();
        $storeUser->tokens()->create([
            'target_id'   => $storeUser->id,
            'type'        => 'store',
            'token'       => $token,
            'deadline_at' => now()->addDay(),
        ]);

        return $formatter->json(['store_token' => $token]);
    }

    /**
     * 變更狀態
     *
     * @url api/store/yzcube/{store_id}/change-status
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_id 商家ID *
     * @param string $status 狀態 pending:等待上架 published:上架中 delete:停用 *
     * @return json
     */
    public function changeStatus(
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store = request('store');

        // 驗證是否為婚宴場地
        if ($store->type != 5) {
            $this->setException('目前只有婚宴場地能夠變更狀態唷！');
        }

        // 只有上架需要額外驗證
        if (request('status') != 'published') {
            $store->status = request('status');
            $store->save();

            return $formatter->json();
        }

        // 驗證Logo & 封面照
        if (!$store->logo || !$store->cover) {
            $this->setException($store->name.'，還沒有上傳Logo和封面照唷！');
        }

        // 驗證是否有可顯示的廳房
        if ($store->showVenueRooms->isEmpty()) {
            $this->setException($store->name.'，還沒有可顯示的廳房唷！');
        }

        // 驗證是否有可顯示的方案
        if ($store->showServicesWithoutActivity->isEmpty()) {
            $this->setException($store->name.'，還沒有可顯示的方案唷！');
        }

        $store->status      = 'published';
        $store->released_at = now();
        $store->save();

        return $formatter->json();
    }
}
