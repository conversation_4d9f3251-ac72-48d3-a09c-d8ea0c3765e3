<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Http\Controllers\Controller;
use App\Repositories\Wdv2\OrderRepository;
use App\Models\Wdv2\Order;
use App\Transformers\Store\Yzcube\OrderTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;
use GuzzleHttp\Client;

class OrderController extends Controller
{
    use ApiErrorTrait;

    /**
     * 商家訂單-列表
     *
     * @url api/store/yzcube/order
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $order_type 訂單類型 season:3期合約 half_year:6期合約 full_year:12期合約 usage_fee:使用費 renew:續約使用費
     * @param string $store_type 商家類別
     * @param string $invoice_status 發票狀態 0:尚未開立 1:已開立 unnecessary:無須開立
     * @param string $payment_status 付款狀態 0:付款失敗 1:已付款 2:其他 unnecessary:無須付款
     * @param string $payment_method 付款方式 credit_card:信用卡 spgateway:匯款 other:其他
     * @param string $date_type 日期類型 paid_at:付款日期 created_at:訂單成立日期
     * @param date $start_date 搜尋起始日
     * @param date $end_date 搜尋結束日
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        OrderRepository $orderRepository,
        OrderTransformer $orderTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得商家訂單列表
        $orders = $orderRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $orderTransformer->list($orders);

        return $formatter->json($result);
    }

    /**
     * 匯出商家訂單紀錄
     *
     * @url api/store/yzcube/order/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $payment_status 付款狀態 success:已付款 fail:付款失敗 unnecessary:無須付款
     * @param string $invoice_status 發票狀態 pending:尚未開立 success:已開立 unnecessary:無須開立 invalid:已作廢
     * @param int $invoice_setting_id 所屬發票設定
     * @param date $start_date 搜尋付款起始日
     * @param date $end_date 搜尋付款結束日
     * @param string $keyword 搜尋關鍵字
     * @return json
     */
    public function exportCSV(
        OrderRepository $orderRepository,
        OrderTransformer $orderTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得商家訂單列表
        $orders = $orderRepository->getYzcubeListByRequest($request, false);

        // Transformer
        $result = $orderTransformer->exportCSV($orders);
        $filename = '[商家訂單紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 商家訂單-詳細頁
     *
     * @url api/store/yzcube/order/{order_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $order_id 商家訂單編號 *
     * @return json
     */
    public function show(
        Client $client,
        OrderTransformer $orderTransformer,
        ApiFormatter $formatter,
        Order $order
    ) {
        // Transformer
        $result = $orderTransformer->getOrderInfo($order);

        //==============================
        // wdv2 api-取得訂單資訊
        //==============================
        $apiPath = config('params.wdv2.api_url').'/tool/order/'.$order->id;
        $headers = ['Access-Token' => request()->header('Access-Token')];
        $resp = $client->request('GET', $apiPath, ['headers' => $headers]);
        if ($resp->getStatusCode() == 200) {
            $respObj = json_decode($resp->getBody()->getContents());
            if ($respObj->status) {
                $result['wdv2'] = $respObj->data;
            }
        }

        return $formatter->json($result);
    }

    /**
     * 商家訂單-補開發票
     *
     * @url api/store/yzcube/order/{order_id}/invoice-reissue
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $order_id 商家訂單編號 *
     * @return json
     */
    public function invoiceReissue(
        Client $client,
        ApiFormatter $formatter,
        Order $order
    ) {
        // 無須開立
        if (!$order->amount) {
            $this->setException('免費訂單無須開立發票！');
        }

        // 訂單必須已付款
        if ($order->payment_status != 1) {
            $this->setException('訂單的付款狀態必須為已付款，才可以補開發票！');
        }

        // 訂單必須已付款
        if ($order->invoice_status != 0) {
            $this->setException('訂單的發票狀態必須為尚未開立，才可以補開發票！');
        }

        //==============================
        // wdv2 api-補開發票
        //==============================
        $apiPath = config('params.wdv2.api_url').'/tool/order/'.$order->id.'/invoice-reissue';
        $headers = ['Access-Token' => request()->header('Access-Token')];
        $resp = $client->request('POST', $apiPath, ['headers' => $headers]);
        if ($resp->getStatusCode() == 200) {
            $respObj = json_decode($resp->getBody()->getContents());
            if ($respObj->status) {
                return $formatter->json($respObj->data);
            }
        }

        $this->setException('補開發票的API失敗！');
    }

    /**
     * 更新商家訂單 - 備註
     *
     * @url api/store/yzcube/order/{order_id}/update-note
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $order_id 商家訂單編號 *
     * @param string $note 備註
     * @return bool
     */
    public function updateNote(Order $order) 
    { 
        $note = request()->get('note');

        $orderRepo = new OrderRepository($order);

        $result = $orderRepo->updateNote($note);

        if(!$result){
            $this->setException('失敗，更新商家訂單備註！');
        }
        
        return (new ApiFormatter)->json();
    }
}
