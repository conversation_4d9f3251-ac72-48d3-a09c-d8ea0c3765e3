<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Formatters\ApiFormatter;
use App\Models\LineGroup;
use App\Models\Store;
use App\Repositories\LineGroupRepository;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Services\Image\CreateImageService;
use App\Traits\ApiErrorTrait;
use App\Transformers\Store\Yzcube\LineGroupTransformer;
use App\Http\Controllers\Controller;

class LineGroupController extends Controller
{
    use ApiErrorTrait;

    /**
     * LINE 群組管理-列表
     *
     * @url api/store/yzcube/line-group
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $store_type 商家類型
     * @param bool $has_store 連結商家狀態
     * @param bool $is_show 前台顯示狀態
     * @param bool $has_line_bot 機器人狀態
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        LineGroupRepository $lineGroupRepository,
        LineGroupTransformer $lineGroupTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // LINE群組列表
        $lineGroups = $lineGroupRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $lineGroupTransformer->list($lineGroups);

        return $formatter->json($result);
    }

    /**
     * LINE 群組管理-收尋商家
     *
     * @url api/store/yzcube/line-group/search-stores
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $keyword 搜尋關鍵字 *
     * @return json
     */
    public function searchStores(
        Store $store,
        SearchService $keywordSearchService,
        ApiFormatter $formatter
    ) {
        if (!request('keyword')) {
            $this->setException('請搜尋關鍵字！');
        }

        // keyword 關鏈字搜尋
        $stores = $keywordSearchService->search($store, request('keyword'));
        $stores = $stores->limit(20)
                            ->get()
                            ->map(function ($store) {
                                return [
                                    'id'        => $store->id,
                                    'name'      => $store->name,
                                    'logo'      => $store->logo->file_name ?? '',
                                    'type_name' => $store->typeList[$store->type],
                                ];
                            });

        return $formatter->json($stores);
    }

    /**
     * LINE 群組管理-更新
     *
     * @url api/store/yzcube/line-group/{line_group_id}/update
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $store_id 所屬商家ID *
     * @param bool $is_show 前台是否顯示 *
     * @param string $qrcode_image 加入LINE群組QRcode *
     * @return json
     */
    public function update(
        LineGroupRepository $lineGroupRepository,
        CreateImageService $createImageService,
        ApiFormatter $formatter,
        LineGroup $lineGroup
    ) {
        // 驗證重複綁定
        if (
            $lineGroupRepository->getModel()
                                ->where('id', '!=', $lineGroup->id)
                                ->where('store_id', request('store_id'))
                                ->exists()
        ) {
            $this->setException('此商家已擁有其他 LINE 群組！');
        }

        // 更新商家關聯
        $lineGroup->store_id = request('store_id');
        $lineGroup->is_show  = request('is_show');
        $lineGroup->save();

        // 加入LINE群組QRcode
        $createImageService->add([
            'file_name' => request('qrcode_image'),
            'type'      => 'line_group_qrcode',
            'target_id' => $lineGroup->id,
            'only'      => true,
        ]);

        return $formatter->json();
    }

    /**
     * LINE 群組管理-刪除
     *
     * @url api/store/yzcube/line-group/{line_group_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $line_group_id LINE群組ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        LineGroup $lineGroup
    ) {
        // 清空連結商家
        $lineGroup->store_id = NULL;
        $lineGroup->is_show  = NULL;
        $lineGroup->save();

        // 刪除
        $lineGroup->delete();

        return $formatter->json();
    }
}
