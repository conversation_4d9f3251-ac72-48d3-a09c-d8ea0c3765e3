<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Store\Yzcube\ArticleRequest;
use App\Services\Tools\GetWebData\GetBlogPostInfoService;
use App\Services\Store\Yzcube\Article\SaveService;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Formatters\ApiFormatter;

class ArticleController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * @SWG\ Post(
     *   path = "/store/yzcube/{store_id}/article/sort",
     *   summary = "排序鑑定團/專欄文章",
     *   tags = {"神之後台-商家管理-鑑定團/專欄文章：/store/yzcube/{store_id}/article"},
     *   description = "神之後台-商家內容頁-排序鑑定團/專欄文章",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "store_id",
     *     in = "path",
     *     description = "商家編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "sequence[]",
     *     in = "formData",
     *     description = "編號排序",
     *     required = true,
     *     type = "array",
     *     items = {
     *         "type": "integer",
     *     },
     *     collectionFormat = "multi",
     *     default = {101, 102, 190}
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function sort(
        $store_id,
        ApiFormatter $formatter
    ) {
        // Request
        $sequence = request('sequence');
        if (!$sequence) {
            $this->setException('找不到排序參數！');
        }

        // Trait
        $this->sortListBySequence('StoreArticle', $sequence);

        return $formatter->json();
    }

    /**
     * @SWG\ Get(
     *   path = "/store/yzcube/{store_id}/article/capture",
     *   summary = "擷取鑑定團/專欄文章",
     *   tags = {"神之後台-商家管理-鑑定團/專欄文章：/store/yzcube/{store_id}/article"},
     *   description = "神之後台-商家內容頁-擷取鑑定團/專欄文章",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "store_id",
     *     in = "path",
     *     description = "商家編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "type",
     *     in = "query",
     *     description = "文章類型",
     *     required = true,
     *     type = "string",
     *     enum = {"kol", "blog"},
     *     default = "kol"
     *   ),
     *   @SWG\ Parameter(
     *     name = "blog_id",
     *     in = "query",
     *     description = "文章編號",
     *     required = true,
     *     type = "integer",
     *     default = 49449
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function capture(
        $store_id,
        GetBlogPostInfoService $getBlogPostInfoService,
        ApiFormatter $formatter
    ) {
        // Service
        $result = $getBlogPostInfoService->getBlogPostById(request('type'), request('blog_id'));
        if ($result === false) {
            $this->setException('找不到此鑑定團/專欄文章，請輸入正確的文章類型、文章 ID');
        }

        $getBlogPostInfoService->saveBlogArticle();
        $blogArticle = $getBlogPostInfoService->getBlogArticle();

        // Transformer
        $result = [
            'article_id' => $blogArticle->id,
            'url'        => config('params.'.$blogArticle->type.'_url').'/'.$blogArticle->blog_id,
            'title'      => $blogArticle->title,
            'image'      => $blogArticle->image,
            'tag'        => ($blogArticle->type == 'kol') ? '好婚鑑定團' : '好婚專欄',
        ];

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/store/yzcube/{store_id}/article/save",
     *   summary = "儲存鑑定團/專欄文章",
     *   tags = {"神之後台-商家管理-鑑定團/專欄文章：/store/yzcube/{store_id}/article"},
     *   description = "神之後台-商家內容頁-儲存鑑定團/專欄文章",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "store_id",
     *     in = "path",
     *     description = "商家編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "article_id",
     *     in = "formData",
     *     description = "文章ID",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "title",
     *     in = "formData",
     *     description = "文章標題",
     *     required = true,
     *     type = "string",
     *     default = "Miss E.試衣間｜六款質感爆表的絕美風格婚紗 發現你的獨一無二！"
     *   ),
     *   @SWG\ Parameter(
     *     name = "image",
     *     in = "formData",
     *     description = "文章封面照",
     *     required = true,
     *     type = "string",
     *     default = "https://cdn.weddingday.com.tw/wordpress/kol/wp-content/uploads/20191015171112/%E5%B0%81%E9%9D%A25-07-768x403.jpg"
     *   ),
     *   @SWG\ Parameter(
     *     name = "tag",
     *     in = "formData",
     *     description = "標籤",
     *     type = "string",
     *     default = "好婚鑑定團"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function save(
        $store_id,
        ArticleRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        // Service
        $saveService->run($request);

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/store/yzcube/{store_id}/article/{article_id}/delete",
     *   summary = "刪除鑑定團/專欄文章",
     *   tags = {"神之後台-商家管理-鑑定團/專欄文章：/store/yzcube/{store_id}/article"},
     *   description = "神之後台-商家內容頁-刪除鑑定團/專欄文章",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "store_id",
     *     in = "path",
     *     description = "商家編號",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Parameter(
     *     name = "article_id",
     *     in = "path",
     *     description = "文章ID",
     *     required = true,
     *     type = "integer",
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function delete(
        $store_id,
        $article_id,
        ApiFormatter $formatter
    ) {
        // 取得商家鑑定團/專欄文章
        $store   = request('store');
        $article = $store->articles->find($article_id);
        if (!$article) {
            $this->setException('找不到此鑑定團/專欄文章！');
        }

        // 移除關聯鑑定團/專欄文章
        $store->articles()->detach($article_id);

        return $formatter->json();
    }
}
