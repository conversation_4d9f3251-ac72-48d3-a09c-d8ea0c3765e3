<?php

namespace App\Http\Controllers\Store\Yzcube;

use App\Formatters\ApiFormatter;
use App\Repositories\ReserveRepository;
use App\Transformers\Store\Yzcube\ReserveTransformer;
use App\Http\Controllers\Controller;

class ReserveController extends Controller
{
    /**
     * 詢問單列表
     *
     * @url api/user/yzcube/reserve
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $start_date 起始時間 Y-m-d
     * @param string $end_date 結束時間 Y-m-d
     * @param string $status 詢問單狀態 1:待處理 2:已處理 3:新人爽約 4:已刪除 5:已滿檔 6:無效詢問單申請中 7:無效單
     * @param string $store_type 商家類別  1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $page 頁碼
     * @return json
     */
    public function index(
        ReserveRepository $reserveRepository,
        ReserveTransformer $reserveTransformer,
        ApiFormatter $formatter
    ) {
        $request = request();
        $request->validate([
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date'   => 'nullable|date_format:Y-m-d',
        ]);
        $reserves = $reserveRepository->getYzcubeListByRequest($request);
        $result = $reserveTransformer->list($reserves);
        return $formatter->json($result);
    }

    /**
     * 匯出詢問單
     *
     * @url api/user/yzcube/reserve/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $start_date 起始時間 Y-m-d
     * @param string $end_date 結束時間 Y-m-d
     * @param string $status 詢問單狀態 1:待處理 2:已處理 3:新人爽約 4:已刪除 5:已滿檔 6:無效詢問單申請中 7:無效單
     * @param string $store_type 商家類別 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @return json
     */
    public function exportCSV(
        ReserveRepository $reserveRepository,
        ReserveTransformer $reserveTransformer,
        ApiFormatter $formatter
    ) {
        $request = request();
        $reserves = $reserveRepository->getYzcubeListByRequest($request, false);

        // Transformer
        $result = $reserveTransformer->exportCSV($reserves);
        $filename = '[詢問單紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 公開詢價內容
     *
     * @url api/user/yzcube/quote/{reserve_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $reserve_id 詢問單ID
     * @return json
     */
    public function show(
        $reserve_id,
        ReserveRepository $reserveRepository,
        ReserveTransformer $reserveTransformer,
        ApiFormatter $formatter
    ) {
        $data = $reserveRepository->getContentByReserveID($reserve_id);
        $result = $reserveTransformer->show($data);
        return $formatter->json($result);
    }
}
