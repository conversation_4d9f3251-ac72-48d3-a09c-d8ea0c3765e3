<?php
/*--------------------------------
 | 神之後台 商家user管理 Controller
 |--------------------------------
 |
 |
 |
 */

namespace App\Http\Controllers\Store\Yzcube;

use App\Http\Controllers\Controller;
use App\Formatters\ApiFormatter;
use App\Models\StoreUser;
use App\Models\AuthToken;
use App\Models\EmailLegalize;
use App\Traits\Auth\CreateTokenTrait;
use App\Traits\RandStringTrait;
use App\Traits\ApiErrorTrait;
use App\Services\Auth\SendPhoneTokenService;
use App\Services\Mail\Auth\EmailAuthKeyService;
use App\Repositories\StoreUserRepository;
use App\Transformers\Store\Yzcube\StoreUserTransformer;
use App\Transformers\Yzcube\AuthTransformer;

class StoreUserController extends Controller
{
    use CreateTokenTrait;
    use RandStringTrait;
    use ApiErrorTrait;

    /**
     * 商家user列表
     *
     * @url /store/yzcube/store-user
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token" *
     * @param string $start_date 開始日期
     * @param string $end_date 結束日期
     * @param string $status 狀態
     * @param string $keyword 關鍵字
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param string $page 頁數
     * @return json
     */
    public function index(
        StoreUserRepository $storeUserRepository,
        ApiFormatter $formatter,
        storeUserTransformer $storeUserTransformer
    )
    {
        // 必填欄位
        try {
            $this->validate(request(), [
                'start_date' => 'nullable|date_format:Y-m-d',
                'end_date' => 'nullable|date_format:Y-m-d',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // Request
        $request = request();

        $data = $storeUserRepository->getYzcubeListByRequest($request);
        $result = $storeUserTransformer->list($data);

        return $formatter->json($result);
    }

    /**
    * 商家user詳細資料
    *
    * @url /store/yzcube/store-user/{store_user_id}
    * @method GET
    * @header string Access-Token *
    * @header string Yzcube-Token *
    * @param int $store_user_id 商家user ID
    * @return json
    */
    public function show(
        StoreUser $storeUser,
        ApiFormatter $formatter,
        StoreUserTransformer $storeUserTransformer,
        AuthTransformer $authTransformer
    )
    {
        // Transformer
        $result = [];
        // 基本資料
        $result['profile'] = $storeUserTransformer->yzcubeStoreUserPage($storeUser);
        // 登入者的身份驗證紀錄
        $result['auth_logs'] = $authTransformer->getLogAuthToken($storeUser);

        return $formatter->json($result);
    }

    /**
     * 變更storeUser帳號狀態
     *
     * @url /store/yzcube/store-user/{store_user_id}/status
     * @method PUT
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param int $status 狀態 0:停用 1:啟用 *
     * @return json
    */
    public function changeStatus(
        StoreUser $storeUser,
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    )
    {
        // 變更狀態
        $origStatus = $storeUser->status; //先取出原始的狀態
        $status = request('status');

        if ($status == 1){
            if($storeUser->email_legalize == 1 && $storeUser->phone_legalize == 1){
                $newStatus = 'published';
            } else {
                $newStatus = 'pending';
            }
        } elseif($status == 0){
            $newStatus = 'delete';
        }
        $storeUser->status = $newStatus;
        $storeUser->save();

        //如果是從停用變啟用..就寄送認證信
        if ($origStatus == 'delete' && $status == 1) {
            $emailAuthKeyService->sendReEnableMail($storeUser, 'storeUser');
        }

        //回傳帳號信用時間
        return $formatter->json(['status' => $storeUser->status]);
    }

    /**
     * 盜帳號
     *
     * @url /store/yzcube/store-user/{store_user_id}/steal-token
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param string $user_token 使用者驗證碼
     * @return json
    */
    public function stealAccount(
        StoreUser $storeUser,
        ApiFormatter $formatter
    )
    {
        // 產生 store_token
        $token = $this->getNewToken();
        $storeUser->tokens()->create([
            'target_id'   => $storeUser->id,
            'type'        => 'store',
            'token'       => $token,
            'deadline_at' => now()->addDay(),
        ]);

        return $formatter->json(['store_token' => $token]);
    }

    /**
     * 更改電話
     *
     * @url /store/yzcube/store-user/{store_user_id}/edit-phone
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param string $phone 電話 *
     * @return json
    */
    public function editPhone(
        StoreUser $storeUser,
        AuthToken $authToken,
        ApiFormatter $formatter,
        SendPhoneTokenService $sendPhoneTokenService
    ) {
        $phone = empty(request('phone')) ? null : request('phone');
        // 驗證手機是否重複
        if ($phone) {
            $sendPhoneTokenService->validateUniquePhone($storeUser->id, $phone, 'store_user');
        }

        // 更改電話&未驗證狀態
        $storeUser->phone = $phone;
        $storeUser->phone_legalize = 0;
        $storeUser->save();

        // 強制登出
        $authToken->where('target_id', $storeUser->id)->where('type', 'store')->delete();

        return $formatter->json(NULL, ['message' => '電話更改成功']);
    }

    /**
     * 重新寄送手機驗證簡訊
     *
     * @url /store/yzcube/store-user/{store_user_id}/resend-phone-auth-key
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @return json
     */
    public function resendPhoneAuthKey(
        StoreUser $storeUser,
        ApiFormatter $formatter,
        SendPhoneTokenService $sendPhoneTokenService
    )
    {
        // 驗證手機是否重複
        // $sendPhoneTokenService->validateUniquePhone($storeUser->id, $storeUser->phone, 'store_user');

        if ($storeUser->phone) {
            // Service
            $data = $sendPhoneTokenService->send($storeUser->id, $storeUser->phone, 'store_user', 'yzcube');
        } else {
            $this->setException('請先設定手機號碼', 4000, ['phone' => '請先設定手機號碼']);
        }

        return $formatter->json($data, ['message' => '請收手機簡訊驗證碼！']);
    }

    /**
     * 更改信箱
     *
     * @url /store/yzcube/store-user/{store_user_id}/edit-email
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int @store_user_id 商家user ID *
     * @param string $email 信箱 *
     * @return json
     */
    public function editEmail(
        StoreUser $storeUser,
        AuthToken $authToken,
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    ){
        // 必填欄位
        try {
            $this->validate(request(), [
                'email' => 'required|email',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setException('欄位輸入驗證錯誤！ ', 4001, $e->errors());
        }

        // 驗證信箱是否重複
        $emailAuthKeyService->validateUniqueEmail('store_user', request('email'), $storeUser->id);

        // 更改信箱&未驗證狀態
        $storeUser->email = request('email');
        $storeUser->email_legalize = 0;
        $storeUser->save();

        // 強制登出
        $authToken->where('target_id', $storeUser->id)->where('type', 'store')->delete();

        return $formatter->json(NULL, ['message' => '信箱更改成功']);
    }

    /**
     * 重新寄送信箱驗證信
     *
     * @url /store/yzcube/store-user/{store_user_id}/resend-email-auth-key
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @return json
    */
    public function resendEmailAuthKey(
        StoreUser $storeUser,
        EmailLegalize $authKey,
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    )
    {
        // 驗證信箱是否重複
        // $emailAuthKeyService->validateUniqueEmail('store_user', $storeUser->email, $storeUser->id);

        // 產生驗證碼
        $recipientStore = StoreUser::where('id', $storeUser->id)->with('liveStores')->orderBy('id', 'desc')->first();
        if (count($recipientStore->liveStores)) {
            $recipient = $recipientStore->liveStores->first()->name;
        }
        preg_match('/^(.*?)@/', $storeUser->email, $matches);
        $mailName = $matches[1] ?? '';
        $recipient = isset($recipient) ? $recipient : $mailName;
        $mailType = 'store_user_email';

        // 驗證碼寫進資料庫
        $authKey = $authKey->create([
            'email'       => $storeUser->email,
            'key'         => $this->getRandString(6),
            'source'      => 'yzcube',
            'recipient'   => $recipient,
            'deadline_at' => now()->addHour(24),
        ]);

        // 寄出驗證信
        $emailAuthKeyService->sendAuthMail($authKey, $mailType);
        return $formatter->json(NULL, ['message' => '請至您的「 '.$storeUser->email.' 」信箱收取驗證信！']);
    }

    /**
     * 更改手機驗證狀態
     *
     * @url /store/yzcube/store-user/{store_user_id}/phone-status
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param int $status 狀態 0:未驗證 1:已驗證 *
     * @return json
     */
    public function changePhoneStatus(
        StoreUser $storeUser,
        AuthToken $authToken,
        ApiFormatter $formatter,
        SendPhoneTokenService $sendPhoneTokenService
    )
    {
        // 改成已驗證要先確認在資料庫中是否為唯一手機
        if (request('status') == 1) {
            if ($storeUser->phone) {
                $sendPhoneTokenService->validateUniquePhone($storeUser->id, $storeUser->phone, 'store_user');
            } else {
                $this->setException('請先設定手機號碼', 4000, ['phone' => '請先設定手機號碼']);
            }
        } else {
            // 強制登出
            $authToken->where('target_id', $storeUser->id)->where('type', 'store')->delete();
        }

        $status = request('status');
        $storeUser->phone_legalize = $status;
        $storeUser->save();

        return $formatter->json(NULL, ['message' => '手機驗證狀態更改成功']);
    }

    /**
     * 更改信箱驗證狀態
     *
     * @url /store/yzcube/store-user/{store_user_id}/email-status
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @param int $status 狀態 0:未驗證 1:已驗證 *
     * @return json
     */
    public function changeEmailStatus(
        StoreUser $storeUser,
        AuthToken $authToken,
        ApiFormatter $formatter,
        EmailAuthKeyService $emailAuthKeyService
    )
    {
        // 改成已驗證要先確認在資料庫中是否為唯一信箱
        if (request('status') == 1) {
            $emailAuthKeyService->validateUniqueEmail('store_user', $storeUser->email, $storeUser->id);
        } else {
            // 強制登出
            $authToken->where('target_id', $storeUser->id)->where('type', 'store')->delete();
        }

        $status = request('status');
        $storeUser->email_legalize = $status;
        $storeUser->save();

        return $formatter->json(NULL, ['message' => '信箱驗證狀態更改成功']);
    }

    /**
     * 移除黑名單
     *
     * @url /store/yzcube/store-user/{store_user_id}/remove-blacklist
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $store_user_id 商家user ID *
     * @return json
     */
    public function removeBlacklist(
        StoreUser $storeUser,
        ApiFormatter $formatter
    )
    {
        if ($storeUser->mailBase && $storeUser->mailBase->valid == 0) {
            $storeUser->mailBase->valid = 1;
            $storeUser->mailBase->save();
        }

        return $formatter->json(NULL, ['message' => '解除黑名單成功']);
    }
}
