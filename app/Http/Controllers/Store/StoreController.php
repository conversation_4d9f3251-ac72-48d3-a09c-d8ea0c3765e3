<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Services\Store\ShowService;
use App\Services\Store\AlbumListService;
use App\Services\Store\AlbumInfoService;
use App\Services\Mail\Store\SuggestService;
use App\Repositories\Wdv2\MallItemRepository;
use App\Models\StoreTag;
use App\Models\StoreSuggest;
use App\Transformers\Store\StoreTransformer;
use App\Transformers\Store\Wdv2\StoreTransformer as StoreTransformerWdv2;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class StoreController extends Controller
{
    use ApiErrorTrait;

    /**
     * 檢查商家
     *
     * @url api/store/{store_id}/check
     * @method GET
     * @header string Access-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function check(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // 取得商家
        $store = request('store');

        // Transformer
        $result = $storeTransformer->check($store);

        return $formatter->json($result);
    }

    /**
     * 商家主頁
     *
     * @url api/store/{store_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @return json
     */
    public function show(
        ShowService $showService,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $user    = request('user');

        // Service
        $data = $showService->run($request);

        // Transformer
        $result = $storeTransformer->show($data, $user);

        return $formatter->json($result);
    }

    /**
     * 相本列表頁
     *
     * @url api/store/{store_id}/album
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param string $data_type 資料類型 album:相本 image:照片
     * @param array.int $image_locations[] 拍攝地點(拍婚紗)
     * @param array.int $price_range[] 價格區間(婚紗禮服)
     * @param array.int $types[] 類型(拍婚紗)
     * @param array.int $album_tags[] 作品集標籤(婚紗禮服)
     * @param array.int $image_tags[] 作品照標籤(拍婚紗/婚攝婚錄/新娘秘書)
     * @param string $sort 排序 recommend:推薦排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function albumList(
        AlbumListService $albumListService,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $user    = request('user');

        // Service
        $data = $albumListService->run($request);

        // Transformer
        $result = $storeTransformer->albumList($data, $user);

        return $formatter->json($result);
    }

    /**
     * 影片列表頁
     *
     * @url api/store/{store_id}/video
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param string $sort 排序 recommend:推薦排序 hot:人氣排序 update:最新排序
     * @param int $page 頁碼
     * @return json
     */
    public function videoList(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $store   = request('store');
        $user    = request('user');

        // Service
        $videos = $store->getReleaseVideosByRequest($request);

        // Transformer
        $result = $storeTransformer->videoList($store, $videos, $user);

        return $formatter->json($result);
    }

    /**
     * 廳房列表頁
     *
     * @url api/store/{store_id}/venue-room
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param string $sort 排序 recommend:推薦排序 hot:人氣排序 update:最新排序
     * @param int $page 頁碼
     * @return json
     */
    public function venueRoomList(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $store   = request('store');
        $user    = request('user');

        // Service
        $rooms = $store->getReleaseVenueRoomsByRequest($request);

        // Transformer
        $result = $storeTransformer->venueRoomList($store, $rooms, $user);

        return $formatter->json($result);
    }

    /**
     * 相本內容頁
     *
     * @url api/store/{store_id}/album/{album_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @path int $album_id 相本ID *
     * @param int $page 頁碼
     * @return json
     */
    public function albumInfo(
        AlbumInfoService $albumInfoService,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $store_id,
        $album_id
    ) {
        // Request
        $request = request();
        $store   = request('store');
        $user    = request('user');

        // 取得商家相本集
        $album = $store->showAlbums()->find($album_id);
        if (!$album) {
            $this->setException('找不到此商家相本！');
        }

        // Service
        $data = $albumInfoService->run($request, $album);

        // Transformer
        $result = $storeTransformer->albumInfo($data, $user);

        return $formatter->json($result);
    }

    /**
     * 影片內容頁
     *
     * @url api/store/{store_id}/video/{video_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @path int $video_id 影片ID *
     * @return json
     */
    public function videoInfo(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $store_id,
        $video_id
    ) {
        // Request
        $request = request();
        $store   = request('store');
        $user    = request('user');

        // 取得商家影片
        $video = $store->showVideos()->find($video_id);
        if (!$video) {
            $this->setException('找不到此商家影片！');
        }

        // 其他推薦作品列表 (預設橫版6筆, 婚紗禮服/新娘秘書 直版8筆, 婚攝/婚錄/婚禮主持人 有動態作品橫版6筆)
        $perPage = in_array($store->type, [2, 4]) ? 8 : 6;

        // Service
        $data = [
            'store'           => $store,
            'video'           => $video,
            'otherAlbums'     => $store->getReleaseAlbums($perPage),
            'otherVideos'     => $store->getOtherReleaseVideos(6, $video),
            'otherVenueRooms' => $store->getReleaseVenueRooms(6),
        ];

        // Transformer
        $result = $storeTransformer->videoInfo($data, $user);

        return $formatter->json($result);
    }

    /**
     * 廳房內容頁
     *
     * @url api/store/{store_id}/venue-room/{room_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @path int $room_id 廳房ID *
     * @param int $page 頁碼
     * @return json
     */
    public function venueRoomInfo(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $store_id,
        $room_id
    ) {
        // Request
        $request = request();
        $store   = request('store');
        $user    = request('user');

        // 取得婚宴場地廳房
        $room = $store->showVenueRooms()->find($room_id);
        if (!$room) {
            $this->setException('找不到此婚宴場地廳房！');
        }

        // Service
        $data = [
            'store'           => $store,
            'room'            => $room,
            'lastVenueRoom'   => $store->getLastReleaseVenueRoom($room),
            'otherVenueRooms' => $store->getOtherReleaseVenueRooms(6, $room),
        ];

        // Transformer
        $result = $storeTransformer->venueRoomInfo($data, $user);

        return $formatter->json($result);
    }

    /**
     * 方案列表頁
     *
     * @url api/store/{store_id}/service
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param int $page 頁碼
     * @return json
     */
    public function serviceList(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $store = request('store');
        $user  = request('user');

        // Service
        $data = [
            'store'    => $store,
            'services' => $store->getReleaseServicesByRequest(),
        ];

        // Transformer
        $result = $storeTransformer->serviceList($data, $user);

        return $formatter->json($result);
    }

    /**
     * 方案內容頁
     *
     * @url api/store/{store_id}/service/{service_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @path int $service_id 方案ID *
     * @return json
     */
    public function serviceInfo(
        StoreTag $storeTag,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $store_id,
        $service_id
    ) {
        // Request
        $store = request('store');
        $user  = request('user');

        // 取得商家方案
        $service = $store->showServicesWithActivity()->find($service_id);
        if (!$service) {
            $this->setException('找不到此商家方案！');
        }

        // Service
        $data = [
            'store'         => $store,
            'service'       => $service,
            'serviceTags'   => $storeTag->getSettingListByType($store->type, 'service'),
            'otherServices' => $store->getOtherReleaseServices(6, $service),
        ];

        // Transformer
        $result = $storeTransformer->serviceInfo($data, $user);

        return $formatter->json($result);
    }

    /**
     * 婚禮小物列表頁
     *
     * @url api/store/{store_id}/mall-item
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param array.int $price_range[] 價格區間
     * @param string $sort 排序 hot:人氣排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function mallItemList(
        MallItemRepository $mallItemRepository,
        StoreTransformerWdv2 $storeTransformerWdv2,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $store   = request('store');
        $user    = request('user');

        // 價格區間列表
        $priceRange = [];
        foreach ($mallItemRepository->priceRangeList as $key => $item) {
            $priceRange[] = [
                'key'        => $key,
                'value'      => $item['label'],
                'item_count' => $store->mallItems()->priceRange($item['value'])->count(),
            ];
        }

        $data = [
            'store'      => $store,
            'priceRange' => $priceRange,
            'mallItems'  => $mallItemRepository->getStoreMallItemsByRequest($store, $request),
        ];

        // Transformer
        $result = $storeTransformerWdv2->mallItemList($data, $user);

        return $formatter->json($result);
    }

    /**
     * 婚禮小物內容頁
     *
     * @url api/store/{store_id}/mall-item/{item_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param array.int $price_range[] 價格區間
     * @param string $sort 排序 hot:人氣排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function mallItemInfo(
        MallItemRepository $mallItemRepository,
        StoreTransformerWdv2 $storeTransformerWdv2,
        ApiFormatter $formatter,
        $store_id,
        $item_id
    ) {
        // Request
        $store = request('store');
        $item  = $store->mallItems()->find($item_id);
        if (!$item) {
            $this->setException('找不到此婚禮小物！');
        }

        // Transformer
        $result = $storeTransformerWdv2->mallItemInfo($store, $item);

        return $formatter->json($result);
    }

    /**
     * 鑑定團文章列表頁
     *
     * @url api/store/{store_id}/kol-article
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param int $page 頁碼
     * @return json
     */
    public function kolArticleList(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $store = request('store');
        $user  = request('user');

        // 除了婚宴場地
        if ($store->type != 5) {

            // 其他推薦作品列表 (預設12筆, 婚攝/婚錄/婚禮主持人 有平面+動態作品各6筆)
            $perPage     = in_array($store->type, [3, 8]) ? 6 : 12;
            $otherAlbums = $store->getReleaseAlbums($perPage);
            $otherVideos = in_array($store->type, [3, 8]) ? $store->getReleaseVideos($perPage) : [];
        }

        // Service
        $data = [
            'store'           => $store,
            'kolArticles'     => $store->kolArticles()->paginate(30),
            'otherAlbums'     => $otherAlbums ?? [],
            'otherVideos'     => $otherVideos ?? [],
            'otherVenueRooms' => $store->getReleaseVenueRooms(6),
        ];

        // Transformer
        $result = $storeTransformer->kolArticleList($data, $user);

        return $formatter->json($result);
    }

    /**
     * 好婚專欄列表頁
     *
     * @url api/store/{store_id}/blog-article
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @param int $page 頁碼
     * @return json
     */
    public function blogArticleList(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $store = request('store');
        $user  = request('user');

        // 除了婚宴場地
        if ($store->type != 5) {

            // 其他推薦作品列表 (預設12筆, 婚攝/婚錄/婚禮主持人 有平面+動態作品各6筆)
            $perPage     = in_array($store->type, [3, 8]) ? 6 : 12;
            $otherAlbums = $store->getReleaseAlbums($perPage);
            $otherVideos = in_array($store->type, [3, 8]) ? $store->getReleaseVideos($perPage) : [];
        }

        // Service
        $data = [
            'store'           => $store,
            'blogArticles'    => $store->blogArticles()->paginate(30),
            'otherAlbums'     => $otherAlbums ?? [],
            'otherVideos'     => $otherVideos ?? [],
            'otherVenueRooms' => $store->getReleaseVenueRooms(6),
        ];

        // Transformer
        $result = $storeTransformer->blogArticleList($data, $user);

        return $formatter->json($result);
    }

    /**
     * 成員作品列表
     *
     * @url api/store/{store_id}/member/{member_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $store_id 商家ID *
     * @path int $member_id 成員ID *
     * @param int $page 頁碼
     * @return json
     */
    public function memberInfo(
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $store_id,
        $member_id
    ) {
        // Request
        $store = request('store');
        $user  = request('user');

        // 取得成員
        $member = $store->members()->find($member_id);
        if (!$member) {
            $this->setException('找不到此商家成員！');
        }

        // 取得特定成員的作品列表 (拍婚紗的作品分類較例外)
        $albums = $member->albums()->withCount(['images', 'imageCollects'])->paginate(36);
        $videos = $member->videos()->paginate(36);

        // 其他推薦作品列表 (預設12筆, 婚攝/婚錄/婚禮主持人 有平面+動態作品各6筆)
        $perPage     = in_array($store->type, [3, 8]) ? 6 : 12;
        $excludeIds  = $member->albums->pluck('id');
        $otherAlbums = $store->getReleaseAlbums($perPage, $excludeIds);
        $excludeIds  = $member->videos->pluck('id');
        $otherVideos = in_array($store->type, [3, 8]) ? $store->getReleaseVideos($perPage, $excludeIds) : [];

        // Service
        $data = [
            'store'       => $store,
            'member'      => $member,
            'albums'      => $albums,
            'videos'      => $videos,
            'otherAlbums' => $otherAlbums,
            'otherVideos' => $otherVideos,
        ];

        // Transformer
        $result = $storeTransformer->memberInfo($data, $user);

        return $formatter->json($result);
    }

    /**
     * 提供商家建議
     *
     * @url api/store/{store_id}/suggest
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param string $content 建議內容 *
     * @return json
     */
    public function suggest(
        StoreSuggest $storeSuggest,
        SuggestService $suggestService,
        ApiFormatter $formatter,
        $store_id
    ) {
        // Request
        $user    = request('user');
        $content = request('content');

        if (!$content) {
            $this->setException('請提供商家建議！');
        }

        // 新增提供商家建議
        $storeSuggest->user_id  = $user->id ?? NULL;
        $storeSuggest->store_id = $store_id;
        $storeSuggest->content  = $content;
        $storeSuggest->save();

        // 提供商家建議通知信
        $suggestService->sendMail($storeSuggest);

        return $formatter->json();
    }

    /**
     * 確認檔期
     *
     * @url api/store/{store_id}/check-date
     * @method GET
     * @header string Access-Token *
     * @path int $store_id 商家ID *
     * @param date $date 日期 *
     *
     * @return json
     */
    public function checkDate(
        ApiFormatter $formatter
    ) {
        // Request
        $store = request('store');
        $date  = request('date');

        // 取得商家檔期
        $storeFulls = $store->full()->where('full_date', $date)->get();

        $data = [1 => true, 2 => true];
        foreach ($storeFulls as $storeFull) {
            $data[$storeFull->type] = false;
        }

        return $formatter->json($data);
    }
}
