<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Services\Brand\IndexService;
use App\Models\Brand;
use App\Traits\ApiErrorTrait;
use App\Transformers\Store\StoreTransformer;
use App\Formatters\ApiFormatter;

class BrandController extends Controller
{
    use ApiErrorTrait;

    /**
     * 品牌主頁
     *
     * @url api/brand/{brand_id}
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @path int $brand_id 品牌ID *
     * @param string $sort 排序 hot:熱門排序 update:最新排序
     * @param int $page 頁碼
     * @return json
     */
    public function index(
        Brand $brand,
        IndexService $indexService,
        StoreTransformer $storeTransformer,
        ApiFormatter $formatter,
        $brand_id
    ) {
        // Request
        $request = request();
        $user    = request('user');

        // 取得品牌
        $brand = $brand->find($brand_id);
        if (!$brand) {
            $this->setException('找不到此品牌！');
        }

        // Service
        $data = $indexService->run($brand, $request);

        // Transformer
        $result = $storeTransformer->brandIndex($data, $user);

        return $formatter->json($result);
    }

    /**
     * 品牌許願池
     *
     * @url api/brand/{brand_id}/wish
     * @method POST
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @path int $brand_id 品牌ID *
     * @return json
     */
    public function wish(
        Brand $brand,
        ApiFormatter $formatter,
        $brand_id
    ) {
        // Request
        $request = request();
        $user    = request('user');

        // 取得品牌
        $brand = $brand->find($brand_id);
        if (!$brand) {
            $this->setException('找不到此品牌！');
        }

        // 更新品牌許願池
        $brand->userWishes()->updateOrCreate(['user_id' => $user->id]);

        // 許願池數量
        $wishCount = $brand->userWishes->count();

        return $formatter->json($wishCount);
    }
}
