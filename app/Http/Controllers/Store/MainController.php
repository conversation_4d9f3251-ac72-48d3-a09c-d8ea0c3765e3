<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Services\Store\Main\StoreListService;
use App\Services\Store\Main\AlbumListService;
use App\Services\Store\Main\VideoListService;
use App\Services\Store\Main\ServiceListService;
use App\Services\Store\Main\Wdv2\MallItemListService as MallItemListServiceWdv2;
use App\Services\Store\Main\VenueRoomListService;
use App\Transformers\Store\MainTransformer;
use App\Transformers\Store\Wdv2\MainTransformer as MainTransformerWdv2;
use App\Formatters\ApiFormatter;

class MainController extends Controller
{
    /**
     * 商家一覽
     *
     * @url api/store
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 地點(拍婚紗/婚紗禮服/婚攝婚錄/新娘秘書/婚宴場地/婚禮主持人/喜餅)
     * @param array.int $store_tags[] 商家標籤
     * @param array.int $free_fares[] 免車馬費地區
     * @param array.int $extra_services[] 服務類型 301:平面攝影 302:動態錄影 303:平面+動態錄影
     * @param bool $has_files 拍婚紗-檔案全贈
     * @param bool $has_trial_fee 婚紗禮服-免試穿費 | 新娘秘書-試妝服務
     * @param bool $has_free_shop_tasting 喜餅-免費門市試吃
     * @param array.int $activity_ids[] 顯示活動方案商家
     * @param bool $has_discount 顯示獨家優惠商家
     * @param string $sort 排序 hot:人氣排序 update:最新排序 evaluationRate:評價數排序 discussionRate:討論度排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function storeList(
        StoreListService $storeListService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $storeListService->run($request);

        // Transformer
        $result = $mainTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 相本/照片一覽
     *
     * @url api/store/albums
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param string $data_type 資料類型 album:相本 image:照片 tasting:宅配試吃
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 商家地點(拍婚紗/婚紗禮服/婚攝婚錄/新娘秘書/婚禮主持人/喜餅)
     * @param array.int $image_locations[] 拍攝地點(拍婚紗)
     * @param array.int $free_fares[] 商家免車馬費地區
     * @param array.int $price_range[] 價格區間(婚紗禮服/婚禮佈置)
     * @param array.int $types[] 類型(拍婚紗/婚禮佈置)
     * @param array.int $album_tags[] 作品集標籤(婚紗禮服/婚禮佈置)
     * @param array.int $image_tags[] 作品照標籤(拍婚紗/婚攝婚錄/新娘秘書)
     * @param bool $can_customized 可客製化(喜餅)
     * @param bool $is_meat 葷(喜餅)
     * @param bool $is_lacto_vegetarian 奶素(喜餅)
     * @param bool $is_ovo_lacto_vegetarian 蛋奶素(喜餅)
     * @param bool $is_vegetarian 全素(喜餅)
     * @param array.int $activity_ids[] 顯示活動方案商家
     * @param string $sort 排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function albumList(
        AlbumListService $albumListService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $request['data_type'] = request('data_type', 'album'); // 預設相本一覽

        // Service
        $data = $albumListService->run($request);

        // Transformer
        $result = $mainTransformer->albumList($data);

        return $formatter->json($result);
    }

    /**
     * 影片一覽
     *
     * @url api/store/videos
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 商家地點(婚攝婚錄/婚禮主持人)
     * @param array.int $free_fares[] 商家免車馬費地區
     * @param string $sort 排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function videoList(
        VideoListService $videoListService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        // Service
        $data = $videoListService->run($request);

        // Transformer
        $result = $mainTransformer->videoList($data);

        return $formatter->json($result);
    }

    /**
     * 方案一覽
     *
     * @url api/store/services
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param int $store_type 商家類型 1:拍婚紗 2:婚紗禮服 3:婚攝/婚錄 4:新娘秘書 5:婚宴場地 6:婚禮佈置 7:婚禮小物 8:婚禮主持人
     * @param string $store_type_key 商家類型索引 studio:拍婚紗 dress:婚紗禮服 photographer:婚攝/婚錄 makeup:新娘秘書 venue:婚宴場地 decoration:婚禮佈置 mall:婚禮小物 host:婚禮主持人
     * @param string $keyword 搜尋關鍵字
     * @param string $wedding_date 婚期
     * @param array.int $locations[] 商家地點
     * @param array.int $free_fares[] 商家免車馬費地區
     * @param array.int $price_range[] 價格區間
     * @param array.string $type[] 方案類型
     * @param bool $studio_photo_file 檔案全贈(拍婚紗)
     * @param bool $studio_casual_wear 拍攝便服(拍婚紗)
     * @param array.int $studio_white_dress[] 拍攝白紗(拍婚紗/婚紗禮服)
     * @param array.int $studio_dress[] 拍攝禮服(拍婚紗/婚紗禮服)
     * @param array.int $wedding_white_dress[] 宴客白紗(拍婚紗/婚紗禮服)
     * @param array.int $wedding_dress[] 宴客禮服(拍婚紗/婚紗禮服)
     * @param array.int $studio_album_photo[] 精修照(拍婚紗)
     * @param bool $for_studio 拍攝用(婚紗禮服)
     * @param bool $for_wedding 宴客用(婚紗禮服)
     * @param bool $witness_ceremony 證婚需求(婚攝婚錄/新娘秘書)
     * @param array.int $ceremony_type[] 習俗儀式類型(婚攝婚錄/新娘秘書/婚禮主持人)
     * @param array.int $banquet_time[] 婚宴時段(婚攝婚錄/新娘秘書/婚禮主持人)
     * @param array.int $photographer_time[] 拍攝時數(婚攝婚錄)
     * @param array.int $photographer_count[] 攝影師人數(婚攝婚錄)
     * @param array.int $makeup_count[] 造型數(新娘秘書)
     * @param array.int $service_tags[] 方案包含的服務(婚宴場地)
     * @param array.int $store_tags[] 商家提供的服務(婚宴場地)
     * @param array.string $condition_type[] 優惠條件 full_quantity:滿盒優惠 full_amount:滿額優惠
     * @param array.int $activity_ids[] 顯示活動方案商家
     * @param bool $has_discount 顯示獨家優惠商家
     * @param string $sort 排序 hot:人氣排序 update:最新排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function serviceList(
        ServiceListService $serviceListService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $serviceListService->run($request);

        // Transformer
        $result = $mainTransformer->serviceList($data);

        return $formatter->json($result);
    }

    /**
     * 廳房一覽
     *
     * @url api/store/venue-rooms
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @param string $keyword 搜尋關鍵字
     * @param array.int $locations[] 商家地點
     * @param array.string $types[] 廳房類型 banquet:宴客 witness:證婚 ceremony:文定/迎娶
     * @param array.int $numbers[] 容納人數
     * @param array.string $devices[] 廳房設備 bridal_room:新娘休息室 wifi:Wi-Fi projection:投影幕設備 led:LED螢幕設備 sound:音響設備 light:特殊燈光設備 stage:舞台 pillar:無樑柱 backplane:送客背板
     * @param string $sort 排序 hot:人氣排序 update:最新排序
     * @param int $page 頁碼
     * @return json
     */
    public function venueRoomList(
        VenueRoomListService $venueRoomListService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $venueRoomListService->run($request);

        // Transformer
        $result = $mainTransformer->venueRoomList($data);

        return $formatter->json($result);
    }

    /**
     * 小物一覽
     *
     * @url api/store/mall-items
     * @method GET
     * @header string Access-Token *
     * @param string $keyword 搜尋關鍵字
     * @param array.int $price_range[] 價格區間
     * @param string $sort 排序 hot:人氣排序 minPrice:價格低到高 maxPrice:價格高到低
     * @param int $page 頁碼
     * @return json
     */
    public function mallItemlist(
        MallItemListServiceWdv2 $mallItemListServiceWdv2,
        MainTransformerWdv2 $mainTransformerWdv2,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $mallItemListServiceWdv2->run($request);

        // Transformer
        $result = $mainTransformerWdv2->mallItemlist($data);

        return $formatter->json($result);
    }
}
