<?php

namespace App\Http\Controllers\Forum;

use App\Http\Controllers\Controller;
use App\Services\Forum\Main\IndexService;
use App\Services\Forum\Article\GetTagService;
use App\Transformers\Forum\MainTransformer;
use App\Formatters\ApiFormatter;
use App\Models\OldData\SharePost;
use App\Repositories\ForumCategoryRepository;
use App\Traits\ApiErrorTrait;

class MainController extends Controller
{
    use ApiErrorTrait;

    /**
     * 論壇首頁
     *
     * @url forum
     * @method GET
     * @haeder User-Token
     * @param int $category_id 分類ID
     * @param string $keyword 搜尋關鍵字
     * @param int $tag_id 標籤ID
     * @param bool $search 關鍵字首次搜尋紀錄
     * @param string $sort 排序 hot:熱門討論 update:最新討論
     * @param int $page 頁碼
     * @return json
     */
    public function index(
        IndexService $indexService,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $indexService->run($request);

        // Transformer
        $result = $mainTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 論壇首頁 分類列表
     *
     * @url forum/category
     * @method GET
     * @header User-Token
     * @return json
     */
    public function category(
        ForumCategoryRepository $categoryRepository,
        ApiFormatter $formatter
    ) {
        $data = $categoryRepository->getSimpleList();
        return $formatter->json($data);
    }

    /**
     * 標籤列表、關鍵字搜尋
     *
     * @url forum/tags
     * @method GET
     * @header User-Token
     * @param string $keyword 搜尋關鍵字
     * @param array $excludes 排除的標籤id
     * @return json
     */
    public function getTags(
        GetTagService $getTagService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        $data = $getTagService->run($request, 10);

        return $formatter->json($data);
    }

    /**
     * W姐妹分享文導轉至好婚聊聊文章
     *
     * @url forum/redirect/share-post/{post_id}
     * @method GET
     * @header User-Token
     * @param int $post_id W姐妹分享文ID
     * @return json
     */
    public function redirectSharePost(
        SharePost $sharePost,
        ApiFormatter $formatter,
        $post_id
    ) {
        $sharePost = $sharePost->release()->live()->whereNotNull('change_article_id')->find($post_id);
        if (!$sharePost) {
            $this->setException('找不到相關文章！');
        }

        return $formatter->json(['article_id' => $sharePost->change_article_id]);
    }
}
