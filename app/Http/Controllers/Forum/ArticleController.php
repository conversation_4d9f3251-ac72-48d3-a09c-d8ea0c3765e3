<?php

namespace App\Http\Controllers\Forum;

use App\Http\Controllers\Controller;
use App\Services\Forum\Article\CreateService;
use App\Services\Forum\Article\SaveService;
use App\Services\Forum\Article\AtUserService;
use App\Services\Forum\Article\PostTagService;
use App\Services\Forum\Article\ShowService;
use App\Services\Forum\Article\LikeService;
use App\Services\Forum\Article\CommentService;
use App\Services\Mail\Forum\TrackArticleService;
use App\Services\MetaScraper\ScraperHandle;
use App\Http\Requests\Forum\ArticleRequest;
use App\Http\Requests\Forum\PostTagRequest;
use App\Http\Requests\Forum\CommentRequest;
use App\Transformers\Forum\ArticleTransformer;
use App\Formatters\ApiFormatter;
use App\Traits\ApiErrorTrait;

class ArticleController extends Controller
{
    use ApiErrorTrait;

    /**
     *
     * 我要發文/編輯文章
     * @url /forum/article/create
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @return "successful operation"
     *
     */
    public function create(
        CreateService $createService,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $createService->run($request);

        // Transformer
        $result = $articleTransformer->create($data);

        return $formatter->json($result);
    }

    /**
     *
     * 儲存文章
     * @url /forum/article/save
     * @method POST
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param category_id 分類ID
     * @param title 文章標題
     * @param content 詳細內容
     * @param is_anonymous 是否匿名
     * @param status 狀態 pending:草稿 published:已發佈 softdelete:顯示軟刪除 delete:刪除
     * @param brands 分享文品牌關聯
     * @param cover_use_image_id 封面使用的圖片編號
     * @return "successful operation"
     *
     */
    public function save(
        ArticleRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        // Service
        $article = $saveService->run($request);

        return $formatter->json(['article_id' => $article->id]);
    }

    /**
     *
     * At User列表
     * @url /forum/article/at-users
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param type 類型 文章:article 留言:comment
     * @param keyword 搜尋關鍵字
     * @param article_id 文章ID
     * @param comment_parent_id 所屬留言ID
     * @return "successful operation"
     *
     */
    public function atUsers(
        AtUserService $atUserService,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $users = $atUserService->run($request);

        // Transformer
        $result = $articleTransformer->atUsers($users);

        return $formatter->json($result);
    }

    /**
     *
     * 連結拿seo
     * @url forum/seo
     * @method GET
     * @header User-Token
     * @param string $url
     * @return json
     *
     */
    public function fetchSEO(
        ScraperHandle $scraperHandle,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $url = $request['url'];

        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            $this->setException('url格式錯誤');
        }
        $data = $scraperHandle->handle($url, $request['type'], $request['target_id']);

        return $formatter->json($data);
    }

    /**
     *
     * 儲存話題標籤
     * @url /forum/article/{article_id}/tags
     * @method POST
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param status 狀態 pending:草稿 published:已發佈
     * @param tags[] 標籤陣列
     * @return "successful operation"
     *
     */
    public function postTags(
        PostTagRequest $request,
        PostTagService $postTagService,
        ApiFormatter $formatter
    ) {
        // 取得文章
        $article = request('article');

        // Service
        $article->status = $request['status'];
        $article->save();
        $postTagService->run($request);

        return $formatter->json();
    }

    /**
     *
     * 文章詳細內容頁
     * @url /forum/article/{article_id}
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param keyword_id 關鍵字搜尋紀錄的編號
     * @return "successful operation"
     *
     */
    public function show(
        ShowService $showService,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $showService->run($request);

        // Transformer
        $result = $articleTransformer->showV2($data);

        return $formatter->json($result);
    }

    /**
     *
     * 文章詳細頁 感興趣的文章
     * @url /forum/article/{article_id}/possible-readings
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @return "successful operation"
     *
     */
    public function possibleReadings(
        ShowService $showService,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $showService->getPossibleReadingsV2($request['article']);

        // Transformer
        $result = $articleTransformer->getPossibleReadings($data);

        return $formatter->json($result);
    }

    /**
     *
     * 文章詳細頁 留言列表
     * @url /forum/article/{article_id}/comments
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param page 頁數
     * @param comment 留言ID
     * @return "successful operation"
     *
     */
    public function getComment(
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $requestComment = $request->only(['page', 'comment']);
        // Transformer
        $result = $articleTransformer->getComment($request['user'], $request['article'], $requestComment);

        return $formatter->json($result);
    }

    /**
     *
     * 文章&留言按讚
     * @url /forum/article/{article_id}/like
     * @method POST
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param like 是否按讚 0:取消 1:按讚
     * @param comment_id 留言ID
     * @return "successful operation"
     *
     */
    public function like(
        LikeService $likeService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $totlaLike = $likeService->run($request);

        return $formatter->json([
            'like'       => (boolean) $request['like'],
            'like_count' => $totlaLike
        ]);
    }

    /**
     *
     * 追蹤文章
     * @url /forum/article/{article_id}/track
     * @method POST
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param track 是否追蹤 0:取消 1:追蹤
     * @return "successful operation"
     *
     */
    public function track(
        TrackArticleService $trackArticleService,
        ApiFormatter $formatter
    ){
        // 取得使用者 & 文章
        $user    = request('user');
        $article = request('article');

        // 更新文章追蹤 & 寄信給作者
        if($article->tracks()->where('user_id', $user->id)->count() > 0) {
            // 已經追蹤過了
            if (!request('track')) {
                // 取消追蹤
                $article->tracks()->detach($user->id);
            }
        } else {
            // 沒有追蹤過
            if (request('track')) {
                // 追蹤文章
                $trackArticleService->sendMail($article);
                $article->tracks()->attach($user->id);
            }
        }

        // 更新文章追蹤數
        $article->track_count = $article->tracks->count();
        $article->save();

        return $formatter->json([
            'track'      => (boolean) request('track'),
            'track_count' => $article->track_count
        ]);
    }

    /**
     *
     * 留言
     * @url /forum/article/{article_id}/comment
     * @method POST
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param comment_id 留言ID
     * @param parent_id 上一層的留言ID
     * @param content 詳細內容
     * @param is_anonymous 是否匿名 0:否 1:是
     * @param hide_type 隱藏類型 line:LineID facebook:FB連結 email:連絡信箱 phone:連絡電話
     * @param hide_content 隱藏內容
     * @return "successful operation"
     *
     */
    public function comment(
        CommentRequest $request,
        CommentService $commentService,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // 取得使用者 & 文章
        $user          = request('user');
        $article       = request('article');
        $needAutoReply = ($article->autoReply && !$request['parent_id'] && !$request['comment_id']);

        // 自動回覆功能
        if ($needAutoReply) {
            $_commentService = clone $commentService; // 保險起見，clone一個
        }

        // Service
        $comment = $commentService->run($request);

        if($request['comment_id']) {
            // 修改留言：replies顯示此留言的回覆
            $replies = $comment->replies()->get()->map(function ($reply) use ($articleTransformer, $user) {
                return $articleTransformer->getCommentInfo($reply, $user);
            })->toArray();
        } else {
            // 新增留言：若為自動回覆文章 replies顯示自動回覆內容
            $replies = [];
            if ($needAutoReply && $comment) {
                $article = $comment->article; // 累計文章留言數

                $_request = clone $request;
                $_request['article']      = $article;
                $_request['user']         = $article->author; // 抽換掉User
                $_request['is_anonymous'] = $article->is_anonymous;
                $_request['auto_reply']   = true;
                $_request['parent_id']    = $comment->id;
                $_request['content']      = $article->autoReply->reply_content;

                $reply     = $_commentService->run($_request);
                $replies[] = $articleTransformer->getCommentInfo($reply, $user);
            }
        }

        // Transformer
        $result = $articleTransformer->getCommentInfo($comment, $user);
        $result['replies'] = $replies;

        return $formatter->json($result);
    }

    /**
     *
     * 社群分享
     * @url /forum/article/{article_id}/share
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @param type 種類 facebook:臉書分享 line:Line分享 link:網址分享
     * @return "successful operation"
     *
     */
    public function share(ApiFormatter $formatter)
    {
        // 取得使用者 & 文章
        $user    = request('user');
        $article = request('article');

        // 新增Log紀錄
        $article->logShares()->create([
            'user_id' => $user ? $user->id : NULL,
            'type'    => request('type'),
        ]);

        return $formatter->json();
    }

    /**
     *
     * 點擊可能想閱讀的文章
     * @url /forum/article/{article_id}/possible-click
     * @method GET
     * @param User-Token 使用者驗證碼
     * @param article_id 文章ID
     * @return "successful operation"
     *
     */
    public function possibleClick(ApiFormatter $formatter)
    {
        $article = request('article');

        $article->possible_read_click ++;
        $article->possible_read_CTR = ($article->possible_read_impressions > 0) ? round($article->possible_read_click / $article->possible_read_impressions, 5) : 0;
        $article->possible_read_CTR = ($article->possible_read_CTR > 1) ? 1 : $article->possible_read_CTR;
        $article->save();

        return $formatter->json();
    }
}
