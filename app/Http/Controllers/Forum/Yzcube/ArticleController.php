<?php

namespace App\Http\Controllers\Forum\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Forum\Yzcube\ArticleRequest;
use App\Http\Requests\Forum\Yzcube\CommentRequest;
use App\Services\Forum\Article\AtUserService;
use App\Services\Forum\Article\GetTagService;
use App\Services\Forum\Yzcube\Article\GetListService;
use App\Services\Forum\Yzcube\Article\SaveService;
use App\Services\Forum\Yzcube\Article\CommentService;
use App\Services\Forum\Yzcube\Article\SaveBrandService;
use App\Services\User\Wedding\GetBrandService;
use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Repositories\ForumArticleRepository as ArticleRepository;
use App\Repositories\UserRepository;
use App\Models\Event;
use App\Transformers\Forum\Yzcube\ArticleTransformer;
use App\Transformers\Forum\ArticleTransformer as ArticleFrontendTransformer;
use App\Transformers\User\WeddingTransformer;
use App\Formatters\ApiFormatter;

class ArticleController extends Controller
{
    /**
     * 神之後台-好婚聊聊-文章列表
     *
     * @url forum/yzcube/article
     * @method GET
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @param int $category_id 分類ID
     * @param string $status 狀態 pending:草稿 published:已發佈 softdelete:顯示軟刪除 delete:刪除
     * @param string $keyword 搜尋關鍵字
     * @param bool $is_exclude_seo 是否排除SEO
     * @param string $sort 排序欄位 id:編號 published_at:發佈日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $page 頁碼
     *
     * @return json
     */
    public function list(
        CategoryRepository $categoryRepository,
        ArticleRepository $articleRepository,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得文章列表、分類列表、商家類型列表
        $data['articles']   = $articleRepository->getYzcubeListByRequest($request);
        $data['categories'] = $categoryRepository->getSimpleList();

        // Transformer
        $result = $articleTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 神之後台-好婚聊聊-At User列表
     *
     * @url forum/yzcube/article/at-users
     * @method GET
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @param int $article_id 文章ID
     * @param string $keyword 搜尋關鍵字
     *
     * @return json
     */
    public function atUsers(
        UserRepository $userRepository,
        AtUserService $atUserService,
        ArticleFrontendTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();
        $request['user'] = $request['article'] ? $request['article']->author : $userRepository->getModel();

        // Service
        $users = $atUserService->run($request);

        // Transformer
        $result = $articleTransformer->atUsers($users);

        return $formatter->json($result);
    }

    /**
     * 神之後台-好婚聊聊-話題標籤列表
     *
     * @url forum/yzcube/article/tags
     * @method GET
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @param string $keyword 搜尋關鍵字
     * @param array.string $excludes[] 排除HashTag
     *
     * @return json
     */
    public function getTags(
        GetTagService $getTagService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $data = $getTagService->run($request);

        return $formatter->json($data);
    }

    /**
     * 神之後台-好婚聊聊-儲存文章
     *
     * @url forum/yzcube/article/save
     * @method POST
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @param int $article_id 文章ID
     * @param int $user_id 使用者ID
     * @param bool $is_anonymous 是否匿名
     * @param int $category_id 分類ID
     * @param int $event_id 活動表單ID
     * @param string $title 文章標題
     * @param string $content 詳細內容
     * @param string $cover_image 封面照
     * @param string $status 狀態 pending:草稿 published:已發佈 softdelete:顯示軟刪除 delete:刪除
     * @param string $set_publish_at 設定發佈時間
     * @param bool $is_exclude_seo 是否排除SEO
     * @param bool $is_top 是否置頂
     * @param string $top_stop_at 置頂結束時間
     * @param bool $is_complain 是否為負評
     * @param array.string $tags[] 標籤陣列
     * @param bool $auto_reply 是否開啟自動回覆功能
     * @param bool $whisper 是否使用悄悄話的功能
     * @param string $whisper_content 悄悄話顯示的內容
     * @param string $reply_content 自動回覆的內容
     *
     * @return json
     */
    public function save(
        ArticleRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        // Service
        $article = $saveService->run($request);

        return $formatter->json(['article_id' => $article->id]);
    }

    /**
     * 神之後台-好婚聊聊-新增文章頁
     *
     * @url forum/yzcube/article/create
     * @method GET
     * @haeder Yzcube-Token 管理者驗證碼 *
     *
     * @return json
     */
    public function create(
        CategoryRepository $categoryRepository,
        UserRepository $userRepository,
        Event $event,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // 取得分類列表、管理者&假帳號列表、活動列表
        $data['categories'] = $categoryRepository->getPublicUseList(false);
        $data['users']      = $userRepository->getAdminFakeUserList();
        $data['events']     = $event->orderBy('id', 'DESC')->get();

        // Transformer
        $result = $articleTransformer->create($data);

        return $formatter->json($result);
    }

    /**
     * 神之後台-好婚聊聊-文章詳細內容頁
     *
     * @url forum/yzcube/article/{article_id}
     * @method GET
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @path int $article_id 文章ID *
     * @param int $page 留言列表分頁
     *
     * @return json
     */
    public function show(
        CategoryRepository $categoryRepository,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    ) {
        // 取得文章、分類列表
        $data['article']    = request('article');
        $data['categories'] = $categoryRepository->getPublicUseList(false);

        // Transformer
        $result = $articleTransformer->show($data);

        return $formatter->json($result);
    }

    /**
     * 神之後台-好婚聊聊-更新留言
     *
     * @url forum/yzcube/article/{article_id}/comment
     * @method POST
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @path int $article_id 文章ID *
     * @param int $comment_id 留言ID *
     * @param string $status 狀態 pending:草稿 published:已發佈 softdelete:顯示軟刪除 delete:刪除
     *
     * @return json
     */
    public function comment(
        CommentRequest $request,
        CommentService $commentService,
        ApiFormatter $formatter
    ) {
        // Service
        $commentService->run($request);

        return $formatter->json();
    }

    /**
     * 神之後台-好婚聊聊-取得品牌列表
     *
     * @url forum/yzcube/article/{article_id}/brands
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token 管理者驗證碼 *
     * @path int $article_id 文章ID *
     * @param string $keyword 搜尋關鍵字
     * @param string $name 商家名稱
     * @param string $website 官網連結
     * @param string $fb_page FB粉絲團連結
     * @param string $instagram Instagram
     * @param string $email Email
     * @param string $phone 行動電話
     * @param string $tel 市話
     * @return json
     */
    public function getBrands(
        GetBrandService $getBrandService,
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter
    )
    {
        // Request
        $request = request();
        $article = request('article');

        // Service
        $data = $getBrandService->run($request);

        // Transformer
        $result = $weddingTransformer->getBrandList($data, $article->author);

        return $formatter->json($result);
    }

    /**
     * 神之後台-好婚聊聊-儲存品牌標籤
     *
     * @url forum/yzcube/article/{article_id}/brand/{brand_id}
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token 管理者驗證碼 *
     * @path int $article_id 文章ID *
     * @path int $brand_id 品牌ID *
     * @return json
     */
    public function saveBrand(
        $article_id,
        $brand_id,
        SaveBrandService $saveBrandService,
        ArticleTransformer $articleTransformer,
        ApiFormatter $formatter
    )
    {
        // Request
        $request = request();

        // Service
        $brand = $saveBrandService->run($brand_id, $request);

        // Transformer
        $result = $articleTransformer->getBrandInfo($brand);

        return $formatter->json($result);
    }

    /**
     * 神之後台-好婚聊聊-刪除品牌標籤
     *
     * @url forum/yzcube/article/{article_id}/brand/{brand_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token 管理者驗證碼 *
     * @path int $article_id 文章ID *
     * @path int $brand_id 品牌ID *
     * @return json
     */
    public function deleteBrand(
        $article_id,
        $brand_id,
        ApiFormatter $formatter
    )
    {
        // 取得分享文
        $article = request('article');

        // 刪除品牌標籤
        $article->allBrands()->where('user_created', 0)->detach($brand_id);

        return $formatter->json();
    }

    /**
     * 神之後台-好婚聊聊-取得新娘個人頁的分頁資料
     *
     * @url forum/yzcube/article/member/{user_id}
     * @method GET
     * @haeder Yzcube-Token 管理者驗證碼 *
     * @path int $user_id 會員ID *
     * @param string $type 資料類型
     * @param bool $anonymous 是否匿名
     * @param string $status 狀態 pending:草稿 published:已發佈 softdelete:顯示軟刪除 delete:刪除
     * @param int $page 頁碼
     *
     * @return json
     */
    public function listByMember(
        GetListService $getListService,
        ApiFormatter $formatter
    )
    {
        // 取得使用者
        $user = request('user');
        $type = request()->input('type', 'articles');
        $status = request()->input('status', 'published');
        $anonymous = request()->input('anonymous', 0);
        $data = $getListService->$type($user, [intval($anonymous)], [$status]);
        return $formatter->json($data);
    }
}
