<?php

namespace App\Http\Controllers\Forum\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Forum\Yzcube\FakeUserRequest;
use App\Services\Forum\Yzcube\FakeUser\StoreService;
use App\Services\Forum\Yzcube\FakeUser\UpdateService;
use App\Repositories\FakeUserRepository;
use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Traits\ApiErrorTrait;
use App\Transformers\Forum\Yzcube\FakeUserTransformer;
use App\Formatters\ApiFormatter;

class FakeUserController extends Controller
{
    use ApiErrorTrait;

    /**
     * 假帳號列表
     *
     * @url forum/yzcube/fake-user
     * @method GET
     * @haeder Yzcube-Token
     * @param keyword, sort:created_at(default)|wedding_date, direction:asc|desc(default), page
     *
     * @return json
     */
    public function list(
        FakeUserRepository $fakeUserRepository,
        FakeUserTransformer $fakeUserTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得假帳號列表
        $fakeUsers = $fakeUserRepository->getListByRequest($request);

        // Transformer
        $result = $fakeUserTransformer->list($fakeUsers);

        return $formatter->json($result);
    }

    /**
     * 新增假帳號
     *
     * @url forum/yzcube/fake-user
     * @method POST
     * @haeder Yzcube-Token
     * @param email, password, count
     *
     * @return json
     */
    public function store(
        FakeUserRequest $request,
        StoreService $storeService,
        ApiFormatter $formatter
    ) {
        // Service
        $storeService->run($request);

        return $formatter->json();
    }

    /**
     * 假帳號詳細內容頁
     *
     * @url forum/yzcube/fake-user/{user_id}
     * @method GET
     * @haeder Yzcube-Token
     *
     * @return json
     */
    public function show(
        $user_id,
        CategoryRepository $categoryRepository,
        FakeUserRepository $fakeUserRepository,
        FakeUserTransformer $fakeUserTransformer,
        ApiFormatter $formatter
    ) {
        // 取得假帳號、分類列表
        $data['categories'] = $categoryRepository->getSimpleList();
        $data['fakeUser']   = $fakeUserRepository->getFirst(['user_id' => $user_id]);
        if (!$data['fakeUser']) {
            $this->setException('找不到這個假帳號！');
        }

        // Transformer
        $result = $fakeUserTransformer->show($data);

        return $formatter->json($result);
    }

    /**
     * 更新假帳號
     *
     * @url forum/yzcube/fake-user/{user_id}
     * @method POST
     * @haeder Yzcube-Token
     * @param wedding_date, tags, is_random_like, note
     *
     * @return json
     */
    public function update(
        $user_id,
        FakeUserRequest $request,
        FakeUserRepository $fakeUserRepository,
        UpdateService $updateService,
        ApiFormatter $formatter
    ) {
        // 取得假帳號
        $fakeUser = $fakeUserRepository->getFirst(['user_id' => $user_id]);
        if (!$fakeUser) {
            $this->setException('找不到這個假帳號！');
        }

        // Service
        $updateService->run($fakeUser, $request);

        return $formatter->json();
    }
}
