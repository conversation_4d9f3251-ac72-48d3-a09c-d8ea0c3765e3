<?php

namespace App\Http\Controllers\Forum\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Forum\Yzcube\CategoryRequest;
use App\Services\Forum\Yzcube\Category\SaveService;
use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Transformers\Forum\Yzcube\CategoryTransformer;
use App\Formatters\ApiFormatter;

class CategoryController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * @SWG\ Get(
     *   path = "/forum/yzcube/category",
     *   summary = "分類列表",
     *   tags = {"神之後台-好婚聊聊分類管理：/forum/yzcube/category"},
     *   description = "神之後台-好婚聊聊分類管理-列表",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function list(
        CategoryRepository $categoryRepository,
        CategoryTransformer $categoryTransformer,
        ApiFormatter $formatter
    ) {
        // 取得分類列表
        $categoryRepository->setOrderBy('sequence');
        $categories = $categoryRepository->getAll();

        // Transformer
        $result = $categoryTransformer->list($categories);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/forum/yzcube/category/sort",
     *   summary = "排序分類",
     *   tags = {"神之後台-好婚聊聊分類管理：/forum/yzcube/category"},
     *   description = "神之後台-好婚聊聊分類管理-排序",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "sequence[]",
     *     in = "formData",
     *     description = "編號排序",
     *     required = true,
     *     type = "array",
     *     items = {
     *         "type": "integer",
     *     },
     *     collectionFormat = "multi",
     *     default = {8, 2, 3}
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function sort(
        ApiFormatter $formatter
    ) {
        // Request
        $sequence = request('sequence');
        if (!$sequence) {
            $this->setException('找不到排序參數！');
        }

        // Trait
        $this->sortListBySequence('ForumCategory', $sequence);

        return $formatter->json();
    }

    /**
     * @SWG\ Post(
     *   path = "/forum/yzcube/category/save",
     *   summary = "儲存分類",
     *   tags = {"神之後台-好婚聊聊分類管理：/forum/yzcube/category"},
     *   description = "神之後台-好婚聊聊分類管理-儲存",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "category_id",
     *     in = "formData",
     *     description = "分享文ID",
     *     type = "integer",
     *     default = 301
     *   ),
     *   @SWG\ Parameter(
     *     name = "name",
     *     in = "formData",
     *     description = "名稱",
     *     required = true,
     *     type = "string",
     *     default = "我要發問"
     *   ),
     *   @SWG\ Parameter(
     *     name = "color",
     *     in = "formData",
     *     description = "色碼",
     *     required = true,
     *     type = "string",
     *     default = "#3bc9d5"
     *   ),
     *   @SWG\ Parameter(
     *     name = "description",
     *     in = "formData",
     *     description = "描述",
     *     required = true,
     *     type = "string",
     *     default = "碰到婚禮相關的疑難雜症時可使用。"
     *   ),
     *   @SWG\ Parameter(
     *     name = "article_placeholder",
     *     in = "formData",
     *     description = "文章預設文字",
     *     type = "string",
     *     default = ""
     *   ),
     *   @SWG\ Parameter(
     *     name = "is_public_use",
     *     in = "formData",
     *     description = "是否開放使用",
     *     required = true,
     *     type = "integer",
     *     enum = {0, 1},
     *     default = 1
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function save(
        CategoryRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        // Service
        $category = $saveService->run($request);

        return $formatter->json(['category_id' => $category->id]);
    }

    /**
     * @SWG\ Post(
     *   path = "/forum/yzcube/category/{category_id}",
     *   summary = "詳細內容頁",
     *   tags = {"神之後台-好婚聊聊分類管理：/forum/yzcube/category"},
     *   description = "神之後台-W姐妹分類管理-詳細內容頁",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "category_id",
     *     in = "path",
     *     description = "分享文ID",
     *     required = true,
     *     type = "integer",
     *     default = 1000
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function show(
        $category_id,
        CategoryRepository $categoryRepository,
        CategoryTransformer $categoryTransformer,
        ApiFormatter $formatter
    ) {
        // 取得分類
        $category = $categoryRepository->getFirst(['id' => $category_id]);
        if (!$category) {
            $this->setException('找不到此分類！');
        }

        // Transformer
        $result = $categoryTransformer->show($category);

        return $formatter->json($result);
    }

    /**
     * @SWG\ Post(
     *   path = "/forum/yzcube/category/{category_id}/delete",
     *   summary = "刪除分類",
     *   tags = {"神之後台-好婚聊聊分類管理：/forum/yzcube/category"},
     *   description = "神之後台-W姐妹分類管理-刪除",
     *   produces = {"application/json"},
     *   @SWG\ Parameter(
     *     name = "Yzcube-Token",
     *     in = "header",
     *     description = "管理者驗證碼",
     *     required = true,
     *     type = "string",
     *     default = "eee82b9703dde16fda60bb2176157de317998f5a"
     *   ),
     *   @SWG\ Parameter(
     *     name = "category_id",
     *     in = "path",
     *     description = "分享文ID",
     *     required = true,
     *     type = "integer",
     *     default = 1000
     *   ),
     *   @SWG\ Response(response = 200, description = "successful operation"),
     * )
     */
    public function delete(
        $category_id,
        CategoryRepository $categoryRepository,
        ApiFormatter $formatter
    ) {
        // 取得分類
        $category = $categoryRepository->getFirst(['id' => $category_id]);
        if (!$category) {
            $this->setException('找不到此分類！');
        }

        // 驗證此分類是否有分享文
        $article_count = $category->articles->count();
        if ($article_count) {
            $this->setException('此分類含有'.$article_count.'篇文章，無法執行刪除！');
        }

        // 刪除分類
        $category->delete();

        return $formatter->json();
    }
}
