<?php

namespace App\Http\Controllers\Forum\Yzcube;

use App\Http\Controllers\Controller;
use App\Repositories\ForumCategoryRepository as CategoryRepository;
use App\Services\Forum\Yzcube\Report\DashboardService;
use App\Transformers\Forum\Yzcube\ReportTransformer;
use App\Formatters\ApiFormatter;

class ReportController extends Controller
{
    /**
     * 數據看板
     *
     * @url forum/yzcube/report
     * @method GET
     * @haeder Yzcube-Token
     * @param category_id, start_date, end_date
     *
     * @return json
     */
    public function dashboard(
        CategoryRepository $categoryRepository,
        DashboardService $dashboardService,
        ReportTransformer $reportTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service 取得報表、分類列表
        $data['categories'] = $categoryRepository->getSimpleList();
        $data['report']     = $dashboardService->run($request);

        // Transformer
        $result = $reportTransformer->dashboard($data);

        return $formatter->json($result);
    }
}
