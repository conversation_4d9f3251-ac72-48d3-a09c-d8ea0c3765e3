<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\CityData;
use App\Models\StoreFare;
use App\Traits\Model\TransformerTrait;
use App\Formatters\ApiFormatter;

class FareController extends Controller
{
    use TransformerTrait;

    /**
     * 車馬費/服務地區-詳細資訊
     *
     * @url api/admin/{store_id}/store/store-fare
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function show(
        CityData $cityData,
        StoreFare $storeFare,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 商家的服務類型列表
        $result['value_list'] = $this->formatAttributeList($storeFare->valueList[$store->type]);

        // 縣市分區，限特定商家
        $result['fares'] = $cityData->region()
                                    ->with([
                                        'children' => function($q1) use ($store) {
                                            $q1->select('id', 'title', 'parent_id')
                                                ->with(['storeFares' => function($q2) use ($store) {
                                                    $q2->where('store_id', $store->id);
                                                }]);
                                        }
                                    ])
                                    ->get()
                                    ->map(function($parent) {
                                        return [
                                            'id'       => $parent->id,
                                            'title'    => $parent->title,
                                            'children' => $parent->children->map(function($children) {
                                                return [
                                                    'id'     => $children->id,
                                                    'title'  => $children->title,
                                                    'value'  => $children->storeFares[0]->value ?? '',
                                                    'number' => $children->storeFares[0]->number ?? '',
                                                ];
                                            }),
                                        ];
                                    });

        return $formatter->json($result);
    }

    /**
     * 車馬費/服務地區-儲存
     *
     * @url api/admin/{store_id}/store/store-fare
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param json $items 所有項目 *
     * @param json.int $items[city_id] 所屬縣市 *
     * @param json.array.int $items[city_id][value] 服務類型 *
     * @param json.array.int $items[city_id][number] 固定費用的值
     * @return json
     */
    public function save(
        ApiFormatter $formatter,
        Store $store
    ) {
        $items = json_decode(request('items'), true);
        $store->fares()->sync($items);

        return $formatter->json();
    }
}
