<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\UpdateRequest;
use App\Http\Requests\Admin\Store\UpdateTastingRequest;
use App\Models\Store;
use App\Models\StoreNotify;
use App\Services\Admin\Store\UpdateService;
use App\Traits\ApiErrorTrait;
use App\Transformers\Admin\Store\ProfileTransformer;
use App\Formatters\ApiFormatter;

class ProfileController extends Controller
{
    use ApiErrorTrait;

    /**
     * 蓋板公告通知-勾選不再提醒
     *
     * @url api/admin/{store_id}/store/notify-not-remind
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @param string $key 蓋板公告通知的索引值 *
     * @return json
     */
    public function notifyNotRemind(
        StoreNotify $storeNotify,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 找出蓋板公告通知
        $storeNotify = $storeNotify->where('key', request('key'))
                                    ->published()
                                    ->whereJsonContains('type_values', $store->type)
                                    ->whereJsonContains('status_values', $store->status)
                                    ->first();
        if (!$storeNotify) {
            $this->setException('找不到此蓋板公告通知！');
        }

        // 紀錄此商家已勾選不再提醒
        $storeNotify->notReminds()->updateOrCreate(['store_id' => $store->id]);

        return $formatter->json();
    }

    /**
     * 編輯商家資訊
     *
     * @url api/admin/{store_id}/store
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function show(
        ProfileTransformer $profileTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $profileTransformer->show($store);

        return $formatter->json($result);
    }

    /**
     * 更新商家資訊
     *
     * @url api/admin/{store_id}/store
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $logo 商家LOGO *
     * @param string $cover 商家封面照 *
     * @param string $name 商家名稱 *
     * @param array.string $narrates[] 特色標語
     * @param string $about 關於你們 *
     * @param int $extra_type 額外的商家類型 301:平面婚攝 302:動態婚錄 303:婚攝+婚錄
     * @param json.array.object $tags 商家提供的服務 *
         * @param int $tag->key 商家提供的服務-標籤索引
         * @param int $tag->value 商家提供的服務-標籤值
         * @param string $tag->note 商家提供的服務-標籤備註
     * @param int $city_id 縣市
     * @param int $area_id 地區
     * @param string $address 地址
     * @param string $address_info 地址備註訊息
     * @param string $map_title 地圖名稱
     * @param string $open_time 開始營業時間
     * @param string $close_time 結束營業時間
     * @param bool $round_clock 全天候營業
     * @param array.object $opening_hours 營業時間列表
         * @param int $opening_hour->start_week  營業時間-星期開始
         * @param int $opening_hour->end_week  營業時間-星期結束
         * @param string $opening_hour->open_time  營業時間-開始營業時間
         * @param string $opening_hour->close_time  營業時間-結束營業時間
     * @param string $public_holidays 公休日
     * @param string $tel 市話
     * @param string $tel_info 市話備註訊息
     * @param string $phone 手機
     * @param string $phone_info 手機備註訊息
     * @param string $contact_email 聯絡Email
     * @param string $line Line ID
     * @param string $website 官網
     * @param string $fb_page FB粉絲專頁
     * @param string $instagram Instagram
     * @param json.array.object $features 特色說明
         * @param int $tag->value 特色說明-圖片
         * @param string $tag->note 特色說明-文案
     * @param string $discount 最新優惠
     * @param string $traffic 交通資訊
     * @param string $device 設備工具
     * @param string $experience 資歷介紹
     * @param string $other 想對新娘說的話
     * @param int $has_files 拍婚紗-有無檔案全贈 0:無 1:有 2:限方案
     * @param int $look_time 婚紗禮服-租借日多久前可參觀禮服
     * @param int $reserve_time 婚紗禮服-租借日多久前可試穿禮服
     * @param int $has_trial_fee 婚紗禮服-有無試穿費 | 新娘秘書-試妝服務
     * @param int $min_trial_fee 婚紗禮服-試穿費金額-最小 | 新娘秘書-試妝費金額-最小
     * @param int $max_trial_fee 婚紗禮服-試穿費金額-最大 | 新娘秘書-試妝費金額-最大
     * @param string $trial_fee_info 婚紗禮服-試穿費說明 | 新娘秘書-試妝優惠與內容說明
     * @param int $pieces 婚紗禮服-店內婚紗件數
     * @param int $rooms 婚紗禮服-試衣間數
     * @param int $frequency 婚紗禮服-試穿件數
     * @param int $use_time 婚紗禮服-試穿時數
     * @param int $people 婚紗禮服-可攜伴數
     * @param bool $is_photograph 婚紗禮服-可否拍照
     * @param bool $is_pet 婚紗禮服-攜帶寵物
     * @param bool $is_eat 婚紗禮服-可否飲食
     * @param int $room_count 婚宴場地-廳房總數
     * @param int $min_number 婚宴場地-最小建議人數
     * @param int $max_number 婚宴場地-最大建議人數
     * @param string $dish_tasting 婚宴場地-試菜優惠說明
     * @param int $countdown_wedding_days 喜餅-建議於婚期多久前下訂
     * @param string $additional_orders 喜餅-喜餅追加及調整說明
     * @param string $free_delivery_method 喜餅-免運計算方式
     * @param string $logistics_info 喜餅-物流配送說明
     * @param string $logistics_date_info 喜餅-指定配送日與時段說明
     * @param string $logistics_main_island 喜餅-運費計算方式-台灣本島
     * @param string $logistics_outer_island 喜餅-運費計算方式-外島地區
     * @param string $logistics_overseas 喜餅-運費計算方式-海外地區
     * @param bool $has_multiple_shops 喜餅-有無多間門市
     * @param json.array.object $shops 門市資訊
         * @param int $shop->id 門市資訊-ID
         * @param string $shop->name 門市資訊-名稱
         * @param int $shop->city_id 門市資訊-地址-縣市
         * @param int $shop->area_id 門市資訊-地址-地區
         * @param string $shop->address 門市資訊-地址-地址
         * @param string $shop->address_info 門市資訊-地址-備註訊息
         * @param array.object $shop->opening_hours 門市資訊-營業時間列表
             * @param int $shop->opening_hour->start_week  門市資訊-營業時間-星期開始
             * @param int $shop->opening_hour->end_week  門市資訊-營業時間-星期結束
             * @param string $shop->opening_hour->open_time  門市資訊-營業時間-開始營業時間
             * @param string $shop->opening_hour->close_time  門市資訊-營業時間-結束營業時間
         * @param string $shop->public_holidays  門市資訊-公休日
         * @param string $shop->tel 門市資訊-市話
         * @param string $shop->tel_info 門市資訊-市話備註訊息
         * @param string $shop->phone 門市資訊-手機
         * @param string $shop->phone_info 門市資訊-手機備註訊息
     *
     * @return json
     */
    public function update(
        UpdateRequest $request,
        UpdateService $updateService,
        StoreNotify $storeNotify,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 上架中的婚攝/婚錄，必須驗證服務類型
        if ($store->type == 3 && $store->status == 'published') {
            if (in_array($request['extra_type'], [301, 303]) && $store->showAlbums->isEmpty()) {
                $this->setException('無法變更服務類型，必須至少有一個顯示中的相本唷！', 4020);
            }
            if (in_array($request['extra_type'], [302, 303]) && $store->showVideos->isEmpty()) {
                $this->setException('無法變更服務類型，必須至少有一個顯示中的影片唷！', 4020);
            }
        }

        // Service
        $updateService->run($store, $request);

        // 蓋板公告通知-紀錄不再提醒 (付費商家)：更新商家基本資料 profile_update_202208
        if ($store->present()->has_paid) {
            $storeNotify = $storeNotify->where('key', 'profile_update_202208')
                                        ->published()
                                        ->whereJsonContains('type_values', $store->type)
                                        // ->whereJsonContains('status_values', $store->status) // 所有狀態都要記錄已更新商家基本資料
                                        ->first();
            if ($storeNotify) {
                $storeNotify->notReminds()->updateOrCreate(['store_id' => $store->id]);
            }
        }

        return $formatter->json();
    }

    /**
     * 試吃資訊設定 (喜餅)
     *
     * @url api/admin/{store_id}/store/tasting
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function showTasting(
        ProfileTransformer $profileTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 限喜餅商家
        if ($store->type != 10) {
            $this->setException('喜餅商家才可以編輯試吃資訊設定！');
        }

        // Transformer
        $result = $profileTransformer->showTasting($store);

        return $formatter->json($result);
    }

    /**
     * 更新試吃資訊設定 (喜餅)
     *
     * @url api/admin/{store_id}/store/tasting
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param bool $has_shop_tasting 是否提供門市試吃
     * @param int $shop_tasting_min_fee 門市試吃-最低價格
     * @param int $shop_tasting_max_fee 門市試吃-最高價格
     * @param string $shop_tasting_time 門市試吃-預約時間
     * @param string $shop_tasting_content 門市試吃-試吃內容
     * @param string $shop_tasting_method 門市試吃-試吃辦法
     * @param string $shop_tasting_image 門市試吃-門市店內照片
     * @param bool $has_delivery_tasting 是否提供宅配試吃
     * @param int $delivery_tasting_min_fee 宅配試吃-最低價格
     * @param int $delivery_tasting_max_fee 宅配試吃-最高價格
     * @param int $delivery_tasting_min_delivery_fee 宅配試吃-運費最低價格
     * @param int $delivery_tasting_max_delivery_fee 宅配試吃-運費最高價格
     * @param string $delivery_tasting_content 宅配試吃-試吃內容
     * @param string $delivery_tasting_method 宅配試吃-試吃辦法
     * @param string $delivery_tasting_image 宅配試吃-宅配禮盒照片
     *
     * @return json
     */
    public function updateTasting(
        UpdateTastingRequest $request,
        UpdateService $updateService,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 限喜餅商家
        if ($store->type != 10) {
            $this->setException('喜餅商家才可以編輯試吃資訊設定！');
        }

        // Service
        $updateService->runTasting($store, $request);

        return $formatter->json();
    }
}
