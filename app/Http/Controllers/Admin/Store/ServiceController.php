<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\ServiceSaveRequest;
use App\Http\Requests\Admin\Store\ServiceChangeStatusRequest;
use App\Models\Store;
use App\Models\StoreService;
use App\Models\StoreNotify;
use App\Models\Activity;
use App\Services\Admin\Store\ServiceListService;
use App\Services\Admin\Store\ServiceSaveService;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\ServiceTransformer;
use App\Formatters\ApiFormatter;

class ServiceController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * 方案管理-列表
     *
     * @url api/admin/{store_id}/store/store-service
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param int $activity_id 活動方案ID
     * @param int $type 方案類型 (除了場地 & 主持人)
     * @param string $status 狀態
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        ServiceListService $serviceListService,
        ServiceTransformer $serviceTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $serviceListService->run($store, $request);

        // Transformer
        $result = $serviceTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 方案管理-排序用的列表
     *
     * @url api/admin/{store_id}/store/store-service/sort-list
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param int $activity_id 活動方案ID
     * @return json
     */
    public function sortList(
        ServiceTransformer $serviceTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        $services = $store->sortServices();

        // 活動方案ID
        if (request('activity_id')) {
            $services = $services->where('activity_id', request('activity_id'));
        } else {
            $services = $services->whereNull('activity_id');
        }

        $services = $services->get();

        // Transformer
        $result = $serviceTransformer->sortList($services);

        return $formatter->json($result);
    }

    /**
     * 方案管理-新增頁
     *
     * @url api/admin/{store_id}/store/store-service/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function create(
        ServiceTransformer $serviceTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $serviceTransformer->create($store);

        return $formatter->json($result);
    }

    /**
     * 方案管理-詳細資訊
     *
     * @url api/admin/{store_id}/store/store-service/{service_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int service_id 方案ID *
     * @return json
     */
    public function show(
        ServiceTransformer $serviceTransformer,
        ApiFormatter $formatter,
        Store $store,
        StoreService $storeService
    ) {
        // Transformer
        $result = $serviceTransformer->show($storeService);

        return $formatter->json($result);
    }

    /**
     * 方案管理-儲存
     *
     * @url api/admin/{store_id}/store/store-service
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $service_id 方案ID
     * @param int $activity_id 活動方案ID
     * @param string $status 狀態 hide:隱藏 show:顯示 *
     * @param string $cover 方案封面照 (required_if: status=show)
     * @param string $name 方案名稱 *
     * @param int $min_price 最低金額 (required_if: is_private=0)
     * @param int $max_price 最高金額 (required_if: is_private=0)
     * @param bool $is_private 價格不公開 (required_if: status=show)
     * @param string $type 方案類型 (required_if: status=show)
     * @param bool $has_tip +10%服務費 (required_if: status=show)
     * @param string $priced_by 計費方式 table:按桌數計費 person:按人數計費 (required_if: status=show)
     * @param int $seater 幾人桌 10:10人桌 12:12人桌 (required_if: priced_by=table)
     * @param int $has_child_discount 兒童優惠 (required_if: priced_by=person)
     * @param string $description 方案說明 (required_if: status=show)
     * @param json.array.object $tags 方案包含的服務 (required_if: status=show)
     * @param json.array.object $images 方案說明照 (required_if: status=show)
     * @param json.array.object $discounts 喜餅方案的優惠內容 (喜餅)
     * @param json.array.int $albums 適用禮盒 (喜餅)
     * @return json
     */
    public function save(
        ServiceSaveRequest $request,
        Activity $activity,
        ServiceSaveService $serviceSaveService,
        StoreNotify $storeNotify,
        ServiceTransformer $serviceTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 驗證至少有一個顯示中的方案
        if (
            !$request['activity_id'] &&
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showServicesWithoutActivity()->where('id', '!=', $request['service_id'])->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的方案唷！', 4011);
        }

        // 驗證商家的活動方案
        if ($request['activity_id']) {
            $activity = $activity->find($request['activity_id']);
            if (!$activity) {
                $this->setException('找不到商家的活動方案唷！');
            }
            $today = now()->format('Y-m-d');
            if ($activity->end_date < $today) {
                $this->setException('此商家活動方案已結束唷！');
            }
            if ($activity->attendSores(0)->pluck('store_id')->contains($store->id)) {
                $this->setException('商家未參加此活動方案唷！');
            }
        }

        // Service
        $storeService = $serviceSaveService->run($store, $request);

        // 更新排序
        $this->sortListBySequence('StoreService', $store->sortServices->pluck('id'));

        // 蓋板公告通知-紀錄不再提醒：商家方案資料改版 service_update_202303
        $storeNotify = $storeNotify->where('key', 'service_update_202303')
                                    ->published()
                                    ->whereJsonContains('type_values', $store->type)
                                    // ->whereJsonContains('status_values', $store->status) // 所有狀態都要記錄已更新商家方案資料
                                    ->first();

        // 若所有顯示中的方案都有更新時間，則不再提醒蓋板通知
        if ($storeNotify && $store->showServicesWithoutActivity()->whereNull('edited_at')->doesntExist()) {
            $storeNotify->notReminds()->updateOrCreate(['store_id' => $store->id]);
        }

        // Transformer
        $result = $serviceTransformer->show($storeService);

        return $formatter->json($result);
    }

    /**
     * 方案管理-排序
     *
     * @url api/admin/{store_id}/store/store-service/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('StoreService', request('sequence'));

        return $formatter->json();
    }

    /**
     * 方案管理-變更狀態
     *
     * @url api/admin/{store_id}/store/store-service/{service_id}/change-status
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int service_id 方案ID *
     * @param string status 狀態 hide:隱藏 show:顯示 *
     * @return json
     */
    public function changeStatus(
        ServiceChangeStatusRequest $request,
        ApiFormatter $formatter,
        Store $store,
        StoreService $storeService
    ) {
        // 驗證至少有一個顯示中的方案
        if (
            !$storeService->activity_id &&
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showServicesWithoutActivity()->where('id', '!=', $storeService->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的方案唷！', 4011);
        }

        // 驗證商家的活動方案
        if ($storeService->activity_id) {
            $activity = $storeService->activity;
            if (!$activity) {
                $this->setException('找不到商家的活動方案唷！');
            }
            $today = now()->format('Y-m-d');
            if ($activity->end_date < $today) {
                $this->setException('此商家活動方案已結束唷！');
            }
            if ($activity->attendSores(0)->pluck('store_id')->contains($store->id)) {
                $this->setException('商家未參加此活動方案唷！');
            }
        }

        $storeService->status    = $request['status'];
        $storeService->edited_at = now();
        $storeService->save();

        // 更新商家的服務價格區間、最後編輯時間
        $store->updatePriceRange();
        $store->edited_at = now();
        $store->save();

        return $formatter->json();
    }

    /**
     * 方案管理-刪除
     *
     * @url api/admin/{store_id}/store/store-service/{service_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int service_id 方案ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        StoreService $storeService,
        StoreNotify $storeNotify
    ) {
        // 驗證至少有一個顯示中的方案
        if (
            !$storeService->activity_id &&
            $store->present()->is_published &&
            $store->showServicesWithoutActivity()->where('id', '!=', $storeService->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的方案唷！', 4011);
        }

        $storeService->delete();

        // 蓋板公告通知-紀錄不再提醒：商家方案資料改版 service_update_202303
        $storeNotify = $storeNotify->where('key', 'service_update_202303')
                                    ->published()
                                    ->whereJsonContains('type_values', $store->type)
                                    // ->whereJsonContains('status_values', $store->status) // 所有狀態都要記錄已更新商家方案資料
                                    ->first();

        // 若所有顯示中的方案都有更新時間，則不再提醒蓋板通知
        if ($storeNotify && $store->showServicesWithoutActivity()->whereNull('edited_at')->doesntExist()) {
            $storeNotify->notReminds()->updateOrCreate(['store_id' => $store->id]);
        }

        return $formatter->json();
    }

    /**
     * 方案管理-活動方案列表
     *
     * @url api/admin/{store_id}/store/store-service/activity
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function listActivity(
        Activity $activity,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 商家的活動方案參加狀態
        $attendActivityList = $store->activities->pluck('pivot.is_attend', 'id');

        // 取得所有活動方案
        $result     = [];
        $activities = $activity->storeType($store->type)->get();
        foreach ($activities as $activity) {
            $result[] = [
                'id'           => $activity->id,
                'title'        => $activity->title,
                'status'       => $activity->getStatus(),
                'release_date' => $activity->release_date,
                'start_date'   => $activity->start_date,
                'end_date'     => $activity->end_date,
                'is_attend'    => $attendActivityList[$activity->id] ?? Null,
            ];
        }

        return $formatter->json($result);
    }
}
