<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\DressSaveRequest;
use App\Http\Requests\Admin\Store\DressOrderSaveRequest;
use App\Models\Store;
use App\Models\DressContract;
use App\Models\DressOrder;
use App\Services\Admin\Store\DressListService;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\DressTransformer;
use App\Formatters\ApiFormatter;
use App\Traits\ApiErrorTrait;

class DressController extends Controller
{
    use SortListTrait;
    use ApiErrorTrait;

    /**
     * 禮服-檔期
     *
     * @url api/admin/{store_id}/store/dress/schedule
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function scheduleList(
        DressListService $dressListService,
        ApiFormatter $formatter,
        DressTransformer $dressTransformer,
        Store $store
    ) {
        // Request
        $request = request();

        // 禮服名稱精準搜尋至該禮服
        $albumName = $request['name'];
        $album = $store->albums()->where('name', $albumName)->first();

        if (!$album) {
            $this->setException('找不到此帳號擁有的禮服！');
        }

        // Service
        $data = $dressListService->scheduleList($album, $request);

        // Transformer
        $result = $dressTransformer->scheduleList($data);

        return $formatter->json($result);
    }

    /**
     * 禮服訂單-列表
     *
     * @url api/admin/{store_id}/store/dress/order
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function orderList(
        DressListService $dressListService,
        ApiFormatter $formatter,
        DressTransformer $dressTransformer,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $dressListService->orderList($store, $request);

        // Transformer
        $result = $dressTransformer->orderList($data);

        return $formatter->json($result);
    }

    /**
     * 禮服合約-列表
     *
     * @url api/admin/{store_id}/store/dress/contract
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function list(
        DressListService $dressListService,
        ApiFormatter $formatter,
        DressTransformer $dressTransformer,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $dressListService->run($store, $request);

        // Transformer
        $result = $dressTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 禮服合約-儲存
     *
     * @url api/admin/{store_id}/store/dress/contract
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $contract_id 合約ID
     * @param string $bride_name 新娘名字 (required_if:bridegroom_name=null)
     * @param string $bride_phone 新娘手機 (required_if:bridegroom_phone=null)
     * @param string $bridegroom_name 新郎名字 (required_if:bridegroom_name=null)
     * @param string $bridegroom_phone 新郎手機 (required_if:bridegroom_phone=null)
     * @param string $note 備註
     * @param string $cup 罩杯
     * @param string $bust 胸圍
     * @param string $waist 腰圍
     * @param string $hips 臀圍
     * @return json
     */
    public function save(
        DressSaveRequest $request,
        DressTransformer $dressTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 驗證自訂合約編號是否重複
        if (!request('contract_id') && request('is_manual') && request('contract_no')) {
            $check_contract = $store->dressContracts()->where('contract_no', request('contract_no'))->exists();
            if ($check_contract) {
                $this->setException('合約編號已存在！', 4022);
            }
        }

        // 更新或新增
        $contract = $store->dressContracts()->updateOrCreate([
            'id' => request('contract_id'),
        ], [
            'is_manual' => request('is_manual'),
            'bride_name' => request('bride_name'),
            'bride_phone' => request('bride_phone'),
            'bridegroom_name' => request('bridegroom_name'),
            'bridegroom_phone' => request('bridegroom_phone'),
            'note' => request('note'),
            'cup' => request('cup'),
            'bust' => request('bust'),
            'waist' => request('waist'),
            'hips' => request('hips'),
            'abdominal' => request('abdominal'),
        ]);

        if (!request('is_manual') && !$contract->contract_no) {
            // 合約編號 contract_no => 年份(兩位數)+合約ID(補0足7位數)
            $contract->contract_no = $contract->created_at->format('y') . str_pad($contract->id, 7, '0', STR_PAD_LEFT);
            $contract->save();
        } elseif (request('is_manual') && request('contract_no') && !$contract->contract_no) {
            $contract->contract_no = request('contract_no');
            $contract->save();
        }

        // Transformer
        $result = $dressTransformer->show($contract);

        return $formatter->json($result);
    }

    /**
     * 禮服合約-訂單儲存
     *
     * @url api/admin/{store_id}/store/dress/order
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $contract_id 合約ID
     * @param int $order_id 訂單-ID
     * @param string $wedding_date 訂單-婚期
     * @param string $dress_use 訂單-禮服用途
     * @param string $prepare_date 訂單-準備日
     * @param string $return_date 訂單-歸還日
     * @param string $tidy_date 訂單-整理日
     * @param array.int $albums 訂單-[禮服ID]
     * @return json
     */
    public function saveOrder(
        DressOrderSaveRequest $request,
        DressTransformer $dressTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 確認合約存在
        $contract = $store->dressContracts()->where('id', request('contract_id'))->first();
        if(!$contract){
            return $this->setException('找不到此合約！');
        }

        // 更新合約訂單
        $orderItem = $contract->orders()->updateOrCreate([
            'id' => request('order_id'),
        ],[
            'wedding_date'   => request('wedding_date'),
            'dress_use'      => request('dress_use'),
            'prepare_date'   => request('prepare_date'),
            'return_date'    => request('return_date'),
            'tidy_date'      => request('tidy_date'),
            'pickup_date'    => request('pickup_date'),
            'store_id'       => $store->id,
        ]);
        // 更新禮服和訂單的關聯
        // 應該要驗證是否禮服有撞檔嗎？
        // 目前沒做，是由前端呼「撞檔 API」去做驗證
        $orderItem->dresses()->sync(request('albums', []));

        // Transformer
        $result = $dressTransformer->order($orderItem);

        return $formatter->json($result);
    }

    /**
     * 禮服合約管理-新增頁
     *
     * @url api/admin/{store_id}/store/dress/contract/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function create(
        DressTransformer $dressTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $dressTransformer->create();

        return $formatter->json($result);
    }

    /**
     * 禮服合約管理-詳細頁
     *
     * @url api/admin/{store_id}/store/dress/contract/{contract_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int contract_id 合約ID *
     * @return json
     */
    public function show(
        DressTransformer $dressTransformer,
        ApiFormatter $formatter,
        Store $store,
        DressContract $dressContract
    ) {

        // Transformer
        $result = $dressTransformer->show($dressContract);

        return $formatter->json($result);
    }   

    /**
     * 禮服合約管理-匯出CSV
     *
     * @url api/admin/{store_id}/store/dress/order/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function exportCSV(
        DressTransformer $dressTransformer,
        DressListService $dressListService,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $dressListService->orderList($store, $request);

        // Transformer
        $result = $dressTransformer->exportCSV($data);
        $filename = '[即將出件禮服] '.date('Ymd', strtotime($request['start_date'])). '-'. date('Ymd', strtotime($request['end_date'])).'.csv';

        return $formatter->exportCSV($filename, $result);
    }
}
