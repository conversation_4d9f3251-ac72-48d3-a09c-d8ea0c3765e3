<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\WeddingcakeCookie;
use App\Services\Image\CreateImageService;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\WeddingcakeCookieTransformer;
use App\Formatters\ApiFormatter;

class WeddingcakeCookieController extends Controller
{
    use SortListTrait;

    /**
     * 喜餅管理-列表
     *
     * @url api/admin/{store_id}/store/weddingcake-cookie
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function list(
        ApiFormatter $formatter,
        Store $store
    ) {
        // 商家的所有喜餅系列
        $cookies = $store->sortWeddingcakeCookies()
                        ->withCount(['items']) // 取得數據
                        ->with([
                            'items:id,weddingcake_cookie_id,name',
                            'items.image:target_id,type,file_name'
                            ]) // 預載入
                        ->get()
                        ->map(function($cookie) {
                            return [
                                'id'    => $cookie->id,
                                'name'  => $cookie->name,
                                'items' => $cookie->items->map(function($item) {
                                            return [
                                                'name'      => $item->name,
                                                'file_name' => $item->image->file_name ?? '',
                                            ];
                                        }),
                            ];
                        });

        return $formatter->json($cookies);
    }

    /**
     * 喜餅管理-儲存
     *
     * @url api/admin/{store_id}/store/weddingcake-cookie
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $cookie_id 喜餅系列ID
     * @param string $name 喜餅分類名稱 *
     * @param json.array.object $items 喜餅品項 *
         * @param int $item->id 喜餅品項-ID
         * @param string $item->file_name 喜餅品項照片
         * @param string $item->name 喜餅品項-名稱
         * @param string $tag->description 喜餅品項-說明
         * @param string $item->vegan_type 喜餅品項-葷素類型 meat:葷 lacto_vegetarian:奶素 ovo_lacto_vegetarian:蛋奶素 wuxinsu:五辛素 vegan:全素
         * @param array.string $item->other 喜餅品項-其他 alcohol:含酒精 nut:含堅果 gluten-free:無麩質

     * @param string $description 喜餅說明
     * @param string $image 喜餅圖片
     * @return json
     */
    public function save(
        CreateImageService $createImageService,
        WeddingcakeCookieTransformer $weddingcakeCookieTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 更新或新增
        $cookie = $store->weddingcakeCookies()->updateOrCreate([
            'id' => request('cookie_id'),
        ], [
            'name' => request('name'),
        ]);

        // 更新喜餅品項
        $cookie->items()->delete();
        $items = json_decode(request('items'));
        foreach ($items as $item) {
            $cookieItem = $cookie->items()->create([
                'name'        => $item->name,
                'description' => $item->description,
                'vegan_type'  => $item->vegan_type,
                'other'       => $item->other ?: NULL,
            ]);

            // 喜餅品項照片
            $createImageService->add([
                'file_name' => $item->file_name,
                'type'      => 'weddingcake_cookie_item',
                'target_id' => $cookieItem->id,
                'only'      => true,
            ]);
        }

        // 更新排序
        $this->sortListBySequence('WeddingcakeCookie', $store->sortWeddingcakeCookies->pluck('id'));

        // Transformer
        $result = $weddingcakeCookieTransformer->show($cookie);

        return $formatter->json($result);
    }

    /**
     * 喜餅管理-排序
     *
     * @url api/admin/{store_id}/store/weddingcake-cookie/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('WeddingcakeCookie', request('sequence'));

        return $formatter->json();
    }

    /**
     * 喜餅管理-新增頁
     *
     * @url api/admin/{store_id}/store/weddingcake-cookie/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function create(
        WeddingcakeCookieTransformer $weddingcakeCookieTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $weddingcakeCookieTransformer->create();

        return $formatter->json($result);
    }

    /**
     * 喜餅管理-詳細頁
     *
     * @url api/admin/{store_id}/store/weddingcake-cookie/{cookie_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int cookie_id 喜餅系列ID *
     * @return json
     */
    public function show(
        WeddingcakeCookieTransformer $weddingcakeCookieTransformer,
        ApiFormatter $formatter,
        Store $store,
        WeddingcakeCookie $weddingcakeCookie
    ) {
        // Transformer
        $result = $weddingcakeCookieTransformer->show($weddingcakeCookie);

        return $formatter->json($result);
    }

    /**
     * 喜餅管理-刪除
     *
     * @url api/admin/{store_id}/store/weddingcake-cookie/{cookie_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int cookie_id 喜餅系列ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        WeddingcakeCookie $weddingcakeCookie
    ) {
        $weddingcakeCookie->delete();

        return $formatter->json();
    }
}
