<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\VenueRoomSaveRequest;
use App\Http\Requests\Admin\Store\VenueRoomChangeStatusRequest;
use App\Models\Store;
use App\Models\VenueRoom;
use App\Services\Admin\Store\VenueRoomListService;
use App\Services\Admin\Store\VenueRoomSaveService;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\VenueRoomTransformer;
use App\Formatters\ApiFormatter;

class VenueRoomController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * 婚宴場地廳房-列表
     *
     * @url api/admin/{store_id}/store/venue-room
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $status 狀態
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        VenueRoomListService $venueRoomListService,
        VenueRoomTransformer $venueRoomTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $venueRoomListService->run($store, $request);

        // Transformer
        $result = $venueRoomTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地廳房-排序用的列表
     *
     * @url api/admin/{store_id}/store/venue-room/sort-list
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function sortList(
        VenueRoomTransformer $venueRoomTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $venueRoomTransformer->sortList($store);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地廳房-新增頁
     *
     * @url api/admin/{store_id}/store/venue-room/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function create(
        VenueRoomTransformer $venueRoomTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $venueRoomTransformer->create($store);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地廳房-詳細資訊
     *
     * @url api/admin/{store_id}/store/venue-room/{room_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int room_id 廳房ID *
     * @return json
     */
    public function show(
        VenueRoomTransformer $venueRoomTransformer,
        ApiFormatter $formatter,
        Store $store,
        VenueRoom $venueRoom
    ) {
        // Transformer
        $result = $venueRoomTransformer->show($venueRoom);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地廳房-儲存
     *
     * @url api/admin/{store_id}/store/venue-room
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param int $room_id 廳房ID
     * @param string $status 狀態 hide:隱藏 show:顯示 *
     * @param string $name 廳房名稱 *
     * @param array.string $narrates[] 特色標語
     * @param bool $is_underground 是否為地下層 (required_if: status=show)
     * @param int $floor 所在樓層 (required_if: status=show)
     * @param int $min_number 最小建議人數 (required_if: status=show)
     * @param int $max_number 最大建議人數 (required_if: status=show)
     * @param bool $has_banquet 供宴客使用 (required_if: status=show)
     * @param bool $has_witness 供證婚使用 (required_if: status=show)
     * @param bool $has_ceremony 供文定/迎娶使用 (required_if: status=show)
     * @param string $tables 桌型 round:中式圓桌 long:西式長桌 (required_if: has_banquet)
     * @param int $guest_table_number 客桌人數 (required_if: tables=round)
     * @param int $main_table_number 主桌人數 (required_if: tables=round)
     * @param bool $has_bridal_room 新娘休息室 (required_if: has_banquet)
     * @param array.int $combine_rooms[] 可與哪個廳合併
     * @param int $combine_min_number 合併後最小人數 (required_if: combine_rooms)
     * @param int $combine_max_number 合併後最大人數 (required_if: combine_rooms)
     * @param bool $has_wifi WiFi (required_if: status=show)
     * @param bool $has_projection 投影幕 (required_if: status=show)
     * @param bool $has_led LED螢幕 (required_if: status=show)
     * @param bool $has_sound 音響 (required_if: status=show)
     * @param bool $has_light 特殊燈光 (required_if: status=show)
     * @param bool $has_stage 舞台 (required_if: status=show)
     * @param bool $has_pillar 樑柱 (required_if: status=show)
     * @param int $backplane 送客背板 0:無 1:有 2:限方案 (required_if: status=show)
     * @param string $description 廳房說明 (required_if: status=show)
     * @param array.string $images[] 廳房照片 (required_if: status=show)
     * @param string $cover 廳房封面照 (required_if: status=show)
     * @return json
     */
    public function save(
        VenueRoomSaveRequest $request,
        VenueRoomSaveService $venueRoomSaveService,
        VenueRoomTransformer $venueRoomTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 驗證至少有一個顯示中的廳房
        if (
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showVenueRooms()->where('id', '!=', $request['room_id'])->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的廳房唷！', 4011);
        }

        if ($store->venueRooms()->where('id', '!=', $request['room_id'])->where('name', $request['name'])->exists()) {
            $this->setException('廳房名稱不可重複！', 4012);
        }

        // Service
        $venueRoom = $venueRoomSaveService->run($store, $request);

        // 更新排序
        $this->sortListBySequence('VenueRoom', $store->sortVenueRooms->pluck('id'));

        // Transformer
        $result = $venueRoomTransformer->show($venueRoom);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地廳房-快速新增
     *
     * @url api/admin/{store_id}/store/venue-room/quickly-add
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $name 廳房名稱 *
     * @return json
     */
    public function quicklyAdd(
        ApiFormatter $formatter,
        Store $store
    ) {
        // 新增
        $venueRoom = $store->venueRooms()->create([
            'name'      => request('name'),
            'edited_at' => now(),
        ]);

        // 更新商家的最後編輯時間
        $store->edited_at = now();
        $store->save();

        return $formatter->json($venueRoom->id);
    }

    /**
     * 婚宴場地廳房-排序
     *
     * @url api/admin/{store_id}/store/venue-room/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('VenueRoom', request('sequence'));

        return $formatter->json();
    }

    /**
     * 婚宴場地廳房-變更狀態
     *
     * @url api/admin/{store_id}/store/venue-room/{room_id}/change-status
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int room_id 廳房ID *
     * @param string status 狀態 hide:隱藏 show:顯示 *
     * @return json
     */
    public function changeStatus(
        VenueRoomChangeStatusRequest $request,
        ApiFormatter $formatter,
        Store $store,
        VenueRoom $venueRoom
    ) {
        // 驗證至少有一個顯示中的廳房
        if (
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showVenueRooms()->where('id', '!=', $venueRoom->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的廳房唷！', 4011);
        }

        $venueRoom->status    = $request['status'];
        $venueRoom->edited_at = now();
        $venueRoom->save();

        // 更新商家的最後編輯時間
        $store->edited_at = now();
        $store->save();

        return $formatter->json();
    }

    /**
     * 婚宴場地廳房-刪除
     *
     * @url api/admin/{store_id}/store/venue-room/{room_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int room_id 廳房ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        VenueRoom $venueRoom
    ) {
        // 驗證至少有一個顯示中的廳房
        if (
            $store->present()->is_published &&
            $store->showVenueRooms()->where('id', '!=', $venueRoom->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的廳房唷！', 4011);
        }

        $venueRoom->delete();

        return $formatter->json();
    }
}
