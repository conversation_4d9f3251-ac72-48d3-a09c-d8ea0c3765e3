<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\VideoSaveRequest;
use App\Http\Requests\Admin\Store\VideoChangeStatusRequest;
use App\Models\Store;
use App\Models\StoreVideo;
use App\Services\Admin\Store\VideoListService;
use App\Services\Admin\Store\VideoSaveService;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\VideoTransformer;
use App\Formatters\ApiFormatter;

class VideoController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * 影片管理-列表
     *
     * @url api/admin/{store_id}/store/store-video
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $status 狀態
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        VideoListService $videoListService,
        VideoTransformer $videoTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $videoListService->run($store, $request);

        // Transformer
        $result = $videoTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 影片管理-排序用的列表
     *
     * @url api/admin/{store_id}/store/store-video/sort-list
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function sortList(
        VideoTransformer $videoTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $videoTransformer->sortList($store);

        return $formatter->json($result);
    }

    /**
     * 影片管理-新增頁
     *
     * @url api/admin/{store_id}/store/store-video/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function create(
        VideoTransformer $videoTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $videoTransformer->create($store);

        return $formatter->json($result);
    }

    /**
     * 影片管理-詳細資訊
     *
     * @url api/admin/{store_id}/store/store-video/{video_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int video_id 影片ID *
     * @return json
     */
    public function show(
        VideoTransformer $videoTransformer,
        ApiFormatter $formatter,
        Store $store,
        StoreVideo $storeVideo
    ) {
        // Transformer
        $result = $videoTransformer->show($storeVideo);

        return $formatter->json($result);
    }

    /**
     * 影片管理-儲存
     *
     * @url api/admin/{store_id}/store/store-video
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $video_id 影片ID
     * @param string $status 狀態 hide:隱藏 show:顯示 *
     * @param string $name 影片名稱 *
     * @param string $url 影片連結 *
     * @param json $oembed_data 影片嵌入資訊 *
     * @param string $description 影片說明
     * @param int $location_type 地點類型 (婚攝婚錄&婚佈)
     * @param int $brand_id 宴客地點 (婚攝婚錄)、佈置地點 (婚佈)
     * @param array.int $members[] 參與成員 (拍婚紗&婚攝婚錄)
     * @return json
     */
    public function save(
        VideoSaveRequest $request,
        VideoSaveService $videoSaveService,
        VideoTransformer $videoTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 驗證至少有一個顯示中的影片
        if (
            in_array($store->extra_type, [302, 303]) && // 婚錄
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showVideos()->where('id', '!=', $request['video_id'])->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的影片唷！', 4011);
        }

        if ($store->videos()->where('id', '!=', $request['video_id'])->where('name', $request['name'])->exists()) {
            $this->setException('影片名稱不可重複！', 4012);
        }

        // Service
        $storeVideo = $videoSaveService->run($store, $request);

        // 更新排序
        $this->sortListBySequence('StoreVideo', $store->sortVideos->pluck('id'));

        // Transformer
        $result = $videoTransformer->show($storeVideo);

        return $formatter->json($result);
    }

    /**
     * 影片管理-排序
     *
     * @url api/admin/{store_id}/store/store-video/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('StoreVideo', request('sequence'));

        return $formatter->json();
    }

    /**
     * 影片管理-變更狀態
     *
     * @url api/admin/{store_id}/store/store-video/{video_id}/change-status
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int video_id 影片ID *
     * @param string status 狀態 hide:隱藏 show:顯示 *
     * @return json
     */
    public function changeStatus(
        VideoChangeStatusRequest $request,
        ApiFormatter $formatter,
        Store $store,
        StoreVideo $storeVideo
    ) {
        // 驗證至少有一個顯示中的影片
        if (
            in_array($store->extra_type, [302, 303]) && // 婚錄
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showVideos()->where('id', '!=', $storeVideo->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的影片唷！', 4011);
        }

        $storeVideo->status    = $request['status'];
        $storeVideo->edited_at = now();
        $storeVideo->save();

        return $formatter->json();
    }

    /**
     * 影片管理-刪除
     *
     * @url api/admin/{store_id}/store/store-video/{video_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int video_id 影片ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        StoreVideo $storeVideo
    ) {
        // 驗證至少有一個顯示中的影片
        if (
            in_array($store->extra_type, [302, 303]) && // 婚錄
            request('status') == 'hide' &&
            $store->present()->is_published &&
            $store->showVideos()->where('id', '!=', $storeVideo->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的影片唷！', 4011);
        }

        $storeVideo->delete();

        return $formatter->json();
    }
}
