<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\VenueDishes;
use App\Services\Image\CreateImageService;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\VenueDishesTransformer;
use App\Formatters\ApiFormatter;

class VenueDishesController extends Controller
{
    use SortListTrait;

    /**
     * 婚宴場地喜宴菜-列表
     *
     * @url api/admin/{store_id}/store/venue-dishes
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @return json
     */
    public function list(
        VenueDishesTransformer $venueDishesTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $venueDishesTransformer->list($store);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地喜宴菜-儲存
     *
     * @url api/admin/{store_id}/store/venue-dishes
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $dishes_id 喜宴菜ID
     * @param string $name 喜宴菜名稱 *
     * @param string $description 喜宴菜說明
     * @param string $image 喜宴菜圖片
     * @return json
     */
    public function save(
        CreateImageService $createImageService,
        VenueDishesTransformer $venueDishesTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 更新或新增
        $venueDishes = $store->venueDishes()->updateOrCreate([
            'id' => request('dishes_id'),
        ], [
            'name'        => request('name'),
            'description' => request('description'),
        ]);

        // 喜宴菜圖片
        $createImageService->add([
            'file_name' => request('image'),
            'type'      => 'venue_dishes',
            'target_id' => $venueDishes->id,
            'only'      => true,
        ]);

        // 更新排序
        $this->sortListBySequence('VenueDishes', $store->sortVenueDishes->pluck('id'));

        // Transformer
        $result = $venueDishesTransformer->getVenueDishesInfo($venueDishes);

        return $formatter->json($result);
    }

    /**
     * 婚宴場地喜宴菜-排序
     *
     * @url api/admin/{store_id}/store/venue-dishes/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('VenueDishes', request('sequence'));

        return $formatter->json();
    }

    /**
     * 婚宴場地喜宴菜-刪除
     *
     * @url api/admin/{store_id}/store/venue-dishes/{dishes_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int dishes_id 喜宴菜ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        VenueDishes $venueDishes
    ) {
        $venueDishes->delete();

        return $formatter->json();
    }
}
