<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\AlbumSaveRequest;
use App\Http\Requests\Admin\Store\AlbumChangeStatusRequest;
use App\Models\Store;
use App\Models\StoreAlbum;
use App\Models\StoreService;
use App\Models\StudioAlbumImageLocation;
use App\Services\Admin\Store\AlbumListService;
use App\Services\Admin\Store\AlbumSaveService;
use App\Services\User\Wedding\GetBrandService;
use App\Services\User\Wedding\CreateBrandService;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\AlbumTransformer;
use App\Transformers\User\WeddingTransformer;
use App\Formatters\ApiFormatter;

class AlbumController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * 作品管理-列表
     *
     * @url api/admin/{store_id}/store/store-album
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param int $type 作品類型 (拍婚紗)、佈置類型 (婚佈)
     * @param int $tag 禮服類型標籤 (禮服)、禮盒類型標籤 (喜餅)
     * @param string $status 狀態
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        AlbumListService $albumListService,
        AlbumTransformer $albumTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $albumListService->run($store, $request);

        // Transformer
        $result = $albumTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 作品管理-排序用的列表
     *
     * @url api/admin/{store_id}/store/store-album/sort-list
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function sortList(
        AlbumTransformer $albumTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $albumTransformer->sortList($store);

        return $formatter->json($result);
    }

    /**
     * 作品管理-新增頁
     *
     * @url api/admin/{store_id}/store/store-album/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function create(
        AlbumTransformer $albumTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $albumTransformer->create($store);

        return $formatter->json($result);
    }

    /**
     * 作品管理-取得品牌列表
     *
     * @url api/admin/{store_id}/store/store-album/brands
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param string $keyword 搜尋關鍵字
     * @param string $name 商家名稱
     * @param string $website 官網連結
     * @param string $fb_page FB粉絲團連結
     * @param string $instagram Instagram
     * @param string $email Email
     * @param string $phone 行動電話
     * @param string $tel 市話
     * @return json
     */
    public function getBrands(
        GetBrandService $getBrandService,
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $request['store_type'] = 5; // 把婚宴場地排前面
        $data = $getBrandService->run($request);

        // Transformer
        $result = $weddingTransformer->getBrandList($data, Null);

        return $formatter->json($result);
    }

    /**
     * 作品管理-新增品牌
     *
     * @url api/admin/{store_id}/store/store-album/create-brand
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param int $brand_id 品牌編號
     * @param string $name 商家名稱
     * @param string $website 官網連結
     * @param string $fb_page FB粉絲團連結
     * @param string $instagram Instagram
     * @param string $email Email
     * @param string $phone 行動電話
     * @param string $tel 市話
     * @return json
     */
    public function createBrand(
        CreateBrandService $createBrandService,
        WeddingTransformer $weddingTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $createBrandService->run($request);

        // Transformer
        $result = $weddingTransformer->getBrandInfo($data, Null);

        return $formatter->json($result);
    }

    /**
     * 作品管理-取得作品照的地點列表(拍婚紗)
     *
     * @url api/admin/{store_id}/store/store-album/image-locations
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param string $keyword 搜尋關鍵字 *
     * @return json
     */
    public function getImageLocations(
        StudioAlbumImageLocation $studioAlbumImageLocation,
        AlbumTransformer $albumTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        if (!request('keyword')) {
            $this->setException('請搜尋關鍵字！');
        }

        $data = $studioAlbumImageLocation->select('name', 'use_count')
                                        ->where('name', 'like', '%'.request('keyword').'%')
                                        ->orderBy('use_count', 'DESC')
                                        ->limit(20)
                                        ->get();

        return $formatter->json($data);
    }

    /**
     * 作品管理-詳細資訊
     *
     * @url api/admin/{store_id}/store/store-album/{album_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $album_id 作品ID *
     * @param string $data_type 資料類型 album:相本 image:照片
     * @return json
     */
    public function show(
        AlbumTransformer $albumTransformer,
        ApiFormatter $formatter,
        Store $store,
        StoreAlbum $storeAlbum
    ) {
        // Transformer
        $method = (request('data_type') == 'image') ? 'showImageList' : 'show';
        $result = $albumTransformer->$method($storeAlbum);

        return $formatter->json($result);
    }

    /**
     * 作品管理-儲存
     *
     * @url api/admin/{store_id}/store/store-album
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param int $album_id 作品ID
     * @param string $status 狀態 hide:隱藏 show:顯示 *
     * @param string $name 作品名稱 *
     * @param string $description 作品說明
     * @param json.array.object $images 作品照 *
     * @param int $cover_id 作品封面照編號 *
     * @param int $type 作品類型 (拍婚紗)、佈置類型 (婚佈)
     * @param int $location_type 地點類型 (婚攝婚錄&婚佈)
     * @param int $brand_id 宴客地點 (婚攝婚錄)、佈置地點 (婚佈)
     * @param int $price 租金 (禮服)
     * @param int $min_price 最低金額 (婚佈&喜餅)
     * @param int $max_price 最高金額 (婚佈&喜餅)
     * @param int $is_private 價格不公開 (禮服&婚佈)
     * @param bool $can_customized 是否可以客製化 (喜餅)
     * @param int $min_category 最少種類數 (喜餅)
     * @param int $max_category 最多種類數 (喜餅)
     * @param int $min_quantity 最少數量 (喜餅)
     * @param int $max_quantity 最多數量 (喜餅)
     * @param int $box_length 禮盒長度 (喜餅)
     * @param int $box_width 禮盒寬度 (喜餅)
     * @param int $box_high 禮盒高度 (喜餅)
     * @param bool $is_meat 葷 (喜餅)
     * @param bool $is_lacto_vegetarian 奶素 (喜餅)
     * @param bool $is_ovo_lacto_vegetarian 蛋奶素 (喜餅)
     * @param bool $is_vegetarian 全素 (喜餅)
     * @param string $preservation_method 保存方式 (喜餅)
     * @param array.string $narrates[] 特色標語 (婚佈)
     * @param array.int $tags[] 禮服標籤 (禮服)、作品集標籤 (婚佈)、禮盒標籤 (喜餅)
     * @param array.int $members[] 參與成員 (拍婚紗&婚攝婚錄)
     * @param array.int $services[] 適用方案 (喜餅)
     * @param string $order_dress_type 禮服訂單類型 (禮服)
     * @param bool $is_watermark 是否顯示浮水印 (禮服)
     * @return json
     */
    public function save(
        AlbumSaveRequest $request,
        AlbumSaveService $albumSaveService,
        AlbumTransformer $albumTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 驗證至少有一個顯示中的相本
        if (
            $store->type != 5 && // 排除婚宴場地
            $store->extra_type != 302 && // 排除婚錄
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showAlbums()->where('id', '!=', $request['album_id'])->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的相本唷！', 4011);
        }

        if ($store->albums()->where('id', '!=', $request['album_id'])->where('name', $request['name'])->exists()) {
            $this->setException('相本名稱不可重複！', 4012);
        }

        // 喜餅商家需驗證，禮盒的所屬方案是否唯一關聯此禮盒 (排除新增)
        $storeAlbum = $store->albums->find($request['album_id']);
        if ($store->type == 10 && $request['status'] == 'hide' && $storeAlbum) {
            $checkServices = [];
            foreach ($storeAlbum->weddingcakeServices as $storeService) {
                if ($storeService->weddingcakeAlbums()->where('store_albums.id', '!=', $storeAlbum->id)->doesntExist()) {
                    $checkServices[] = $storeService->only(['id', 'name']);
                }
            }
            if ($checkServices) {
                $this->setException('此禮盒為下列方案的唯一關聯禮盒，請先至下列方案更換適用禮盒，否則無法刪除/隱藏。', 4019, $checkServices);
            }
        }

        // Service
        $storeAlbum = $albumSaveService->run($store, $request);

        // 更新排序
        $this->sortListBySequence('StoreAlbum', $store->sortAlbums->pluck('id'));

        // Transformer
        $result = $albumTransformer->show($storeAlbum);

        return $formatter->json($result);
    }

    /**
     * 作品管理-排序
     *
     * @url api/admin/{store_id}/store/store-album/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('StoreAlbum', request('sequence'));

        return $formatter->json();
    }

    /**
     * 作品管理-變更狀態
     *
     * @url api/admin/{store_id}/store/store-album/{album_id}/change-status
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $album_id 作品ID *
     * @param string $status 狀態 hide:隱藏 show:顯示 *
     * @return json
     */
    public function changeStatus(
        AlbumChangeStatusRequest $request,
        ApiFormatter $formatter,
        Store $store,
        StoreAlbum $storeAlbum
    ) {
        // 驗證至少有一個顯示中的相本
        if (
            $store->type != 5 && // 排除婚宴場地
            $store->extra_type != 302 && // 排除婚錄
            $request['status'] == 'hide' &&
            $store->present()->is_published &&
            $store->showAlbums()->where('id', '!=', $storeAlbum->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的相本唷！', 4011);
        }

        // 喜餅商家需驗證，禮盒的所屬方案是否唯一關聯此禮盒
        if ($store->type == 10 && $request['status'] == 'hide') {
            $checkServices = [];
            foreach ($storeAlbum->weddingcakeServices as $storeService) {
                if ($storeService->weddingcakeAlbums()->where('store_albums.id', '!=', $storeAlbum->id)->doesntExist()) {
                    $checkServices[] = $storeService->only(['id', 'name']);
                }
            }
            if ($checkServices) {
                $this->setException('此禮盒為下列方案的唯一關聯禮盒，請先至下列方案更換適用禮盒，否則無法刪除/隱藏。', 4019, $checkServices);
            }
        }

        $storeAlbum->status    = request('status');
        $storeAlbum->edited_at = now();
        $storeAlbum->save();

        // 更新商家的服務價格區間、最後編輯時間
        $store->updatePriceRange();
        $store->edited_at = now();
        $store->save();

        return $formatter->json();
    }

    /**
     * 作品管理-刪除
     *
     * @url api/admin/{store_id}/store/store-album/{album_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $album_id 作品ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        StoreAlbum $storeAlbum
    ) {
        // 驗證至少有一個顯示中的相本
        if (
            $store->type != 5 && // 排除婚宴場地
            $store->extra_type != 302 && // 排除婚錄
            $store->present()->is_published &&
            $store->showAlbums()->where('id', '!=', $storeAlbum->id)->doesntExist()
        ) {
            $this->setException('無法刪除/隱藏，必須至少有一個顯示中的相本唷！', 4011);
        }

        // 喜餅商家需驗證，禮盒的所屬方案是否唯一關聯此禮盒
        if ($store->type == 10 && request('status') == 'hide') {
            $checkServices = [];
            foreach ($storeAlbum->weddingcakeServices as $storeService) {
                if ($storeService->weddingcakeAlbums()->where('store_albums.id', '!=', $storeAlbum->id)->doesntExist()) {
                    $checkServices[] = $storeService->only(['id', 'name']);
                }
            }
            if ($checkServices) {
                $this->setException('此禮盒為下列方案的唯一關聯禮盒，請先至下列方案更換適用禮盒，否則無法刪除/隱藏。', 4019, $checkServices);
            }
        }

        $storeAlbum->delete();

        return $formatter->json();
    }

    /**
     * 作品管理-驗證喜餅方案是否有唯一關聯的禮盒
     *
     * @url api/admin/{store_id}/store/store-album/{album_id}/check-weddingcake-service/{service_id}
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $album_id 禮盒ID *
     * @path int $service_id 方案ID *
     * @return json
     */
    public function checkWeddingcakeService(
        ApiFormatter $formatter,
        Store $store,
        StoreAlbum $storeAlbum,
        StoreService $storeService
    ) {
        // 喜餅商家需驗證，禮盒的所屬方案是否唯一關聯此禮盒
        if (
            $store->type == 10 && // 喜餅商家
            $storeService->status == 'show' && // 顯示中的方案
            $storeService->weddingcakeAlbums()->where('store_albums.id', '!=', $storeAlbum->id)->doesntExist()
        ) {
            $this->setException('無法刪除，請先至此方案「更換適用禮盒」或「狀態改為隱藏」', 4019);
        }

        return $formatter->json();
    }
}
