<?php

namespace App\Http\Controllers\Admin\Store;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Store\MemberSaveRequest;
use App\Models\Store;
use App\Models\StoreMember;
use App\Services\Admin\Store\MemberListService;
use App\Services\Admin\Store\MemberSaveService;
use App\Traits\ApiErrorTrait;
use App\Traits\Model\SortListTrait;
use App\Transformers\Admin\Store\MemberTransformer;
use App\Formatters\ApiFormatter;

class MemberController extends Controller
{
    use ApiErrorTrait;
    use SortListTrait;

    /**
     * 成員管理-列表
     *
     * @url api/admin/{store_id}/store/store-member
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        MemberListService $memberListService,
        MemberTransformer $memberTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $memberListService->run($store, $request);

        // Transformer
        $result = $memberTransformer->list($data);

        return $formatter->json($result);
    }

    /**
     * 成員管理-排序用的列表
     *
     * @url api/admin/{store_id}/store/store-member/sort-list
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function sortList(
        MemberTransformer $memberTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $memberTransformer->sortList($store);

        return $formatter->json($result);
    }

    /**
     * 成員管理-詳細資訊
     *
     * @url api/admin/{store_id}/store/store-member/{member_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int member_id 成員ID *
     * @return json
     */
    public function show(
        MemberTransformer $memberTransformer,
        ApiFormatter $formatter,
        Store $store,
        StoreMember $storeMember
    ) {
        // Transformer
        $result = $memberTransformer->show($storeMember);

        return $formatter->json($result);
    }

    /**
     * 成員管理-儲存
     *
     * @url api/admin/{store_id}/store/store-member
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param string $member_id 成員ID
     * @param string $cover 成員照 *
     * @param string $name 姓名 *
     * @param string $title 職稱 *
     * @param string $description 成員說明 *
     * @return json
     */
    public function save(
        MemberSaveRequest $request,
        MemberSaveService $memberSaveService,
        MemberTransformer $memberTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        if ($store->members()->where('id', '!=', $request['member_id'])->where('name', $request['name'])->exists()) {
            $this->setException('成員名稱不可重複！', 4012);
        }

        // Service
        $storeMember = $memberSaveService->run($store, $request);

        // 更新排序
        $this->sortListBySequence('StoreMember', $store->members->pluck('id'));

        // Transformer
        $result = $memberTransformer->show($storeMember);

        return $formatter->json($result);
    }

    /**
     * 成員管理-排序
     *
     * @url api/admin/{store_id}/store/store-member/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('StoreMember', request('sequence'));

        return $formatter->json();
    }

    /**
     * 成員管理-刪除
     *
     * @url api/admin/{store_id}/store/store-member/{member_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int store_id 商家ID *
     * @path int member_id 成員ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        StoreMember $storeMember
    ) {
        $storeMember->delete();

        return $formatter->json();
    }
}
