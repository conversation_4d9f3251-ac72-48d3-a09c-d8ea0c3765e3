<?php

namespace App\Http\Controllers\Admin\Invoice;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\Invoice;
use App\Services\Payment\EzPay\InvoiceHandle;
use App\Traits\ApiErrorTrait;
use App\Transformers\Admin\Invoice\MainTransformer;
use App\Formatters\ApiFormatter;
use Illuminate\Support\Str;

class AllowanceController extends Controller
{
    use ApiErrorTrait;

    /**
     * 發票管理-開立折讓
     *
     * @url api/admin/{store_id}/invoice/{invoice_id}/allowance
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $invoice_id 發票記錄ID *
     * @param int $amount 折讓金額 *
     * @return json
     */
    public function save(
        InvoiceHandle $invoiceHandle,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        Store $store,
        Invoice $invoice
    ) {
        // 驗證發票已作廢
        if ($invoice->invoice_status == 'invalid') {
            $this->setException('此發票已作廢！（作廢原因：'.$invoice->invalid_reason.'）');
        }

        // 驗證折讓額度
        if ($invoice->allowances->sum('amount') + request('amount') > $invoice->total) {
            $this->setException('折讓的金額不能大於發票可折讓金額！');
        }

        // ezPay 電子發票開立折讓
        $invoiceResult = $invoiceHandle->handle('allowance_issue', [
            'invoice' => $invoice,
            'amount'  => request('amount'),
        ]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->setException('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
        }

        // 新增折讓紀錄
        $allowance = $invoice->allowances()->create([
            'allowance_no' => $invoiceResult->Result->AllowanceNo,
            'amount'       => request('amount'),
        ]);

        // Transformer
        $result = $mainTransformer->getAllowanceInfo($allowance);

        return $formatter->json($result);
    }

    /**
     * 發票管理-作廢折讓
     *
     * @url api/admin/{store_id}/invoice/{invoice_id}/allowance/{allowance_no}/invalid
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $invoice_id 發票記錄ID *
     * @param string $allowance_no 折讓號 *
     * @param string $invalid_reason 作廢原因 *
     * @return json
     */
    public function invalid(
        InvoiceHandle $invoiceHandle,
        ApiFormatter $formatter,
        Store $store,
        Invoice $invoice,
        $allowance_no
    ) {
        // 驗證發票已作廢
        if ($invoice->invoice_status == 'invalid') {
            $this->setException('此發票已作廢！（作廢原因：'.$invoice->invalid_reason.'）');
        }

        // 驗證折讓是否有效
        $allowance = $invoice->allowances->firstWhere('allowance_no', request('allowance_no'));
        if (!$allowance) {
            $this->setException('找不到此折讓單！');
        }

        // ezPay 電子發票作廢折讓
        $invoiceResult = $invoiceHandle->handle('allowance_invalid', [
            'allowance'     => $allowance,
            'invalidReason' => request('invalid_reason'),
        ]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->setException('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
        }

        // 刪除折讓紀錄
        $allowance->invalid_reason = request('invalid_reason');
        $allowance->save();
        $allowance->delete();

        return $formatter->json();
    }
}
