<?php

namespace App\Http\Controllers\Admin\Invoice;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\InvoiceProduct;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Traits\Model\SortListTrait;
use App\Formatters\ApiFormatter;

class ProductController extends Controller
{
    use SortListTrait;

    /**
     * 品項管理-列表
     *
     * @url api/admin/{store_id}/invoice/product
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param string $keyword 搜尋關鍵字
     * @return json
     */
    public function list(
        SearchService $keywordSearchService,
        ApiFormatter $formatter,
        Store $store
    ) {
        $products = $store->invoiceSetting->products()
                                            ->select('id', 'name', 'price');
        // 關鍵字 keyword
        if (request('keyword')) {
            $products = $keywordSearchService->search($products, request('keyword'));
        }

        $products = $products->get();

        return $formatter->json(compact('products'));
    }

    /**
     * 品項管理-儲存
     *
     * @url api/admin/{store_id}/invoice/product
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param int $product_id 品項ID
     * @param string $name 名稱 *
     * @param string $price 單價 *
     * @return json
     */
    public function save(
        ApiFormatter $formatter,
        Store $store
    ) {
        $products = $store->invoiceSetting->products()->updateOrCreate([
            'id' => request('product_id'),
        ], [
            'name'  => request('name'),
            'price' => request('price'),
        ]);

        return $formatter->json();
    }

    /**
     * 品項管理-排序
     *
     * @url api/admin/{store_id}/invoice/product/sort
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param array.int $sequence[] 編號排序 *
     * @return json
     */
    public function sort(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Trait
        $this->sortListBySequence('InvoiceProduct', request('sequence'));

        return $formatter->json();
    }

    /**
     * 品項管理-刪除
     *
     * @url api/admin/{store_id}/invoice/product/{product_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $product_id 品項ID *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Store $store,
        InvoiceProduct $invoiceProduct
    ) {
        $invoiceProduct->delete();

        return $formatter->json();
    }
}
