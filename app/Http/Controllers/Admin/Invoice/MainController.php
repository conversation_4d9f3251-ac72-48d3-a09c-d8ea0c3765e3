<?php

namespace App\Http\Controllers\Admin\Invoice;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Invoice\SaveRequest;
use App\Models\Store;
use App\Models\Invoice;
use App\Repositories\InvoiceRepository;
use App\Services\Admin\Invoice\SaveService;
use App\Services\Payment\EzPay\InvoiceHandle;
use App\Services\Mail\Invoice\ResendNotificationService;
use App\Traits\ApiErrorTrait;
use App\Transformers\Admin\Invoice\MainTransformer;
use App\Formatters\ApiFormatter;
use Illuminate\Support\Str;

class MainController extends Controller
{
    use ApiErrorTrait;

    /**
     * 發票管理-列表
     *
     * @url api/admin/{store_id}/invoice
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param datetime $created_at 發票日期
     * @param string $type 發票類型 paper:紙本列印 email:E-mail通知 carrier:存載具
     * @param string $invoice_status 發票狀態 success:已開立 invalid:已作廢
     * @param string $upload_status 上傳狀態 pending:未上傳 completed:已上傳成功 uploading:上傳中 failed:上傳失敗 timeout:上傳逾時
     * @param string $search_type 關鍵字搜尋項目 buyer_ubn:買受人統編 buyer_name:買受人名稱 order_no:訂單編號 invoice_number:發票號碼
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        InvoiceRepository $invoiceRepository,
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        $invoices = $invoiceRepository->getAdminListByRequest($store, $request);

        // Transformer
        $result = $mainTransformer->list($invoices);

        return $formatter->json($result);
    }

    /**
     * 發票管理-開立發票頁
     *
     * @url api/admin/{store_id}/invoice/create
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function create(
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Transformer
        $result = $mainTransformer->create($store);

        return $formatter->json($result);
    }

    /**
     * 發票管理-開立發票儲存
     *
     * @url api/admin/{store_id}/invoice/save
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param string $type 發票類型 paper:紙本列印 email:E-mail通知 carrier:存載具 *
     * @param string $order_no 訂單編號 *
     * @param string $buyer_name 買受人名稱 *
     * @param string $buyer_ubn 買受人統編
     * @param string $buyer_email 買受人E-mail (required_if: type=email)
     * @param string $carrier_type 載具類型 phone:手機條碼 citizen:自然人憑證條碼 donate:愛心捐贈碼 (required_if: type=carrier)
     * @param string $carrier_number 載具編號 (required_if: type=carrier)
     * @param json.array.object $items 品項 *
     * @param string $note 備註
     * @param int $sales 銷售額 *
     * @param int $tax 稅額 *
     * @param int $total 總計 *
     * @return json
     */
    public function save(
        SaveRequest $request,
        SaveService $saveService,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Service
        $data = $saveService->run($store, $request);

        return $formatter->json($data);
    }

    /**
     * 發票管理-詳細頁
     *
     * @url api/admin/{store_id}/invoice/{invoice_id}
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $invoice_id 發票記錄ID *
     * @return json
     */
    public function show(
        MainTransformer $mainTransformer,
        ApiFormatter $formatter,
        Store $store,
        Invoice $invoice
    ) {
        // Transformer
        $result = $mainTransformer->show($invoice);

        return $formatter->json($result);
    }

    /**
     * 發票管理-補印發票
     *
     * @url api/admin/{store_id}/invoice/{invoice_id}/reprint
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $invoice_id 發票記錄ID *
     * @return json
     */
    public function reprint(
        InvoiceHandle $invoiceHandle,
        ApiFormatter $formatter,
        Store $store,
        Invoice $invoice
    ) {
        // 驗證發票已作廢
        if ($invoice->invoice_status == 'invalid') {
            $this->setException('此發票已作廢！（作廢原因：'.$invoice->invalid_reason.'）');
        }

        // ezPay 補印電子發票
        $invoiceResult = $invoiceHandle->handle('invoice_search', ['invoice' => $invoice]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->setException('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
        }

        // 補印次數+1 & 順便更新上傳狀態
        $invoice->reprint_times++;
        $invoice->upload_status = $invoice->ezPayUploadList[$invoiceResult->Result->UploadStatus];
        $invoice->save();

        return $formatter->json($invoiceResult->Result);
    }

    /**
     * 發票管理-補寄發票開立通知信
     *
     * @url api/admin/{store_id}/invoice/{invoice_id}/email-notification
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $invoice_id 發票記錄ID *
     * @param string $email 通知E-mail *
     * @return json
     */
    public function emailNotification(
        ResendNotificationService $resendNotificationService,
        ApiFormatter $formatter,
        Store $store,
        Invoice $invoice
    ) {
        // 驗證發票已作廢
        if ($invoice->invoice_status == 'invalid') {
            $this->setException('此發票已作廢！（作廢原因：'.$invoice->invalid_reason.'）');
        }

        // 驗證為Emial電子發票類型
        if ($invoice->type != 'email') {
            $this->setException('發票類型為E-mail電子發票，才可補寄發票開立通知信！');
        }

        // 補寄發票開立通知信
        $resendNotificationService->sendMail($invoice, request('email'));

        // 新增電子發票通知紀錄
        $invoice->logEmails()->create(['email' => request('email')]);

        return $formatter->json();
    }

    /**
     * 發票管理-作廢發票
     *
     * @url api/admin/{store_id}/invoice/{invoice_id}/invalid
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path int $invoice_id 發票記錄ID *
     * @param string $invalid_reason 作廢原因 *
     * @return json
     */
    public function invalid(
        InvoiceHandle $invoiceHandle,
        ApiFormatter $formatter,
        Store $store,
        Invoice $invoice
    ) {
        // 驗證發票已作廢
        if ($invoice->invoice_status == 'invalid') {
            $this->setException('此發票已作廢！（作廢原因：'.$invoice->invalid_reason.'）');
        }

        // ezPay 作廢電子發票
        $invoiceResult = $invoiceHandle->handle('invoice_invalid', [
            'invoice'       => $invoice,
            'invalidReason' => request('invalid_reason'),
        ]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->setException('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
        }

        // 作廢發票
        $invoice->invoice_status = 'invalid';
        $invoice->invalid_reason = request('invalid_reason');
        $invoice->save();

        // 更新活動訂單的發票狀態
        if ($invoice->eventOrder) {
            $invoice->eventOrder->invoice_status = 'invalid';
            $invoice->eventOrder->save();
        }

        return $formatter->json();
    }

    /**
     * 發票管理-儲存列印設定
     *
     * @url api/admin/{store_id}/invoice/print-setting
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @param string $seller_brand 賣方品牌名稱 *
     * @param string $printer_ip 列表機IP *
     * @return json
     */
    public function printSetting(
        ApiFormatter $formatter,
        Store $store
    ) {
        // 取得商家
        $store = request('store');

        // 儲存賣方發票設定
        $store->invoiceSetting()->update([
            'seller_brand' => request('seller_brand'),
            'printer_ip'   => request('printer_ip'),
        ]);

        return $formatter->json();
    }
}
