<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\EmailLegalize;
use App\Services\Admin\IndexService;
use App\Services\Mail\Store\EditEmailService;
use App\Services\Mail\Auth\EmailAuthKeyService;
use App\Jobs\Firebase\Notification;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;
use App\Transformers\Admin\DashboardTransformer;
use App\Formatters\ApiFormatter;

class DashboardController extends Controller
{
    use ApiErrorTrait;
    use RandStringTrait;

    /**
     * 商家中心
     *
     * @url api/admin/{store_id}/dashboard
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function index(
        IndexService $indexService,
        DashboardTransformer $dashboardTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $indexService->run($request);

        // Transformer
        $result = $dashboardTransformer->index($data);

        return $formatter->json($result);
    }

    /**
     * 更多數據
     *
     * @url api/admin/{store_id}/dashboard/detail
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function detail(
        IndexService $indexService,
        DashboardTransformer $dashboardTransformer,
        ApiFormatter $formatter,
        Store $store
    ) {
        // Request
        $request = request();

        // Service
        $data = $indexService->detail($request);

        // Transformer
        $result = $dashboardTransformer->detail($data);

        return $formatter->json($result);
    }

    /**
     * 建立專屬頁面 (上架)
     *
     * @url api/admin/{store_id}/dashboard/release
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function release(
        IndexService $indexService,
        ApiFormatter $formatter,
        Store $store
    ) {
        // 限等待上架中的商家
        if ($store->status != 'pending') {
            $this->setException('限「等待上架中」的商家使用！');
        }

        // 檢查商家可否上架 (建立商家專屬主頁)
        $checkLaunched = $indexService->checkLaunched($store);
        foreach ($checkLaunched as $key => $item) {
            if ($item['limit'] > $item['count']) {
                $this->setException('上架資訊尚未完成唷！');
            }
        }

        // 先記錄是否上架過
        $rawReleasedAt = $store->released_at;

        // 上架
        $store->update([
            'status'      => 'published',
            'released_at' => now(),
        ]);

        // 小鈴鐺通知使用者 use Job
        if (!$rawReleasedAt || $rawReleasedAt < now()->subMonths(6)) {
            Notification::dispatch('release_store', $store);
        }

        return $formatter->json(NULL, ['message' => '成功建立專屬頁面！']);
    }

    /**
     * 更改通知信箱
     *
     * @url api/admin/{store_id}/dashboard/edit-email
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @param string $email 信箱 *
     * @return json
     */
    public function editEmail(
        EmailAuthKeyService $emailAuthKeyService,
        ApiFormatter $formatter,
        Store $store,
        EmailLegalize $emailLegalize
    ) {
        // 產生信箱驗證碼
        $key = $this->getRandString(6);

        // 驗證碼寫進資料庫
        $authKey = $emailLegalize->create([
            'email'      => request('email'),
            'key'        => $key,
            'recipient'  => $store->name,
            'deadline_at'=> now()->addMinutes(5),
        ]);

        // WeddingDay 更改信箱驗證信
        $emailAuthKeyService->sendAuthMail($authKey, 'store_notify_email');

        return $formatter->json(NULL, ['message' => '請收信箱驗證碼！']);
    }

    /**
     * 更改通知信箱驗證
     *
     * @url api/admin/{store_id}/dashboard/edit-email-verified
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @param string $email 信箱 *
     * @param int $email_token 更改信箱驗證碼 *
     * @return json
     */
    public function editEmailVerified(
        ApiFormatter $formatter,
        Store $store,
        EmailLegalize $emailLegalize
    ) {
        // 檢查驗證碼
        $storeEmailLegalize = $emailLegalize->whereRaw('BINARY `key` = ?', [request('key')])
                                                ->where('email', request('email'))
                                                ->where('deadline_at', '>', now())
                                                ->first();
        if (!$storeEmailLegalize) {
            $this->setException('驗證碼有誤，請重新輸入！');
        }
        $emailLegalize->where('email', request('email'))->delete();

        // 更新商家通知信箱
        $store->update(['email' => request('email')]);

        // 商家帳號下相同 Email 的商家，不需要重複驗證
        $storeUser = request('store_user');
        $storeUser->liveStores()
                    ->where('email', request('email'))
                    ->update(['email_legalize' => 1]);

        return $formatter->json(NULL, ['message' => '更改帳號信箱成功！']);
    }

    /**
     * 更新通知設定
     *
     * @url api/admin/{store_id}/dashboard/update-notification
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token *
     * @path int $store_id 商家ID *
     * @param string $type 通知類型 email:Email通知 line:LINE通知 *
     * @param string $item 通知項目 message:即時通訊 quote:公開詢價 event:活動通知 *
     * @param bool $value 設定值 *
     * @return json
     */
    public function updateNotification(
        ApiFormatter $formatter,
        Store $store
    ) {
        // Email通知設定
        if (request('type') == 'email') {
            $column = 'email_'.request('item');
            $store->subscription()->updateOrCreate([
                'store_id' => $store->id,
            ], [
                $column => request('value'),
            ]);
        }

        // Line通知設定
        if (request('type') == 'line') {
            $storeUser = request('store_user');
            $column    = 'line_'.request('item');
            $store->accounts()->syncWithoutDetaching([
                $storeUser->id => [$column => request('value')],
            ]);
        }

        return $formatter->json(NULL, ['message' => '已更新通知設定！']);
    }
}
