<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Models\LogSetBrowserNotify;
use App\Services\Tools\GetWebData\GetVideoOembedService;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class MainController extends Controller
{
    use ApiErrorTrait;

    /**
     * 自動回覆訊息
     *
     * @url api/admin/{store_id}/auto-reply
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @return json
     */
    public function autoReply(
        ApiFormatter $formatter,
        Store $store
    ) {
        return $formatter->json([
            'status'      => $store->autoReply->status ?? NULL,
            'choose_type' => $store->autoReply->choose_type ?? NULL,
            'reference'   => $store->autoReply->reference ?? ($store->defaultAutoReply->content ?? NULL),
            'customize'   => $store->autoReply->customize ?? NULL,
        ]);
    }

    /**
     * 自動回覆訊息-更新
     *
     * @url api/admin/{store_id}/auto-reply
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @path int $store_id 商家ID *
     * @path bool $status 狀態 0:關閉 1:開啟 *
     * @path string $choose_type 選擇類型 reference:公版回覆 customize:客製回覆 *
     * @path string $customize 客製回覆內容
     * @return json
     */
    public function updateAutoReply(
        ApiFormatter $formatter,
        Store $store
    ) {
        $store->autoReply()->updateOrCreate([], [
            'status'      => request('status'),
            'choose_type' => request('choose_type'),
            'customize'   => request('customize'),
        ]);

        return $formatter->json();
    }

    /**
     * 更新瀏覽器推播通知
     *
     * @url api/admin/web-notification
     * @method POST
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @param bool $is_approve 是否同意
     * @param array $tokens Token列表 (* required if is_approve)
     * @param bool $btn_action 按鈕動作 0:暫不需要 1:好的 *
     * @param string $initial_value 初始值 default:預設 granted:允許 denied:封鎖 *
     * @param string $action_value 設定值 (空值):無 default:預設 granted:允許 denied:封鎖
     * @param string $device 裝置系統 *
     * @param string $browser 瀏覽器 *
     * @param string $version 瀏覽器版本
     * @return json
     */
    public function updateWebNotification(
        LogSetBrowserNotify $logSetBrowserNotify,
        ApiFormatter $formatter
    ) {
        // 取得 Store User
        $storeUser = request('store_user');

        // 更新瀏覽器推播通知
        if (!is_null(request('is_approve'))) {
            $storeUser->webNotification()->updateOrCreate([
                'target_type' => 'store_user',
            ], [
                'canceled_at' => request('is_approve') ? NULL : now(),
                'tokens'      => request('tokens'),
            ]);
        }

        // 新增瀏覽器通知的設定紀錄
        $logSetBrowserNotify->create([
            'target_type'   => 'store_user',
            'target_id'     => $storeUser->id,
            'btn_action'    => request('btn_action'),
            'initial_value' => request('initial_value'),
            'initial_value' => request('initial_value'),
            'action_value'  => request('action_value'),
            'device'        => request('device'),
            'browser'       => request('browser'),
            'version'       => request('version'),
        ]);

        return $formatter->json();
    }

    /**
     * 影片嵌入資訊
     *
     * @url api/admin/video-oembed
     * @method GET
     * @header string Access-Token *
     * @header string Store-Token 使用者驗證碼 *
     * @param string $url 影片連結 *
     * @return json
     */
    public function videoOembed(
        GetVideoOembedService $getVideoOembedService,
        ApiFormatter $formatter
    ) {
        // 取得影片資訊
        $result = $getVideoOembedService->getDataByUrl(request('url'));
        if (!$result) {
            $this->setException('此影片連結無法使用，請更換另一個連結');
        } elseif (isset($result->error_msg)) {
            $this->setException($result->error_msg);
        }

        return $formatter->json($result);
    }
}
