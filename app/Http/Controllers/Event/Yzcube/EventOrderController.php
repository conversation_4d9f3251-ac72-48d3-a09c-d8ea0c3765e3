<?php

namespace App\Http\Controllers\Event\Yzcube;

use App\Http\Controllers\Controller;
use App\Repositories\EventOrderRepository;
use App\Models\EventOrder;
use App\Services\Payment\EzPay\InvoiceHandle;
use App\Services\Admin\Invoice\SaveService as AdminInvoiceSaveService;
use App\Jobs\Invoice\AddGoogleSheets;
use App\Transformers\Event\Yzcube\EventOrderTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class EventOrderController extends Controller
{
    use ApiErrorTrait;

    /**
     * 活動訂單-列表
     *
     * @url api/event/yzcube/order
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $payment_status 付款狀態 success:已付款 fail:付款失敗 unnecessary:無須付款
     * @param string $invoice_status 發票狀態 pending:尚未開立 success:已開立 unnecessary:無須開立 invalid:已作廢
     * @param int $invoice_setting_id 所屬發票設定
     * @param date $start_date 搜尋付款起始日
     * @param date $end_date 搜尋付款結束日
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        EventOrderRepository $eventOrderRepository,
        EventOrderTransformer $eventOrderTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得活動訂單列表
        $eventOrders = $eventOrderRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $eventOrderTransformer->list($eventOrders);

        return $formatter->json($result);
    }

    /**
     * 匯出活動訂單紀錄
     *
     * @url api/event/yzcube/order/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $payment_status 付款狀態 success:已付款 fail:付款失敗 unnecessary:無須付款
     * @param string $invoice_status 發票狀態 pending:尚未開立 success:已開立 unnecessary:無須開立 invalid:已作廢
     * @param int $invoice_setting_id 所屬發票設定
     * @param date $start_date 搜尋付款起始日
     * @param date $end_date 搜尋付款結束日
     * @param string $keyword 搜尋關鍵字
     * @return json
     */
    public function exportCSV(
        EventOrderRepository $eventOrderRepository,
        EventOrderTransformer $eventOrderTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得活動訂單列表
        $eventOrders = $eventOrderRepository->getYzcubeListByRequest($request, false);

        // Transformer
        $result = $eventOrderTransformer->exportCSV($eventOrders);
        $filename = '[活動訂單紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 活動訂單-詳細頁
     *
     * @url api/event/yzcube/order/{event_order_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $order_id 活動訂單編號 *
     * @return json
     */
    public function show(
        EventOrderTransformer $eventOrderTransformer,
        ApiFormatter $formatter,
        EventOrder $eventOrder
    ) {
        // Transformer
        $result = $eventOrderTransformer->show($eventOrder);

        return $formatter->json($result);
    }

    /**
     * 活動訂單-更新發票設定
     *
     * @url api/event/yzcube/order/{event_order_id}/update-invoice-setting
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $invoice_setting_id 所屬發票設定
     * @return json
     */
    public function updateInvoiceSetting(
        ApiFormatter $formatter,
        EventOrder $eventOrder
    ) {
        if (!in_array($eventOrder->invoice_status, ['pending', 'unnecessary'])) {
            $this->setException('已開立過發票，則無法變更賣方的發票設定！');
        }

        // 選擇其他(NULL)，則自動將發票狀態改為無需開立
        if (!request('invoice_setting_id')) {
            $eventOrder->invoice_status = 'unnecessary';
        }

        $eventOrder->invoice_setting_id = request('invoice_setting_id');
        $eventOrder->save();

        return $formatter->json();
    }

    /**
     * 活動訂單-變更發票狀態
     *
     * @url api/event/yzcube/order/{event_order_id}/change-invoice-status
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $invoice_status 發票狀態 pending:尚未開立 unnecessary:無須開立 *
     * @return json
     */
    public function changeInvoiceStatus(
        ApiFormatter $formatter,
        EventOrder $eventOrder
    ) {
        if (!in_array($eventOrder->invoice_status, ['pending', 'unnecessary'])) {
            $this->setException('已開立過發票，則無法變更賣方的發票設定！');
        }

        if (!in_array(request('invoice_status'), ['pending', 'unnecessary'])) {
            $this->setException('僅限「尚未開立」和「無須開立」的發票狀態才可變更！');
        }

        $eventOrder->invoice_status = request('invoice_status');
        $eventOrder->save();

        return $formatter->json();
    }

    /**
     * 活動訂單-開立發票
     *
     * @url api/event/yzcube/order/{event_order_id}/invoice-issue
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $order_id 活動訂單編號 *
     * @param json $invoice_data 發票資訊 *
     * {
     *     "type": 'email', // 發票類型 paper:紙本列印 email:E-mail通知 carrier:存載具 *
     *     "buyer_ubn": NULL, // 買受人統一編號
     *     "buyer_name": '', // 買受人名稱 *
     *     "buyer_email": '', // 買受人E-mail (required_if: type=email)
     *     "carrier_type": NULL, // 載具類型 phone:手機條碼 citizen:自然人憑證條碼 donate:愛心捐贈碼 (required_if: type=carrier)
     *     "carrier_number": NULL, // 載具編號 (required_if: type=carrier)
     * }
     * @return json
     */
    public function invoiceIssue(
        InvoiceHandle $invoiceHandle,
        AdminInvoiceSaveService $adminInvoiceSaveService,
        ApiFormatter $formatter,
        EventOrder $eventOrder
    ) {
        if ($eventOrder->payment_status != 'success') {
            $this->setException('已完成付款的訂單才可以開立發票！');
        }

        if (!$eventOrder->invoiceSetting) {
            $this->setException('找不到商家的發票設定！');
        }

        if (!$eventOrder->invoice_data OR !request('invoice_data')) {
            $this->setException('找不到訂購人的發票資訊！');
        }

        if ($eventOrder->invoice_status == 'unnecessary' OR !$eventOrder->amount) {
            $this->setException('無須開立發票！');
        }

        if ($eventOrder->invoice_status == 'success' OR $eventOrder->invoices()->where('invoice_status', 'success')->exists()) {
            $this->setException('不行重複開立發票！');
        }

        $invoiceSetting = $eventOrder->invoiceSetting;

        // 更新開立發票內容欄位
        $invoiceData = array_merge(
            $eventOrder->invoice_data,
            json_decode(request('invoice_data'), true)
        );

        // ezPay 開立電子發票
        $invoiceResult  = $invoiceHandle->handle('invoice_issue', [
            'merchantOrderNo' => $eventOrder->order_no,
            'invoiceSetting'  => $invoiceSetting,
            'data'            => $invoiceData,
        ]);

        // ezPay 錯誤代碼
        if ($invoiceResult->Status != 'SUCCESS') {
            $this->setException('[ezPay Error] '.$invoiceResult->Status.'：'.$invoiceResult->Message);
        }

        // 更新活動訂單的發票狀態
        $eventOrder->invoice_status = 'success';
        $eventOrder->save();

        // 儲存發票記錄
        $invoice = $adminInvoiceSaveService->createInvoice($invoiceSetting, $invoiceData, $invoiceResult->Result);

        // 發票管理-Google試算表-新增紀錄 use Job
        if ($invoiceSetting->spreadsheet_id) {
            AddGoogleSheets::dispatch($invoice);
        }

        return $formatter->json();
    }
}
