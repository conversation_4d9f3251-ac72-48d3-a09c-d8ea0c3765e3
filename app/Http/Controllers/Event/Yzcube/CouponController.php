<?php

namespace App\Http\Controllers\Event\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Coupon\Yzcube\SaveRequest;
use App\Repositories\CouponRepository;
use App\Models\Coupon;
use App\Models\Event;
use App\Services\Coupon\Yzcube\SaveService;
use App\Transformers\Event\Yzcube\CouponTransformer;
use App\Formatters\ApiFormatter;

class CouponController extends Controller
{
    /**
     * 優惠券列表
     *
     * @url api/event/yzcube/coupon
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $type 優惠卷類型 single:單組通用代碼 multiple:多組獨立代碼
     * @param string $status 優惠卷狀態 pending:尚未開始 published:進行中 completed:已結束
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        CouponRepository $couponRepository,
        CouponTransformer $couponTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得優惠券列表
        $coupons = $couponRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $couponTransformer->list($coupons);

        return $formatter->json($result);
    }

    /**
     *  新增優惠卷
     *
     * @url api/event/yzcube/coupon/create
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function create(
        Coupon $coupon,
        Event $event,
        CouponTransformer $couponTransformer,
        ApiFormatter $formatter
    ) {
        // 取得優惠券列表
        $coupons = $coupon->select('id', 'title')
                            ->where('type', 'multiple')
                            ->orderBy('created_at', 'DESC')
                            ->get();

        // 取得團購表單列表
        $events = $event->select('id', 'title')
                        ->where('use_payment', 1)
                        ->orderBy('created_at', 'DESC')
                        ->get();

        // Transformer
        $result = $couponTransformer->create(compact('coupons', 'events'));

        return $formatter->json($result);
    }

    /**
     * 儲存優惠券
     *
     * @url api/event/yzcube/coupon/save
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $coupon_id 優惠券編號
     * @param string $type 優惠卷類型 single:單組通用代碼 multiple:多組獨立代碼 *
     * @param string $title 優惠券標題 *
     * @param string $code 優惠代碼 (required_if: type=single)
     * @param string $condition_type 折價條件 price:滿額 quantity:滿件 *
     * @param int $condition_value 折價條件值 *
     * @param string $discount_type 折價方式 cash:折扣金額 ratio:折扣%數 *
     * @param int $discount_value 折價值 *
     * @param int $limit 數量限制，NULL:無上限 *
     * @param int $take_coupon_id 要搶走的優惠券編號
     * @param array.int $take_number_range 要搶走優惠碼的序號區間 (required_if: take_coupon_id)
     * @param datetime $start_date 上架時間
     * @param datetime $end_date 下架時間
     * @param array.int $event_ids 團購表單編號 *
     * @param string $note 備註
     * @return json
     */
    public function save(
        SaveRequest $request,
        SaveService $saveService,
        CouponTransformer $couponTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        $coupon = $saveService->run($request);

        // Transformer
        $result = $couponTransformer->show($coupon);

        return $formatter->json($result);
    }

    /**
     * 優惠券詳細頁
     *
     * @url api/event/yzcube/coupon/{coupon_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $coupon_id 優惠券編號 *
     * @return json
     */
    public function show(
        CouponTransformer $couponTransformer,
        ApiFormatter $formatter,
        Coupon $coupon
    ) {
        // Transformer
        $result = $couponTransformer->show($coupon);

        return $formatter->json($result);
    }

    /**
     * 匯出優惠代碼紀錄
     *
     * @url api/event/yzcube/coupon/{coupon_id}/export-multiple
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $coupon_id 優惠券編號 *
     * @return json
     */
    public function exportMultiple(
        CouponTransformer $couponTransformer,
        ApiFormatter $formatter,
        Coupon $coupon
    ) {
        // Transformer
        $result = $couponTransformer->exportCSV($coupon);
        $filename = '[優惠代碼紀錄] '.$coupon->title.'_'.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 查詢多組獨立代碼
     *
     * @url api/event/yzcube/coupon/{coupon_id}/search-multiple/{code}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $coupon_id 優惠券編號 *
     * @path string $code 優惠代碼 *
     * @return json
     */
    public function searchMultiple(
        ApiFormatter $formatter,
        Coupon $coupon,
        $code
    ) {
        // 去除前綴碼
        $code = substr($code, 3);

        // 取得多組獨立代碼
        $log = $coupon->multiple()->whereRaw('BINARY `code` = ?', $code)->first();
        if (!$log) {
            return $formatter->json(NULL, [
                'status_code' => 1001,
                'message'     => '查無此優惠代碼！'
            ]);
        }

        // 已使用
        if ($log->used_at) {
            return $formatter->json([
                'created_at'  => $log->created_at,
                'number'      => $log->number,
                'code'        => $coupon->code.$log->code,
                'used_at'     => $log->used_at,
                'event_title' => $log->event->title,
                'user'        => $log->eventReportWithTrashed->only(['name', 'email', 'phone', 'deleted_at']),
            ], [
                'status_code' => 1002,
                'message'     => '此優惠代碼已使用過！'
            ]);
        }

        // 尚未使用
        return $formatter->json(NULL, [
            'status_code' => 1003,
            'message'     => '優惠代碼可使用！'
        ]);
    }

    /**
     * 刪除優惠卷
     *
     * @url api/event/yzcube/coupon/{coupon_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $coupon_id 優惠券編號 *
     * @return json
     */
    public function delete(
        ApiFormatter $formatter,
        Coupon $coupon
    ) {
        // 刪除優惠卷
        $coupon->delete();

        return $formatter->json();
    }
}
