<?php

namespace App\Http\Controllers\Event\Yzcube;

use App\Http\Controllers\Controller;
use App\Http\Requests\Event\Yzcube\SaveRequest;
use App\Repositories\EventRepository;
use App\Models\EventTool;
use App\Models\InvoiceSetting;
use App\Services\Event\Yzcube\SaveService;
use App\Services\Mail\Event\CompletedService;
use App\Transformers\Event\Yzcube\EventTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;

class EventController extends Controller
{
    use ApiErrorTrait;

    /**
     * 活動列表
     *
     * @url api/event/yzcube
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $type 表單類型 normal:一般表單 order:團購表單
     * @param string $status 表單狀態 pending:尚未開始 published:進行中 completed:已結束
     * @param string $keyword 搜尋關鍵字
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        EventRepository $eventRepository,
        EventTransformer $eventTransformer,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得活動列表
        $events = $eventRepository->getYzcubeListByRequest($request);

        // Transformer
        $result = $eventTransformer->list($events);

        return $formatter->json($result);
    }

    /**
     * 婚展報到系統列表
     *
     * @url api/event/yzcube/tool-list
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function toolList(
        EventTool $eventTool,
        ApiFormatter $formatter
    ) {
        // 取得活動列表
        $eventTools = $eventTool->select('id', 'name')
                                ->sort()
                                ->get();

        return $formatter->json($eventTools);
    }

    /**
     * 發票設定列表
     *
     * @url api/event/yzcube/invoice-setting-list
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function invoiceSettingList(
        InvoiceSetting $invoiceSetting,
        ApiFormatter $formatter
    ) {
        // 取得發票設定列表
        $invoiceSettings = $invoiceSetting->select('id', 'seller_name', 'seller_ubn')
                                            ->get();

        return $formatter->json($invoiceSettings);
    }

    /**
     * 活動詳細頁
     *
     * @url api/event/yzcube/{event_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $event_id 活動編號 *
     * @return json
     */
    public function show(
        EventRepository $eventRepository,
        EventTransformer $eventTransformer,
        ApiFormatter $formatter,
        $event_id
    ) {
        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $event_id]);
        if (!$event) {
            $this->setException('找不到此活動！');
        }

        // Transformer
        $result = $eventTransformer->show($event);

        return $formatter->json($result);
    }

    /**
     * 儲存活動表單
     *
     * @url api/event/yzcube/save
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param int $event_id Event編號
     * @param bool $use_payment 是否使用付款功能 *
     * @param datetime $start_date 活動起始時間
     * @param datetime $end_date 活動結束時間
     * @param string $path 網址名稱 *
     * @param string $title 活動標題 *
     * @param string $description 活動描述
     * @param string $image 圖檔名 *
     * @param string $image_xs 圖檔名mobile版 *
     * @param string $image_meta 圖檔名meta版 *
     * @param json $columns 欄位設定 *
     * [{
     *     "name": "欄位名稱",
     *     "key": "欄位索引值",
     *     "format": "欄位格式 heading,text,textarea,radio,checkbox,select,date,file",
     *     "placeholder": "提示訊息",
     *     "info": "說明",
     *     "is_required": 1,
     *     "validate_type": "驗證類型 date,number,email,url",
     *     "value": "",
     *     "status": 1,
     *     "format_options": {
     *         "text": "選項名稱",
     *         "value": "選項值"
     *     },
     *     "file_limit": "檔案格式限制 image,audio,video,other",
     *     "module": "欄位模組 basic,wedding_date,gold_team"
     * }]
     * @param string $share_url 社群分享連結
     * @param string $float_btn 浮動按鈕文字 *
     * @param string $pending_info 活動尚未開始的訊息
     * @param string $result_new_info 新會員完成報名的訊息
     * @param bool $result_new_btn_show 新會員完成報名的按鈕是否顯示 *
     * @param string $result_new_btn 新會員完成報名的按鈕文字 *
     * @param string $result_new_url 新會員完成報名的按鈕連結
     * @param string $result_old_info 舊會員完成報名的訊息
     * @param string $result_old_btn 舊會員完成報名的按鈕文字 *
     * @param string $result_old_url 舊會員完成報名的按鈕連結
     * @param string $completed_info 活動結束的訊息
     * @param string $email_title 活動報名完成信件標題
     * @param string $email_info 活動報名完成信件內容
     * @param string $email_info_more 活動報名完成信件內容-更多資訊
     * @param string $spreadsheet_id Google雲端試算表編號 *
     * @param bool $use_report_no 是否使用報名編號(報名完成通知信) *
     * @param bool $show_report_no_page 是否顯示報名編號在報名完成頁面 *
     * @param bool $use_qrcode 是否使用QRcode(報名完成通知信) *
     * @param bool $show_qrcode_page 是否顯示QRcode在報名完成頁面 *
     * @param int $event_tool_id 婚展報到系統編號
     * @param string $onsite_signup_dates 現場報名日期 (逗號分隔)
     * @param bool $use_calendar 是否使用Google行事曆
     * @param string $calendar_summary 行事曆標題
     * @param string $calendar_location 行事曆地點
     * @param string $calendar_description 行事曆描述
     * @param datetime $calendar_start 行事曆起始時間
     * @param datetime $calendar_end 行事曆結束時間
     * @param bool $purchase_item_unique 所有品項單一限購 (required_if: use_payment=1)
     * @param bool $need_invoice 需要開立發票 (required_if: use_payment=1)
     * @param bool $invoice_now 立即開立發票 (required_if: need_invoice=1)
     * @param int $invoice_setting_id 所屬發票設定
     * @param string $seller_name 營業人名稱 (required_if: use_payment=1)
     * @param string $seller_ubn 營業人統編 (required_if: use_payment=1)
     * @param json $products 活動商品
     * [{
     *     "product_id": 1,
     *     "name": "M12 多倫多系列商品/蠟燭/擴香瓶/噴霧",
     *     "items": [{
     *         "item_id": 1,
     *         "name": "香氛噴霧 50ml",
     *         "original_price": null,
     *         "selling_price": 580,
     *         "inventory": 99,
     *         "purchase_limit": 2,
     *         "use_upload_images": 1,
     *         "use_crop_images": 1,
     *         "crop_width": 1200,
     *         "crop_height": 900,
     *         "limit_min_side": 600,
     *     }]
     * }]
     * @param json $extra_fees 活動訂單的額外費用
     * [{
     *     "name": "訂金(會退還)",
     *     "is_positive": 1,
     *     "type": "cash",
     *     "value": 200
     * }]
     * @param json $exhibitions 婚展多選項目
     * [{
     *     "exhibition_id": 1,
     *     "tool_id": 5,
     *     "name": "台北華山【好婚市集】主題婚紗展",
     *     "expired_at": "2022-05-01 18:00:00",
     *     "onsite_signup_dates": "2022-04-30,2022-05-01",
     *     "use_calendar": 1,
     *     "calendar_summary": "台北華山【好婚市集】主題婚紗展",
     *     "calendar_location": "華山1914文化創意產業園區",
     *     "calendar_description": "好婚市集主辦，主題婚紗展",
     *     "calendar_start": "2022-04-30 11:00:00",
     *     "calendar_end": "2022-05-01 18:00:00"",
     * }]
     * @return json
     */
    public function save(
        SaveRequest $request,
        EventRepository $eventRepository,
        SaveService $saveService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $request['event_id']]);
        if (!$event) {
            $event = $eventRepository->getModel();
        }

        $event = $saveService->run($request, $event);

        return $formatter->json([
            'event_id' => $event->id,
            'status'   => $event->getStatusLabel(),
            'columns'  => json_decode($event->columns), // 還沒有報名資料會硬刪除欄位，回傳給前端更新
        ]);
    }

    /**
     * 匯出報名紀錄
     *
     * @url api/event/yzcube/{event_id}/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $event_id 活動編號 *
     * @return json
     */
    public function exportCSV(
        EventRepository $eventRepository,
        EventTransformer $eventTransformer,
        ApiFormatter $formatter,
        $event_id
    ) {
        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $event_id]);
        if (!$event) {
            $this->setException('找不到此活動！');
        }

        // Transformer
        $result = $eventTransformer->exportCSV($event);
        $filename = '[活動報名紀錄] '.$event->title.'_'.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $result);
    }

    /**
     * 刪除活動
     *
     * @url api/event/yzcube/{event_id}/export-csv
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $event_id 活動編號 *
     * @return json
     */
    public function delete(
        EventRepository $eventRepository,
        ApiFormatter $formatter,
        $event_id
    ) {
        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $event_id]);
        if (!$event) {
            $this->setException('找不到此活動！');
        }

        // 驗證此活動是否有成功訂購/報名人數
        $report_count = $event->countSuccessReports();
        if ($report_count) {
            $this->setException('此活動含有'.$report_count.'個成功訂購/報名人數，無法執行刪除！');
        }

        // 刪除活動
        $event->delete();

        return $formatter->json();
    }

    /**
     * 報名紀錄-檢視內容
     *
     * @url api/event/yzcube/{event_id}/report/{report_id}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $event_id 活動編號 *
     * @path int $report_id 報名序號 *
     * @return json
     */
    public function reportShow(
        EventRepository $eventRepository,
        ApiFormatter $formatter,
        $event_id,
        $report_id
    ) {
        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $event_id]);
        if (!$event) {
            $this->setException('找不到此活動！');
        }

        // 取得活動報名紀錄
        $report = $event->reports()->find($report_id);
        if (!$report) {
            $this->setException('找不到此報名紀錄！');
        }

        return $formatter->json([
            'name'  => $report->name,
            'email' => $report->email,
            'phone' => $report->phone,
        ]);
    }

    /**
     * 報名紀錄-刪除
     *
     * @url api/event/yzcube/{event_id}/report/{report_id}/delete
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $event_id 活動編號 *
     * @path int $report_id 報名序號 *
     * @return json
     */
    public function reportDelete(
        EventRepository $eventRepository,
        ApiFormatter $formatter,
        $event_id,
        $report_id
    ) {
        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $event_id]);
        if (!$event) {
            $this->setException('找不到此活動！');
        }

        // 取得活動報名紀錄
        $report = $event->reports()->find($report_id);
        if (!$report) {
            $this->setException('找不到此報名紀錄！');
        }

        // 刪除報名紀錄 & 訂購紀錄
        if ($event->use_payment && $report->order) {
            $report->order->delete();
        }
        $report->delete();

        // 回傳成功訂購/報名人數
        return $formatter->json([
            'report_count' => $event->countSuccessReports(),
        ]);
    }

    /**
     * 報名紀錄-補寄通知信
     *
     * @url api/event/yzcube/{event_id}/report/{report_id}/resend-email
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $event_id 活動編號 *
     * @path int $report_id 報名序號 *
     * @param string $email 補寄Email *
     * @param bool $is_update 同步修改資料庫 *
     * @return json
     */
    public function reportResendEmail(
        EventRepository $eventRepository,
        CompletedService $completedService,
        ApiFormatter $formatter,
        $event_id,
        $report_id
    ) {
        // 取得活動報名資訊
        $event = $eventRepository->getFirst(['id' => $event_id]);
        if (!$event) {
            $this->setException('找不到此活動！');
        }

        // 取得活動報名紀錄
        $report = $event->reports()->find($report_id);
        if (!$report) {
            $this->setException('找不到此報名紀錄！');
        }

        // 同步修改資料庫
        $report->email = request('email');
        if (request('is_update')) {
            $report->save();
        }

        // WeddingDay 活動報名完成信
        $completedService->sendMail($event, $report);

        return $formatter->json();
    }
}
