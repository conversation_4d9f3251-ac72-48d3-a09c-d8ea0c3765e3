<?php

namespace App\Http\Controllers\Event;

use App\Http\Controllers\Controller;
use App\Http\Requests\Event\CheckOrderRequest;
use App\Http\Requests\Event\SaveRequest;
use App\Services\Event\SaveService;
use App\Services\Event\CreateReportQrcodeService;
use App\Models\EventEmailVerified;
use App\Models\EventReport;
use App\Transformers\Event\EventTransformer;
use App\Traits\ApiErrorTrait;
use App\Formatters\ApiFormatter;
use Imagick;

class EventController extends Controller
{
    use ApiErrorTrait;

    /**
     * 前台-活動報名系統：活動報名詳細頁
     *
     * @url api/event/{path}
     * @method GET
     * @header string Access-Token *
     * @path string $path 網址名稱 *
     * @param string $code 【報名前的身份驗證暫先移除】信箱登入驗證碼
     * @return json
     */
    public function index(
        EventEmailVerified $eventEmailVerified,
        EventTransformer $eventTransformer,
        ApiFormatter $formatter
    ) {
        // 取得活動報名資訊
        $event        = request('event');
        $user         = NULL;
        $baseResponse = [];

        // 若有信箱登入驗證碼
        if (request('code')) {

            // 取得活動信箱驗證
            $emailVerified = $eventEmailVerified->where('event_id', $event->id)
                                                ->where('code', request('code'))
                                                ->first();
            // 取得使用者資訊
            if ($emailVerified) {
                $user = $emailVerified->user;

            // 活動信箱驗證錯誤
            } else {
                $baseResponse = [
                    'status'      => 'fail',
                    'status_code' => 4006,
                    'message'     => '發生錯誤了，活動報名表單的網址連結不是有效的，請<span>聯絡客服</span>！',
                ];
            }
        }

        // Transformer
        $result = $eventTransformer->index($event, $user);

        return $formatter->json($result, $baseResponse);
    }

    /**
     * 前台-活動報名系統：驗證欄位唯一值
     *
     * @url api/event/{path}/unique-column
     * @method GET
     * @header string Access-Token *
     * @path string $path 網址名稱 *
     * @param string $column 欄位 email|phone *
     * @param string $value 值 *
     * @var json
     */
    public function uniqueColumn(
        EventReport $eventReport,
        ApiFormatter $formatter
    ) {
        // 取得活動報名資訊
        $event = request('event');

        // 驗證欄位唯一值
        $exists = $eventReport->where('event_id', $event->id)
                            ->where(request('column'), request('value'))
                            ->whereDoesntHave('order.logPayment', function ($q) {
                                $q->fail();
                            })
                            ->exists();
        if ($exists) {
            $this->setException('此欄位資訊已經有人使用了！');
        }

        return $formatter->json();
    }

    /**
     * 前台-活動報名系統：驗證訂購內容
     *
     * @url api/event/{path}/check-order
     * @method POST
     * @header string Access-Token *
     * @path string $path 網址名稱 *
     * @param json $order 訂購內容 *
     * [{
     *     "product_id": 1,
     *     "item_id": 1,
     *     "original_price": 600,
     *     "selling_price": 580,
     *     "quantity": 2,
     * }]
     * @param string $coupon_code 優惠代碼
     * @var json
     */
    public function checkOrder(
        CheckOrderRequest $request,
        ApiFormatter $formatter
    ) {
        // Transformer
        return $formatter->json($request['orderList'], $request['baseResponse']);
    }

    /**
     * 前台-活動報名系統：儲存表單
     *
     * @url api/event/{path}/save
     * @method POST
     * @header string Access-Token *
     * @path string $path 網址名稱 *
     * @param string $access_token 【報名前的身份驗證暫先移除】FB用戶存取權杖 (若沒有email，則此欄位為必填)
     * @param string $email 【報名前的身份驗證暫先移除】帳號email (若沒有access_token，則此欄位為必填)
     * @param string $password 【報名前的身份驗證暫先移除】密碼 (需編碼，若沒有access_token，則此欄位為必填)
     * @param json $content 表單內容 *
     * {
     *     "basicHeading": "",
     *     "event_name": "周胖",
     *     "event_phone": "0910358946",
     *     "event_email": "<EMAIL>"
     * }
     * @param json $order 訂購內容
     * [{
     *     "product_id": 1,
     *     "item_id": 1,
     *     "original_price": 600,
     *     "selling_price": 580,
     *     "quantity": 2,
     *     "images": [],
     * }]
     * @param json $invoice 發票資訊
     * {
     *     "type": 'email', // 發票類型 paper:紙本列印 email:E-mail通知 carrier:存載具 *
     *     "buyer_ubn": NULL, // 買受人統一編號
     *     "buyer_name": '', // 買受人名稱 *
     *     "buyer_email": '', // 買受人E-mail (required_if: type=email)
     *     "carrier_type": NULL, // 載具類型 phone:手機條碼 citizen:自然人憑證條碼 donate:愛心捐贈碼 (required_if: type=carrier)
     *     "carrier_number": NULL, // 載具編號 (required_if: type=carrier)
     * }
     * @param string $coupon_code 優惠代碼
     * @param string $prime TapPay的付款憑證
     * @param array.int $exhibitions 婚展多選項目值
     * @return json
     */
    public function save(
        SaveRequest $request,
        SaveService $saveService,
        CreateReportQrcodeService $createReportQrcodeService,
        ApiFormatter $formatter
    ) {
        // 取得活動報名資訊
        $event = request('event');

        // Service
        $data = $saveService->run($request, $event);

        // Transformer
        return $formatter->json([
            'is_exists' => $data->user->exists,
            'is_new'    => $data->user->wasRecentlyCreated,
            'report_id' => $data->report->id,
            'report_no' => $event->use_report_no ? $data->report->id : '',
            'qrcode'    => $event->use_qrcode ? $createReportQrcodeService->generate($data->report->qrcode_token, true) : '',
        ]);
    }

    /**
     * 前台-活動報名完成頁
     *
     * @url api/event/{path}/completed/{report_id}
     * @method GET
     * @header string Access-Token *
     * @path string $path 網址名稱 *
     * @path int $report_id 報名ID *
     * @var json
     */
    public function completed(
        EventTransformer $eventTransformer,
        CreateReportQrcodeService $createReportQrcodeService,
        ApiFormatter $formatter,
        $path,
        $report_id
    ) {
        // 取得活動報名資訊
        $event = request('event');
        $report = $event->reports()->find($report_id);

        // Transformer
        return $formatter->json([
            'event'     => $eventTransformer->getEventInfo($event),
            'report_no' => ($report && $event->use_report_no) ? $report->id : '',
            'qrcode'    => ($report && $event->use_qrcode) ? $createReportQrcodeService->generate($report->qrcode_token, true) : '',
        ]);
    }

    /**
     * 前台-活動報名系統：取得活動報名人數
     *
     * @url api/event/{path}/report-count
     * @method GET
     * @param path $path 網址名稱 *
     * @return json
     */
    public function reportCount(
        ApiFormatter $formatter
    ) {
        // 取得活動報名資訊
        $event = request('event');

        return $formatter->json($event->reports->count());
    }

    /**
     * 取得活動表單的 QRcode image
     *
     * @url api/event/{path}/qrcode-image
     * @method GET
     * @param string $token QRcode Token *
     * @return image
     */
    public function getQrcodeImage(
        CreateReportQrcodeService $createReportQrcodeService
    ) {
        $imageBlob = $createReportQrcodeService->generate(request('token'));

        $imagick = new Imagick();
        $imagick->readImageBlob($imageBlob);

        return response($imagick)->header('Content-Type', 'image/png');
    }

    /**
     * 取得活動報名記錄
     *
     * @url api/event/{path}/report/{report_id}
     * @method GET
     * @header string Access-Token *
     * @path int $report_id 報名ID *
     * @param int $user_id 使用者ID *
     * @return json
     */
    public function getReportData(
        ApiFormatter $formatter,
        $path,
        $report_id
    ) {
        // 取得活動報名資訊
        $event  = request('event');
        $report = $event->reports()
                        ->where('user_id', request('user_id'))
                        ->find($report_id);
        if (!$report) {
            $this->setException('找不到此活動報名表單報名記錄！');
        }

        // 訂單商品列表
        $orderList = [];
        if ($event->use_payment) {
            foreach ($report->order->orderItems->groupBy('product_id') as $orderItems) {
                $orderList[] = [
                    'product_image' => $orderItems[0]->product->image->file_name ?? '', // 商品圖
                    'product_name'  => $orderItems[0]->product_name, // 商品名稱

                    // 商品品項列表
                    'items' => $orderItems->map(function($item) {
                                    return [
                                        'item_name'      => $item->item_name, // 品名
                                        'quantity'       => $item->quantity, // 數量
                                        'original_price' => $item->original_price, // 原價
                                        'amount'         => $item->amount, // 金額
                                        'images'         => $item->images->pluck('file_name'), // 訂購品項的上傳照
                                    ];
                                }),
                ];
            }
        }

        // 回覆內容-檔案路徑
        $filePath = env('APP_DEBUG') ? 'wdv3-dev' : 'wdv3';
        $filePath = config('params.file_url').'/'.$filePath.'/event/'.$event->path.'/';

        return $formatter->json([
            'use_payment'  => $event->use_payment, // 是否使用付款功能
            'columns'      => json_decode($event->columns), // 欄位設定
            'content'      => json_decode($report->content), // 回覆內容
            'file_path'    => $filePath, // 回覆內容-檔案路徑
            'created_at'   => $report->created_at, // 訂購時間
            'payment_type' => ($report->order->amount ?? 0) ? '信用卡' : '無需付款', // 付款方式
            'amount'       => $report->order->amount ?? NULL, // 訂單總額
            'order_list'   => $orderList, // 訂單商品列表
            'detail_list'  => $event->use_payment ? $report->order->present()->detailList : [], // 訂單明細
        ]);
    }
}
