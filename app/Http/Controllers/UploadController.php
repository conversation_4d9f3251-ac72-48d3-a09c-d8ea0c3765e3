<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\File\UploadImageFormService;
use App\Services\File\UploadFileFormService;
use App\Services\File\DeleteFileService;
use App\Formatters\ApiFormatter;
use App\Traits\ApiErrorTrait;

/*
 |--------------------------------------
 |  前台/商家後台/神之後台-上傳相關
 |--------------------------------------
 |
 |
 */
class UploadController extends Controller
{
    use ApiErrorTrait;

    /**
     * 上傳圖片
     *
     * @url api/upload/image/form 前台
     * @url api/admin/upload/image/form 商家後台
     * @url api/yzcube/upload/image/form 神之後台
     * @method POST
     * @header string Access-Token *
     * @header string User-Token|Store-Token|Yzcube-Token *
     * @param file $upload_image 上傳圖檔 *
     * @return json
     */
    public function imageForm(
        UploadImageFormService $uploadImageFormService,
        ApiFormatter $formatter
    ) {
        // 上傳圖片
        $result = $uploadImageFormService->uploadFile(request('upload_image'));

        return $formatter->json($result);
    }

    /**
     * 上傳base64(裁切)圖片
     *
     * @url api/upload/image/base64 前台
     * @url api/admin/upload/image/base64 商家後台
     * @method POST
     * @header string Access-Token *
     * @header string User-Token
     * @header string Store-Token *
     * @param string $image_base64 base64圖片 *
     * @return json
     */
    public function imageBase64(
        UploadImageFormService $uploadImageFormService,
        ApiFormatter $formatter
    ) {
        // 上傳圖片
        $result = $uploadImageFormService->uploadBase64(request('image_base64'));

        return $formatter->json($result);
    }

    /**
     * 刪除圖片 (待移除)
     *
     * @url api/upload/delete 前台
     * @url api/yzcube/upload/delete 神之後台
     * @method POST
     * @header string Access-Token *
     * @header string User-Token
     * @header string Yzcube-Token *
     * @param string $file_name 圖檔名稱 *
     * @return json
     */
    public function deleteFile(
        DeleteFileService $deleteFileService,
        ApiFormatter $formatter
    ) {
        // Request
        $request = request();

        // Service
        //$deleteFileService->delete($request);

        //能跑到這就是刪除成功了
        return $formatter->json();
    }

    /**
     * 上傳檔案
     *
     * @url api/upload/file/form 前台
     * @method POST
     * @header string Access-Token *
     * @header string User-Token
     * @param file $file 上傳檔案 *
     * @param string $path 儲存目錄 *
     * @return json
     */
    public function fileForm(
        UploadFileFormService $uploadFileFormService,
        ApiFormatter $formatter
    ) {
        // 上傳檔案
        $result = $uploadFileFormService->run(request('file'), request('path'));

        return $formatter->json($result);
    }
}
