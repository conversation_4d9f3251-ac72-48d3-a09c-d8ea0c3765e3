<?php

namespace App\Http\Controllers\RingEvent;

use App\Http\Controllers\Controller;
use App\Models\RingUser;
use App\Models\RingReserve;
use App\Models\RingPoint;
use App\Models\RingOrder;
use App\Models\RingBrand;
use App\Services\Tools\KeywordSearch\SearchService;
use App\Jobs\Firebase\Message;
use App\Traits\Model\TransformerTrait;
use App\Formatters\ApiFormatter;

class YzcubeController extends Controller
{
    use TransformerTrait;

    /**
     * 數據看板
     *
     * @url api/ring-event/yzcube/dashboard
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @return json
     */
    public function dashboard(
        RingBrand $ringBrand,
        RingUser $ringUser,
        RingReserve $ringReserve,
        RingPoint $ringPoint,
        RingOrder $ringOrder,
        ApiFormatter $formatter
    ) {
        return $formatter->json([

            // 活動數據
            'user_count'        => $ringUser->count(), // 報名人數
            'reserve_count'     => $ringReserve->count(), // 預約數
            'user_point_count'  => $ringPoint->select('id')->groupBy('ring_user_id')->get()->count(), // 到店人數
            'point_count'       => $ringPoint->count(), // 到店數
            'user_order_count'  => $ringOrder->select('id')->approved()->groupBy('ring_user_id')->get()->count(), // 下訂人數
            'order_count'       => $ringOrder->approved()->count(), // 訂單數
            'order_price_total' => $ringOrder->approved()->pluck('price')->sum(), // 訂單總額

            // 品牌的數據統計
            'brand_list' => $ringBrand->with([
                                            'reserves:id,ring_brand_id',
                                            'points:id,ring_brand_id',
                                            'approvedOrders:id,ring_user_id,ring_brand_id,price',
                                            'ringStores.reserves:id,ring_store_id',
                                            'ringStores.points:id,ring_store_id',
                                        ])
                                        ->get()
                                        ->map(function($brand) {
                                            return [
                                                'name'              => $brand->name,
                                                'spreadsheet_id'    => $brand->spreadsheet_id ?: '',
                                                'sheet_id'          => $brand->sheet_id ?: '',
                                                'reserve_count'     => $brand->reserves->count(), // 預約數
                                                'point_count'       => $brand->points->count(), // 到店數
                                                'user_order_count'  => $brand->approvedOrders->groupBy('ring_user_id')->count(), // 到店人數
                                                'order_count'       => $brand->approvedOrders->count(), // 訂單數
                                                'order_price_total' => $brand->approvedOrders->sum('price'), // 訂單總額

                                                // 品牌下所有門市的數據統計
                                                'store_list' => $brand->ringStores->map(function($store) {
                                                                    return [
                                                                        'name'          => $store->name,
                                                                        'mall_floor'    => $store->mall_floor,
                                                                        'reserve_count' => $store->reserves->count(), // 預約數
                                                                        'point_count'   => $store->points->count(), // 到店數
                                                                    ];
                                                                }),
                                            ];
                                        }),
        ]);
    }

    /**
     * 報名列表
     *
     * @url api/ring-event/yzcube
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $keyword 搜尋關鍵字
     * @param string $date_type 篩選日期 chehckin:報名日期 order:下訂日期
     * @param arrary.date $date_range[] 日期區間
     * @param int $point_count 到店數 0~5 & more
     * @param int $collect_count 累積點數
     * @param int $brand_id 下訂品牌ID
     * @param string $ring_style 下訂款式 diamond:鑽戒 lovers:對戒 gold:金飾 other:其他
     * @param string $is_pending 是否未審核
     * @param string $sort 排序欄位 id:編號 created_at:建立日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @param int $page 頁碼
     * @return json
     */
    public function list(
        RingUser $ringUser,
        RingOrder $ringOrder,
        RingBrand $ringBrand,
        ApiFormatter $formatter
    ) {
        // 取得報名列表
        $ringUsers = $this->getRingUserList($ringUser);

        $list = [];
        foreach ($ringUsers as $ringUser) {

            // 整理訂單資訊
            $brandId    = [];
            $orderDate  = [];
            $ringStyle  = [];
            $orderTotal = 0;
            $isAudited  = NULL;
            foreach ($ringUser->orders as $order) {
                if (!in_array($order->ring_brand_id, $brandId)) {
                    $brandId[] = $order->ring_brand_id;
                }
                if (!in_array($order->date, $orderDate)) {
                    $orderDate[] = $order->date;
                }
                if (!in_array($order->ring_style, $ringStyle)) {
                    $ringStyle[] = $order->ring_style;
                }
                $orderTotal += $order->price;
                $isAudited = ($isAudited === 0 OR $order->status == 'pending') ? 0 : 1;
            }

            $list[] = [
                'id'            => $ringUser->id,
                'created_at'    => $ringUser->created_at->format('Y-m-d H:i:s'),
                'user_id'       => $ringUser->user_id,
                'user_name'     => $ringUser->user->name ?? '',
                'name'          => $ringUser->name,
                'mate_name'     => $ringUser->mate_name,
                'phone'         => $ringUser->phone,
                'email'         => $ringUser->email,
                'point_count'   => $ringUser->point_count,
                'collect_count' => $ringUser->collect_count,
                'brand_id'      => $brandId,
                'order_date'    => $orderDate,
                'ring_style'    => $ringStyle,
                'order_total'   => $orderTotal,
                'is_audited'    => $isAudited,
                'deleted_at'    => $ringUser->deleted_at ? $ringUser->deleted_at->format('Y-m-d H:i:s') : '',
            ];
        }

        // 報名列表，顯示分頁資訊
        $ringUsers = $ringUsers->toArray();
        $ringUsers['data'] = $list;

        return $formatter->json([
            'ring_users'      => $ringUsers, // 報名列表
            'ring_style_list' => $this->formatAttributeList($ringOrder->ringStyleList), // 下訂款式列表
            'brand_list'      => $ringBrand->select('id', 'name')->sort()->get(), // 品牌列表
        ]);
    }

    /**
     * 匯出報名紀錄
     *
     * @url api/ring-event/yzcube/export-csv
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @param string $keyword 搜尋關鍵字
     * @param string $date_type 篩選日期 chehckin:報名日期 order:下訂日期
     * @param arrary.date $date_range[] 日期區間
     * @param int $point_count 到店數
     * @param int $collect_count 累積點數
     * @param int $brand_id 下訂品牌ID
     * @param string $ring_style 下訂款式 diamond:鑽戒 lovers:對戒 gold:金飾 other:其他
     * @param string $is_audited 是否已審核
     * @param string $sort 排序欄位 id:編號 created_at:建立日期
     * @param string $direction 升冪降冪 asc:升冪 desc:降冪
     * @return json
     */
    public function exportCSV(
        RingUser $ringUser,
        ApiFormatter $formatter
    ) {
        // 取得報名列表
        $ringUsers = $this->getRingUserList($ringUser, false);

        // Column Header
        $data = "ID,報名者,Email,電話,婚期,填寫報名表次數,預約品牌數,新人自主勾選品牌數,先到店品牌數,先上傳訂單品牌數,到店數,累積點數,先填表再到店數,直接到店數,是否下訂,刪除時間\r\n";
        foreach ($ringUsers as $ringUser) {

            // Column Data
            $data .= $ringUser->id.",";
            $data .= $ringUser->name.",";
            $data .= $ringUser->email.",";
            $data .= $ringUser->phone.",";
            $data .= $ringUser->wedding_date.",";
            $data .= $ringUser->reserve_times.",";
            $data .= $ringUser->allReserves->count().",";
            $data .= $ringUser->allReserves()->where('type', 'reserve')->count().",";
            $data .= $ringUser->allReserves()->where('type', 'point')->count().",";
            $data .= $ringUser->allReserves()->where('type', 'order')->count().",";
            $data .= $ringUser->point_count.",";
            $data .= $ringUser->collect_count.",";
            $data .= $ringUser->points()->where('has_reserve', 1)->count().",";
            $data .= $ringUser->points()->where('has_reserve', 0)->count().",";
            $data .= ($ringUser->orders()->where('status', 'approved')->exists() ? '是' : '否').",";
            $data .= $ringUser->deleted_at."\r\n";
        }

        $filename = '[婚戒大賞報名紀錄] '.now()->format('Y-m-d_Hi').'.csv';

        return $formatter->exportCSV($filename, $data);
    }

    /**
     * 報名詳細頁
     *
     * @url api/ring-event/yzcube/{ringUser}
     * @method GET
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $ring_user_id 報名資訊ID *
     * @return json
     */
    public function show(
        RingUser $ringUser,
        ApiFormatter $formatter
    ) {
        // 會員資訊
        $user = NULL;
        if ($ringUser->user) {
            $user = [
                'id'     => $ringUser->user_id,
                'name'   => $ringUser->user->name,
                'avatar' => $ringUser->user->present()->display_avatar,
            ];
        }

        // 訂單資訊
        $orders = $ringUser->orders->map(function($order) {
            return [
                'id'               => $order->id,
                'date'             => $order->date,
                'brand_name'       => $order->ringBrand->name,
                'price'            => $order->price,
                'ring_style'       => $order->ringStyleList[$order->ring_style],
                'ring_style_other' => $order->ring_style_other ?: '',
                'image'            => $order->image,
                'agent_image'      => $order->agent_image ?: '',
                'status'           => $order->status,
                'status_invalid'   => $order->status_invalid ?: '',
                'audited_at'       => $order->audited_at ?: '',
            ];
        });

        return $formatter->json([
            'user'          => $user,
            'name'          => $ringUser->name,
            'mate_name'     => $ringUser->mate_name,
            'email'         => $ringUser->email,
            'phone'         => $ringUser->phone,
            'wedding_date'  => $ringUser->wedding_date,
            'created_at'    => $ringUser->created_at->format('Y-m-d H:i:s'),
            'point_count'   => $ringUser->point_count,
            'collect_count' => $ringUser->collect_count,
            'orders'        => $orders,
        ]);
    }

    /**
     * 更新報名資訊
     *
     * @url api/ring-event/yzcube/{ringUser}
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $ring_user_id 報名資訊ID *
     * @param string $name 真實姓名 *
     * @param string $mate_name 另一半姓名 *
     * @param string $email Email *
     * @param string $phone 聯絡電話 *
     * @param date $wedding_date 婚期 *
     * @return json
     */
    public function update(
        RingUser $ringUser,
        ApiFormatter $formatter
    ) {
        $ringUser->update([
            'name'          => request('name'),
            'mate_name'     => request('mate_name'),
            'email'         => request('email'),
            'phone'         => request('phone'),
            'wedding_date'  => request('wedding_date'),
        ]);

        return $formatter->json();
    }

    /**
     * 報名詳細頁-審核訂單
     *
     * @url api/ring-event/yzcube/order-audit/{ringOrder}
     * @method POST
     * @header string Access-Token *
     * @header string Yzcube-Token *
     * @path int $ring_user_id 報名資訊ID *
     * @param bool $status 狀態 approved:已認證 invalid:無效的 *
     * @param string $status_invalid 審核無效的原因 (required_if: status=0)
     * @return json
     */
    public function auditOrder(
        RingOrder $ringOrder,
        ApiFormatter $formatter
    ) {
        $ringOrder->status         = request('status');
        $ringOrder->status_invalid = request('status_invalid');
        $ringOrder->audited_at     = now();
        $ringOrder->save();

        // 若審核成功
        if ($ringOrder->status == 'approved') {

            // 回填預約記錄
            if ($ringOrder->ringUser->allReserves()->where('ring_brand_id', $ringOrder->ring_brand_id)->doesntExist()) {
                $ringOrder->ringUser->allReserves()->create([
                    'ring_brand_id' => $ringOrder->ring_brand_id,
                    'date'          => now()->format('Y-m-d'),
                    'type'          => 'order',
                    'created_at'    => now(),
                ]);
            }

            // 回填點數紀錄
            if ($ringOrder->ringUser->points()->where('ring_brand_id', $ringOrder->ring_brand_id)->doesntExist()) {
                $ringOrder->ringUser->points()->create([
                    'ring_brand_id' => $ringOrder->ring_brand_id,
                    'has_reserve'   => 0,
                    'created_at'    => now(),
                ]);
            }
        }

        // 更新報名資訊的到店數&累積點數
        $ringOrder->ringUser->updateCollectPointCount();

        // 即時通訊處理 use Job
        Message::dispatch('ring_event_audit_order', $ringOrder);

        return $formatter->json();
    }

    /**
     * 取得報名列表
     *
     * @return array
     */
    private function getRingUserList($ringUser, $pagination = true)
    {
        $model = $ringUser->with(['user:id,name', 'orders.ringBrand:id'])
                            ->withTrashed();

        // 關鍵字 keyword
        if (request('keyword')) {
            $keywordSearchService = resolve(SearchService::class);
            $model = $keywordSearchService->search($model, request('keyword'));
        }

        // 篩選日期-報名日期 date_type:chehckin|order
        if (request('date_type') == 'chehckin' && request('date_range')) {
            $model = $model->whereBetween('created_at', [
                                request('date_range')[0].' 00:00:00',
                                request('date_range')[1].' 23:59:59',
                            ]);
        }

        // 篩選日期-下訂日期 date_type:order
        if (request('date_type') == 'order' && request('date_range')) {
            $model = $model->whereHas('orders', function ($q) {
                                $q->whereBetween('date', request('date_range'));
                            });
        }

        // 到店數 point_count
        if (request('point_count') == 'more') {
            $model = $model->where('point_count', '>=', 6);
        } elseif (is_numeric(request('point_count'))) {
            $model = $model->where('point_count', request('point_count'));
        }

        // 累積點數 collect_count
        if (is_numeric(request('collect_count'))) {
            $model = $model->where('collect_count', request('collect_count'));
        }

        // 下訂品牌ID brand_id
        if (request('brand_id')) {
            $model = $model->whereHas('orders', function ($q) {
                                $q->where('ring_brand_id', request('brand_id'));
                            });
        }

        // 下訂款式 ring_style
        if (request('ring_style')) {
            $model = $model->whereHas('orders', function ($q) {
                                $q->where('ring_style', request('ring_style'));
                            });
        }

        // 是否未審核 is_pending
        if (request('is_pending')) {
            $model = $model->whereHas('orders', function ($q) {
                                $q->where('status', 'pending');
                            });
        }

        // 排序 sort:id(default)|created_at
        // 升降冪 direction:asc|desc(default)
        $sort = request('sort') ?: 'id';
        $direction = request('direction') ?: 'DESC';
        $model = $model->orderBy($sort, $direction);

        // 分頁 page
        if ($pagination) {
            $model = $model->paginate(20);
        } else {
            $model = $model->get();
        }

        return $model;
    }
}
