<?php

namespace App\Http\Controllers\RingEvent;

use App\Http\Controllers\Controller;
use App\Models\CityData;
use App\Models\RingMall;
use App\Models\RingBrand;
use App\Models\RingStore;
use App\Models\RingOrder;
use App\Models\RingUser;
use App\Models\User;
use App\Services\Mail\Auth\FounderLetterService;
use App\Services\Mail\Auth\EmailVerificationService;
use App\Services\Mail\RingEvent\AddReserveService;
use App\Services\Event\CreateReportQrcodeService;
use App\Jobs\Firebase\Message;
use App\Traits\ApiErrorTrait;
use App\Traits\RandStringTrait;
use App\Traits\Model\TransformerTrait;
use App\Traits\Auth\MakeAnonymousKeyTrait;
use App\Traits\Auth\CreateTokenTrait;
use App\Formatters\ApiFormatter;
use Illuminate\Support\Str;
use Imagick;

class FrontendController extends Controller
{
    use ApiErrorTrait;
    use RandStringTrait;
    use TransformerTrait;
    use MakeAnonymousKeyTrait;
    use CreateTokenTrait;

    /**
     * 活動主頁
     *
     * @url api/ring-event
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼
     * @return json
     */
    public function index(
        CityData $cityData,
        RingBrand $ringBrand,
        RingStore $ringStore,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 報名資訊
        $ringUser = NULL;
        if ($user && $user->ringUser) {
            $ringUser = [
                'name'         => $user->ringUser->name,
                'email'        => $user->ringUser->email,
                'phone'        => $user->ringUser->phone,
                'wedding_date' => $user->ringUser->wedding_date,
                'mate_name'    => $user->ringUser->mate_name,

                // 預約記錄
                'reserves' => $user->ringUser->allReserves->map(function($item) {
                                return $item->only(['ring_brand_id', 'ring_store_id', 'date', 'time', 'items']);
                            }),

                // 訂單列表
                'orders' => $user->ringUser->orders->map(function($item) {
                                return $item->only(['ring_brand_id', 'status']);
                            }),
            ];
        }

        return $formatter->json([

            // 縣市列表
            'city_list'  => $cityData->region()
                                        ->get()
                                        ->map(function($parent) {
                                            return [
                                                'title'    => $parent->title,
                                                'children' => $parent->children->map(function($children) {
                                                                return $children->only(['id', 'title']);
                                                            }),
                                            ];
                                        }),
            // 品牌列表
            'brand_list' => $ringBrand->select('id', 'name', 'full_name', 'link', 'image', 'reserve_limit_days', 'reserve_time_list', 'reserve_item_list')
                                        ->sort()
                                        ->get(),

            // 門市列表
            'store_list' => $ringStore->select('id', 'city_id', 'ring_brand_id', 'name', 'phone')
                                        ->sort()
                                        ->get(),

            // 報名資訊
            'ring_user'  => $ringUser,
        ]);
    }

    /**
     * 活動主頁-驗證報名資訊重複
     *
     * @url api/ring-event/unique-user
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @param string $column 欄位索引 name|email|phone *
     * @param arrary|string $value 欄位值 (if column='name' then value[]) *
     * @return json
     */
    public function uniqueUser(
        RingUser $ringUser,
        ApiFormatter $formatter
    ) {
        if (request('column') == 'name') {
            $ringUser = $ringUser->whereIn('name', request('value'))
                                    ->whereIn('mate_name', request('value'));
        } else {
            $ringUser = $ringUser->where(request('column'), request('value'));
        }

        if ($ringUser->whereNotNull('user_id')->exists()) {
            $this->setException('報名資訊重複囉！');
        }

        return $formatter->json();
    }

    /**
     * 活動主頁-儲存
     *
     * @url api/ring-event
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @param string $name 真實姓名 *
     * @param string $mate_name 另一半姓名 *
     * @param string $email Email *
     * @param string $phone 聯絡電話 *
     * @param date $wedding_date 婚期 *
     * @param json.array $reserves 預約記錄 *
     * @param bool $is_approve 是否同意加入會員 *
     * @return json
     */
    public function save(
        RingUser $ringUser,
        User $user,
        FounderLetterService $founderLetterService,
        EmailVerificationService $emailVerificationService,
        AddReserveService $addReserveService,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user      = request('user') ?: clone $user;
        $ringUser  = $user->ringUser ?? clone $ringUser;
        $authToken = $this->getNewToken();

        // 非登入的使用者，同意加入會員，且Email不存在資料庫
        if (!$user->exists && request('is_approve') && $user->where('email', request('email'))->doesntExist()) {

            // 註冊使用者
            $user = $user->create([
                'email'               => request('email'),
                'name'                => request('name'),
                'real_name'           => request('name'),
                'avatar'              => $user->present()->rand_avatar_image,
                'anonymous_key'       => $this->getAnonymousKey(),
                'phone'               => request('phone'),
                'email_verified_code' => Str::random(45),
            ]);

            // 新增婚期
            $user->weddingTypes()->create(['type' => 2, 'date' => request('wedding_date')]);

            // 修改密碼
            $user->pwd_tokens()->create([
                'target_id'   => $user->id,
                'type'        => 'user_password',
                'token'       => $authToken,
                'deadline_at' => now()->addDay(),
            ]);

            // 來自於好婚市集共同創辦人的一封信
            $founderLetterService->sendMail($user);
            // 好婚市集信箱驗證信
            $emailVerificationService->sendMail($user);
        }

        // 更新報名資訊
        $insertData = [
            'name'          => request('name'),
            'mate_name'     => request('mate_name'),
            'email'         => request('email'),
            'phone'         => request('phone'),
            'wedding_date'  => request('wedding_date'),
            'reserve_times' => $ringUser->reserve_times ? $ringUser->reserve_times + 1 : 1,
            'auth_token'    => ($user->exists && !$user->wasRecentlyCreated) ? NULL : $authToken,
        ];
        if ($user->exists) {
            $ringUser = $ringUser->updateOrCreate(['user_id' => $user->id], $insertData);
        } else {
            $ringUser = $ringUser->create($insertData);
        }

        // 找出已預約 & 已上傳訂單的品牌ID
        $reserveBrandIds = $ringUser->allReserves->pluck('ring_brand_id');
        $orderBrandIds   = $ringUser->orders->pluck('ring_brand_id');
        $userBrandIds    = $reserveBrandIds->merge($orderBrandIds);
        $reservesObj     = json_decode(request('reserves'));
        foreach ($reservesObj as $item) {

            // 排除已預約 & 已上傳訂單
            if ($userBrandIds->contains($item->brand_id)) {
                continue;
            }

            // 新增預約紀錄
            $ringUser->allReserves()->create([
                'ring_brand_id' => $item->brand_id,
                'ring_store_id' => $item->store_id,
                'date'          => $item->date,
                'time'          => $item->time ?? NULL,
                'items'         => $item->items ?? NULL,
                'created_at'    => now(),
            ]);
        }

        // 新增預約 Email通知
        $addReserveService->sendMail($ringUser);

        return $formatter->json([
            'auth_token' => $ringUser->auth_token,
        ]);
    }

    /**
     * 活動主頁-取得綁定帳號
     *
     * @url api/ring-event/bind-user/{authToken}
     * @method GET
     * @header string Access-Token *
     * @path string $authToken 身份驗證碼 *
     * @return json
     */
    public function getBindUser(
        RingUser $ringUser,
        User $user,
        ApiFormatter $formatter,
        $authToken
    ) {
        // 取得預約資訊
        $ringUser = $ringUser->where('auth_token', $authToken)->first();
        if (!$ringUser) {
            $this->setException('身份驗證錯誤！');
        }

        // Email是否存在資料庫
        $existUser = $user->where('email', $ringUser->email)->first();
        if ($existUser) {
            $existUser = [
                'name'         => $existUser->name,
                'phone'        => $existUser->phone,
                'avatar'       => $existUser->present()->display_avatar,
                'has_password' => (bool)$existUser->password,
            ];
        }

        return $formatter->json([
            'is_new'    => (bool)$ringUser->user_id,
            'ring_user' => [
                'email' => $ringUser->email,
                'name'  => $ringUser->name,
                'phone' => $ringUser->phone,
            ],
            'exist_user' => $existUser,
        ]);
    }

    /**
     * 門市-資訊
     *
     * @url api/ring-event/store/{ringStore}
     * @method GET
     * @header string Access-Token *
     * @path int $ring_store_id 門市ID *
     * @return json
     */
    public function storeInfo(
        RingStore $ringStore,
        ApiFormatter $formatter
    ) {
        return $formatter->json([
            'store_id'   => $ringStore->id,
            'brand_name' => $ringStore->ringBrand->name,
            'store_name' => $ringStore->name,
        ]);
    }

    /**
     * 門市-掃描集點
     *
     * @url api/ring-event/store/{ringStore}/scan
     * @method POST
     * @header string Access-Token *
     * @path int $ring_store_id 門市ID *
     * @param int $token 集點驗證碼 *
     * @return json
     */
    public function storeScan(
        RingStore $ringStore,
        RingUser $ringUser,
        ApiFormatter $formatter
    ) {
        // 測試驗證碼
        $token = request('token');
        if (!is_numeric($token)) {
            $this->setException('驗證碼錯誤！', 4001);
        } elseif ($token == '0000') {
            $this->setException('一組新人只能集點一次！');
        } elseif ($token == '1111') {
            return $formatter->json();
        }

        // 檢查驗證碼
        $ringUser = $ringUser->validToken($token)->first();
        if (!$ringUser) {
            $this->setException('驗證碼錯誤！', 4001);
        }

        // 檢查重複集點
        if ($ringUser->points()->where('ring_brand_id', $ringStore->ring_brand_id)->exists()) {
            $this->setException('一組新人只能集點一次！');
        }

        // 截止驗證碼的有效期限
        // $ringUser->deadline_at = now();
        // $ringUser->save();

        // 新增點數紀錄
        $ringUser->points()->create([
            'ring_brand_id' => $ringStore->ring_brand_id,
            'ring_store_id' => $ringStore->id,
            'created_at'    => now(),
        ]);

        // 更新報名資訊的到店數&累積點數
        $ringUser->updateCollectPointCount();

        // 集點通知
        if ($ringUser->collect_count % 2) {

            // 即時通訊處理 use Job
            Message::dispatch('ring_event_collect_point', $ringUser);
        }

        // 回填預約記錄
        if ($ringUser->allReserves()->where('ring_store_id', $ringStore->id)->doesntExist()) {
            $ringUser->allReserves()->create([
                'ring_brand_id' => $ringStore->ring_brand_id,
                'ring_store_id' => $ringStore->id,
                'date'          => now()->format('Y-m-d'),
                'type'          => 'point',
                'created_at'    => now(),
            ]);
        }

        return $formatter->json();
    }

    /**
     * 婚禮中心-集點趣
     *
     * @url api/ring-event/user
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @return json
     */
    public function userInfo(
        CityData $cityData,
        RingMall $ringMall,
        RingBrand $ringBrand,
        RingStore $ringStore,
        RingOrder $ringOrder,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 預約記錄
        $reserves = [];
        if ($user->ringUser) {
            foreach ($user->ringUser->reserves->groupBy('date') as $date => $items) {
                $reserves[] = [
                    'date' => $date,
                    'list' => $items->map(function($item) {
                                    return $item->only(['ring_brand_id', 'ring_store_id']);
                                }),
                ];
            }
        }

        return $formatter->json([
            'city_list'       => $cityData->select('id', 'title')->whereNotNull('parent_id')->sort()->get(), // 縣市列表
            'mall_list'       => $ringMall->select('id', 'city_id', 'name', 'address', 'map_link')->sort()->get(), // 百貨公司列表
            'brand_list'      => $ringBrand->select('id', 'name', 'link', 'point_image')->sort()->get(), // 品牌列表
            'store_list'      => $ringStore->sort()->get(), // 門市列表
            'ring_style_list' => $this->formatAttributeList($ringOrder->ringStyleList), // 下訂款式
            'checkin_email'   => $user->ringUser->email ?? '', // 報名資訊中的email
            'point_brands'    => $user->ringUser ? $user->ringUser->points->pluck('ring_brand_id') : [], // 點數列表
            'orders'          => $user->ringUser ? $user->ringUser->orders : [], // 訂單列表
            'reserves'        => $reserves, // 預約記錄
        ]);
    }

    /**
     * 婚禮中心-產生Qrcode
     *
     * @url api/ring-event/user/create-qrcode
     * @method POST
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @return json
     */
    public function userCreateQrcode(
        RingUser $ringUser,
        CreateReportQrcodeService $createReportQrcodeService,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 檢查是否有報名資訊
        if (!$user->ringUser) {
            $this->setException('請先填寫活動表單預約，即可開啟集點卡！');
        }

        // 檢查重複驗證碼
        $token = $this->getRandString(4, ['number']);
        while ($ringUser->validToken($token)->exists()) {
            $token = $this->getRandString(4, ['number']);
        }

        // 更新驗證碼
        $user->ringUser->deadline_at = now()->addMinutes(5);
        $user->ringUser->token       = $token;
        $user->ringUser->save();

        return $formatter->json([
            'qrcode' => $createReportQrcodeService->generate($token, true),
            'token'  => $token,
        ]);
    }

    /**
     * 婚禮中心-檢查集點狀態
     *
     * @url api/ring-event/user/check-scan
     * @method GET
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @param int $token 集點驗證碼 *
     * @return json
     */
    public function userCheckScan(
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 檢查是否有報名資訊
        if (!$user->ringUser) {
            $this->setException('請先填寫活動表單預約，即可開啟集點卡！');
        }

        if (
            $user->ringUser->token == request('token') &&
            $user->ringUser->points()->where('created_at', '>', now()->subSeconds(10))->exists()
        ) {
            return $formatter->json();
        }

        $this->setException('尚未集點成功！');
    }

    /**
     * 婚禮中心-儲存訂單
     *
     * @url api/ring-event/user/save-order
     * @method POST
     * @header string Access-Token *
     * @header string User-Token 使用者驗證碼 *
     * @param string $name 真實姓名
     * @param string $mate_name 另一半姓名
     * @param string $email Email
     * @param string $phone 聯絡電話
     * @param date $wedding_date 婚期
     * @param int $order_id 訂單ID
     * @param int $brand_id 品牌ID *
     * @param date $date 訂購日期 *
     * @param int $price 訂購總額 *
     * @param string $ring_style 款式 diamond:鑽戒 lovers:對戒 gold:金飾 other:其他 *
     * @param string $ring_style_other 其他款式說明 (required_if: ring_style=other)
     * @param string $image 訂單收據 *
     * @param string $agent_image 親友幫買證明
     * @return json
     */
    public function userSaveOrder(
        RingUser $ringUser,
        ApiFormatter $formatter
    ) {
        // 取得使用者
        $user = request('user');

        // 新增報名資訊
        if (!$user->ringUser) {
            $user->ringUser = $user->ringUser()->create([
                'name'         => request('name'),
                'mate_name'    => request('mate_name'),
                'email'        => request('email'),
                'phone'        => request('phone'),
                'wedding_date' => request('wedding_date'),
            ]);
        }

        // 儲存訂單
        $user->ringUser->orders()->updateOrCreate([
            'id' => request('order_id'),
        ], [
            'ring_brand_id'    => request('brand_id'),
            'date'             => request('date'),
            'price'            => request('price'),
            'ring_style'       => request('ring_style'),
            'ring_style_other' => request('ring_style_other'),
            'image'            => request('image'),
            'agent_image'      => request('agent_image'),
            'status'           => 'pending',
        ]);

        return $formatter->json();
    }

    /**
     * 取得測試的 QRcode image
     *
     * @url api/ring-event/qrcode-image
     * @method GET
     * @param string $token QRcode Token *
     * @return image
     */
    public function getQrcodeImage(
        CreateReportQrcodeService $createReportQrcodeService
    ) {
        $imageBlob = $createReportQrcodeService->generate(request('token'));

        $imagick = new Imagick();
        $imagick->readImageBlob($imageBlob);

        return response($imagick)->header('Content-Type', 'image/png');
    }
}
