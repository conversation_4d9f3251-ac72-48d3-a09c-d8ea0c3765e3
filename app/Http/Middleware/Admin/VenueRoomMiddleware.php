<?php
/*
 |--------------------------------------
 |  此商家必須擁有，Router中的婚宴場地廳房編號 (request()->route()->room_id)
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class VenueRoomMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 此商家必須擁有，Router中的婚宴場地廳房編號
        $store     = request()->route()->store;
        $venueRoom = request()->route()->venueRoom;

        if (!$store->venueRooms->find($venueRoom->id)) {
            $this->setException('找不到此商家擁有的婚宴場地廳房！', 3006);
        }

        return $next($request);
    }

}
