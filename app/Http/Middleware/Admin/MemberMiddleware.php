<?php
/*
 |--------------------------------------
 |  此商家必須擁有，Router中的成員編號 (request()->route()->member_id)
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class MemberMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 此商家必須擁有，Router中的影片編號
        $store       = request()->route()->store;
        $storeMember = request()->route()->storeMember;

        if (!$store->members->find($storeMember->id)) {
            $this->setException('找不到此商家擁有的影片！', 3006);
        }

        return $next($request);
    }

}
