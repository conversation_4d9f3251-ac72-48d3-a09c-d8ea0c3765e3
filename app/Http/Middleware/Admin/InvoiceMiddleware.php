<?php
/*
 |--------------------------------------
 |  此商家必須擁有，Router中的發票記錄編號 (request()->route()->invoice_id)
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class InvoiceMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 此商家必須擁有，Router中的發票記錄編號
        $store   = request()->route()->store;
        $invoice = request()->route()->invoice;

        if (!$store->invoiceSetting->invoices->find($invoice->id)) {
            $this->setException('找不到此商家擁有的發票記錄！', 3006);
        }

        return $next($request);
    }

}
