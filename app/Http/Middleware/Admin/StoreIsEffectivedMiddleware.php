<?php
/*
 |--------------------------------------
 |  驗證此商家必須是已生效的商家
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class StoreIsEffectivedMiddleware
{
    use ApiErrorTrait;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 商家後台的登入身份，必須要擁有Router中的商家編號
        $store = request()->route()->store;
        if (!$store->present()->is_effectived) {
            $this->setException('這個商家已經下架囉！');
        }

        return $next($request);
    }

}
