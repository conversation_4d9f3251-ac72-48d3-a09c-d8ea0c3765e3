<?php
/*
 |--------------------------------------
 |  商家後台的訪問權限
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Models\AdminPermission;
use App\Traits\ApiErrorTrait;
use Closure;

class AccessPermissionMiddleware
{
    private $adminPermission;

    use ApiErrorTrait;

    public function __construct(AdminPermission $adminPermission)
    {
        $this->adminPermission = $adminPermission;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 驗證必填
        if (!$request->current_url) {
            $this->setException('請輸入目前網址！');
        }

        // 取代商家ID {store_id}
        $store    = $request->store;
        $parseUrl = parse_url($request->current_url);
        $linkPath = preg_replace("/{$store->id}/",'{store_id}', $parseUrl['path'], 1); // 只取網址目錄、並只取代一次

        // 取代詳細頁ID {target_id}
        $pathArray = explode('/', $linkPath);
        foreach ($this->adminPermission->infoPageTypes as $type => $value) { // 所有詳細頁的類型
            $typeKey = array_search($type, $pathArray);
            if (in_array($type, $pathArray) && isset($pathArray[$typeKey + 1])) { // 有找到詳細頁的類型
                $targetId = $pathArray[$typeKey + 1];
                if (is_numeric($targetId)) { // 有找到詳細頁的ID
                    $linkPath = preg_replace("/\/{$type}\/{$targetId}/", "/{$type}/{$value}", $linkPath, 1); // 只取代一次
                    break;
                }
            }
        }

        // 訊息管理/聯絡客服，差在參數不同
        if ($linkPath == '/admin/{store_id}/message' && isset($parseUrl['query'])) {
            parse_str($parseUrl['query'], $query);
            if (isset($query['admin_id']) && $query['admin_id'] == 1) {
                $linkPath = '/admin/{store_id}/message?admin_id=1';
            }
        }

        // 驗證目前網址
        $adminPermission = $this->adminPermission->where('link_path', $linkPath)
                                                    ->storeType($store->type)
                                                    ->storeStatus($store->status)
                                                    ->first();
        if (!$adminPermission) {
           $this->setException('找不到此頁面！', 4017);
        }

        // 是否有發票管理
        if (stripos($linkPath, '/admin/{store_id}/invoice') !== false && !$store->invoiceSetting) {
            $this->setException('沒有此頁面的訪問權限！', 4018);
        }

        // 將 admin_permission 資料放置request，取得方式為 request('admin_permission')
        $request->merge(['admin_permission' => $adminPermission]);

        return $next($request);
    }

}
