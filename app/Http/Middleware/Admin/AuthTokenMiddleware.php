<?php
/*
 |--------------------------------------
 |  store-token 轉成 Admin 資料
 |  強制必須要有 store-token，且取得 Admin資料，否則中斷
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Services\Auth\TokenToAdminService;
use App\Traits\ApiErrorTrait;
use Closure;

class AuthTokenMiddleware
{
    private $tokenToAdminService;

    use ApiErrorTrait;

    public function __construct(TokenToAdminService $tokenToAdminService)
    {
        $this->tokenToAdminService = $tokenToAdminService;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!request()->header('Store-Token')) {
            $this->setException('需要登入管理者才能允許操作！', 3003);
        }
        $storeUser = $this->tokenToAdminService->get(request()->header('Store-Token'));
        if (!$storeUser) {
            $this->setException('找不到此管理者！', 3003);
        }
        if ($storeUser->status == 'delete') {
            $this->setException('此管理者帳號已停用！', 3003);
        }

        // 將 storeUser 資料放置request，取得方式為 request('store_user')
        request()->merge(['store_user' => $storeUser]);

        return $next($request);
    }

}
