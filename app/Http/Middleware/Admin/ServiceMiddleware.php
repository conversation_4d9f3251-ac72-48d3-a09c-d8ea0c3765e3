<?php
/*
 |--------------------------------------
 |  此商家必須擁有，Router中的方案編號 (request()->route()->service_id)
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class ServiceMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 此商家必須擁有，Router中的方案編號
        $store        = request()->route()->store;
        $storeService = request()->route()->storeService;

        if (!$store->services->find($storeService->id)) {
            $this->setException('找不到此商家擁有的方案！', 3006);
        }

        return $next($request);
    }

}
