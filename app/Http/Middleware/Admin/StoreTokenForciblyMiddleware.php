<?php
/*
 |--------------------------------------
 |  Store-Token 轉成 storeUser 資料
 |  強制必須要有 User-Token，且取得 User資料，否則中斷
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Services\Auth\TokenToAdminService;
use App\Traits\ApiErrorTrait;
use Closure;

class StoreTokenForciblyMiddleware
{
    private $tokenToAdminService;

    use ApiErrorTrait;

    public function __construct(TokenToAdminService $tokenToAdminService)
    {
        $this->tokenToAdminService = $tokenToAdminService;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!request()->header('Store-Token')) {
            $this->setException('需要登入才能允許操作！', 3001, NULL, false);
        }
        $user = $this->tokenToAdminService->get(request()->header('Store-Token'));
        if (!$user) {
            $this->setException('找不到此會員！', 3001);
        }
        if ($user->status == 'delete') {
            $this->setException('此會員帳號已停用！', 3001);
        }

        // 將 User 資料放置request，取得方式為 request('store_user')
        request()->merge(['store_user' => $user]);

        return $next($request);
    }

}
