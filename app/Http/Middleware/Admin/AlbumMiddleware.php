<?php
/*
 |--------------------------------------
 |  此商家必須擁有，Router中的相本集編號 (request()->route()->album_id)
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class AlbumMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 此商家必須擁有，Router中的相本集編號
        $store      = request()->route()->store;
        $storeAlbum = request()->route()->storeAlbum;

        if (!$store->albums->find($storeAlbum->id)) {
            $this->setException('找不到此商家擁有的相本！', 3006);
        }

        return $next($request);
    }

}
