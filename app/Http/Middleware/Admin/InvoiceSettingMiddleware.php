<?php
/*
 |--------------------------------------
 |  此商家必須擁有發票設定
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class InvoiceSettingMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!request()->route()->store->invoiceSetting) {
            $this->setException('找不到此商家的發票設定！', 3006);
        }

        return $next($request);
    }

}
