<?php
/*
 |--------------------------------------
 |  商家後台的登入身份，必須要擁有Router中的商家編號 (request()->route()->store_id)
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Admin;

use App\Traits\ApiErrorTrait;
use Closure;

class StoreMiddleware
{
    use ApiErrorTrait;

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 驗證 Header Store-Token
        $storeUser = request('store_user');
        if (!$storeUser) {
            $this->setException('需要登入管理者才能允許操作！', 3006);
        }

        // 商家後台的登入身份，必須要擁有Router中的商家編號
        $store = request()->route()->store;
        if (!$storeUser->liveStores->find($store->id)) {
            $this->setException('找不到此帳號擁有的商家！', 3006);
        }

        return $next($request);
    }

}
