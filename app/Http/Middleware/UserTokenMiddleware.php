<?php
/*
 |--------------------------------------
 |  User-Token 轉成 User 資料
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware;

use App\Services\Auth\TokenToUserService;
use Closure;

class UserTokenMiddleware
{
    private $tokenToUserService;

    public function __construct(TokenToUserService $tokenToUserService)
    {
        $this->tokenToUserService = $tokenToUserService;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (request()->header('User-Token')) {
            $user = $this->tokenToUserService->get(request()->header('User-Token'));
            if ($user && $user->status != 'delete') {
                // 將User資料放置request，取得方式為 request('user')
                request()->merge(['user' => $user]);
            }
        }

        return $next($request);
    }

}
