<?php
/*
 |--------------------------------------
 |  商家類型名稱
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Store;

use App\Models\Store;
use Closure;

class StoreTypeNameMiddleware
{
    private $store;

    public function __construct(Store $store)
    {
        $this->store = $store;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 若沒有商家類型，有商家類型名稱
        if (!$request->store_type && $request->store_type_key) {
            $storeType = array_search($request->store_type_key, $this->store->typeKeyList);

            // 將商家類型資料放置request，取得方式為 request('store_type')
            request()->merge(['store_type' => $storeType]);
        }

        // 若有商家類型
        if ($request->store_type) {

            // 將商家類型名稱資料放置request，取得方式為 request('store_type_key')
            request()->merge(['store_type_key' => $this->store->typeKeyList[$request->store_type]]);
        }

        return $next($request);
    }

}
