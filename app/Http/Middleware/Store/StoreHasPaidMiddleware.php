<?php
/*
 |--------------------------------------
 |  驗證有效商家
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Store;

use App\Models\Store;
use App\Traits\ApiErrorTrait;
use Closure;

class StoreHasPaidMiddleware
{
    private $store;

    use ApiErrorTrait;

    public function __construct(Store $store)
    {
        $this->store = $store;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 取得商家
        $store = $this->store->find($request->store_id);
        if (!$store) {
            $this->setException('找不到這個商家！');
        }

        if (!$store->present()->has_paid) {
            $this->setException('這個商家已經下架囉！');
        }

        // 將商家資料放置request，取得方式為 request('store')
        request()->merge(['store' => $store]);

        return $next($request);
    }

}
