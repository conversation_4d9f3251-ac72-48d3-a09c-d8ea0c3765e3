<?php
/*
 |--------------------------------------
 |  User-Token 轉成 User 資料
 |  強制必須要有 User-Token，且取得 User資料，否則中斷
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware;

use App\Services\Auth\TokenToUserService;
use App\Traits\ApiErrorTrait;
use Closure;

class UserTokenForciblyMiddleware
{
    private $tokenToUserService;

    use ApiErrorTrait;

    public function __construct(TokenToUserService $tokenToUserService)
    {
        $this->tokenToUserService = $tokenToUserService;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!request()->header('User-Token')) {
            $this->setException('需要登入才能允許操作！', 3001, NULL, false);
        }
        $user = $this->tokenToUserService->get(request()->header('User-Token'));
        if (!$user) {
            $this->setException('找不到此會員！', 3001);
        }
        if ($user->status == 'delete') {
            $this->setException('此會員帳號已停用！', 3001);
        }

        // 將 User 資料放置request，取得方式為 request('user')
        request()->merge(['user' => $user]);

        return $next($request);
    }

}
