<?php
/*
 |--------------------------------------
 |  驗證活動
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Event;

use App\Models\Event;
use App\Traits\ApiErrorTrait;
use Closure;

class EventMiddleware
{
    private $event;

    use ApiErrorTrait;

    public function __construct(Event $event)
    {
        $this->event = $event;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 取得活動報名資訊
        $event = $this->event->where('path', $request->path)->first();
        if (!$event) {
            $this->setException('找不到活動報名表單！');
        }

        // 將活動報名資訊放置request，取得方式為 request('event')
        request()->merge(['event' => $event]);

        return $next($request);
    }

}
