<?php
/*
 |--------------------------------------
 |  驗證限作者的文章
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Forum;

use App\Models\ForumArticle as Article;
use App\Traits\ApiErrorTrait;
use Closure;

class ArticleAuthorMiddleware
{
    private $article;

    use ApiErrorTrait;

    public function __construct(Article $article)
    {
        $this->article = $article;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 取得文章
        $article = $this->article->live()->find($request->article_id);
        if (!$article) {
            $this->setException('找不到這篇文章！');
        }

        // 驗證刪除文章
        if ($article->status == 'softdelete') {
            $this->setException('這篇文章已經被刪除囉！');
        }

        // 驗證文章作者
        if ($article->user_id != $request['user']->id) {
            $this->setException('找不到你的文章！');
        }

        // 將 Article 資料放置request，取得方式為 request('article')
        request()->merge(['article' => $article]);

        return $next($request);
    }

}
