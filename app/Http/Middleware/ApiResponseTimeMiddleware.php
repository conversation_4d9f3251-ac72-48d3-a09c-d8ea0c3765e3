<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\LogApiLongQuery;

class ApiResponseTimeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 記錄開始時間
        $startTime = microtime(true);

        // 執行 API
        $response = $next($request);

        // 記錄結束時間
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 3); // 執行時間（秒）

        // 若執行時間超過 1 秒，記錄到資料庫
        if ($executionTime > 1) {
            $log = new LogApiLongQuery();
            $log->path = $request->getRequestUri();
            $log->action = $request->route()->getActionName();
            $log->query_duration = $executionTime;
            $log->save();
        }

        // 在 Response Header 附加執行時間
        return $response->header('X-Execution-Time', $executionTime . 's');
    }
}
