<?php
/*
 |--------------------------------------
 |  驗證品牌
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Yzcube;

use App\Models\Brand;
use App\Traits\ApiErrorTrait;
use Closure;

class BrandMiddleware
{
    private $brand;

    use ApiErrorTrait;

    public function __construct(
        Brand $brand
    ) {
        $this->brand = $brand;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 取得品牌
        $brand = $this->brand
                        ->with('stores')
                        ->with(['articles' => function($query) {
                            $query->orderBy('published_at')
                                    ->paginate(10);
                        }])
                        ->where('id', $request->brand_id)
                        ->withCount('articles')
                        ->withCount('stores')
                        ->first();
        if (!$brand) {
            $this->setException('找不到這個品牌！');
        }

        // 將商家資料放置request，取得方式為 request('brand')
        request()->merge(['brand' => $brand]);

        return $next($request);
    }

}
