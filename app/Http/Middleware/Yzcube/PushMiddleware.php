<?php
/*
 |--------------------------------------
 |  驗證全站推播的接收者類型
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Yzcube;

use App\Models\PushMessage;
use App\Traits\ApiErrorTrait;
use Closure;

class PushMiddleware
{
    private $pushMessage;

    use ApiErrorTrait;

    public function __construct(
        PushMessage $pushMessage
    ) {
        $this->pushMessage = $pushMessage;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 驗證接收者類型
        if (!in_array($request->receiver_type, array_keys($this->pushMessage->receiverTypeList))) {
            $this->setException('接收者類型有誤！');
        }

        return $next($request);
    }

}
