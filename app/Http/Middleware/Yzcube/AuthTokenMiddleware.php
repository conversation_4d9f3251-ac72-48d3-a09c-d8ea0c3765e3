<?php
/*
 |--------------------------------------
 |  Yzcube-Token 轉成 Yzcube 資料
 |  強制必須要有 Yzcube-Token，且取得 Yzcube 資料，否則中斷
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Yzcube;

use App\Services\Auth\TokenToYzcubeService;
use App\Traits\ApiErrorTrait;
use Closure;

class AuthTokenMiddleware
{
    private $tokenToYzcubeService;

    use ApiErrorTrait;

    public function __construct(TokenToYzcubeService $tokenToYzcubeService)
    {
        $this->tokenToYzcubeService = $tokenToYzcubeService;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!request()->header('Yzcube-Token')) {
            $this->setException('需要登入管理者才能允許操作！', 3002);
        }
        $yzcubeUser = $this->tokenToYzcubeService->get(request()->header('Yzcube-Token'));
        if (!$yzcubeUser) {
            $this->setException('找不到此管理者！', 3002);
        }
        if ($yzcubeUser->status != 'published') {
            $this->setException('此管理者帳號尚未驗證！', 3002);
        }

        // 將 yzcubeUser 資料放置request，取得方式為 request('yzcube_user')
        request()->merge(['yzcube_user' => $yzcubeUser]);

        return $next($request);
    }

}
