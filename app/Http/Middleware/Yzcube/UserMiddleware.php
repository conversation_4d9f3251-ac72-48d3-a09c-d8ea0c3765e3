<?php
/*
 |--------------------------------------
 |  驗證新娘
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Yzcube;

use App\Models\User;
use App\Traits\ApiErrorTrait;
use Closure;

class UserMiddleware
{
    private $user;

    use ApiErrorTrait;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // 取得新娘資料
        $user = $this->user->withTrashed()->find($request->user_id);
        if (!$user) {
            $this->setException('找不到這位新娘！');
        }

        // 將 Post 資料放置request，取得方式為 request('post')
        request()->merge(['user' => $user]);

        return $next($request);
    }

}
