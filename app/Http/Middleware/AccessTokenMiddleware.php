<?php
/*
 |--------------------------------------
 |  Access-Token 驗證存取權狀
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware;

use App\Repositories\SystemInfoRepository;
use App\Traits\ApiErrorTrait;
use Closure;

class AccessTokenMiddleware
{
    private $systemInfoRepository;

    use ApiErrorTrait;

    public function __construct(SystemInfoRepository $systemInfoRepository)
    {
        $this->systemInfoRepository = $systemInfoRepository;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (request()->header('Access-Token') != $this->systemInfoRepository->get('access_token')) {
            $this->setException('存取權杖錯誤！', 3005);
        }

        return $next($request);
    }
}
