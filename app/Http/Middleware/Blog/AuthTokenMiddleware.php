<?php
/*
 |--------------------------------------
 |  Blog-Token 轉成 Blog 資料
 |  強制必須要有 Blog-Token，且取得 Blog 資料，否則中斷
 |--------------------------------------
 |
 |
 */


namespace App\Http\Middleware\Blog;

use App\Services\Auth\TokenToBlogService;
use App\Traits\ApiErrorTrait;
use Closure;

class AuthTokenMiddleware
{
    private $tokenToBlogService;

    use ApiErrorTrait;

    public function __construct(TokenToBlogService $tokenToBlogService)
    {
        $this->tokenToBlogService = $tokenToBlogService;
    }

    /**
     * 執行請求過濾器。
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (!request()->header('Blog-Token')) {
            $this->setException('需要登入管理者才能允許操作！', 3002);
        }
        $blogUser = $this->tokenToBlogService->get(request()->header('Blog-Token'));
        if (!$blogUser) {
            $this->setException('找不到此管理者！', 3002);
        }
        if ($blogUser->status != 'published') {
            $this->setException('此管理者帳號尚未驗證！', 3002);
        }

        // 將 blogUser 資料放置request，取得方式為 request('blog_user')
        request()->merge(['blog_user' => $blogUser]);

        return $next($request);
    }

}
