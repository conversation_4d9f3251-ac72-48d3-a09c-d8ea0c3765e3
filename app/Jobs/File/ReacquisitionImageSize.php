<?php
/*
 |--------------------------------
 |  重新處理圖片的寬高資訊
 |--------------------------------
 */
namespace App\Jobs\File;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Image;
use App\Services\Image\CheckImageService;

class ReacquisitionImageSize implements ShouldQueue
{
    private $imageId;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        $imageId
    ) {
        $this->imageId = $imageId;
    }

    /**
     * 重新處理圖片的寬高資訊
     *
     * @return void
     */
    public function handle(
        Image $image,
        CheckImageService $checkImageService
    ) {
        // 找出 Image Model
        $image = $image->where('id', $this->imageId)
                        ->where(function($q) {
                            $q->whereNull('width')->orWhereNull('height');
                        })
                        ->first();
        if (!$image) {
            return;
        }

        // 檢查圖片及縮圖
        $checkImageService->run($image);
    }
}
