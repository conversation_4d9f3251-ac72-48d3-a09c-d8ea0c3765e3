<?php

namespace App\Jobs\Event;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Event\GoogleCalendarService;

class AddGoogleCalendar implements ShouldQueue
{
    private $calendarEventId;
    private $email;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($calendarEventId, $email)
    {
        $this->calendarEventId = $calendarEventId;
        $this->email           = $email;
    }

    /**
     * Google行事曆-新增與會者邀請事件
     *
     * @return void
     */
    public function handle(GoogleCalendarService $googleCalendarService)
    {
        $googleCalendarService->addAttendee($this->calendarEventId, $this->email);
    }
}
