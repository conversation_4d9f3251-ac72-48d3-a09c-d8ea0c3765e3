<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\ArticleAtUsersService;
use App\Models\ForumArticle as Article;

class SendEmailArticleAtUsers implements ShouldQueue
{
    private $article;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Article $article)
    {
        $this->article = $article;
    }

    /**
     * WeddingDay 文章標記通知
     *
     * @return void
     */
    public function handle(ArticleAtUsersService $articleAtUsersService)
    {
        // 過濾已寄送的user
        $users = $this->article->atUsersFilterUnsendEmail;
        foreach ($users as $user) {

            // 有人在文章中提到您
            $articleAtUsersService->sendMail($user, $this->article);

            // 標記已寄送
            $user->pivot->send_email = 1;
            $user->pivot->save();
        }
    }
}
