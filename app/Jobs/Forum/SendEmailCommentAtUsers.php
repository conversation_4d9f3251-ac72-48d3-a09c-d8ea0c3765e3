<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\CommentAtUsersService;
use App\Models\ForumComment as Comment;

class SendEmailCommentAtUsers implements ShouldQueue
{
    private $comment;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Comment $comment)
    {
        $this->comment = $comment;
    }

    /**
     * WeddingDay 留言標記通知
     *
     * @return void
     */
    public function handle(CommentAtUsersService $commentAtUsersService)
    {
        // 過濾已寄送的user
        $users = $this->comment->atUsersFilterUnsendEmail;
        foreach ($users as $user) {

            // 有人在留言中提到您
            $commentAtUsersService->sendMail($user, $this->comment);

            // 標記已寄送
            $user->pivot->send_email = 1;
            $user->pivot->save();
        }
    }
}
