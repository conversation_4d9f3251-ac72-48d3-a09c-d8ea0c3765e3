<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\NewComment\ArticleAuthorByCommentService;
use App\Services\Mail\Forum\NewComment\ParentCommentAuthorService;
use App\Models\ForumComment as Comment;

class SendEmailNewComment implements ShouldQueue
{
    private $comment;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Comment $comment)
    {
        $this->comment = $comment;
    }

    /**
     * WeddingDay 文章新留言通知
     *
     * @return void
     */
    public function handle(
        ArticleAuthorByCommentService $articleAuthorByCommentService,
        ParentCommentAuthorService $parentCommentAuthorService
    ) {
        // For文章原PO (排除自己的留言/回覆)，有人在您的文章留言給您(非回覆留言)
        if ($this->comment->user_id != $this->comment->article->user_id && !$this->comment->parent_id) {
            $articleAuthorByCommentService->sendMail($this->comment->article->author, $this->comment);
        }

        // 若是回覆留言
        if ($this->comment->parent_id) {

            // For留言原PO (排除自己的回覆)，有人回覆了您的留言
            if ($this->comment->user_id != $this->comment->parent->user_id) {
                $parentCommentAuthorService->sendMail($this->comment->parent->author, $this->comment);
            }
        }
    }
}
