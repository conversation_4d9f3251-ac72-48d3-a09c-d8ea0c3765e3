<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\NewArticleService;
use App\Services\Mail\Yzcube\ErrorNotificationService;
use App\Models\ForumArticle as Article;
use App\Models\MailCampaign;
use App\Models\User;
use App\Traits\Job\GetSqsClientTrait;
use App\Traits\Job\DelayAllocationTrait;
use Exception;
// use Log;

class SendEmailNewArticle implements ShouldQueue
{
    private $article;
    private $startDate;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use GetSqsClientTrait;
    use DelayAllocationTrait;

    /**
     * 隊列任務最大的嘗試次數
     *
     * @var int
     */
    public $tries = 1;

    /**
     * 隊列任務允許執行的最大秒數
     *
     * @var int
     */
    public $timeout = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Article $article)
    {
        // 能夠背景執行到結束
        ignore_user_abort(true);
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->article   = $article;
        $this->startDate = now();
    }

    /**
     * WeddingDay 發佈文章互動通知
     *
     * @return void
     */
    public function handle(
        MailCampaign $mailCampaign,
        User $userModel,
        NewArticleService $newArticleService
    ) {
        // 驗證EDM活動 (mail_campaign)
        if ($mailCampaign->where('type', 'forum_new_article')->where('target_id', $this->article->id)->exists()) {
            $this->sendErrorNotification('驗證EDM活動已存在，防止重複執行「發佈文章互動通知」Job Queue.');
            return;
        }

        // 近3個月內有互動的新娘
        $afterAt = now()->subMonths(3);
        $users   = $userModel->interactiveByAfterAt($afterAt)
                                // 排除取消訂閱
                                ->whereDoesntHave('subscription', function ($q1) {
                                    $q1->where('forum_new_article', 0);
                                })
                                ->get();
        $total    = $users->count();
        $sqsArgs  = [
            'client' => $this->getSqsClient(),
            'delay'  => 0,
        ];
        foreach ($users as $index => $user) {

            // 新娘發了一篇新文章，來看看吧！
            $sqsArgs['delay'] = $this->getDelayTimeByIndex($index, $total);
            $newArticleService->sendMail($user, $this->article, NULL, $sqsArgs);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        // 額外寄信給管理者，並顯示統計數量
        $sqsArgs['delay'] = $this->getDelayTimeByIndex($total, $total);
        $admins = $userModel->where('is_admin', 1)->get();
        foreach ($admins as $admin) {

            // 新娘發了一篇新文章，來看看吧！
            $newArticleService->sendMail($admin, $this->article, $total, $sqsArgs);
        }

        // Log紀錄
        // Log::info('WeddingDay 發佈文章互動通知', [
        //     'class'      => class_basename(get_class($this)),
        //     'seconds'    => now()->diffInSeconds($this->startDate),
        //     'article_id' => $this->article->id,
        //     'total'      => $total,
        // ]);
    }

    /**
     * 任務處理失敗
     *
     * @param  Exception  $e
     * @return void
     */
    public function failed(Exception $e)
    {
        $this->sendErrorNotification($e->getMessage());
    }

    /**
     * 實作錯誤通知
     *
     * @param  $message
     * @return void
     */
    private function sendErrorNotification($message)
    {
        $title    = '[錯誤通知] WeddingDay 發佈文章互動通知';
        $messages = [
            'class'      => class_basename(get_class($this)),
            'seconds'    => now()->diffInSeconds($this->startDate),
            'article_id' => $this->article->id,
            'message'    => $message,
        ];

        // Log紀錄
        // Log::error($title, $messages);

        // 錯誤通知
        $errorNotificationService = resolve('App\Services\Mail\Yzcube\ErrorNotificationService');
        $errorNotificationService->sendMail($title, $messages);
    }
}
