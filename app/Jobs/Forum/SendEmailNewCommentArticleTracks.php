<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\NewCommentArticleTracksService;
use App\Models\ForumComment as Comment;
use App\Models\User;

class SendEmailNewCommentArticleTracks implements ShouldQueue
{
    private $comment;
    private $user;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        Comment $comment,
        User $user
    ) {
        $this->comment = $comment;
        $this->user    = $user;
    }

    /**
     * WeddingDay 追蹤文章新留言通知
     *
     * @return void
     */
    public function handle(NewCommentArticleTracksService $newCommentArticleTracksService)
    {
        // 文章追蹤者
        $users = $this->comment->article->tracks()
                                        ->where('user_id', '!=', $this->user->id)
                                        ->get();
        foreach ($users as $user) {

            // 您追蹤的文章有新回應囉！
            $newCommentArticleTracksService->sendMail($user, $this->comment);
        }
    }
}
