<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\ForbiddenArticleService;
use App\Models\ForumArticle as Article;
use App\Repositories\UserRepository;

class SendEmailForbiddenArticle implements ShouldQueue
{
    private $article;
    private $forbiddenText;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        Article $article,
        $forbiddenText
    ) {
        $this->article       = $article;
        $this->forbiddenText = $forbiddenText;
    }

    /**
     * WeddingDay 禁言文章審核通知
     *
     * @return void
     */
    public function handle(
        UserRepository $userRepository,
        ForbiddenArticleService $forbiddenArticleService
    ) {
        // 寄信給管理者
        $admins = $userRepository->getData(['is_admin' => 1]);
        foreach ($admins as $admin) {

            // 需審核一篇好婚聊聊文章
            $forbiddenArticleService->sendMail($admin, $this->article, $this->forbiddenText);
        }
    }
}
