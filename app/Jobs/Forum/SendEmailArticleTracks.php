<?php

namespace App\Jobs\Forum;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\Forum\ArticleTracksService;
use App\Models\ForumArticle as Article;
use App\Models\User;

class SendEmailArticleTracks implements ShouldQueue
{
    private $article;
    private $user;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        Article $article,
        User $user
    ) {
        $this->article = $article;
        $this->user    = $user;
    }

    /**
     * WeddingDay 追蹤文章更新通知
     *
     * @return void
     */
    public function handle(ArticleTracksService $articleTracksService)
    {
        // 文章追蹤者
        $users = $this->article->tracks()
                                ->where('user_id', '!=', $this->user->id)
                                ->get();
        foreach ($users as $user) {

            // 您追蹤的文章更新囉！
            $articleTracksService->sendMail($user, $this->article);
        }
    }
}
