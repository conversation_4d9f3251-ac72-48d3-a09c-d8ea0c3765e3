<?php

namespace App\Jobs\Invoice;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Invoice;
use App\Traits\GoogleSheetTrait;
use Sheets;

class AddGoogleSheets implements ShouldQueue
{
    private $invoice;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    use GoogleSheetTrait;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Invoice $invoice)
    {
        $this->invoice = $invoice;
    }

    /**
     * 發票管理-Google試算表-新增紀錄
     *
     * @return void
     */
    public function handle()
    {
        // 初始化Google試算表
        $spreadsheet = Sheets::spreadsheet($this->invoice->setting->spreadsheet_id);

        // 若沒有工作表ID，就新增工作表並記錄ID
        $sheetId = $this->invoice->setting->sheet_id;
        if (!array_key_exists($sheetId, $spreadsheet->sheetList())) {
            $spreadsheet->addSheet('【勿動】'.$this->invoice->setting->seller_brand.'_發票記錄');
            $this->invoice->setting->sheet_id = array_key_last($spreadsheet->sheetList());
            $this->invoice->setting->save();
        }
        $sheet = $spreadsheet->sheetById($this->invoice->setting->sheet_id);

        // 更新試算表的欄位標題
        $headers = ["【勿動】\nID", '訂單編號', '買受人名稱', '買受人統編', '發票號碼', '開立日期', '銷售額(未稅)', '稅額(5%)', '總計', '備註', '品項', '金額', '自訂備註'];
        $sheet->range('A1')->update([$headers]);

        // 品項列表
        foreach ($this->invoice->items as $key => $item) {
            $data = [
                $this->invoice->id,
                $key ? '' : $this->invoice->order_no,
                $key ? '' : $this->invoice->buyer_name,
                $key ? '' : ($this->invoice->buyer_ubn ?: ''),
                $key ? '' : $this->invoice->invoice_number,
                $key ? '' : $this->invoice->created_at->format('Y-m-d H:i:s'),
                $key ? '' : $this->invoice->sales,
                $key ? '' : $this->invoice->tax,
                $key ? '' : $this->invoice->total,
                $key ? '' : str_replace(',', '，', $this->invoice->note),
                $item->name,
                $item->amount,
            ];

            // 新增一筆紀錄
            $this->appendRecordWithGoogleSheet($sheet, [$data]);
        }
    }
}
