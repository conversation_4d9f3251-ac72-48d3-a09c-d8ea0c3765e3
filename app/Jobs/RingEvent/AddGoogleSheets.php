<?php

namespace App\Jobs\RingEvent;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\RingReserve;
use App\Traits\GoogleSheetTrait;
use Sheets;

class AddGoogleSheets implements ShouldQueue
{
    private $ringReserve;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    use GoogleSheetTrait;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(RingReserve $ringReserve)
    {
        $this->ringReserve = $ringReserve;
    }

    /**
     * 婚戒大賞-Google試算表-新增紀錄
     *
     * @return void
     */
    public function handle()
    {
        // 初始化Google試算表
        $spreadsheet = Sheets::spreadsheet($this->ringReserve->ringBrand->spreadsheet_id);

        // 若沒有工作表ID，就新增工作表並記錄ID
        $sheetId = $this->ringReserve->ringBrand->sheet_id;
        if (!array_key_exists($sheetId, $spreadsheet->sheetList())) {
            $spreadsheet->addSheet('【勿動】'.$this->ringReserve->ringBrand->name.'_預約記錄');
            $this->ringReserve->ringBrand->sheet_id = array_key_last($spreadsheet->sheetList());
            $this->ringReserve->ringBrand->save();
        }
        $sheet = $spreadsheet->sheetById($this->ringReserve->ringBrand->sheet_id);

        // 更新試算表的欄位標題
        $headers = ['建立時間', '真實姓名', '另一半姓名', '聯絡電話', 'Email', '婚期', '品牌', '門市', '預約日期'];
        if ($this->ringReserve->ringBrand->reserve_time_list) {
            $headers[] = '預約時段';
        }
        if ($this->ringReserve->ringBrand->reserve_item_list) {
            $headers[] = '諮詢商品';
        }
        $headers[] = '系統備註';
        $sheet->range('A1')->update([$headers]);

        // 新增一筆紀錄
        $data = [
            date('Y-m-d H:i:s'),
            $this->ringReserve->ringUser->name,
            $this->ringReserve->ringUser->mate_name,
            $this->ringReserve->ringUser->phone,
            $this->ringReserve->ringUser->email,
            $this->ringReserve->ringUser->wedding_date,
            $this->ringReserve->ringBrand->name ?? '',
            $this->ringReserve->ringStore->name ?? '',
            $this->ringReserve->date,
        ];
        if ($this->ringReserve->ringBrand->reserve_time_list) {
            $data[] = $this->ringReserve->time ?? '';
        }
        if ($this->ringReserve->ringBrand->reserve_item_list) {
            $data[] = $this->ringReserve->items ? implode('、', $this->ringReserve->items) : '';
        }
        $data[] = $this->ringReserve->noteList[$this->ringReserve->type ?? 'reserve'];
        $this->appendRecordWithGoogleSheet($sheet, [$data]);
    }
}
