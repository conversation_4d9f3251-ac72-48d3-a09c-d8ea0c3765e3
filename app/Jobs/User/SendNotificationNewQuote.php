<?php

namespace App\Jobs\User;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Mail\User\NewQuoteUserService;
use App\Services\Mail\User\NewQuoteStoreService;
use App\Services\Mail\User\NewQuoteYzcubeService;
use App\Services\Line\NewQuoteStoreService as NewQuoteStoreLineService;
use App\Services\Line\NewQuoteYzcubeService as NewQuoteYzcubeLineService;
use App\Models\Wdv2\UserQuote;
use App\Repositories\StoreRepository;
use App\Repositories\UserRepository;
use App\Traits\Job\GetSqsClientTrait;
use App\Traits\Job\DelayAllocationTrait;
// use Log;

class SendNotificationNewQuote implements ShouldQueue
{
    private $userQuote;
    private $startDate;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use GetSqsClientTrait;
    use DelayAllocationTrait;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(UserQuote $userQuote)
    {
        $this->userQuote = $userQuote;
        $this->startDate = now();
    }

    /**
     * WeddingDay 主動報價新增通知
     *
     * @return void
     */
    public function handle(
        StoreRepository $storeRepository,
        UserRepository $userRepository,
        NewQuoteUserService $newQuoteUserService,
        NewQuoteStoreService $newQuoteStoreService,
        NewQuoteYzcubeService $newQuoteYzcubeService,
        NewQuoteStoreLineService $newQuoteStoreLineService,
        NewQuoteYzcubeLineService $newQuoteYzcubeLineService
    ) {

        // WeddingDay 我要詢價新增通知
        $newQuoteUserService->sendMail($this->userQuote);

        // 寄信通知商家
        $stores  = $storeRepository->getNewQuoteListByType($this->userQuote->type);
        $total   = $stores->count();
        $sqsArgs = [
            'client' => $this->getSqsClient(),
            'delay'  => 0,
        ];
        foreach ($stores as $index => $store) {

            // WeddingDay 主動報價的商家通知
            $sqsArgs['delay'] = $this->getDelayTimeByIndex($index, $total);
            $newQuoteStoreService->sendMail($store, $this->userQuote, $sqsArgs);

            // 測試環境寄一封意思意思就好拉，好嗎？
            if (env('APP_DEBUG')) {
                break;
            }
        }

        // 額外寄信給管理者，並顯示統計數量
        $sqsArgs['delay'] = $this->getDelayTimeByIndex($total, $total);
        $admins = $userRepository->getData(['is_admin' => 1]);
        foreach ($admins as $admin) {

            // WeddingDay 主動報價的管理者通知
            $newQuoteYzcubeService->sendMail($admin, $this->userQuote, $total, $sqsArgs);
        }

        // WeddingDay 主動報價的商家Line通知
        $newQuoteStoreLineService->sendLine($this->userQuote);

        // WeddingDay 主動報價的管理者Line通知
        $newQuoteYzcubeLineService->sendLine($this->userQuote);

        // Log紀錄
        // Log::info('WeddingDay 主動報價新增通知', [
        //     'class'         => class_basename(get_class($this)),
        //     'seconds'       => now()->diffInSeconds($this->startDate),
        //     'user_quote_id' => $this->userQuote->id,
        //     'total'         => $total,
        // ]);
    }
}
