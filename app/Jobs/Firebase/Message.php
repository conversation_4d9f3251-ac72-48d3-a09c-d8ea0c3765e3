<?php
/*
 |--------------------------------
 |  Firebase 訊息處理 use Job
 |--------------------------------
 */
namespace App\Jobs\Firebase;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Google\Firebase\Message\MessageHandle;

class Message implements ShouldQueue
{
    protected $type;
    protected $data;
    protected $messageHandle;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 隊列任務最大的嘗試次數
     *
     * @var int
     */
    public $tries = 1;

    /**
     * 隊列任務允許執行的最大秒數
     *
     * @var int
     */
    public $timeout = 0;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($type, $data)
    {
        // 能夠背景執行到結束
        ignore_user_abort(true);
        // 釋放memory_limit
        ini_set('memory_limit', '-1');
        // 設置最大執行時間,0為無限制
        set_time_limit(0);

        $this->type = $type;
        $this->data = $data;
    }

    /**
     * Firebase 訊息處理
     *
     * @return void
     */
    public function handle(
        MessageHandle $messageHandle
    ) {
        $messageHandle->handle($this->type, $this->data);
    }
}
