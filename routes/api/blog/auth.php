<?php
/*
|--------------------------------------------------------------------------
| Api Auth Blog Routes
|--------------------------------------------------------------------------
|
| 部落格 身份驗證
|
*/

// 登入: api/auth/blog/login
Route::post('login', 'AuthController@login');
Route::post('edit-password/{pwd_token}', 'Auth<PERSON>ontroller@editPassword');

// Middleware 驗證登入權限
Route::group(['middleware' => 'blog.token'], function () {
//     Route::get('check-token', 'AuthController@checkToken');
//     Route::get('check-access/{store}', 'Auth<PERSON><PERSON>roller@checkAccess')->middleware('blog.store', 'blog.access.permission');
    Route::get('logout', 'Auth<PERSON><PERSON>roller@logout');
});

// 作者帳號管理: /api/auth/blog
// Route::get('/', [AuthController::class, 'list'])->middleware('yzcube.token');
// Route::post('save', [AuthController::class, 'save'])->middleware('yzcube.token');
// Route::post('{yzcubeUser}/reset-password', [AuthController::class, 'resetPassword'])->middleware('yzcube.token');
// Route::post('{yzcubeUser}/delete', [AuthController::class, 'delete'])->middleware('yzcube.token');
// Route::post('edit-password/{pwd_token}', [AuthController::class, 'editPassword']);
